# 表格标注软件产品需求文档 (PRD)

## 1. 产品概述

### 1.1 项目目标
开发基于PyQt5的表格图像标注软件，支持用户对表格图像进行物理边界标注和逻辑结构编辑，生成结构化的JSON标注数据。软件架构与LabelMe保持一致，便于后续集成和扩展。

### 1.2 核心功能
- 表格图像的单元格物理边界标注
- 表格逻辑结构的自动生成和手动编辑
- 单元格内容和属性编辑
- 多种数据格式的导入导出
- 边框样式的批量设置

## 2. 数据结构定义

### 2.1 核心数据格式
```json
{
  "table_ind": 1,
  "image_path": "path/to/image.png",
  "type": 1,  // 0:非表格纯文本, 1:表格(有线/无线通过边框样式区分)
  "cells": [
    {
      "cell_ind": 1,
      "header": true,
      "content": [
        {
          "bbox": [x1, y1, x2, y2],
          "direction": "horizontal",
          "text": "示例文本",
          "score": 0.99
        }
      ],
      "bbox": {
        "p1": [x1, y1],  // 左上角，顺时针顺序
        "p2": [x2, y2],
        "p3": [x3, y3],
        "p4": [x4, y4]
      },
      "lloc": {
        "start_row": 0,
        "end_row": 0,
        "start_col": 0,
        "end_col": 0
      },
      "border": {
        "style": {
          "top": 0,     // 0:无边框, 1:实线
          "right": 0,
          "bottom": 0,
          "left": 0
        }
      }
    }
  ]
}
```

### 2.2 外部数据格式支持
支持导入以下JSON格式并转换为内部格式：
```json
{
  "table_id": 108105,
  "html": {
    "structure": {
      "tokens": ["<table>", "<tr>", "<td>", "</td>", "</table>"]
    },
    "cells": [
      {
        "tokens": ["D", "e", "c", "e"],
        "bbox": [468.15, 282.02, 514.81, 290.03]
      }
    ]
  },
  "split": "train",
  "filename": "HBAN/2011/page_204.pdf",
  "bbox": [78, 234.495, 534, 291.249]
}
```

## 3. 界面设计规范

### 3.1 总体布局
```
┌─────────────────────────────────────────┐
│ 顶部：文件操作界面                      │
├─────────────────────────┬───────────────┤
│                         │               │
│ 上层：物理标注界面      │               │
│ (图像显示+边界标注)     │ 右侧：        │
│                         │ 表格属性视图  │
├─────────────────────────┤               │
│                         │               │
│ 下层：逻辑视图界面      │               │
│ (表格结构显示+内容编辑) │               │
│                         │               │
└─────────────────────────┼───────────────┤
                          │ 右下：        │
                          │ 标注文件信息  │
                          └───────────────┘
```

### 3.2 界面组件详细定义

#### 3.2.1 顶部文件操作界面
- **文件菜单**：新建、打开、保存、导出
- **编辑菜单**：撤销、重做、恢复
- **视图菜单**：缩放、适应窗口、显示网格
- **工具栏**：常用操作快捷按钮

#### 3.2.2 上层物理标注界面
- **图像显示区域**：支持缩放、平移的图像查看器
- **标注工具**：
  - 矩形标注工具（单元格边界）
  - 选择工具（移动、调整控制点）
  - 删除工具
- **控制面板**：
  - 表格类型选择（非表格文本/有线表格/无线表格）
  - 一键生成表格结构按钮
  - 清除所有标注按钮

#### 3.2.3 下层逻辑视图界面
- **表格结构显示**：使用自定义QTableWidget，支持合并单元格显示
- **实现方案**：
  - 基于PyQt QTableWidget扩展，重写paintEvent实现合并单元格绘制
  - 通过单元格背景色和边框样式区分表头和普通单元格
  - 合并单元格通过setSpan()方法实现视觉效果
- **视觉元素**：
  - 表头单元格：浅蓝色背景标识
  - 边框样式：根据border属性绘制不同线型（实线/虚线/无边框）
  - 选中状态：高亮边框标识当前选中单元格
- **内容交互**：
  - 双击单元格进入内容编辑模式
  - 单击选择单元格，信息同步到右侧属性面板
  - 支持多选操作（Ctrl+点击）
- **性能优化**：
  - 原生Qt控件，响应速度快
  - 按需重绘，只刷新变化的单元格区域
  - 支持大表格的虚拟滚动

#### 3.2.4 右侧表格属性视图
- **选中单元格属性编辑**：
  - 逻辑坐标编辑（start_row, end_row, start_col, end_col）
  - 边框样式设置（上/下/左/右）
  - 表头属性设置（是否为表头）
  - 单元格内容预览和编辑
- **批量操作工具**：
  - 批量边框设置（设置所有边框/清除所有边框/仅设置外边框）
  - 批量表头设置
  - 选中区域信息显示
- **表格全局属性**：
  - 表格类型选择
  - 表格索引编辑
  - 表格统计信息（行数、列数、单元格数量）

#### 3.2.5 右下标注文件信息
- **当前文件状态**：文件路径、图像尺寸、保存状态
- **标注进度**：单元格数量、表格数量、完成度
- **操作历史**：最近操作记录、撤销/重做状态

## 4. 用户交互流程

### 4.1 标注工作流程
1. **文件加载**：打开图像文件或文件夹
2. **表格类型选择**：选择非表格文本/有线表格/无线表格
3. **物理边界标注**：
   - 使用矩形工具标注单元格边界
   - 调整边界框控制点
   - 设置边框样式（基于表格类型的默认值）
4. **生成逻辑结构**：
   - 手动框选区域生成表格结构
   - 或使用一键生成功能
5. **逻辑编辑**：
   - 编辑单元格内容
   - 调整逻辑坐标
   - 设置表头属性
6. **保存导出**：保存为JSON格式

### 4.2 数据一致性机制
- **物理→逻辑同步**：物理边界变化后，逻辑界面锁定，提示用户确认更新
- **确认对话框内容**：
  - 变化影响的单元格数量
  - 变化概览信息
  - 确认/取消选项
- **状态恢复**：
  - 恢复单个表格的物理状态
  - 恢复整个图像的所有标注

### 4.3 边框样式设置流程
1. **默认设置**：
   - 有线表格：所有边框默认为1（实线）
   - 无线表格：所有边框默认为0（无边框）
2. **手动调整**：
   - 框选单元格（支持Ctrl+点击多选）
   - 在属性面板设置边框样式
   - 支持批量操作

## 5. 功能需求详细规范

### 5.1 核心功能

#### 5.1.1 物理标注功能
- **单元格边界标注**：
  - 矩形标注工具，支持拖拽绘制
  - 控制点拖拽调整边界
  - 边界框的选择、移动、删除
- **标注质量控制**：
  - 预留接口，暂不实现
  - 人工调整和删除机制

#### 5.1.2 逻辑结构生成
- **手动生成**：
  - 矩形框选多个单元格
  - 识别框选区域内的单元格
  - 生成行列逻辑信息
- **一键生成**：
  - 基于位置的网格推断算法
  - 容错机制（±10像素偏差）
  - 异常单元格标记为独立单元格

#### 5.1.3 逻辑编辑功能
- **内容编辑**：在逻辑视图中双击单元格进入文本编辑模式
- **结构调整**：在右侧属性面板修改逻辑坐标实现合并/拆分单元格
- **属性设置**：在右侧属性面板设置表头标识、边框样式等属性
- **选择操作**：支持单选、多选，选中后在属性面板统一编辑

#### 5.1.4 数据管理功能
- **文件操作**：
  - 打开单个图像文件
  - 打开图像文件夹（批量处理）
  - 保存JSON标注文件
  - 导出多种格式
- **数据导入**：
  - 支持外部JSON格式导入
  - 自动格式转换
- **状态管理**：
  - 自动保存机制
  - 操作历史记录
  - 多级恢复功能

### 5.2 辅助功能

#### 5.2.1 界面操作
- **图像查看**：
  - 缩放（滚轮、快捷键）
  - 平移（拖拽、方向键）
  - 适应窗口大小
- **选择操作**：
  - 单个单元格选择
  - 多选（Ctrl+点击）
  - 框选区域
- **逻辑结构查看**：
  - 基于QTableWidget的原生表格控件
  - 自定义绘制支持合并单元格可视化
  - 边框样式实时显示
  - 表头单元格颜色标识

#### 5.2.2 效率优化
- **快捷键支持**：
  - 标注工具切换
  - 常用操作快捷键
  - 预留接口，后续版本实现
- **批量操作**：
  - 边框样式批量设置
  - 属性批量修改
- **模板功能**：
  - 预留接口，后续版本实现

### 5.3 预留接口

#### 5.3.1 OCR集成接口
```python
class OCRInterface:
    def recognize_text(self, image_region) -> dict:
        """
        OCR文本识别接口
        Args:
            image_region: 图像区域
        Returns:
            识别结果字典，包含text、bbox、score等
        """
        pass
```

#### 5.3.2 质量控制接口
```python
class QualityControlInterface:
    def validate_annotation(self, annotation_data) -> list:
        """
        标注质量检查接口
        Args:
            annotation_data: 标注数据
        Returns:
            问题列表
        """
        pass
```

#### 5.3.3 效率优化接口
```python
class AutomationInterface:
    def suggest_template(self, image) -> dict:
        """
        模板建议接口
        Args:
            image: 输入图像
        Returns:
            建议的模板配置
        """
        pass
```

## 6. 技术规范

### 6.1 技术栈要求
- **前端框架**：PyQt5（与LabelMe保持一致）
- **后端逻辑**：Python 3.7+
- **数据存储**：JSON格式
- **图像处理**：PIL/OpenCV
- **文件管理**：标准文件系统操作

### 6.2 架构设计
- **MVC架构**：模型-视图-控制器分离
- **逻辑视图实现**：基于QTableWidget扩展，通过重写paintEvent和setSpan()实现合并单元格显示
- **模块化设计**：各功能模块独立，便于扩展
- **插件机制**：预留OCR和质量控制插件接口
- **配置管理**：用户设置和软件配置分离

### 6.3 性能要求
- **响应时间**：界面操作响应时间<100ms
- **内存使用**：单个图像处理内存占用<500MB
- **文件处理**：支持单个图像文件大小≤50MB
- **批量处理**：支持同时处理≤1000个图像文件

### 6.4 兼容性要求
- **操作系统**：Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **Python版本**：3.7及以上
- **图像格式**：PNG, JPG, JPEG, BMP, TIFF
- **数据导入**：支持指定外部JSON格式

## 7. 质量要求

### 7.1 可用性要求
- **学习成本**：新用户30分钟内掌握基本操作
- **操作效率**：熟练用户标注单个表格<5分钟
- **错误处理**：所有用户操作都有明确反馈
- **帮助系统**：提供操作指南和工具提示

### 7.2 可靠性要求
- **数据安全**：自动保存机制，防止数据丢失
- **异常处理**：程序异常时优雅退出，保存工作进度
- **数据一致性**：确保物理标注和逻辑数据的一致性
- **恢复机制**：支持操作撤销和状态恢复

### 7.3 扩展性要求
- **代码结构**：与LabelMe兼容，便于集成
- **接口预留**：为OCR、质量控制等功能预留标准接口
- **配置灵活**：支持自定义标签、快捷键等配置
- **数据格式**：支持多种导入导出格式