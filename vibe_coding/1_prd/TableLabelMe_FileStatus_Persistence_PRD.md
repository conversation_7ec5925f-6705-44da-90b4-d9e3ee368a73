# TableLabelMe 文件状态持久化需求规格说明书

## 1. 需求背景

### 1.1 问题描述

目前，TableLabelMe 应用程序在 `_update_file_item_status` 方法中仅在内存中管理文件浏览状态，当程序退出、意外中断或崩溃时，这些状态信息会丢失。用户在重新启动应用程序后，无法恢复之前的文件浏览状态，导致工作连续性受到影响。

### 1.2 现有实现

当前的文件状态管理主要通过以下几个方法实现：

- `_get_file_view_status`: 获取文件浏览状态，返回 'never_viewed'（未浏览）、'viewed_no_save'（已浏览未保存）或 'saved'（已保存）
- `_update_file_item_status`: 根据文件状态更新文件列表项的显示
- `viewed_files`: 一个集合（set），用于在内存中记录已浏览过的文件

浏览状态的判断逻辑：
1. 检查是否有对应的标注文件（`_has_annotation_file`）
2. 检查文件是否在 `viewed_files` 集合中
3. 根据上述检查结果返回相应的状态

### 1.3 需求目标

开发一个简单的文件状态持久化机制，确保文件浏览状态（特别是"已浏览未保存"状态）在程序退出后仍能保存，并在程序重新启动时恢复，提高用户工作的连续性和效率。

## 2. 功能需求

### 2.1 核心功能

1. **状态持久化**：将文件浏览状态（主要是"已浏览未保存"状态）持久化到磁盘
2. **状态恢复**：程序启动时自动加载并恢复之前的文件浏览状态
3. **状态更新**：当文件状态变化时更新持久化数据

### 2.2 状态数据模型

需要持久化的文件状态信息仅包括：

1. **文件路径**：文件的相对路径或绝对路径，作为唯一标识
2. **浏览状态**：主要关注"已浏览未保存"（viewed_no_save）状态
   - 注意："已保存"（saved）状态可通过检查标注文件是否存在来判断，无需额外存储
   - "未浏览"（never_viewed）状态是默认状态，也无需存储

### 2.3 用户界面需求

无需额外的用户界面变更，保持现有的文件状态显示方式即可。

## 3. 技术方案

### 3.1 存储方式

使用简单的 JSON 格式存储已浏览文件列表：

```json
{
  "version": "1.0",
  "last_updated": "2025-06-24T22:30:00",
  "viewed_files": [
    "/path/to/file1.jpg",
    "/path/to/file2.jpg",
    "/path/to/file3.jpg"
  ]
}
```

### 3.3 自动保存机制

实现简单的自动保存机制：

1. **程序退出保存**：在程序正常退出时保存状态数据
2. **状态变更保存**：当文件浏览状态发生变化时保存状态数据

## 4. 实现计划

### 4.1 代码修改

1. 添加 `_save_viewed_files_state` 和 `_load_viewed_files_state` 方法
2. 修改 `__init__` 方法，调用 `_load_viewed_files_state` 加载状态
3. 修改 `closeEvent` 方法，调用 `_save_viewed_files_state` 保存状态
4. 修改 `load_image` 方法，在更新 `viewed_files` 集合后调用 `_save_viewed_files_state`