# 表格标注工具产品需求文档（PRD）

## 一、产品背景

为构建高质量的表格识别数据集，我们需要开发一款支持 **真实表格标注** 的工具。用户可通过该工具完成对图片中的表格结构、单元格框、逻辑位置、边框属性、文字内容等多维度信息的精细标注，并导出符合模型训练所需的标准 JSON 格式数据。

本工具将以开源项目 **[LabelMe](https://github.com/wkentaro/labelme)** 为基础进行二次开发，并由两名开发人员 Xelawk+AI 和 OYY+AI 协作完成（ +AI 的意思是通过 LLM Based IDE 进行开发），要求功能高度解耦、便于模块化 mock 联调。

## 二、核心功能模块拆解

### 1. 图片导入与管理

* ✅ 支持加载单张图片与多张批量浏览
* ✅ 支持缩放、拖拽、切换图片
* ✅ 支持显示当前图片标注状态（是否完成）

### 2. 单元格框选标注（cell bbox）

#### 功能点：

* ✅ 支持鼠标拉框标注单个单元格（按顺时针四点）
* ✅ 支持大范围框选批量初始标注（自动按网格分解成多个 cell）
* ✅ 支持拖动角点 / 边界，微调单元格框位置
* ✅ 支持自动对齐吸附到相邻 cell 边缘（选配）
* ✅ 支持删除 / 撤销 / 重做操作

#### 状态机说明：

* 空闲 → 框选中 → 框选完成 → 微调中 → 完成标注

#### 标注输出字段（示例）：

```json
"bbox": {
  "p1": [x1, y1], "p2": [x2, y2],
  "p3": [x3, y3], "p4": [x4, y4]
}
```

### 3. 表格逻辑结构标注（逻辑行列位置 lloc）

#### 功能点：

* ✅ 支持选中多个 cell，通过框选、点选等方式，设置统一逻辑位置（如全部设置为第 2 行）
* ✅ 支持批量横向/纵向划线，推理行列逻辑（模拟 Excel 框线操作）
* ✅ 支持系统根据 cell 水平/垂直分布，自动聚类初步行列索引（可手动调整）
* ✅ 支持跨行/列单元格逻辑合并

#### 状态机说明：

* 空闲 → 批量选中 → 指定为某行或列 → 设置起止索引 → 完成

#### 标注输出字段（示例）：

```json
"lloc": {
  "start_row": 0, "end_row": 0,
  "start_col": 0, "end_col": 0
}
```

### 4. 边框属性标注（border style）

#### 功能点：

* ✅ 支持为每个 cell 四条边设置是否有线（实线/无线）
* ✅ 预留支持未来“虚线/点线”等扩展样式
* ✅ 支持快捷键批量设边属性，如“一键全设为无线”

#### 标注输出字段（示例）：

```json
"border": {
  "style": { "top": 1, "right": 1, "bottom": 0, "left": 0 }
}
```

### 5. 文本内容标注（OCR 结果录入）

#### 功能点：

* ✅ 支持点击单元格后弹出文本框，人工输入内容
* ✅ 支持接入本地模型推理接口，自动识别图片区域文本填入 cell
* ✅ 支持每个 cell 内容包括 bbox + text + score + direction 等信息

#### 状态机说明：

* 点击 cell → 加载内容 → 手动填充 / 模型填充 → 可编辑 → 保存

#### 标注输出字段（示例）：

```json
"content": [
  {
    "bbox": [...],
    "text": "张三",
    "score": 0.99,
    "direction": "horizontal"
  }
]
```

### 6. 表格结构渲染与可视化

#### 功能点：

* ✅ 实时将标注结构渲染为 HTML 表格（含合并单元格、边框样式）
* ✅ 支持 Markdown 模式快速预览
* ✅ 支持点击表格单元格，高亮图中原始位置
* ✅ 支持 hover 时显示详细信息（逻辑位置、内容、边框状态等）

### 7. 多表格支持

* ✅ 支持一张图中存在多个 table（table\_ind）
* ✅ 支持手动标记某一区域为一个表格区域，作为 table 单元入口
* ✅ 每个表格拥有独立 cells 数组、type 类型、table\_ind 编号

### 8. 标注导入 / 导出

#### 功能点：

* ✅ 支持导入已有 bbox/ocr/lloc 标注文件，继续人工修正
* ✅ 支持导出每张图一个 JSON 文件，与原图文件名一致
* ✅ 支持 JSON Schema 校验结构一致性
* ✅ 支持标注版本管理（预留）

### 9. 模型辅助模块（外部推理模块）

#### 功能点：

* ✅ 提供 API 接口，调用本地模型对整图/区域做 OCR 推理
* ✅ 模型结果自动匹配到 cell 中（基于重叠面积、中心点等策略）
* ✅ 用户可对模型填入结果进行人工修正

---

## 三、JSON 数据结构规范

```json
{
  "table_ind": 0,
  "image_path": "xxx/xxx.png",
  "type": 1,
  "cells": [
    {
      "cell_ind": 0,
      "header": false,
      "content": [...],
      "bbox": { "p1": [...], "p2": [...], "p3": [...], "p4": [...] },
      "lloc": {
        "start_row": 0, "end_row": 0,
        "start_col": 0, "end_col": 0
      },
      "border": {
        "style": {
          "top": 1, "right": 1, "bottom": 1, "left": 1
        }
      }
    }
  ]
}
```

---

## 四、开发任务拆解

| 模块       | 子任务                | 负责人       |
| -------- | ------------------ | --------- |
| 图像导入模块   | 加载图像、多图浏览、缩放拖拽     | Xelawk+AI |
| 单元格标注模块  | 框选、微调、自动对齐、撤销/重做   | Xelawk+AI |
| 表格逻辑结构模块 | 批量选中设行列、划线识别       | OYY+AI    |
| 边框属性模块   | 设置四边属性、快捷批量处理      | OYY+AI    |
| OCR文本标注  | 接入模型、人工编辑、字段管理     | Xelawk+AI |
| HTML渲染模块 | 渲染逻辑结构表格、联动高亮      | OYY+AI    |
| 多表格管理模块  | 支持多个 table 区块、编号管理 | Xelawk+AI |
| 导入导出模块   | 解析/生成 JSON 文件、格式校验 | OYY+AI    |
| 模型推理接口   | 接入 OCR 模型接口、结果融合策略 | Xelawk+AI |

> 所有模块需保持配置解耦，支持 mock stub 测试开发流程。

---

如需继续生成接口文档、交互事件列表、JSON schema 校验器说明，请告知我继续完善。
