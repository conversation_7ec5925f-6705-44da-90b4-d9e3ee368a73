# **表格标注工具最终产品需求文档 (PRD)**

## 1. 产品概述

### 1.1 项目背景与目标
为构建高质量的表格识别模型数据集，我们需开发一款功能强大的图像表格标注工具。该工具旨在支持用户对图片中的表格进行多维度信息的精细化标注，包括单元格物理边界（bbox）、逻辑结构（行列位置）、边框样式及文字内容。最终产出物为符合模型训练需求的、结构化的JSON数据。

本项目将以开源项目 **[LabelMe](https://github.com/wkentaro/labelme)** 的架构为基础进行二次开发，确保代码结构清晰、便于集成与后续扩展。框架采用 **PyQt5** 进行开发。

### 1.2 核心特性
* **物理标注**: 精确的单元格四点边框框选与微调。
* **逻辑标注**: 便捷的单元格逻辑行列位置定义与合并。
* **内容录入**: 支持手动输入和OCR模型辅助识别。
* **样式标注**: 灵活的单元格边框（实线/无线）属性设置。
* **实时预览**: 所见即所得的HTML/Markdown表格结构渲染。
* **高效交互**: 批量操作、快捷键支持与智能辅助功能。
* **高度扩展**: 预留OCR、质量控制等外部模块的集成接口。

## 2. 功能需求详解

### 2.1 图像管理模块
* **文件加载**: 支持加载单张图片，或打开文件夹进行多张图片的批量浏览。
* **视图操作**:
    * 支持使用鼠标滚轮或快捷键对图像进行缩放。
    * 支持拖拽平移图像。
    * 提供"适应窗口"功能，使图像完整显示在可视区域。
* **状态显示**: 在文件列表中明确标识每张图片的标注状态（例如：未开始、进行中、已完成）。

### 2.2 单元格物理标注模块 (Cell Bbox)
* **基础框选**: 支持用户按住鼠标左键，通过拉框方式标注单个单元格的矩形边界。标注点应为顺时针四点。
* **批量初始标注**: 支持用户框选一个大的表格区域，系统可自动按网格初步分解为多个单元格。
* **微调与对齐**:
    * 支持拖动单元格的四个角点或四条边，对位置和大小进行精确调整。
    * (可选)提供自动吸附功能，在拖动时自动对齐到邻近单元格的边缘，容错偏差范围为±10像素。
    * **对齐工具**: 提供顶端对齐、底端对齐、左对齐、右对齐功能，作用于物理视图中选中的单元格。对齐工具按钮位于顶部工具栏和右侧属性面板中。
* **基础编辑**: 提供删除、撤销（Undo）、重做（Redo）等基本编辑操作。

### 2.3 表格逻辑结构模块 (Logical Location)
* **逻辑位置分配**:
    * 支持用户通过点选（Ctrl+单击）或框选方式选中多个单元格。
    * 在属性面板中，可为所有选中的单元格统一设置其逻辑位置（如：起始行、终止行、起始列、终止列）。
    * 支持跨行列的复杂单元格（合并单元格）的逻辑位置定义。
* **智能生成与辅助**:
    * **划线推理**: 支持用户通过模拟Excel画表格线的方式（横向/纵向划线），辅助系统推断行列逻辑。
    * **一键生成**: 提供"一键生成表格结构"按钮。系统根据所有已标注单元格的物理位置（水平/垂直分布）自动进行聚类，初步推断并生成全表的行列索引。异常单元格将被标记为独立的单元格。
* **智能数据同步机制**: 当用户在物理视图中修改单元格bbox后，系统按以下规则处理：
    * **轻微调整 (Minor Change)**: 
        - 触发条件：用户小幅移动或调整单元格大小，但单元格的拓扑中心仍在原逻辑行列范围内
        - 系统行为：静默更新。系统仅更新受影响单元格的bbox物理坐标，不触碰任何lloc逻辑信息，不弹出任何对话框
    * **重大变更 (Major Change)**:
        - 触发条件：用户删除单元格、新增单元格，或大幅移动单元格导致其拓扑中心跨越到新的行列
        - 系统行为：弹出确认对话框，提供以下选择：
            - **智能更新 (未来实现)**: (预留接口) 尝试局部更新逻辑，最大限度保留用户的手动修改
            - **完全重算 (V1实现)**: 弹窗内容需明确警告："此操作将清除所有手动的逻辑修改（如合并、拖拽排序等），并根据当前的物理布局重新生成整个表格结构。"
            - **仅更新物理坐标**: 告知用户逻辑结构可能已不匹配，但允许用户只保存物理位置的变更，手动去修正逻辑
            - **取消**: 撤销刚才的物理边界改动，恢复到修改前的状态

### 2.4 单元格属性标注模块

#### 2.4.1 边框属性 (Border Style)
* **样式定义**: 支持为每个单元格的四条边（上、下、左、右）分别设置边框样式。初期支持 **实线 (1)** 和 **无线 (0)** 两种。
* **默认规则**:
    * 当表格类型设置为"有线表格"时，新建单元格的边框默认为全实线。
    * 当表格类型设置为"无线表格"时，新建单元格的边框默认为全无线。
* **批量操作**: 支持框选多个单元格后，在属性面板进行批量设置，如"一键设为无线"、"仅保留外边框"等。
* **扩展性**: 预留接口，以便未来支持虚线、点线等更多边框样式。

#### 2.4.2 文本内容 (OCR & Manual Input)
* **手动输入**: 支持用户单击或双击逻辑视图中的单元格，在弹出的文本框或属性面板中人工录入/编辑文字内容。
* **模型辅助 (OCR)**:
    * 提供调用外部OCR模型推理的接口。
    * 支持对整图或选中区域进行OCR识别。
    * 模型返回的文本结果（包含text, bbox, score等）将基于空间重叠度（如IoU）或中心点位置策略，自动填充到对应的单元格中。
    * 用户可对模型自动填充的结果进行人工修正。

#### 2.4.3 其他属性
* **表头设置**: 支持将一个或多个单元格标记为"表头"（header），在逻辑视图中以特定背景色（如浅蓝色）进行区分。

### 2.5 渲染与可视化模块
* **逻辑视图**:
    * 使用基于 `PyQt QTableWidget` 扩展的自定义原生控件，实时将标注的逻辑结构渲染为一个HTML风格的表格。
    * 通过重写 `paintEvent` 和调用 `setSpan()` 方法，准确支持合并单元格的可视化。
    * 根据单元格的 `border` 属性，实时绘制其边框样式（实线/无线）。
* **联动高亮**:
    * 在逻辑视图中单击或悬停（hover）某个单元格时，物理标注界面中的对应单元格边框高亮显示。
    * 反之，在物理标注界面中点击某个单元格时，逻辑视图中的对应单元格也应高亮或滚动到可视区域。
* **信息预览**:
    * 支持Markdown模式快速预览生成的表格结构。
    * 悬停在单元格上时，显示其详细信息Tooltip（如逻辑坐标、内容、边框状态等）。

### 2.6 多表格支持
* **表格区域定义**: 支持在一张图片中标注多个独立的表格区域。
* **独立管理**: 每个表格拥有独立的 `table_ind` 编号、`cells` 数组以及表格类型（`type`）等属性，数据彼此隔离。

### 2.7 数据导入/导出模块
* **导出功能**:
    * 支持将当前图片的标注结果导出为单个JSON文件，文件名与原图保持一致。
    * 导出的JSON文件需经过Schema校验，确保结构的标准与一致性。
* **导入功能**:
    * 支持导入已有的JSON标注文件，加载后可继续进行人工编辑和修正。
    * 支持导入指定的外部JSON格式（如下方 `3.2` 节所示），并能自动转换成系统内部标准格式。
* **版本管理**: (预留)为未来的标注版本控制预留设计空间。

## 3. 数据结构规范

### 3.1 核心数据格式
```json
{
  "image_path": "path/to/your/image.png",
  "table_ind": 0,
  "type": 1,
  "cells": [
    {
      "cell_ind": 0,
      "header": false,
      "content": [
        {
          "bbox": [x1, y1, x2, y2],
          "text": "示例文字",
          "score": 0.99,
          "direction": "horizontal"
        }
      ],
      "bbox": {
        "p1": [x1, y1],
        "p2": [x2, y2],
        "p3": [x3, y3],
        "p4": [x4, y4]
      },
      "lloc": {
        "start_row": 0, "end_row": 0,
        "start_col": 0, "end_col": 0
      },
      "border": {
        "style": {
          "top": 1, "right": 1, "bottom": 1, "left": 1
        }
      }
    }
  ]
}
```
**字段说明:**
* `image_path`: 图像的相对或绝对路径。
* `table_ind`: 表格在当前图片中的索引，从0开始。
* `type`: 表格类型。例如: `1` 代表普通表格（有线/无线通过border区分），`0` 可预留给非表格的纯文本区域。
* `cells`: 包含该表格内所有单元格对象的数组。
* `cell_ind`: 单元格在当前表格内的唯一索引，从0开始。
* `header`: 布尔值，`true` 表示该单元格是表头。
* `content`: 包含OCR识别结果的数组，支持一个单元格内有多个文字片段。
    * `bbox`: 文字片段的矩形边界框 `[x_min, y_min, x_max, y_max]`。
    * `text`: 识别出的文本。
    * `score`: 识别结果的置信度。
    * `direction`: 文字方向（如: horizontal）。
* `bbox`: 单元格的物理边界，顺时针四点坐标 `[x, y]`。
* `lloc`: 单元格的逻辑位置，定义其在表格中的行列跨度。
* `border.style`: 定义四条边的样式，`1`为实线，`0`为无线。

### 3.2 外部数据格式兼容
需支持导入并转换以下结构的外部JSON数据，以兼容其他数据集。
```json
{
  "table_id": 108105,
  "html": {
    "structure": { "tokens": [...] },
    "cells": [
      {
        "tokens": [...],
        "bbox": [468.15, 282.02, 514.81, 290.03]
      }
    ]
  },
  "filename": "...",
  "bbox": [...]
}
```

## 4. 用户界面 (UI) 与交互流程 (UX)

### 4.1 总体布局
采用经典的多区域布局，保证信息清晰、操作高效。
```
┌─────────────────────────────────────────┐
│ [A] 顶部菜单栏与工具栏                     │
├─────────────────────────┬───────────────┤
│                         │ [C]           │
│ [B] 物理标注界面          │  右侧属性      │
│                         │   与          │
│ (图像显示, 单元格框选)     │ 工具面板       │
│                         │               │
├─────────────────────────┤               │
│                         │               │
│ [D] 逻辑视图界面          │               │
│ (HTML表格渲染, 内容编辑)   ├──────────────┤          
│                         │   [E] 右下角   │
│                         │  文件与状态信息 │
└─────────────────────────────────────────┤
```
### 4.2 界面组件定义
* **[A] 顶部区域**:
    * **菜单栏**: 文件（新建、打开、保存）、编辑（撤销、重做）、视图（缩放、显示网格）、工具等。
    * **工具栏**: 
        - 常用功能的快捷图标按钮。
        - **对齐工具按钮**: 顶端对齐、底端对齐、左对齐、右对齐按钮，便于快速对齐选中的单元格。
* **[B] 物理标注界面**:
    * 核心图像显示区域，支持流畅的缩放与平移。
    * 提供标注工具：选择工具、矩形框选工具、删除工具等。
* **[C] 右侧属性与工具面板**:
    * **单元格属性**: 显示和编辑当前选中单元格的逻辑坐标、边框样式、表头属性等。
    * **内容编辑区**: 预览和编辑单元格文本内容。
    * **批量操作**: 提供批量设置边框、批量设置表头等工具。
    * **对齐工具**: 提供顶端、底端、左、右对齐按钮，作用于物理视图中选中的单元格。
    * **表格全局属性**: 设置表格类型、表格索引，并显示行数、列数等统计信息。
* **[D] 逻辑视图界面**:
    * 基于 `QTableWidget` 实现的可交互表格，实时渲染逻辑结构。
    * 支持合并单元格、不同边框样式和表头背景色。
    * 双击单元格可直接进入内容编辑模式。
* **[E] 右下角信息区**:
    * 显示当前文件路径、图像尺寸、保存状态。
    * 显示标注进度，如已标注的单元格数量和表格数量。

### 4.3 核心工作流程
1.  **加载**: 用户通过"文件"->"打开"加载单张图片或图片文件夹。
2.  **区域定义**: 用户在图片上框选出整个表格区域。
3.  **物理标注**: 使用矩形工具在表格区域内对所有单元格进行框选，可利用对齐工具进行规整。
4.  **生成逻辑**: 点击"一键生成表格结构"，系统自动分析并创建初始逻辑表格。
5.  **逻辑修正**: 在逻辑视图和属性面板中，对合并单元格、表头、行列错误等进行手动调整。
6.  **内容与样式**: 运行OCR或手动输入文字内容，并根据需要调整边框样式。
7.  **保存**: 保存标注结果为与图片同名的JSON文件。

## 5. 技术与非功能性需求

### 5.1 技术栈与架构
* **前端框架**: PyQt5
* **后端逻辑**: Python 3.7+
* **图像处理**: Pillow / OpenCV
* **架构模式**: 采用模型-视图-控制器 (MVC) 分离的设计，确保代码的低耦合和高可维护性。
* **模块化**: 所有功能模块需高度解耦，支持独立的mock联调测试。

### 5.2 性能要求
* **响应时间**: 所有UI交互操作的响应时间应低于100毫秒。
* **内存占用**: 处理单张常规尺寸图片时，内存占用应控制在500MB以内。
* **文件处理**: 支持最大50MB的图像文件，支持文件夹内上千张图片的批量处理列表。

### 5.3 兼容性要求
* **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+。
* **图像格式**: 支持常见的PNG, JPG/JPEG, BMP等。

### 5.4 质量与可用性
* **可靠性**: 提供自动保存机制，防止意外退出时数据丢失。提供多级撤销/重做功能。
* **易用性**: 交互逻辑清晰，新用户能在30分钟内掌握核心标注流程。提供必要的工具提示（Tooltips）。
* **扩展性**: 预留清晰的插件接口，便于未来集成更多功能。

## 6. 扩展接口规范 (预留)

为保证软件的未来扩展能力，预定义以下Python类接口。

#### OCR集成接口
```python
class OCRInterface:
    def recognize_text(self, image_region: object) -> dict:
        """
        对指定的图像区域进行文本识别。
        Args:
            image_region: 待识别的图像数据 (e.g., a PIL Image or NumPy array)。
        Returns:
            包含text, bbox, score等信息的识别结果字典。
        """
        pass
```

#### 质量控制接口
```python
class QualityControlInterface:
    def validate_annotation(self, annotation_data: dict) -> list:
        """
        对单份标注数据进行质量校验。
        Args:
            annotation_data: 符合内部规范的JSON数据字典。
        Returns:
            一个包含所有发现问题的字符串列表。
        """
        pass
```

## 7. 开发任务拆解

| 模块 | 子任务 | 负责人 |
| :--- | :--- | :--- |
| **基础框架与图像模块** | 图像加载、多图浏览、缩放拖拽 | Xelawk+AI |
| **单元格物理标注模块** | 框选、微调、自动对齐、撤销/重做、对齐工具 | Xelawk+AI |
| **表格逻辑结构模块** | 批量选中设行列、划线识别、一键生成、智能数据同步 | OYY+AI |
| **单元格属性模块** | 边框、表头属性设置，快捷批量处理 | OYY+AI |
| **文本内容标注模块** | 接入模型接口、人工编辑、字段管理 | Xelawk+AI |
| **渲染与可视化模块** | 逻辑结构表格渲染（QTableWidget）、联动高亮 | OYY+AI |
| **多表格管理模块** | 支持多个table区块、编号管理 | Xelawk+AI |
| **数据导入/导出模块** | 解析/生成标准JSON、外部格式兼容、格式校验 | OYY+AI |
| **模型推理接口模块** | 定义并实现OCR模型调用接口、结果融合策略 | Xelawk+AI |

> **协作要求**: 所有模块需保持配置解耦，支持 mock stub 测试开发流程，便于并行开发与集成。