# TableLabelMe第三方API辅助打标功能需求文档

## 1. 功能概述

开发一个第三方API辅助打标功能，允许用户通过调用外部表格识别API自动生成表格标注，从而提高表格标注效率，减少从头标注的时间成本。

## 2. 用户场景与痛点

**核心痛点**：从头标注一张表格样本的时间成本过高，严重影响标注效率。

**使用场景**：用户在开始标注新表格图片时，希望能快速获得一个基础标注结果，然后基于此进行修改和完善，而不是从零开始手动标注。

## 3. 功能详细需求

### 3.1 用户界面需求
- 在表格属性栏中新增"第三方API辅助打标"功能区域
- 提供API选择下拉框，允许用户选择不同的第三方API服务
- 操作简单明了，一键式体验

### 3.2 API调用需求
- 支持多个第三方表格识别API的集成
- 图像预处理：限制上传图像的最长边为2048像素，如有尺寸变换需进行坐标映射转换
- API调用失败时显示错误信息提示
- 不显示API调用进度或加载状态

### 3.3 结果处理需求
- 将API返回的结果完整映射到TableLabelMe的标注协议上
- API识别成功后，立即可视化显示识别结果
- 保存API识别的原始结果，以便后续分析或对比
  - 原始结果保存格式：`[文件名]-api-resp.json`

### 3.4 异常处理需求
- 仅在API调用失败时弹出失败信息
- 对于识别结果不理想的情况，不需要特别处理

## 4. 非功能需求

### 4.1 性能需求
- API调用响应时间应在用户可接受范围内
- 处理大型表格图像时不应导致应用程序卡顿

### 4.2 兼容性需求
- 确保与现有的表格标注功能无缝集成
- 保持与现有数据结构和文件格式的兼容性

### 4.3 可扩展性需求
- 设计应支持未来添加更多第三方API的可能性
- API接口应设计为可配置的，以适应不同API的参数和认证需求

## 5. 技术约束

- 不需要提供API调用频率限制或额度显示功能
- 不需要支持API密钥或认证信息的配置界面
- 不需要用户对API返回的结果进行审核和确认步骤
- 不需要提供图像预处理选项（如图像增强、旋转校正等）

## 6. 交付标准

- 用户能够通过一键操作调用第三方API进行表格预标注
- API返回的结果能够正确映射到TableLabelMe的标注协议
- 原始API响应结果能够正确保存为JSON文件
- 在API调用失败时能够显示明确的错误信息 