# 表格标注软件产品需求文档 (PRD) - v2.0

## 1. 产品概述

### 1.1 项目目标
开发一款基于PyQt5的专业级表格图像标注软件。软件需支持用户对单个表格图像进行精确的物理边界标注和高效的逻辑结构编辑，最终生成用于AI模型训练的、高度结构化的JSON标注数据。软件架构应与LabelMe保持一致，以确保未来的可扩展性和集成性。

### 1.2 核心功能
- **单元格物理边界标注**：支持任意四边形标注
- **表格逻辑结构生成与编辑**：包括自动生成、手动拖拽调整、合并/拆分等
- **单元格内容手动编辑**：支持在逻辑视图中直接输入文本
- **数据格式导入导出**：支持核心数据格式的存取及外部格式的兼容
- **批量操作与效率工具**：包括批量边框设置和物理位置对齐工具

## 2. 数据结构定义

### 2.1 核心数据格式
```json
{
  "table_ind": 1,
  "image_path": "path/to/image.png",
  "type": 1,
  "cells": [
    {
      "cell_ind": 1,
      "header": false,
      "content": [
        {
          "bbox": [x1, y1, x2, y2],
          "direction": "horizontal",
          "text": "示例文本",
          "score": 0.99
        }
      ],
      "bbox": {
        "p1": [x1, y1],
        "p2": [x2, y2],
        "p3": [x3, y3],
        "p4": [x4, y4]
      },
      "lloc": {
        "start_row": 0,
        "end_row": 0,
        "start_col": 0,
        "end_col": 0
      },
      "border": {
        "style": {
          "top": 1,
          "right": 1,
          "bottom": 1,
          "left": 1
        }
      }
    }
  ]
}
```

**字段说明**：
- **type**: 0代表非表格纯文本, 1代表表格
- **header**: [PM Note] 此字段为未来OCR或自动分析功能保留，V1版本中用户界面不提供设置此属性的功能
- **content.text**: [PM Note] V1版本中，此内容由用户手动输入
- **bbox**: [PM Note] 定义任意四边形的四个角点，顺时针或逆时针顺序需保持一致
- **border.style**: [PM Note] 边框样式仅定义两种状态，以区分逻辑分隔的有无
  - 0: 无边框 (在UI中可渲染为虚线或灰色线以作区分)
  - 1: 实线 (在UI中渲染为实线)

### 2.2 外部数据格式支持
支持导入以下JSON格式并转换为内部格式：
```json
{
  "table_id": 108105,
  "html": {
    "structure": {
      "tokens": ["<table>", "<tr>", "<td>", "</td>", "</table>"]
    },
    "cells": [
      {
        "tokens": ["D", "e", "c", "e"],
        "bbox": [468.15, 282.02, 514.81, 290.03]
      }
    ]
  },
  "split": "train",
  "filename": "HBAN/2011/page_204.pdf",
  "bbox": [78, 234.495, 534, 291.249]
}
```

## 3. 界面设计规范

### 3.1 总体布局
```
┌─────────────────────────────────────────┐
│ 顶部：文件操作与工具栏区域              │
├─────────────────────────┬───────────────┤
│                         │               │
│ 上层：物理标注界面      │               │
│ (图像显示+边界标注)     │ 右侧：        │
│                         │ 表格属性视图  │
├─────────────────────────┤               │
│                         │               │
│ 下层：逻辑视图界面      │               │
│ (表格结构显示+内容编辑) │               │
│                         │               │
└─────────────────────────┼───────────────┤
                          │ 右下：        │
                          │ 标注文件信息  │
                          └───────────────┘
```

### 3.2 界面组件详细定义

#### 3.2.1 顶部文件操作界面
- **文件菜单**：新建、打开、保存、导出
- **编辑菜单**：撤销、重做、恢复
- **视图菜单**：缩放、适应窗口、显示网格
- **工具栏**：
  - 常用操作快捷按钮（打开、保存等）
  - 标注工具切换按钮（选择、绘制单元格）
  - **对齐工具**：顶端对齐、底端对齐、左对齐、右对齐按钮

#### 3.2.2 上层物理标注界面
- **图像显示区域**：支持缩放、平移的图像查看器
- **标注工具**：
  - **单元格绘制工具**：用户通过拖拽绘制一个矩形，然后可拖动矩形的四个角点，将其调整为任意四边形
  - 选择工具：用于选择、移动、调整已标注的单元格四边形
  - 删除工具
- **控制面板**：
  - 表格类型选择（非表格文本/有线表格/无线表格）
  - **生成表格结构按钮**：对当前图像中所有已标注的物理单元格执行逻辑结构推断
  - 清除所有标注按钮

#### 3.2.3 下层逻辑视图界面
- **表格结构显示**：使用自定义QTableWidget，支持合并单元格的精确可视化
- **视觉元素**：
  - 边框样式：根据border属性实时绘制实线或虚线（或无边框）
  - 选中状态：高亮边框标识当前选中的一个或多个单元格
  - **数据绑定**：在物理视图中选择单元格，逻辑视图中对应单元格高亮，反之亦然
- **内容交互**：
  - 双击单元格：进入文本编辑模式，允许用户手动输入内容
  - 单击选择单元格：信息同步到右侧属性面板
  - 多选操作：支持Ctrl+点击或框选进行多选
  - **拖拽操作**：支持拖拽单个或多个已合并的单元格到新的逻辑位置，软件自动更新其lloc属性
- **右键上下文菜单**：
  - 合并选中单元格
  - 拆分单元格（仅对已合并的单元格有效）
  - 批量设置边框 -> 子菜单: [全部设为实线, 全部设为无边框]

#### 3.2.4 右侧表格属性视图
- **选中单元格属性编辑**：
  - 逻辑坐标编辑（start_row, end_row, start_col, end_col），与逻辑视图拖拽操作双向绑定
  - 边框样式设置（上/下/左/右复选框或下拉菜单）
  - **[已移除] 表头属性设置功能**
  - 单元格内容预览和编辑
- **批量操作工具**：
  - **对齐工具**：提供顶端、底端、左、右对齐按钮，作用于物理视图中选中的单元格
  - 批量边框设置：提供"全部设为实线"、"全部设为无边框"按钮
  - 选中区域信息显示
- **表格全局属性**：
  - 表格类型选择
  - 表格索引编辑
  - 表格统计信息（行数、列数、单元格数量）

#### 3.2.5 右下标注文件信息
- **当前文件状态**：文件路径、图像尺寸、保存状态
- **标注进度**：单元格数量、表格数量、完成度
- **操作历史**：最近操作记录、撤销/重做状态

## 4. 用户交互流程

### 4.1 标注工作流程
1. **文件加载**：打开单个图像文件
2. **物理边界标注**：使用单元格绘制工具，先画矩形再拖拽角点，标注所有单元格。可使用对齐工具规整标注框
3. **生成逻辑结构**：点击"生成表格结构"按钮，软件根据所有物理框的相对位置，自动推断并生成初始的逻辑网格
4. **逻辑编辑与修正**：
   - **结构调整**：通过在逻辑视图中拖拽单元格、右键合并/拆分，或在属性面板修改lloc来修正表格结构
   - **内容输入**：双击逻辑单元格，手动输入其文本内容
   - **属性调整**：通过右键菜单或属性面板，批量或单独调整单元格的边框样式
5. **保存导出**：保存为核心JSON格式

### 4.2 智能数据同步机制
> [PM Note] 此机制旨在保护用户的编辑成果，避免因微小物理调整导致所有逻辑编辑丢失。

当用户在物理视图中修改一个或多个单元格的bbox后，系统按以下规则处理：

#### **轻微调整 (Minor Change)**：
- **触发条件**: 用户小幅移动或调整单元格大小，但单元格的拓扑中心仍在原逻辑行列范围内
- **系统行为**: 静默更新。系统仅更新受影响单元格的bbox物理坐标，不触碰任何lloc逻辑信息，不弹出任何对话框

#### **重大变更 (Major Change)**：
- **触发条件**: 用户删除单元格、新增单元格，或大幅移动单元格导致其拓扑中心跨越到新的行列
- **系统行为**: 弹出确认对话框，提供以下选择，将控制权交给用户：
  - **【智能更新 (未来实现)】**: (预留接口) 尝试局部更新逻辑，最大限度保留用户的手动修改
  - **【完全重算 (V1实现)】**: 弹窗内容需明确警告："此操作将清除所有手动的逻辑修改（如合并、拖拽排序等），并根据当前的物理布局重新生成整个表格结构。"
  - **【仅更新物理坐标】**: 告知用户逻辑结构可能已不匹配，但允许用户只保存物理位置的变更，手动去修正逻辑
  - **【取消】**: 撤销刚才的物理边界改动，恢复到修改前的状态

### 4.3 边框样式设置流程
1. **默认设置**: 根据加载时选择的"有线/无线表格"类型，初始化所有单元格边框
2. **批量调整**:
   - 在物理视图或逻辑视图中，按住Ctrl键或框选，选择多个单元格
   - 在逻辑视图中右键，选择"批量设置边框" -> "全部设为实线"或"全部设为无边框"
   - 或者，在右侧属性面板点击相应批量操作按钮
3. **单个调整**: 选中单个单元格，在右侧属性面板精细调整其四条边的边框样式

## 5. 功能需求详细规范

### 5.1 核心功能

#### 5.1.1 物理标注功能
- **单元格边界标注**: 提供"矩形+四角拖拽"的四边形绘制工具
- **选择与变换**: 支持对单个或多个四边形进行选择、移动、删除
- **对齐工具**: 提供对齐选中四边形的功能（顶/底/左/右对齐）

#### 5.1.2 逻辑结构生成
- **全图生成**: 基于当前图像中所有已标注的物理单元格，通过位置推断算法一键生成完整的行列逻辑信息

#### 5.1.3 逻辑编辑功能
- **内容编辑**: 在逻辑视图中双击单元格，进入手动文本输入模式
- **结构调整**:
  - **拖拽**: 在逻辑视图中拖拽单元格到新位置以改变其lloc
  - **合并/拆分**: 通过右键菜单对选中单元格进行合并/拆分操作
  - **属性面板**: 直接编辑lloc数值
- **属性设置**: 在右侧属性面板或通过右键菜单设置边框样式

#### 5.1.4 数据管理功能
- **文件操作**：
  - 打开单个图像文件
  - 打开图像文件夹（批量处理）
  - 保存JSON标注文件
  - 导出多种格式
- **数据导入**：
  - 支持外部JSON格式导入
  - 自动格式转换
- **状态管理**：
  - 自动保存机制
  - 操作历史记录
  - 多级恢复功能

### 5.2 辅助功能

#### 5.2.1 界面操作
- **图像查看**：
  - 缩放（滚轮、快捷键）
  - 平移（拖拽、方向键）
  - 适应窗口大小
- **选择操作**：
  - 单个单元格选择
  - 多选（Ctrl+点击）
  - 框选区域
- **逻辑结构查看**：
  - 基于QTableWidget的原生表格控件
  - 自定义绘制支持合并单元格可视化
  - 边框样式实时显示

#### 5.2.2 效率优化
- **快捷键支持**：
  - 标注工具切换
  - 常用操作快捷键
  - 预留接口，后续版本实现
- **批量操作**：
  - **边框样式批量设置**: 对所有选中单元格，统一设为实线或无边框
  - **物理位置对齐**: 对所有选中单元格，执行顶/底/左/右对齐
- **模板功能**：
  - 预留接口，后续版本实现

### 5.3 预留接口

#### 5.3.1 OCR集成接口
```python
class OCRInterface:
    def recognize_text(self, image_region) -> dict:
        """
        OCR文本识别接口
        Args:
            image_region: 图像区域
        Returns:
            识别结果字典，包含text、bbox、score等
        """
        pass
```

#### 5.3.2 质量控制接口
```python
class QualityControlInterface:
    def validate_annotation(self, annotation_data) -> list:
        """
        标注质量检查接口
        Args:
            annotation_data: 标注数据
        Returns:
            问题列表
        """
        pass
```

#### 5.3.3 效率优化接口
```python
class AutomationInterface:
    def suggest_template(self, image) -> dict:
        """
        模板建议接口
        Args:
            image: 输入图像
        Returns:
            建议的模板配置
        """
        pass
```

## 6. 技术规范

### 6.1 技术栈要求
- **前端框架**：PyQt5（与LabelMe保持一致）
- **后端逻辑**：Python 3.7+
- **数据存储**：JSON格式
- **图像处理**：PIL/OpenCV
- **文件管理**：标准文件系统操作

### 6.2 架构设计
- **MVC架构**：模型-视图-控制器分离
- **逻辑视图实现**：基于QTableWidget扩展，通过重写paintEvent和setSpan()实现合并单元格显示
- **模块化设计**：各功能模块独立，便于扩展
- **插件机制**：预留OCR和质量控制插件接口
- **配置管理**：用户设置和软件配置分离

### 6.3 性能要求
- **响应时间**：界面操作响应时间<100ms
- **内存使用**：单个图像处理内存占用<500MB
- **文件处理**：支持单个图像文件大小≤50MB
- **批量处理**：支持同时处理≤1000个图像文件

### 6.4 兼容性要求
- **操作系统**：Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **Python版本**：3.7及以上
- **图像格式**：PNG, JPG, JPEG, BMP, TIFF
- **数据导入**：支持指定外部JSON格式

## 7. 质量要求

### 7.1 可用性要求
- **学习成本**：新用户30分钟内掌握物理标注、逻辑生成和核心编辑流程
- **操作效率**：熟练用户能利用批量操作和对齐工具，高效完成复杂表格的标注
- **错误处理**：所有用户操作都有明确反馈，特别是智能数据同步机制的提示必须清晰、无歧义
- **帮助系统**：提供操作指南和工具提示

### 7.2 可靠性要求
- **数据安全**：自动保存机制，防止数据丢失
- **异常处理**：程序异常时优雅退出，保存工作进度
- **数据一致性**：通过智能数据同步机制，在提供灵活性的同时，最大限度保障物理和逻辑数据的一致性与用户劳动成果
- **恢复机制**：支持操作撤销和状态恢复

### 7.3 扩展性要求
- **代码结构**：与LabelMe兼容，便于集成
- **接口预留**：为OCR、质量控制等功能预留标准接口
- **配置灵活**：支持自定义标签、快捷键等配置
- **数据格式**：支持多种导入导出格式

