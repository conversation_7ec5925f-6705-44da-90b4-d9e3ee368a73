# 表格标注工具详细设计文档 (Low-Level Design)

## 项目结构与总体设计 (Project Structure & Overall Design)

本项目基于开源LabelMe架构进行增强式扩展，采用MVC模式，最小化对原有代码的侵入性修改。核心设计原则：

1. **增强式设计**：复用LabelMe现有的MainWindow、Canvas、Shape体系
2. **模块化扩展**：新增表格专用组件，通过接口与原系统集成
3. **拦截式控制**：TableController拦截Canvas事件，添加表格特定逻辑
4. **数据隔离**：表格数据与原有标注数据并行存储，互不干扰

## 目录结构树 (Directory Tree)

```
labelme/
├── __init__.py                     # 包初始化文件
├── __main__.py                     # 程序入口点
├── app.py                          # 主应用窗口类 (MainWindow)
├── app_tableme.py                  # 表格专用启动器
├── shape.py                        # 基础形状类 (Shape)
├── table_shape.py                  # 表格单元格形状类 (TableCellShape)
├── label_file.py                   # 标注文件处理
├── config/                         # 配置管理
│   ├── __init__.py
│   └── default_config.yaml
├── utils/                          # 工具模块
│   ├── __init__.py
│   ├── _io.py                      # 文件I/O操作
│   ├── image.py                    # 图像处理工具
│   ├── qt.py                       # Qt相关工具函数
│   ├── shape.py                    # 形状操作工具
│   ├── table_analyzer.py           # 表格结构分析器
│   ├── table_data_sync.py          # 智能数据同步管理器
│   ├── table_data_manager.py       # 表格数据统一管理器
│   └── table_exporter.py           # 表格数据导出器
└── widgets/                        # UI组件
    ├── __init__.py
    ├── canvas.py                   # 绘图画布 (增强table_cell支持)
    ├── label_dialog.py             # 标签对话框
    ├── label_list_widget.py        # 标签列表组件
    ├── multi_table_controller.py   # 多表格协调器 (核心新增)
    ├── table_controller.py         # 表格控制器 (核心业务逻辑)
    ├── table_structure_widget.py   # 逻辑表格显示组件
    ├── table_properties_widget.py  # 属性编辑面板
    ├── tool_bar.py                 # 工具栏
    └── zoom_widget.py              # 缩放控制组件
```

## 整体逻辑和交互时序图 (Overall Logic & Sequence Diagram)

以下展示用户进行多表格标注的完整工作流程，**体现MultiTableController的核心协调作用**：

```mermaid
sequenceDiagram
    participant User as 用户
    participant App as TableLabelMe主应用
    participant Canvas as Canvas(增强)
    participant MTC as MultiTableController
    participant TC as TableController
    participant TCS as TableCellShape
    participant TSW as TableStructureWidget
    participant TPW as TablePropertiesWidget
    participant TA as TableAnalyzer
    participant DSM as DataSyncManager
    participant TDM as TableDataManager

    User->>App: 1. 加载图片文件
    App->>Canvas: 显示图像并初始化画布
    App->>MTC: 初始化多表格控制器
    
    User->>Canvas: 2. 切换到table_region模式
    User->>Canvas: 框选表格区域
    Canvas->>MTC: 请求创建新表格实例
    MTC->>TC: 创建TableController(table_ind=1)
    MTC->>TSW: 创建对应的逻辑视图
    MTC->>MTC: 设置为活动表格
    
    User->>Canvas: 3. 切换到table_cell模式
    loop 标注多个单元格
        User->>Canvas: 框选单元格
        Canvas->>MTC: 拦截绘制事件
        MTC->>TC: 转发给活动TableController
        TC->>TCS: 创建TableCellShape
        TC->>TSW: 实时更新逻辑视图
    end
    
    User->>Canvas: 4. 框选第二个表格区域
    Canvas->>MTC: 请求创建新表格实例
    MTC->>TC: 创建TableController(table_ind=2)
    MTC->>MTC: 切换活动表格到table_ind=2
    
    User->>App: 5. 触发"生成表格结构"
    App->>MTC: 获取活动TableController
    MTC->>TC: 转发结构分析请求
    TC->>TA: 分析当前表格单元格空间关系
    TA->>TC: 返回逻辑网格结果
    TC->>TSW: 渲染完整表格结构
    
    User->>Canvas: 6. 拖动调整单元格位置
    Canvas->>DSM: 检查变更类型
    DSM->>DSM: 判断Minor/Major变更
    alt Major变更
        DSM->>User: 弹出确认对话框
        User->>DSM: 选择处理方式
        DSM->>TC: 执行对应操作
    else Minor变更
        DSM->>TC: 静默更新物理坐标
    end
    
    User->>TPW: 7. 编辑单元格属性
    TPW->>MTC: 获取活动TableController
    MTC->>TC: 转发属性更新请求
    TC->>TCS: 更新table_properties
    TC->>TSW: 同步刷新显示
    
    User->>App: 8. 保存标注数据
    App->>TDM: 收集所有表格数据
    TDM->>MTC: 获取所有TableController实例
    loop 遍历所有表格
        MTC->>TC: 提取表格数据
        TC->>TDM: 返回序列化数据
    end
    TDM->>App: 完成数据整合
    App->>App: 保存到JSON文件
```

## 配置项 (Configuration)

本系统的配置项主要通过LabelMe原有的配置系统管理，新增表格相关配置：

```yaml
# default_config.yaml 新增部分
table:
  # 默认边框样式 (0=无边框, 1=实线)
  default_border_style: 1
  
  # 默认表格类型 (0=纯文本, 1=有线表格, 2=无线表格)
  default_table_type: 1
  
  # 自动对齐容差(像素)
  auto_align_tolerance: 10
  
  # OCR相关配置
  ocr:
    enabled: false
    api_endpoint: ""
    api_key: ""
    confidence_threshold: 0.8
  
  # 表格分析参数
  analysis:
    grid_tolerance: 5    # 网格分析容差
    merge_threshold: 0.7 # 单元格合并阈值
```

## 模块化组件详解 (File-by-File Breakdown)

### labelme/app.py

**a. 文件用途说明**
主应用窗口类，基于QMainWindow的GUI框架。负责整个应用的UI布局、菜单栏、工具栏、状态栏管理，以及各组件间的信号连接。在原有基础上增加表格标注相关的UI元素和事件处理。

**b. 文件内容概览(类与函数)**
- `MainWindow`: 主窗口类 (约2200行，现有代码)
- 新增表格相关方法：
  - `init_table_components()`: 初始化表格组件
  - `toggle_table_mode()`: 切换表格标注模式
  - `handle_table_events()`: 处理表格相关事件
  - `init_table_alignment_tools()`: 初始化对齐工具按钮

**c. 文件内类图**
```mermaid
classDiagram
    class MainWindow {
        -canvas: Canvas
        -table_controller: TableController
        -table_structure_widget: TableStructureWidget
        -table_properties_widget: TablePropertiesWidget
        +init_table_components()
        +toggle_table_mode()
        +handle_table_events()
        +init_table_alignment_tools()
        +setupUi()
        +connectSignals()
    }
    
    MainWindow --> Canvas : uses
    MainWindow --> TableController : manages
    MainWindow --> TableStructureWidget : contains
    MainWindow --> TablePropertiesWidget : contains
```

#### 函数/方法详解

##### init_table_components()
- **输入参数**: 无
- **输出**: 无
- **实现步骤和要点**:
  1. 创建TableController实例并关联Canvas
  2. 创建TableStructureWidget和TablePropertiesWidget
  3. 设置布局和停靠区域
  4. 连接信号槽

##### toggle_table_mode()
- **输入参数**: 无
- **输出**: 无  
- **实现步骤和要点**:
  1. 检查当前模式状态
  2. 调用TableController的enter_cell_mode()或exit_table_mode()
  3. 更新UI状态(按钮、菜单等)

##### init_table_alignment_tools()
- **输入参数**: 无
- **输出**: 无
- **实现步骤和要点**:
  1. 在现有工具栏中添加4个对齐按钮(顶端、底端、左、右对齐)
  2. 创建对应的QAction对象并设置图标和提示文本
  3. 连接按钮信号到TableController的对齐方法
  4. 添加到工具栏布局中

### labelme/table_shape.py

**a. 文件用途说明**
定义表格单元格形状类TableCellShape，继承自基础Shape类。封装表格单元格的所有属性，包括物理边界、逻辑位置、边框样式、文本内容等。提供表格单元格的创建、编辑、序列化等核心功能。

**b. 文件内容概览(类与函数)**
- `TableCellShape`: 表格单元格形状类
- `create_table_cell_from_rect()`: 从矩形创建单元格
- `create_table_cell_from_points()`: 从点列表创建单元格  
- `is_table_cell()`: 检查形状类型
- `get_table_cells_from_shapes()`: 筛选表格单元格

**c. 文件内类图**
```mermaid
classDiagram
    class Shape {
        <<abstract>>
        +label: str
        +points: list
        +shape_type: str
        +copy()
        +moveBy()
    }
    
    class TableCellShape {
        +table_properties: dict
        +table_properties.lloc: dict
        +table_properties.border: dict
        +table_properties.table_type: int
        +table_properties.cell_text: str
        +table_properties.is_confirmed: bool
        +copy()
        +set_logical_location()
        +set_border_style()
        +set_text_content()
        +to_dict()
        +from_dict()
    }
    
    Shape <|-- TableCellShape : inherits
```

#### 函数/方法详解

##### __init__(label=None, line_color=None, flags=None, group_id=None, description=None)
- **输入参数**: 
  - label: 单元格标签
  - line_color: 线条颜色  
  - flags: 标志
  - group_id: 组ID
  - description: 描述
- **输出**: TableCellShape实例
- **实现步骤和要点**:
  1. 调用父类Shape初始化，设置shape_type="table_cell"
  2. 初始化table_properties字典，包含lloc、border、table_type等
  3. 设置默认值

##### set_logical_location(start_row, end_row, start_col, end_col)
- **输入参数**: 逻辑位置坐标
- **输出**: 无
- **实现步骤和要点**:
  1. 验证输入参数的有效性
  2. 更新table_properties.lloc字典
  3. 触发视图更新事件

##### to_dict()
- **输入参数**: 无
- **输出**: dict (JSON序列化数据)
- **实现步骤和要点**:
  1. 调用父类to_dict()获取基础数据
  2. 添加table_properties到字典
  3. 处理特殊数据类型转换

### labelme/widgets/table_controller.py

**a. 文件用途说明**
单个表格的业务逻辑处理器，由MultiTableController创建和管理。负责处理其管辖范围内所有单元格的增删改查、结构分析和属性同步。采用拦截式设计模式，拦截Canvas的绘图事件，将普通矩形转换为表格单元格。

**b. 文件内容概览(类与函数)**
- `TableController`: 表格控制器主类
- 模式管理方法：`enter_cell_mode()`, `exit_table_mode()`, `enter_line_inference_mode()`
- 事件拦截方法：`intercept_finalize_shape()`, `handle_shape_selection()`
- 数据管理方法：`update_table_structure()`, `sync_data()`
- 划线推理方法：`_handle_guide_line_creation()`, `_analyze_line_direction()`, `_update_cells_by_line()`
- 对齐功能方法：`align_selected_cells_top()`, `align_selected_cells_left()`, `align_selected_cells_bottom()`, `align_selected_cells_right()`
- 批量网格生成方法：`auto_decompose_region_to_grid()`

**c. 文件内类图**
```mermaid
classDiagram
    class TableController {
        -canvas: Canvas
        -table_id: int
        -table_region: tuple
        -mode: str
        -internal_flag: str
        -current_table_grid: dict
        -table_cells: list
        -highlighted_cells: list
        -last_analysis_result: dict
        +enter_cell_mode()
        +exit_table_mode()
        +enter_line_inference_mode()
        +intercept_finalize_shape()
        +handle_shape_selection()
        +update_table_structure()
        +sync_data()
        +get_selected_cells()
        +highlight_cells()
        +_handle_guide_line_creation()
        +_analyze_line_direction()
        +_update_cells_by_line()
        +align_selected_cells_top()
        +align_selected_cells_left()
        +align_selected_cells_bottom()
        +align_selected_cells_right()
        +auto_decompose_region_to_grid()
    }
    
    class MultiTableController {
        -table_controllers: Dict[int, TableController]
        +create_table_instance()
        +get_active_controller()
    }
    
    MultiTableController --> TableController : creates and manages
    TableController --> Canvas : intercepts events
    TableController --> TableCellShape : manages
    TableController --> TableAnalyzer : uses
```

#### 函数/方法详解

##### enter_cell_mode()
- **输入参数**: 无
- **输出**: 无
- **实现步骤和要点**:
  1. 设置mode="cell", internal_flag="drawing_cell"
  2. 配置Canvas为rectangle创建模式
  3. 设置Canvas为非编辑状态
  4. 更新UI提示信息

##### intercept_finalize_shape(shape)
- **输入参数**: shape (Shape对象)
- **输出**: TableCellShape对象或None
- **实现步骤和要点**:
  1. 检查internal_flag是否为"drawing_cell"
  2. 验证shape类型和points有效性
  3. 创建TableCellShape实例并设置属性
  4. 添加到table_cells列表
  5. 触发表格结构分析

##### update_table_structure()
- **输入参数**: 无
- **输出**: 无
- **实现步骤和要点**:
  1. 调用TableAnalyzer分析当前单元格布局
  2. 生成逻辑网格结构
  3. 更新TableStructureWidget显示
  4. 同步属性面板数据

##### enter_line_inference_mode()
- **输入参数**: 无
- **输出**: 无
- **实现步骤和要点**:
  1. 设置mode="line_inference", internal_flag="drawing_guide_line"
  2. 配置Canvas为line创建模式，复用现有line模式
  3. 设置Canvas为非编辑状态
  4. 更新UI提示信息为"划线辅助模式"

##### _handle_guide_line_creation(line_shape)
- **输入参数**: line_shape (Line形状对象)
- **输出**: dict (拦截处理结果)
- **实现步骤和要点**:
  1. 调用_analyze_line_direction()判断线条方向
  2. 根据线条位置找到相关的表格单元格
  3. 调用_update_cells_by_line()重新分配逻辑坐标
  4. 更新表格结构显示
  5. 返回拦截成功结果

##### _analyze_line_direction(line_shape)
- **输入参数**: line_shape (Line形状对象)
- **输出**: str ("horizontal"或"vertical")
- **实现步骤和要点**:
  1. 获取线条的起始和结束点坐标
  2. 计算X轴和Y轴的变化量
  3. 比较变化量判断主要方向
  4. 返回线条方向字符串

##### _update_cells_by_line(line_direction, line_position)
- **输入参数**: line_direction (线条方向), line_position (线条位置坐标)
- **输出**: 无
- **实现步骤和要点**:
  1. 遍历当前所有表格单元格
  2. 根据线条方向和位置重新计算单元格的逻辑行列
  3. 更新单元格的logical_location属性
  4. 触发视图更新

##### align_selected_cells_top()
- **输入参数**: 无
- **输出**: 无
- **实现步骤和要点**:
  1. 获取Canvas中选中的TableCellShape对象
  2. 检查至少有2个单元格被选中
  3. 找到所有选中单元格中最上方的Y坐标
  4. 调整所有选中单元格的top坐标到该位置
  5. 更新Canvas显示

##### align_selected_cells_left()
- **输入参数**: 无
- **输出**: 无
- **实现步骤和要点**:
  1. 获取Canvas中选中的TableCellShape对象
  2. 检查至少有2个单元格被选中
  3. 找到所有选中单元格中最左边的X坐标
  4. 调整所有选中单元格的left坐标到该位置
  5. 保持单元格的宽度不变

##### align_selected_cells_bottom()
- **输入参数**: 无
- **输出**: 无
- **实现步骤和要点**:
  1. 获取Canvas中选中的TableCellShape对象
  2. 检查至少有2个单元格被选中
  3. 找到所有选中单元格中最下方的Y坐标
  4. 调整所有选中单元格的bottom坐标到该位置
  5. 保持单元格的高度不变

##### align_selected_cells_right()
- **输入参数**: 无
- **输出**: 无
- **实现步骤和要点**:
  1. 获取Canvas中选中的TableCellShape对象
  2. 检查至少有2个单元格被选中
  3. 找到所有选中单元格中最右边的X坐标
  4. 调整所有选中单元格的right坐标到该位置
  5. 保持单元格的宽度不变

##### auto_decompose_region_to_grid(rows=None, cols=None)
- **输入参数**: rows (行数，可选), cols (列数，可选)
- **输出**: List[TableCellShape] (生成的单元格列表)
- **实现步骤和要点**:
  1. 检查当前是否有有效的表格区域
  2. 如果未指定行列数，智能估算合适的网格尺寸
  3. 根据表格区域和行列数计算每个单元格的位置和大小
  4. 批量创建TableCellShape对象
  5. 自动分配逻辑坐标并添加到table_cells列表

### labelme/widgets/table_structure_widget.py

**a. 文件用途说明**
逻辑表格显示组件，基于QTableWidget实现。负责将标注的单元格以HTML表格的形式进行可视化展示，支持合并单元格、边框样式渲染、联动高亮等功能。

**b. 文件内容概览(类与函数)**
- `TableStructureWidget`: 主要的表格显示组件类
- `TableCellDelegate`: 自定义单元格委托渲染器
- `update_structure_view()`: 更新表格结构显示
- `handle_cell_selection()`: 处理单元格选择事件

**c. 文件内类图**
```mermaid
classDiagram
    class QTableWidget {
        <<PyQt5>>
    }
    
    class TableStructureWidget {
        -grid_data: dict
        -cell_mapping: dict
        -selected_cells: list
        +update_structure_view()
        +handle_cell_selection()
        +highlight_cell()
        +render_borders()
        +set_cell_span()
        +paintEvent()
    }
    
    class TableCellDelegate {
        +paint()
        +createEditor()
        +setEditorData()
        +setModelData()
    }
    
    QTableWidget <|-- TableStructureWidget : inherits
    TableStructureWidget --> TableCellDelegate : uses
```

#### 函数/方法详解

##### update_structure_view(grid_data)
- **输入参数**: grid_data (dict, 表格网格数据)
- **输出**: 无
- **实现步骤和要点**:
  1. 清空现有表格内容
  2. 根据grid_data设置行列数
  3. 遍历每个单元格设置内容和样式
  4. 处理合并单元格(setSpan)
  5. 应用边框样式

##### handle_cell_selection(row, col)
- **输入参数**: row, col (int, 单元格坐标)
- **输出**: 无
- **实现步骤和要点**:
  1. 查找对应的物理单元格ID
  2. 发送cell_selected信号给TableController
  3. 更新属性面板显示
  4. 高亮对应的Canvas单元格

### labelme/widgets/table_properties_widget.py

**a. 文件用途说明**
属性编辑面板组件，提供表格单元格属性的编辑界面。包括逻辑位置设置、边框样式选择、文本内容编辑、表格类型设置等功能。支持单个和批量编辑模式。

**b. 文件内容概览(类与函数)**
- `TablePropertiesWidget`: 主要的属性编辑组件
- `BorderStyleWidget`: 边框样式编辑子组件
- `LogicalLocationWidget`: 逻辑位置编辑子组件
- `update_properties()`: 更新属性显示
- `apply_changes()`: 应用属性修改

**c. 文件内类图**
```mermaid
classDiagram
    class QWidget {
        <<PyQt5>>
    }
    
    class TablePropertiesWidget {
        -selected_cells: list
        -border_widget: BorderStyleWidget
        -location_widget: LogicalLocationWidget  
        -text_edit: QTextEdit
        +update_properties()
        +apply_changes()
        +handle_property_changed()
        +set_batch_mode()
    }
    
    class BorderStyleWidget {
        -border_buttons: dict
        +set_border_style()
        +get_border_style()
        +toggle_border()
    }
    
    class LogicalLocationWidget {
        -spin_boxes: dict
        +set_location()
        +get_location()
        +validate_location()
    }
    
    QWidget <|-- TablePropertiesWidget : inherits
    TablePropertiesWidget --> BorderStyleWidget : contains
    TablePropertiesWidget --> LogicalLocationWidget : contains
```

#### 函数/方法详解

##### update_properties(selected_cells)
- **输入参数**: selected_cells (list, 选中的单元格列表)
- **输出**: 无
- **实现步骤和要点**:
  1. 保存当前选中的单元格引用
  2. 分析单元格属性的共同点和差异
  3. 更新各子组件的显示状态
  4. 设置批量编辑模式(如果多选)

##### apply_changes()
- **输入参数**: 无
- **输出**: 无
- **实现步骤和要点**:
  1. 收集各子组件的当前值
  2. 验证数据有效性
  3. 批量更新选中单元格的属性
  4. 发送property_changed信号
  5. 触发视图刷新

### labelme/utils/table_analyzer.py

**a. 文件用途说明**
表格结构分析器，负责将物理标注的单元格转换为逻辑表格结构。实现单元格的空间聚类、行列索引分配、合并单元格检测等核心算法。

**b. 文件内容概览(类与函数)**
- `TableAnalyzer`: 表格分析器主类
- `analyze_table_structure()`: 主要分析方法
- `cluster_cells_by_position()`: 基于位置的聚类
- `assign_logical_coordinates()`: 分配逻辑坐标
- `detect_merged_cells()`: 检测合并单元格

**c. 文件内类图**
```mermaid
classDiagram
    class TableAnalyzer {
        -tolerance: int
        -merge_threshold: float
        +analyze_table_structure()
        +cluster_cells_by_position()
        +assign_logical_coordinates()
        +detect_merged_cells()
        +generate_grid_structure()
        +validate_structure()
    }
```

#### 函数/方法详解

##### analyze_table_structure(cells)
- **输入参数**: cells (list, TableCellShape对象列表)
- **输出**: dict (网格结构数据)
- **实现步骤和要点**:
  1. 提取所有单元格的中心点坐标
  2. 基于Y坐标进行行聚类
  3. 基于X坐标进行列聚类  
  4. 分配逻辑行列索引
  5. 检测和处理合并单元格
  6. 生成最终的网格结构

##### cluster_cells_by_position(cells, axis='y')
- **输入参数**: 
  - cells: 单元格列表
  - axis: 聚类轴向('x'或'y')
- **输出**: list (聚类结果)
- **实现步骤和要点**:
  1. 提取指定轴向的坐标值
  2. 使用DBSCAN或简单距离聚类
  3. 根据tolerance参数合并邻近的组
  4. 返回排序后的聚类结果

### labelme/widgets/canvas.py (增强部分)

**a. 文件用途说明**
原有的绘图画布类，在现有基础上增加对table_cell类型的支持。主要修改包括：形状类型识别、渲染方式、事件处理等。保持向后兼容性。

**b. 新增/修改的方法**
- `paintTableCell()`: 绘制表格单元格
- `finalizeShape()` (修改): 增加table_cell类型处理
- `selectedShapeChanged()` (修改): 支持表格单元格选择

#### 函数/方法详解

##### paintTableCell(shape)
- **输入参数**: shape (TableCellShape对象)
- **输出**: 无
- **实现步骤和要点**:
  1. 根据边框属性设置画笔样式
  2. 绘制单元格边界矩形
  3. 根据选中状态添加高亮效果
  4. 可选：绘制逻辑坐标标签

### 数据流程关键决策

1. **事件拦截机制**: TableController通过拦截Canvas的finalizeShape事件，在不修改Canvas核心逻辑的前提下，实现形状类型转换。

2. **数据同步策略**: 采用观察者模式，当物理单元格发生变化时，自动触发逻辑结构重新分析，但提供用户确认机制避免意外覆盖。

3. **渲染性能优化**: TableStructureWidget使用局部更新策略，只重绘发生变化的单元格区域，避免全表刷新。

4. **扩展接口设计**: 预留OCRProvider、QualityController等接口，通过插件模式支持未来功能扩展。

## 开发分工建议

### 阶段1：核心架构 (2周)
- **开发者A**: 完善TableCellShape类和table_analyzer.py
- **开发者B**: 完善TableController拦截逻辑和Canvas集成

### 阶段2：UI组件 (2周)  
- **开发者A**: 实现TableStructureWidget逻辑表格显示
- **开发者B**: 实现TablePropertiesWidget属性编辑面板

### 阶段3：集成测试 (1周)
- **开发者A+B**: MainWindow集成、数据序列化、端到端测试

### 阶段4：扩展功能 (2周)
- **开发者A**: OCR集成、对齐工具
- **开发者B**: 批量操作、快捷键支持

此详细设计文档确保了基于现有LabelMe架构的最小侵入性扩展，同时提供足够的技术细节供AI辅助编程工具进行代码实现。

### 1. widgets/multi_table_controller.py (🆕 核心新增)

**文件用途说明**：
多表格协调器，负责管理一个图像中的多个表格实例。这是本次架构的核心组件，实现了PRD中要求的多表格支持。

**文件内容概览**：
- `MultiTableController`：主控制器类
- `TableInstanceManager`：表格实例管理器
- `TableSwitchManager`：表格切换管理器

**类图设计**：
```mermaid
classDiagram
    class MultiTableController {
        -canvas: Canvas
        -table_controllers: Dict[int, TableController]
        -active_table_id: int
        -instance_manager: TableInstanceManager
        -switch_manager: TableSwitchManager
        +create_table_instance(table_region: tuple) TableController
        +switch_active_table(table_id: int) bool
        +get_active_controller() TableController
        +get_all_controllers() Dict[int, TableController]
        +remove_table_instance(table_id: int) bool
        +handle_canvas_event(event: CanvasEvent) bool
        +export_all_tables() Dict[str, Any]
    }
    
    class TableInstanceManager {
        -next_table_id: int
        -table_regions: Dict[int, tuple]
        +register_table_region(region: tuple) int
        +validate_region_overlap(region: tuple) bool
        +get_table_region(table_id: int) tuple
        +update_table_region(table_id: int, region: tuple) bool
    }
    
    class TableSwitchManager {
        -controller_ref: MultiTableController
        +switch_to_table(table_id: int) bool
        +auto_detect_active_table(cursor_pos: tuple) int
        +highlight_active_table(table_id: int) void
    }
    
    MultiTableController --> TableInstanceManager
    MultiTableController --> TableSwitchManager
    MultiTableController --> TableController
```

**核心方法详解**：

1. **create_table_instance(table_region: tuple) -> TableController**
   - 输入参数：table_region (x1, y1, x2, y2) 表格区域坐标
   - 输出：创建的TableController实例
   - 实现步骤：
     1. 调用instance_manager.register_table_region()验证区域
     2. 创建新的TableController实例
     3. 初始化对应的TableStructureWidget
     4. 设置为当前活动表格
     5. 返回TableController实例

2. **switch_active_table(table_id: int) -> bool**
   - 输入参数：table_id 目标表格ID
   - 输出：切换是否成功
   - 实现步骤：
     1. 验证table_id有效性
     2. 通知当前活动表格失去焦点
     3. 更新active_table_id
     4. 通知新活动表格获得焦点
     5. 更新UI显示状态

3. **handle_canvas_event(event: CanvasEvent) -> bool**
   - 输入参数：Canvas事件对象
   - 输出：事件是否被处理
   - 实现步骤：
     1. 检查事件类型(绘制/编辑/选择)
     2. 自动检测事件发生的表格区域
     3. 如需要，自动切换活动表格
     4. 将事件转发给对应的TableController
     5. 返回处理结果

### 2. utils/table_data_sync.py (🆕 智能数据同步)

**文件用途说明**：
智能数据同步管理器，实现PRD中定义的复杂同步逻辑，包括Minor/Major变更检测和用户交互确认。

**文件内容概览**：
- `DataSyncManager`：数据同步管理器
- `ChangeDetector`：变更检测器
- `SyncStrategy`：同步策略类
- `UserInteractionHandler`：用户交互处理器

**类图设计**：
```mermaid
classDiagram
    class DataSyncManager {
        -change_detector: ChangeDetector
        -sync_strategy: SyncStrategy
        -interaction_handler: UserInteractionHandler
        +sync_physical_to_logical(cell: TableCellShape) bool
        +sync_logical_to_physical(row: int, col: int, table_id: int) bool
        +detect_change_type(old_pos: tuple, new_pos: tuple) str
        +handle_major_change(change_info: dict) bool
        +handle_minor_change(change_info: dict) bool
    }
    
    class ChangeDetector {
        -threshold_config: dict
        +detect_position_change(old_pos: tuple, new_pos: tuple) str
        +detect_size_change(old_size: tuple, new_size: tuple) str
        +calculate_change_magnitude(old_val: tuple, new_val: tuple) float
        +is_major_change(magnitude: float, change_type: str) bool
    }
    
    class SyncStrategy {
        +sync_cell_position(cell: TableCellShape, target_pos: tuple) bool
        +rebuild_logical_structure(table_controller: TableController) bool
        +incremental_update(affected_cells: list) bool
        +full_rebuild(table_controller: TableController) bool
    }
    
    class UserInteractionHandler {
        +confirm_major_change(change_info: dict) str
        +show_conflict_resolution_dialog(conflicts: list) dict
        +display_sync_progress(progress: float) void
    }
    
    DataSyncManager --> ChangeDetector
    DataSyncManager --> SyncStrategy
    DataSyncManager --> UserInteractionHandler
```

**核心方法详解**：

1. **sync_physical_to_logical(cell: TableCellShape) -> bool**
   - 输入参数：cell 被修改的单元格对象
   - 输出：同步是否成功
   - 实现步骤：
     1. 获取单元格的旧位置和新位置
     2. 调用change_detector检测变更类型
     3. 根据变更类型选择处理策略
     4. 如是Major变更，调用用户交互确认
     5. 执行相应的同步操作

2. **handle_major_change(change_info: dict) -> bool**
   - 输入参数：change_info 包含变更详情的字典
   - 输出：处理是否成功
   - 实现步骤：
     1. 分析变更影响范围
     2. 弹出用户确认对话框
     3. 根据用户选择执行操作：
        - "保持原结构"：恢复单元格位置
        - "重新分析"：重新生成逻辑结构
        - "手动调整"：进入手动编辑模式

### 3. utils/table_data_manager.py (🆕 数据统一管理)

**文件用途说明**：
表格数据统一管理器，负责多表格数据的集中管理、序列化和完整性校验。

**文件内容概览**：
- `TableDataManager`：数据管理主类
- `DataSerializer`：数据序列化器
- `TableSchemaValidator`：表格结构校验器
- `TableMetadata`：表格元数据类

**类图设计**：
```mermaid
classDiagram
    class TableDataManager {
        -serializer: DataSerializer
        -validator: TableSchemaValidator
        -metadata_store: Dict[int, TableMetadata]
        +collect_all_table_data(multi_controller: MultiTableController) dict
        +validate_table_data(table_data: dict) bool
        +serialize_to_json(table_data: dict) str
        +deserialize_from_json(json_str: str) dict
        +backup_table_data(table_id: int) bool
        +restore_table_data(table_id: int) bool
    }
    
    class DataSerializer {
        +serialize_table_cells(cells: list) dict
        +serialize_table_structure(structure: dict) dict
        +deserialize_table_cells(data: dict) list
        +deserialize_table_structure(data: dict) dict
    }
    
    class TableSchemaValidator {
        -cell_validator: CellValidator
        -structure_validator: StructureValidator
        -validation_rules: List[ValidationRule]
        +validate_table_data(table_data: dict) ValidationResult
        +validate_runtime_consistency(table_data: dict) bool
        +validate_export_format(table_data: dict) bool
    }
    
    class TableMetadata {
        +table_id: int
        +creation_time: datetime
        +last_modified: datetime
        +cell_count: int
        +structure_version: str
        +is_validated: bool
    }
    
    TableDataManager --> DataSerializer
    TableDataManager --> TableSchemaValidator : uses for validation
    TableDataManager --> TableMetadata
```

**核心方法详解**：

1. **collect_all_table_data(multi_controller: MultiTableController) -> dict**
   - 输入参数：multi_controller 多表格控制器实例
   - 输出：包含所有表格数据的字典
   - 实现步骤：
     1. 获取所有TableController实例
     2. 遍历每个表格，收集单元格数据
     3. 序列化表格结构信息
     4. 添加元数据信息
     5. 合并为统一的数据结构

2. **validate_table_data(table_data: dict) -> bool**
   - 输入参数：table_data 待校验的表格数据
   - 输出：校验是否通过
   - 实现步骤：
     1. 检查数据结构完整性
     2. 验证单元格坐标有效性
     3. 检查逻辑位置一致性
     4. 验证表格间关系正确性
     5. 返回校验结果

### 4. utils/table_exporter.py (🆕 导出功能)

**文件用途说明**：
表格数据导出器，支持多种格式导出，包括标准JSON、自定义格式和外部系统格式。

**文件内容概览**：
- `TableExporter`：导出器主类
- `JSONExporter`：JSON格式导出器
- `CustomFormatExporter`：自定义格式导出器
- `ExportTemplate`：导出模板类

**类图设计**：
```mermaid
classDiagram
    class TableExporter {
        -json_exporter: JSONExporter
        -custom_exporter: CustomFormatExporter
        -template_manager: ExportTemplate
        +export_to_json(table_data: dict, filepath: str) bool
        +export_to_custom_format(table_data: dict, template: str) str
        +export_single_table(table_id: int, format: str) bool
        +export_all_tables(format: str) bool
        +load_export_template(template_name: str) dict
    }
    
    class JSONExporter {
        +export_standard_format(table_data: dict) dict
        +export_labelme_compatible(table_data: dict) dict
        +add_export_metadata(data: dict) dict
    }
    
    class CustomFormatExporter {
        +apply_template(data: dict, template: dict) dict
        +transform_coordinates(cells: list, transform: dict) list
        +filter_properties(data: dict, filters: list) dict
    }
    
    class ExportTemplate {
        -templates: Dict[str, dict]
        +load_template(name: str) dict
        +validate_template(template: dict) bool
        +get_available_templates() list
    }
    
    TableExporter --> JSONExporter
    TableExporter --> CustomFormatExporter
    TableExporter --> ExportTemplate
```

**核心方法详解**：

1. **export_to_json(table_data: dict, filepath: str) -> bool**
   - 输入参数：table_data 表格数据字典，filepath 导出文件路径
   - 输出：导出是否成功
   - 实现步骤：
     1. 验证数据完整性
     2. 应用JSON导出模板
     3. 添加导出元数据
     4. 格式化输出内容
     5. 写入文件

2. **export_to_custom_format(table_data: dict, template: str) -> str**
   - 输入参数：table_data 表格数据，template 模板名称
   - 输出：格式化后的字符串
   - 实现步骤：
     1. 加载指定模板
     2. 应用坐标转换规则
     3. 过滤属性字段
     4. 格式化输出结构
     5. 返回结果字符串