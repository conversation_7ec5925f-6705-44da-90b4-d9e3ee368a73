# **TableLabelMe 表格标注工具系统概要设计**

## 架构概览(Architecture Overview)

### 系统分层架构

基于 LabelMe 的增强式架构设计，采用独立应用方案（app_tableme.py），确保与原系统完全解耦的同时复用核心组件。

**分层组成：**
- **应用层（Application Layer）**: TableLabelMe主应用控制器
- **界面层（UI Layer）**: 多区域布局界面组件与交互控制
- **业务层（Business Layer）**: 表格标注业务逻辑处理
- **数据层（Data Layer）**: 表格数据模型与持久化存储
- **扩展层（Extension Layer）**: OCR、AI辅助、导出等插件接口

### 核心端到端请求流程

```mermaid
sequenceDiagram
    participant User as 用户
    participant App as TableLabelMe主应用
    participant Canvas as Canvas(增强)
    participant MTC as MultiTableController
    participant TC as TableController
    participant TA as TableAnalyzer
    participant TSW as TableStructureWidget
    participant TPW as TablePropertiesWidget
    participant TDM as TableDataManager
    participant Export as TableExporter
    
    User->>App: 1. 加载图片文件
    App->>Canvas: 显示图像并初始化画布
    
    User->>Canvas: 2. 切换到table_region模式
    User->>Canvas: 框选表格区域
    Canvas->>MTC: 创建新表格实例(table_ind)
    MTC->>TC: 初始化TableController
    TC->>TSW: 创建逻辑表格视图
    
    User->>Canvas: 3. 切换到table_cell模式
    loop 标注多个单元格
        User->>Canvas: 框选单元格
        Canvas->>TC: 创建TableCellShape
        TC->>TSW: 实时更新逻辑视图
    end
    
    User->>App: 4. 触发"生成表格结构"
    App->>TC: 执行结构分析
    TC->>TA: 分析单元格空间关系
    TA->>TC: 返回逻辑网格结果
    TC->>TSW: 渲染完整表格结构
    
    User->>TPW: 5. 编辑单元格属性
    TPW->>TC: 批量更新属性数据
    TC->>TSW: 同步刷新显示
    
    User->>App: 6. 保存标注数据
    App->>TDM: 收集所有表格数据
    TDM->>Export: 转换目标格式
    Export->>App: 完成保存操作
```

## 组件拆分(Components)

### 应用层组件
- **TableLabelMe** (`app_tableme.py`)
  - 核心职责：主应用窗口管理、菜单系统、工具栏、多区域布局协调
  - 关键功能：文件生命周期管理、模式切换控制、全局状态管理、插件集成

### 界面层组件
- **Canvas** (`widgets/canvas.py` - 基于现有)
  - 核心职责：图像显示、多形状标注交互、绘制引擎
  - 关键增强：添加表格绘制模式、表格专用交互、对齐辅助线
  - 新增模式：`table_region`、`table_cell`、`table_batch`、`table_line_guide`

- **TableStructureWidget** (`widgets/table_structure_widget.py`)
  - 核心职责：逻辑表格结构可视化、合并单元格渲染
  - 关键功能：QTableWidget扩展、联动高亮显示、双击编辑支持

- **TablePropertiesWidget** (`widgets/table_properties_widget.py`)
  - 核心职责：单元格属性编辑、批量操作面板
  - 关键功能：边框样式设置、逻辑坐标编辑、文本内容管理、表头标记

- **TableToolbar** (`widgets/table_toolbar.py`)
  - 核心职责：表格专用操作工具集合
  - 关键功能：对齐工具组、结构生成按钮、模式切换控件

### 业务层组件
- **MultiTableController** (`widgets/multi_table_controller.py`)
  - 核心职责：多表格实例的统一管理与协调
  - 关键功能：表格创建销毁、活动表格切换、跨表格操作

- **TableController** (`widgets/table_controller.py`)
  - 核心职责：单表格的完整业务逻辑处理
  - 关键功能：单元格CRUD操作、结构生成控制、属性同步机制

### 数据层组件
- **TableCellShape** (`table_shape.py`)
  - 核心职责：表格单元格数据模型与Shape系统集成
  - 关键功能：物理坐标存储、逻辑位置映射、边框样式、文本内容、状态管理

- **TableAnalyzer** (`utils/table_analyzer.py`)
  - 核心职责：物理单元格到逻辑网格的智能转换
  - 关键功能：坐标聚类算法、行列结构分析、网格构建、冲突检测

- **TableDataManager** (`utils/table_data_manager.py`)
  - 核心职责：多表格数据的统一存储管理
  - 关键功能：数据序列化、版本控制、格式转换、完整性校验

### 扩展层组件
- **OCRAdapter** (`plugins/ocr_adapter.py`)
  - 核心职责：外部OCR服务的统一接口适配
  - 关键功能：多OCR引擎支持、结果标准化、错误处理、性能优化

- **TableExporter** (`utils/table_exporter.py`)
  - 核心职责：多格式数据导出与兼容性处理
  - 关键功能：自定义JSON格式、PubTabNet格式、Schema校验、模板支持

## 目录结构树(Directory Tree)

```
labelme/
├── app_tableme.py                        # 🆕 表格标注主应用
├── widgets/
│   ├── canvas.py                         # ⚡ 增强现有画布
│   ├── table_controller.py               # ⚡ 增强现有控制器
│   ├── multi_table_controller.py         # 🆕 多表格协调器
│   ├── table_structure_widget.py         # ⚡ 增强逻辑视图组件
│   ├── table_properties_widget.py        # ⚡ 增强属性编辑面板
│   ├── table_toolbar.py                  # 🆕 表格专用工具栏
│   └── table_status_widget.py            # 🆕 状态信息显示组件
├── table_shape.py                        # ⚡ 大幅增强现有Shape
├── utils/
│   ├── table_analyzer.py                 # ⚡ 完善现有分析器
│   ├── table_data_manager.py             # 🆕 数据统一管理器
│   ├── table_exporter.py                 # 🆕 JSON格式导出器
│   ├── table_alignment.py                # 🆕 智能对齐算法
│   ├── table_data_sync.py                # 🆕 智能数据同步
│   └── table_schema_validator.py         # 🆕 数据完整性校验
├── plugins/
│   ├── __init__.py
│   ├── ocr_adapter.py                    # 🆕 OCR统一适配器
│   └── ocr_interface.py                  # 🆕 OCR抽象接口定义
├── configs/
│   ├── table_config.yaml                # 🆕 表格专用配置
│   └── export_templates/                 # 🆕 导出模板目录
│       ├── custom_format.json
│       └── external_format.json
├── resources/
│   ├── icons/table/                      # 🆕 表格相关图标
│   │   ├── table_region.svg
│   │   ├── table_cell.svg
│   │   ├── align_tools.svg
│   │   └── generate_structure.svg
│   └── templates/                        # 🆕 界面布局模板
│       ├── table_layout.ui
│       └── property_panel.ui
└── tests/
    ├── test_table_analyzer.py            # 🆕 分析器单元测试
    ├── test_table_controller.py          # 🆕 控制器测试
    ├── test_data_sync.py                 # 🆕 数据同步测试
    └── fixtures/                         # 🆕 测试数据
        ├── sample_tables.json
        └── test_images/
```

## 数据流(Data Flow)

### 关键业务场景：智能表格结构生成与属性编辑流程

用户完成物理单元格标注后，触发系统自动分析生成逻辑表格结构，并进行属性编辑的完整数据流：

**阶段描述：** 物理标注完成 → 触发结构分析 → 智能网格生成 → 逻辑坐标分配 → 属性编辑同步 → 数据持久化

```mermaid
sequenceDiagram
    participant User as 用户操作
    participant App as TableLabelMe主应用
    participant Canvas as MultiTableCanvas
    participant TC as TableController
    participant TA as TableAnalyzer
    participant TCS as TableCellShape[]
    participant TSW as TableStructureWidget
    participant TPW as TablePropertiesWidget
    participant Sync as DataSyncManager
    participant TDM as TableDataManager
    participant Validator as SchemaValidator
    
    %% 阶段1: 触发结构生成
    User->>App: 点击"生成表格结构"按钮
    App->>TC: 发起结构分析请求
    TC->>TA: 传递所有物理单元格数据
    
    %% 阶段2: 智能分析处理
    TA->>TA: 执行坐标聚类算法
    Note over TA: X/Y坐标聚类<br/>检测行列边界<br/>构建逻辑网格
    TA->>TA: 计算单元格逻辑位置
    TA->>TC: 返回网格分析结果
    
    %% 阶段3: 更新数据模型
    TC->>TCS: 批量更新逻辑坐标(lloc)
    loop 处理每个单元格
        TCS->>TCS: 更新lloc属性
        TCS->>Sync: 触发数据同步检查
        Sync->>Sync: 验证逻辑一致性
    end
    
    %% 阶段4: 界面同步更新
    TC->>TSW: 触发逻辑视图重建
    TSW->>TSW: 根据新lloc渲染表格
    Note over TSW: 合并单元格处理<br/>边框样式渲染<br/>表头标记显示
    TSW->>Canvas: 触发物理视图高亮更新
    
    %% 阶段5: 用户属性编辑
    User->>TSW: 选择单元格(row=1,col=2)
    TSW->>TPW: 加载单元格属性数据
    TPW->>TCS: 查询选中单元格属性
    TCS->>TPW: 返回属性详情
    
    User->>TPW: 修改边框样式/文本内容
    TPW->>TCS: 更新属性数据
    TCS->>Sync: 触发智能同步机制
    
    %% 阶段6: 智能同步处理
    Sync->>Sync: 检测变更类型(Minor/Major)
    alt 轻微变更
        Sync->>TSW: 静默更新显示
    else 重大变更
        Sync->>User: 弹出确认对话框
        User->>Sync: 选择同步策略
        Sync->>TC: 执行相应同步操作
    end
    
    %% 阶段7: 数据验证与保存
    TC->>TDM: 收集完整表格数据
    TDM->>Validator: 执行数据完整性校验
    Validator->>Validator: Schema验证
    Validator->>TDM: 返回校验结果
    TDM->>App: 标记数据状态(已修改/已验证)
```

## 数据模型设计(Data Model Design)

### 核心业务实体关系

```mermaid
erDiagram
    IMAGE ||--o{ TABLE_REGION : contains
    TABLE_REGION ||--o{ TABLE_CELL : contains
    TABLE_CELL ||--o{ CELL_CONTENT : contains
    TABLE_CELL ||--|| LOGICAL_LOCATION : has
    TABLE_CELL ||--|| BORDER_STYLE : has
    TABLE_REGION ||--|| TABLE_PROPERTIES : has
    IMAGE ||--|| ANNOTATION_METADATA : has
    
    IMAGE {
        string image_path PK "图片文件路径"
        int width "图片宽度像素"
        int height "图片高度像素"
        string format "图片格式(PNG/JPG)"
        datetime created_at "创建时间戳"
        datetime modified_at "最后修改时间"
        string checksum "文件校验和"
    }
    
    TABLE_REGION {
        int table_ind PK "表格索引号"
        string image_path FK "关联图片路径"
        int table_type "表格类型 0-纯文本 1-有线 2-无线"
        int rows "逻辑总行数"
        int cols "逻辑总列数"
        json bbox "表格区域四点坐标"
        boolean is_confirmed "用户确认状态"
        datetime created_at "创建时间"
        json table_metadata "扩展元数据"
    }
    
    TABLE_CELL {
        int cell_ind PK "单元格索引"
        int table_ind FK "所属表格索引"
        json bbox "物理边界四点坐标[p1,p2,p3,p4]"
        boolean header "是否为表头单元格"
        boolean is_confirmed "用户确认状态"
        int merge_status "合并状态 0-普通 1-合并主 2-合并从"
        datetime modified_at "最后修改时间"
        string notes "备注信息"
    }
    
    LOGICAL_LOCATION {
        int cell_ind FK "关联单元格索引"
        int start_row "起始行索引(0-based)"
        int end_row "结束行索引(inclusive)"
        int start_col "起始列索引(0-based)"
        int end_col "结束列索引(inclusive)"
        int span_rows "跨越行数"
        int span_cols "跨越列数"
        float confidence "位置置信度[0,1]"
    }
    
    BORDER_STYLE {
        int cell_ind FK "关联单元格索引"
        int top "上边框样式 0-无 1-实线 2-虚线"
        int right "右边框样式"
        int bottom "下边框样式"
        int left "左边框样式"
        string border_color "边框颜色(HEX)"
        int border_width "边框粗细(像素)"
    }
    
    CELL_CONTENT {
        int content_id PK "内容唯一标识"
        int cell_ind FK "关联单元格索引"
        string text "文本内容"
        json text_bbox "文本区域坐标[x1,y1,x2,y2]"
        float confidence "识别置信度[0,1]"
        string text_direction "文字方向 horizontal/vertical"
        string source "内容来源 manual/ocr/ai"
        json style_properties "文本样式属性"
        datetime created_at "内容创建时间"
    }
    
    TABLE_PROPERTIES {
        int table_ind FK "关联表格索引"
        string table_title "表格标题"
        string table_caption "表格说明"
        json header_config "表头配置信息"
        json style_config "表格样式配置"
        json export_settings "导出设置"
        float analysis_confidence "整体分析置信度"
        string quality_status "质量状态 draft/review/final"
    }
    
    ANNOTATION_METADATA {
        string image_path FK "关联图片路径"
        string version "标注格式版本号"
        string annotator "标注人员"
        int total_tables "图片中表格总数"
        int total_cells "所有表格单元格总数"
        datetime annotation_start "标注开始时间"
        datetime annotation_complete "标注完成时间"
        json quality_metrics "质量评估指标"
        json export_history "导出历史记录"
    }
```

## API接口定义(API Definitions)

### 主应用控制接口

| 接口方法 | 功能说明 | 参数 | 返回值 |
|---------|----------|------|--------|
| `load_image(filepath: str)` | 加载图片文件并初始化标注环境 | 图片文件路径 | 成功状态及图片元信息 |
| `save_annotation(format_type: str = "custom")` | 保存当前标注数据到指定格式 | 保存格式类型 | 保存结果状态 |
| `export_to_format(format_type: str, output_path: str)` | 导出标注数据到指定格式文件 | 格式类型, 输出路径 | 导出结果状态 |
| `import_annotation(file_path: str)` | 导入已有标注数据 | 标注文件路径 | 导入结果及数据概要 |
| `switch_annotation_mode(mode: str)` | 切换标注工作模式 | 模式名称 | 切换结果状态 |

### 多表格管理接口

| 接口方法 | 功能说明 | 参数 | 返回值 |
|---------|----------|------|--------|
| `create_table_region(bbox: dict)` | 创建新的表格区域 | 区域边界坐标 | 新表格索引号 |
| `delete_table(table_ind: int)` | 删除指定表格及所有单元格 | 表格索引号 | 删除结果状态 |
| `switch_active_table(table_ind: int)` | 切换当前活动编辑表格 | 表格索引号 | 切换结果状态 |
| `get_table_list()` | 获取当前图片所有表格列表 | 无 | 表格基本信息列表 |
| `duplicate_table(table_ind: int)` | 复制指定表格结构 | 源表格索引号 | 新表格索引号 |

### 单元格操作接口

| 接口方法 | 功能说明 | 参数 | 返回值 |
|---------|----------|------|--------|
| `add_cell(bbox: dict, table_ind: int)` | 添加新单元格到指定表格 | 边界坐标, 表格索引 | 新单元格索引号 |
| `delete_cell(cell_ind: int)` | 删除指定单元格 | 单元格索引号 | 删除结果状态 |
| `update_cell_bbox(cell_ind: int, new_bbox: dict)` | 更新单元格物理边界 | 单元格索引, 新坐标 | 更新结果状态 |
| `merge_cells(cell_list: List[int])` | 合并多个单元格 | 单元格索引列表 | 合并结果状态 |
| `split_cell(cell_ind: int, split_config: dict)` | 拆分单元格 | 单元格索引, 拆分配置 | 拆分结果状态 |

### 表格结构分析接口

| 接口方法 | 功能说明 | 参数 | 返回值 |
|---------|----------|------|--------|
| `generate_table_structure(table_ind: int)` | 自动生成表格逻辑结构 | 表格索引号 | 分析结果状态 |
| `analyze_cells_to_grid(cells: List[dict])` | 分析单元格生成网格结构 | 单元格数据列表 | 网格分析结果 |
| `validate_table_structure(table_ind: int)` | 验证表格结构完整性 | 表格索引号 | 验证结果报告 |
| `auto_align_cells(cell_list: List[int], align_type: str)` | 自动对齐选中单元格 | 单元格列表, 对齐类型 | 对齐结果状态 |

### 属性管理接口

| 接口方法 | 功能说明 | 参数 | 返回值 |
|---------|----------|------|--------|
| `update_cell_property(cell_ind: int, property: str, value: any)` | 更新单元格属性 | 单元格索引, 属性名, 属性值 | 更新结果状态 |
| `batch_update_properties(cell_list: List[int], properties: dict)` | 批量更新单元格属性 | 单元格列表, 属性字典 | 批量更新结果 |
| `set_cell_border_style(cell_ind: int, border_config: dict)` | 设置单元格边框样式 | 单元格索引, 边框配置 | 设置结果状态 |
| `set_cell_text_content(cell_ind: int, text: str, source: str)` | 设置单元格文本内容 | 单元格索引, 文本, 来源 | 设置结果状态 |
| `mark_cells_as_header(cell_list: List[int])` | 标记单元格为表头 | 单元格索引列表 | 标记结果状态 |

### 数据导出接口

| 接口方法 | 功能说明 | 参数 | 返回值 |
|---------|----------|------|--------|
| `export_custom_format(tables: List[dict])` | 导出自定义JSON格式 | 表格数据列表 | 导出文件路径 |
| `export_external_format(tables: List[dict])` | 导出外部兼容JSON格式 | 表格数据列表 | 导出文件路径 |
| `import_external_format(file_path: str)` | 导入外部JSON格式并转换 | 外部文件路径 | 转换结果状态 |
| `preview_markdown_table(table_ind: int)` | 生成Markdown预览格式 | 表格索引号 | Markdown文本 |

### OCR集成接口

| 接口方法 | 功能说明 | 参数 | 返回值 |
|---------|----------|------|--------|
| `recognize_image_region(image: any, bbox: dict)` | 识别指定图像区域文本 | 图像数据, 区域坐标 | OCR识别结果 |
| `recognize_full_image(image: any)` | 识别整图文本内容 | 图像数据 | 全图OCR结果 |
| `auto_fill_cell_text(table_ind: int)` | 自动填充表格单元格文本 | 表格索引号 | 填充结果状态 |
| `configure_ocr_provider(provider_config: dict)` | 配置OCR服务提供者 | 配置参数 | 配置结果状态 |

## 关键决策的依据(Rationale for Key Decisions)

### 1. 独立应用架构决策 (app_tableme.py)

**决策内容**: 采用独立的 `app_tableme.py` 作为表格标注专用应用，而非直接修改LabelMe主应用

**技术依据**:
- **风险隔离原则**: 避免对成熟稳定的LabelMe核心代码造成破坏性影响，降低回归风险
- **开发效率考虑**: 支持并行开发模式，不受LabelMe主项目发布节奏限制
- **功能专一性**: 表格标注具有特殊的交互模式和界面需求，独立应用能提供更专业的用户体验
- **维护便利性**: 独立版本管理便于后续功能迭代、问题定位和性能优化

**架构优势**:
- 可完全复用LabelMe的Shape系统、Canvas引擎、文件管理等核心组件
- 支持渐进式功能增强，降低技术债务积累
- 便于与LabelMe主线保持版本同步和功能兼容

### 2. 多表格支持架构设计

**决策内容**: 设计 `MultiTableController` 统一管理多个 `TableController` 实例

**业务驱动因素**:
- **真实需求场景**: 实际应用中一张图片常包含多个独立表格，需要分别标注和管理
- **数据隔离要求**: 不同表格的单元格数据需要独立存储，避免逻辑混乱
- **用户体验需求**: 支持表格间快速切换、批量操作、独立导出等功能

**技术架构优势**:
- **职责单一原则**: TableController专注单表格业务逻辑，MultiTableController负责协调管理
- **扩展性设计**: 便于后续支持表格关联分析、跨表格操作等高级功能
- **性能优化**: 支持懒加载和按需渲染，提升大量表格场景下的响应性能

### 3. Canvas增强设计决策

**决策内容**: 扩展现有Canvas而非创建MultiTableCanvas，通过增强绘制模式支持多表格标注

**技术权衡分析**:
- **现有Canvas能力充分**: LabelMe的Canvas已支持多个shapes共存，有完整的createModes系统
- **避免重复开发**: 现有Canvas在图像处理、缩放变换、交互响应等方面已经非常成熟
- **兼容性最佳**: 保持与现有Shape系统、撤销重做、文件保存等功能的完美集成
- **开发效率高**: 只需添加表格专用的绘制模式，避免重写复杂的底层逻辑

**增强策略**:
```python
# 在现有Canvas.createModes中增加
ENHANCED_CREATE_MODES = [
    "table_region",     # 表格区域框选
    "table_cell",       # 单个单元格标注 (已有,增强)
    "table_batch",      # 批量网格生成
    "table_line_guide", # 划线辅助
]
```

**实现效果**:
- 复用现有800+行的成熟Canvas代码
- 新增功能与现有功能无缝集成
- 支持多表格的独立管理和切换

### 4. 智能数据同步机制设计

**决策内容**: 实现轻微调整与重大变更的智能判断，采用不同的同步策略

**问题解决导向**:
- **用户体验痛点**: 避免频繁弹窗打断用户操作流程
- **数据一致性**: 确保物理坐标与逻辑结构的动态一致性
- **操作容错性**: 为用户提供明确的变更控制和撤销机制

**算法设计思路**:
- **空间阈值判断**: 基于单元格中心点位移和面积变化率进行量化判断
- **拓扑关系分析**: 检测单元格是否跨越原有的行列边界
- **智能提示机制**: 提供清晰的变更影响说明和多种处理选项

**实现效果**:
- 95%以上的微调操作实现静默更新，提升操作流畅性
- 重大变更提供明确的用户确认，保障数据完整性
- 支持操作回滚和批量修正，提高错误恢复能力

### 5. 数据格式设计决策

**决策内容**: 采用自定义JSON格式为主，同时兼容PubTabNet等主流格式

**标准化考虑**:
- **需求完整覆盖**: 自定义格式完全匹配PRD中所有数据结构要求
- **扩展灵活性**: 便于添加边框样式、表头标记、置信度等表格特有属性
- **生态兼容性**: 支持主流数据集格式的双向转换，便于数据共享和模型训练
- **质量保障**: 统一的Schema校验确保数据格式一致性和完整性

**格式设计亮点**:
- 层次化的数据结构设计，清晰区分表格区域和单元格
- 丰富的元数据支持，便于质量控制和版本管理
- 向后兼容的版本机制，支持格式演进和数据迁移

### 6. 组件通信机制设计

**决策内容**: 采用Qt信号槽机制与控制器协调的混合通信模式

**架构设计原理**:
- **响应式UI更新**: Qt信号槽确保界面组件间的实时同步和响应式更新
- **业务逻辑集中**: 控制器模式保证复杂业务逻辑的统一管理和状态控制
- **松耦合设计**: 组件间通过标准化接口通信，支持独立测试和模块替换
- **可维护性**: 清晰的数据流向便于问题定位、功能扩展和性能优化

**通信链路设计**:
- Canvas → Controller: 用户交互事件传递
- Controller → Widgets: 业务状态同步
- Widgets → Canvas: 界面操作反馈
- Controller → DataManager: 数据持久化操作

### 7. TableAnalyzer纯分析器设计

**决策内容**: TableAnalyzer专注算法分析，不包含UI交互和状态管理

**设计模式应用**:
- **单一职责原则**: 专注于坐标分析和逻辑网格构建的核心算法
- **纯函数设计**: 输入确定则输出确定，便于单元测试和算法验证
- **算法可插拔**: 支持不同分析策略的动态切换和A/B测试
- **性能优化**: 独立的分析器便于算法优化和并行计算

**算法技术栈**:
- 基于DBSCAN的坐标聚类算法
- 层次化的行列结构分析
- 几何约束的网格一致性检验
- 统计学方法的异常检测和修正