# TableLabelMe第三方API辅助打标功能编码步骤文档

## 项目概述

基于现有的TableLabelMe表格标注系统，新增第三方API辅助打标功能，通过调用外部表格识别API自动生成表格标注，提高标注效率。

## 代码目录结构

### 现有目录结构
```
labelme/
├── app_tableme.py                    # 主应用入口
├── table_shape.py                    # 表格单元格形状定义
├── widgets/                          # UI组件模块
│   ├── canvas.py                     # 画布组件
│   ├── multi_table_controller.py     # 多表格协调器
│   ├── table_controller.py           # 单表格控制器
│   ├── table_structure_widget.py     # 表格结构视图
│   ├── table_properties_widget.py    # 表格属性面板
│   └── zoom_widget.py               # 缩放控件
├── utils/                           # 工具模块
│   ├── table_analyzer.py            # 表格结构分析器
│   └── log.py                       # 日志工具
└── managers/                        # 管理器模块
    ├── history_manager.py           # 历史管理器
    └── mode_manager.py              # 模式管理器
```

### 新增目录结构
```
labelme/
├── api/                             # API相关模块（新增）
│   ├── __init__.py
│   ├── api_manager.py               # API管理器
│   ├── image_preprocessor.py        # 图像预处理器
│   ├── clients/                     # API客户端模块
│   │   ├── __init__.py
│   │   ├── base_client.py           # API客户端基类
│   │   ├── client_factory.py        # 客户端工厂
│   │   └── demo_client.py           # 示例API客户端
│   ├── converters/                  # 结果转换器模块
│   │   ├── __init__.py
│   │   ├── base_converter.py        # 转换器基类
│   │   └── demo_converter.py        # 示例结果转换器
│   └── storage/                     # 存储模块
│       ├── __init__.py
│       ├── response_storage.py      # API响应存储
│       └── coordinate_mapper.py     # 坐标映射器
├── widgets/
│   └── api_selection_widget.py      # API选择组件（新增）
└── utils/
    └── api_utils.py                 # API相关工具函数（新增）
```

## 受影响的现有模块

### 1. TablePropertiesWidget (`labelme/widgets/table_properties_widget.py`)
- **影响类型**：功能扩展
- **修改内容**：在现有属性面板中添加API辅助打标UI区域
- **依据**：需求文档要求在表格属性栏中新增"第三方API辅助打标"功能区域

### 2. TableLabelMainWindow (`labelme/app_tableme.py`)
- **影响类型**：组件集成
- **修改内容**：集成APIManager到主窗口，建立与现有组件的连接
- **依据**：需要在主应用中协调API功能与现有表格标注功能

### 3. Canvas (`labelme/widgets/canvas.py`)
- **影响类型**：数据接入
- **修改内容**：支持批量添加API生成的TableCellShape对象
- **依据**：API识别结果需要在Canvas上可视化显示

## 渐进式小步迭代开发步骤

### 步骤1：基础架构搭建
**目标**：创建API模块的基础架构和抽象接口
**验证标准**：应用能正常启动，新增模块可正常导入

#### 1.1 创建API模块基础结构
- 创建 `labelme/api/__init__.py`
- 创建 `labelme/api/clients/__init__.py`
- 创建 `labelme/api/converters/__init__.py`
- 创建 `labelme/api/storage/__init__.py`

#### 1.2 实现基础抽象类
- 创建 `labelme/api/clients/base_client.py` - API客户端基类
- 创建 `labelme/api/converters/base_converter.py` - 结果转换器基类
- 创建 `labelme/utils/api_utils.py` - API工具函数

#### 1.3 验证基础架构
- 在主应用中导入新模块，确保无导入错误
- 运行应用，确保现有功能不受影响

### 步骤2：图像预处理器实现
**目标**：实现图像预处理功能，支持图像尺寸限制和坐标映射
**验证标准**：能够正确处理图像尺寸，生成坐标映射信息

#### 2.1 实现ImagePreprocessor
- 创建 `labelme/api/image_preprocessor.py`
- 实现图像尺寸检查和缩放功能
- 实现坐标映射信息记录

#### 2.2 实现CoordinateMapper
- 创建 `labelme/api/storage/coordinate_mapper.py`
- 实现坐标映射转换算法
- 支持缩放、偏移等几何变换的坐标映射

#### 2.3 单元测试验证
- 测试不同尺寸图像的预处理结果
- 测试坐标映射的准确性

### 步骤3：API响应存储实现
**目标**：实现API原始响应的存储功能
**验证标准**：能够正确保存和读取API响应JSON文件

#### 3.1 实现APIResponseStorage
- 创建 `labelme/api/storage/response_storage.py`
- 实现API响应的JSON文件保存功能
- 支持文件命名规范：`[文件名]-api-resp.json`

#### 3.2 集成文件系统操作
- 处理文件路径和权限问题
- 实现异常处理和错误日志

#### 3.3 功能验证
- 测试文件保存和读取功能
- 验证文件命名规范的正确性

### 步骤4：示例API客户端实现
**目标**：实现一个示例API客户端，建立API调用的基础框架
**验证标准**：能够模拟API调用流程，返回模拟的表格识别结果

#### 4.1 实现DemoAPIClient
- 创建 `labelme/api/clients/demo_client.py`
- 继承BaseAPIClient，实现模拟API调用
- 返回符合预期格式的模拟表格数据

#### 4.2 实现ClientFactory
- 创建 `labelme/api/clients/client_factory.py`
- 实现API客户端的工厂模式创建
- 支持根据API类型创建相应客户端

#### 4.3 API调用测试
- 测试模拟API的调用流程
- 验证返回数据格式的正确性

### 步骤5：结果转换器实现
**目标**：实现API响应到TableCellShape的转换功能
**验证标准**：能够将API响应正确转换为TableCellShape对象

#### 5.1 实现DemoResultConverter
- 创建 `labelme/api/converters/demo_converter.py`
- 实现API响应数据到TableCellShape的转换
- 处理坐标、文本、样式等属性的映射

#### 5.2 集成现有数据结构
- 确保生成的TableCellShape与现有系统兼容
- 处理表格ID、分组等属性的正确设置

#### 5.3 转换功能验证
- 测试不同API响应格式的转换
- 验证生成的TableCellShape对象的正确性

### 步骤6：API管理器核心实现
**目标**：实现APIManager核心功能，协调整个API调用流程
**验证标准**：能够完整执行从图像输入到结果输出的完整流程

#### 6.1 实现APIManager
- 创建 `labelme/api/api_manager.py`
- 集成图像预处理、API调用、结果转换、存储等功能
- 实现完整的API调用工作流

#### 6.2 错误处理和异常管理
- 实现API调用失败的错误处理
- 添加超时、重试等机制
- 提供详细的错误信息反馈

#### 6.3 集成测试
- 测试完整的API调用流程
- 验证各个组件的协作正确性

### 步骤7：UI组件实现
**目标**：实现API选择和触发的用户界面
**验证标准**：用户能够通过UI选择API并触发调用

#### 7.1 实现APISelectionWidget
- 创建 `labelme/widgets/api_selection_widget.py`
- 实现API服务选择下拉框
- 实现"开始辅助打标"按钮

#### 7.2 扩展TablePropertiesWidget
- 在现有属性面板中集成API选择组件
- 建立UI事件与APIManager的连接
- 实现用户操作的响应逻辑

#### 7.3 UI交互测试
- 测试UI组件的显示和交互
- 验证用户操作能够正确触发API调用

### 步骤8：主应用集成
**目标**：将API功能集成到主应用中，建立与现有功能的协调
**验证标准**：API功能与现有表格标注功能能够无缝协作

#### 8.1 集成到TableLabelMainWindow
- 在主窗口中初始化APIManager
- 建立APIManager与Canvas、MultiTableController的连接
- 处理API结果的可视化显示

#### 8.2 事件处理和信号连接
- 建立UI事件与业务逻辑的信号槽连接
- 处理API调用状态的UI反馈
- 实现错误信息的用户提示

#### 8.3 整体功能验证
- 测试完整的用户操作流程
- 验证API结果在Canvas上的正确显示
- 测试与现有功能的兼容性

### 步骤9：配置和优化
**目标**：完善配置管理和性能优化
**验证标准**：系统配置灵活，性能满足使用要求

#### 9.1 配置文件支持
- 添加API配置到默认配置文件
- 支持API服务的配置化管理
- 实现配置的动态加载

#### 9.2 性能优化
- 优化图像处理的内存使用
- 实现API调用的异步处理
- 添加必要的缓存机制

#### 9.3 日志和监控
- 添加API调用的详细日志
- 实现操作状态的监控和反馈
- 完善错误信息的记录

### 步骤10：测试和文档
**目标**：完善测试覆盖和使用文档
**验证标准**：功能测试通过，文档完整清晰

#### 10.1 单元测试完善
- 为所有新增模块编写单元测试
- 实现边界条件和异常情况的测试
- 确保测试覆盖率达到要求

#### 10.2 集成测试
- 实现端到端的集成测试
- 测试不同场景下的功能正确性
- 验证与现有功能的兼容性

#### 10.3 使用文档
- 编写用户使用指南
- 提供API集成的开发文档
- 创建故障排除指南

## 开发注意事项

### 1. 代码质量要求
- 每个新增文件不超过1000行代码
- 遵循现有的代码风格和命名规范
- 添加必要的类型注解和文档字符串

### 2. 兼容性保证
- 不修改现有API的签名
- 确保新功能不影响现有功能的稳定性
- 保持与现有数据格式的兼容性

### 3. 错误处理策略
- 遵循fail-fast原则，不掩盖错误
- 提供清晰的错误信息和用户提示
- 实现适当的降级和恢复机制

### 4. 测试验证标准
- 每个步骤完成后必须能够正常运行应用
- 新功能必须有明显的效果展示
- 不能破坏现有功能的正常使用

## 项目里程碑

- **里程碑1**：基础架构搭建完成（步骤1-3）
- **里程碑2**：核心功能实现完成（步骤4-6）
- **里程碑3**：用户界面集成完成（步骤7-8）
- **里程碑4**：功能完善和优化完成（步骤9-10）

每个里程碑完成后需要进行完整的功能验证和回归测试，确保系统的稳定性和可用性。 