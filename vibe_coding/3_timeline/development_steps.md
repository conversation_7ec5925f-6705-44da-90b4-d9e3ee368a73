~~# 表格标注工具完整开发计划（基于真实现状）

## 1. 现有组件真实状况重新评估

### 1.1 已基本完成的组件 ✅

#### table_shape.py - 基本完成 ✅
**已实现功能**：
- ✅ TableCellShape类完整实现（继承Shape）
- ✅ table_properties完整属性管理（lloc, border, table_type, cell_text, is_confirmed）
- ✅ 自定义paint()方法，支持边框样式渲染
- ✅ 序列化/反序列化接口（get_table_data, from_table_data）
- ✅ 完整的属性getter/setter方法
- ✅ 工具函数（create_table_cell_from_rect等）

**仅需微调**：
- ❓ _scale_point()方法可能需要补充（在paint中使用）
- ❓ 与Canvas的集成测试

#### table_analyzer.py - 基本完成 ✅
**已实现功能**：
- ✅ 核心分析算法 analyze_cells_to_grid()
- ✅ 单元格聚类和边界检测
- ✅ 逻辑网格构建

**仅需微调**：
- ❓ 与TableCellShape的接口适配验证

### 1.2 功能严重不完整的组件 ⚠️

#### table_controller.py - 框架完整，核心方法缺失 ⚠️
**已实现**：
- ✅ 模式管理框架
- ✅ Canvas事件拦截框架  
- ✅ 单元格转换基础逻辑
- ✅ 高亮和选择机制

**关键缺失**：
- ❌ 对齐工具具体算法实现
- ❌ 批量网格生成算法
- ❌ 划线推理完整实现
- ❌ 与UI组件的双向绑定

#### table_properties_widget.py - UI完整，业务逻辑缺失 ⚠️
**已实现**：
- ✅ 完整UI布局
- ✅ 基础数据接口

**关键缺失**：
- ❌ 信号槽连接和事件处理
- ❌ 与TableController双向绑定
- ❌ 批量编辑业务逻辑

#### table_structure_widget.py - 显示完整，交互缺失 ⚠️
**已实现**：
- ✅ 基础表格显示
- ✅ 基础点击事件

**关键缺失**：
- ❌ 边框样式实际渲染
- ❌ 合并单元格setSpan实现
- ❌ 与Canvas双向联动

#### canvas.py - 钩子预留，集成不完整 ⚠️
**已实现**：
- ✅ 表格控制器钩子预留
- ✅ 基础拦截框架

**关键缺失**：
- ❌ TableCellShape与现有paint系统的集成
- ❌ 表格模式UI状态管理

### 1.3 完全缺失的核心组件 ❌

- ❌ **multi_table_controller.py** - 多表格协调器
- ❌ **table_data_manager.py** - 数据统一管理器  
- ❌ **table_data_sync.py** - 智能数据同步管理器
- ❌ **table_exporter.py** - 数据导出器
- ❌ **app_tableme.py** - 表格专用主窗口

---

## 2. 重新优化的开发计划

> 基于table_shape.py和table_analyzer.py已基本完成的现状，重新优化开发计划

### 第一阶段：现有组件功能补全

#### 阶段目标：让现有组件达到完全可用状态

### Xelawk开发线 - 控制器核心算法补全

#### 步骤X1：补全TableController核心算法
**扩展文件**：`labelme/widgets/table_controller.py`（新增约250行）

**受影响的现有模块**：
- 基于现有table_controller.py框架
- 需要调用table_analyzer.py和table_shape.py

**核心功能补全**：
```python
# 1. 对齐工具算法实现
def align_selected_cells_top(self):
    """顶端对齐：找到最小Y坐标，调整所有选中单元格"""
    selected_cells = self.get_highlighted_cells()
    if len(selected_cells) < 2:
        return False
    
    # 找到最顶端的Y坐标
    min_y = min(min(p.y() for p in cell.points) for cell in selected_cells)
    
    # 调整所有单元格
    for cell in selected_cells:
        current_min_y = min(p.y() for p in cell.points)
        dy = min_y - current_min_y
        # 移动所有点
        for point in cell.points:
            point.setY(point.y() + dy)
    
    self.canvas.update()
    return True

def align_selected_cells_left(self):
    """左对齐算法"""
    # 类似实现...

def align_selected_cells_bottom(self):
    """底端对齐算法"""
    # 类似实现...

def align_selected_cells_right(self):
    """右对齐算法"""
    # 类似实现...

# 2. 批量网格生成算法
def auto_decompose_region_to_grid(self, rows=None, cols=None):
    """将表格区域分解为均匀网格"""
    if not hasattr(self, 'table_region') or not self.table_region:
        print("❌ 没有设置表格区域")
        return []
    
    x1, y1, x2, y2 = self.table_region
    
    # 智能估算行列数（如果未指定）
    if rows is None or cols is None:
        area = (x2 - x1) * (y2 - y1)
        estimated_cells = max(4, int(area / 10000))  # 基于面积估算
        rows = rows or int(estimated_cells ** 0.5)
        cols = cols or int(estimated_cells / rows)
    
    # 计算单元格尺寸
    cell_width = (x2 - x1) / cols
    cell_height = (y2 - y1) / rows
    
    created_cells = []
    
    # 批量创建单元格
    for row in range(rows):
        for col in range(cols):
            # 计算单元格位置
            cx1 = x1 + col * cell_width
            cy1 = y1 + row * cell_height
            cx2 = cx1 + cell_width
            cy2 = cy1 + cell_height
            
            # 创建TableCellShape
            from labelme.table_shape import create_table_cell_from_rect
            cell = create_table_cell_from_rect(
                cx1, cy1, cx2, cy2,
                label=f"cell_{row}_{col}"
            )
            
            # 设置逻辑位置
            cell.set_logical_location(row, row, col, col)
            cell.set_confirmed(True)
            
            # 添加到管理列表
            self.table_cells.append(cell)
            created_cells.append(cell)
    
    print(f"✅ 成功创建 {rows}x{cols} 网格，共 {len(created_cells)} 个单元格")
    return created_cells

# 3. 划线推理算法
def _handle_guide_line_creation(self, line_shape):
    """处理用户划线，重新分配单元格逻辑坐标"""
    direction = self._analyze_line_direction(line_shape)
    line_position = self._get_line_position(line_shape)
    
    affected_cells = self._find_cells_affected_by_line(line_position, direction)
    
    if direction == "horizontal":
        self._split_cells_horizontally(affected_cells, line_position)
    elif direction == "vertical":
        self._split_cells_vertically(affected_cells, line_position)
    
    # 重新分析表格结构
    self.update_table_structure()
    
    return {
        'intercepted': True,
        'action': 'line_inference_applied',
        'result_data': {
            'direction': direction,
            'affected_cells': len(affected_cells)
        }
    }

def _analyze_line_direction(self, line_shape):
    """分析线条方向"""
    if len(line_shape.points) < 2:
        return "unknown"
    
    start, end = line_shape.points[0], line_shape.points[1]
    dx = abs(end.x() - start.x())
    dy = abs(end.y() - start.y())
    
    return "horizontal" if dy < dx else "vertical"

# 4. 与UI组件双向绑定
def bind_properties_widget(self, properties_widget):
    """绑定属性面板"""
    self.properties_widget = properties_widget
    # 连接信号
    properties_widget.property_changed.connect(self._on_properties_changed)
    
def bind_structure_widget(self, structure_widget):
    """绑定逻辑结构视图"""
    self.structure_widget = structure_widget
    # 连接信号
    structure_widget.cellClicked.connect(self._on_structure_cell_clicked)
```

**验证标准**：
- 对齐工具能正确对齐2-10个选中单元格
- 批量网格生成能创建3x3到10x10的均匀网格
- 划线推理能正确调整单元格逻辑坐标
- UI组件双向绑定功能正常

#### 步骤X2：验证Canvas集成
**修改文件**：`labelme/widgets/canvas.py`（修改约50行）

**受影响的现有模块**：
- 验证table_shape.py的paint()方法与Canvas的兼容性
- 确保拦截钩子正确工作

**核心验证和修复**：
```python
# 1. 确保TableCellShape在Canvas中正确渲染
def paintEvent(self, event):
    # 现有代码...
    
    for shape in self.shapes:
        if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell':
            # TableCellShape有自己的paint方法，直接调用
            shape.paint(p)
        else:
            # 其他形状使用原有渲染
            if (shape.selected or not self._hideBackround) and self.isVisible(shape):
                shape.fill = shape.selected or shape == self.hShape
                shape.paint(p)

# 2. 补充可能缺失的_scale_point方法（如果table_shape.py需要）
# 这个方法在table_shape.py的paint中使用，但Shape基类可能没有
def _ensure_table_shape_compatibility(self):
    """确保TableCellShape与Canvas兼容"""
    # 如果需要，为TableCellShape添加_scale_point方法
    pass
```

### OYY开发线 - UI组件业务逻辑补全

#### 步骤Y1：完善TablePropertiesWidget业务逻辑
**扩展文件**：`labelme/widgets/table_properties_widget.py`（新增约200行）

**受影响的现有模块**：
- 基于现有UI布局
- 需要与table_controller.py集成

**核心功能补全**：
```python
# 1. 信号槽完整连接
def __init__(self, parent=None):
    super().__init__(parent)
    self._setup_ui()
    self._connect_signals()  # 新增
    self.controller = None
    self.selected_cells = []

def _connect_signals(self):
    """连接所有控件信号"""
    # 表格类型变更
    self.table_type_combo.currentIndexChanged.connect(self._on_type_changed)
    
    # 边框样式变更
    self.border_top.toggled.connect(self._on_border_changed)
    self.border_right.toggled.connect(self._on_border_changed)
    self.border_bottom.toggled.connect(self._on_border_changed)
    self.border_left.toggled.connect(self._on_border_changed)
    
    # 逻辑位置变更
    self.row_start.valueChanged.connect(self._on_position_changed)
    self.row_end.valueChanged.connect(self._on_position_changed)
    self.col_start.valueChanged.connect(self._on_position_changed)
    self.col_end.valueChanged.connect(self._on_position_changed)
    
    # 内容变更
    self.content_text.textChanged.connect(self._on_content_changed)

# 2. 与控制器双向绑定
def bind_controller(self, controller):
    """绑定TableController"""
    self.controller = controller
    controller.bind_properties_widget(self)

def update_from_selection(self, selected_cells):
    """根据选中的单元格更新界面"""
    self.selected_cells = selected_cells
    
    if not selected_cells:
        self._clear_display()
        return
    
    if len(selected_cells) == 1:
        # 单选模式
        self._update_single_selection(selected_cells[0])
    else:
        # 多选模式
        self._update_multiple_selection(selected_cells)

def _update_single_selection(self, cell):
    """更新单选模式显示"""
    # 表格类型
    self.table_type_combo.setCurrentIndex(cell.get_table_type())
    
    # 边框样式
    border = cell.get_border_style()
    self.border_top.setChecked(border["top"] == 1)
    self.border_right.setChecked(border["right"] == 1)
    self.border_bottom.setChecked(border["bottom"] == 1)
    self.border_left.setChecked(border["left"] == 1)
    
    # 逻辑位置
    lloc = cell.get_logical_location()
    self.row_start.setValue(lloc["start_row"])
    self.row_end.setValue(lloc["end_row"])
    self.col_start.setValue(lloc["start_col"])
    self.col_end.setValue(lloc["end_col"])
    
    # 内容
    self.content_text.setPlainText(cell.get_cell_text())

def _update_multiple_selection(self, cells):
    """更新多选模式显示"""
    # 分析共同属性
    common_props = self._analyze_common_properties(cells)
    
    # 显示共同属性，差异项显示为"混合"
    self._display_common_properties(common_props)

# 3. 事件处理方法
def _on_type_changed(self, index):
    """表格类型变更处理"""
    self._apply_changes()

def _on_border_changed(self):
    """边框样式变更处理"""
    self._apply_changes()

def _on_position_changed(self):
    """逻辑位置变更处理"""
    self._apply_changes()

def _on_content_changed(self):
    """内容变更处理"""
    self._apply_changes()

def _apply_changes(self):
    """应用修改到选中的单元格"""
    if not self.selected_cells or not self.controller:
        return
    
    # 收集当前界面数据
    changes = self.get_data()
    
    # 应用到选中的单元格
    for cell in self.selected_cells:
        if "table_type" in changes:
            cell.set_table_type(changes["table_type"])
        
        if "border" in changes:
            border = changes["border"]
            cell.set_border_style(
                top=border["top"],
                right=border["right"],
                bottom=border["bottom"],
                left=border["left"]
            )
        
        if "logical_position" in changes:
            pos = changes["logical_position"]
            cell.set_logical_location(
                pos["start_row"], pos["end_row"],
                pos["start_col"], pos["end_col"]
            )
        
        if "cell_text" in changes:
            cell.set_cell_text(changes["cell_text"])
    
    # 更新Canvas显示
    if hasattr(self.controller, 'canvas'):
        self.controller.canvas.update()
    
    print(f"✅ 已更新 {len(self.selected_cells)} 个单元格的属性")

# 4. 批量编辑支持
def _analyze_common_properties(self, cells):
    """分析多个单元格的共同属性"""
    if not cells:
        return {}
    
    # 分析表格类型
    types = set(cell.get_table_type() for cell in cells)
    common_type = list(types)[0] if len(types) == 1 else "mixed"
    
    # 分析边框样式
    borders = [cell.get_border_style() for cell in cells]
    common_border = {}
    for side in ["top", "right", "bottom", "left"]:
        values = set(border[side] for border in borders)
        common_border[side] = list(values)[0] if len(values) == 1 else "mixed"
    
    # 分析文本内容
    texts = set(cell.get_cell_text() for cell in cells)
    common_text = list(texts)[0] if len(texts) == 1 else "mixed"
    
    return {
        "table_type": common_type,
        "border": common_border,
        "cell_text": common_text
    }
```

**验证标准**：
- 选中单元格时属性面板正确更新
- 修改属性能实时应用到单元格
- 多选时显示共同属性
- 批量编辑功能正常

#### 步骤Y2：完善TableStructureWidget交互逻辑
**扩展文件**：`labelme/widgets/table_structure_widget.py`（新增约150行）

**受影响的现有模块**：
- 基于现有显示功能
- 需要与canvas.py和table_controller.py集成

**核心功能补全**：
```python
# 1. 边框样式实际渲染
def _setup_table_view(self):
    """设置表格视图的渲染"""
    # 使用自定义委托渲染器
    delegate = TableCellDelegate()
    self.table_view.setItemDelegate(delegate)

class TableCellDelegate(QtWidgets.QStyledItemDelegate):
    """自定义单元格渲染器"""
    
    def paint(self, painter, option, index):
        """自定义绘制单元格边框"""
        super().paint(painter, option, index)
        
        # 获取单元格数据
        cell_data = index.data(QtCore.Qt.UserRole)
        if not cell_data or "border" not in cell_data:
            return
        
        # 绘制边框
        self._draw_cell_borders(painter, option.rect, cell_data["border"])
    
    def _draw_cell_borders(self, painter, rect, border):
        """绘制单元格边框"""
        # 根据border字典绘制四条边
        # ... 实现边框绘制逻辑

# 2. 与Canvas双向联动
def bind_canvas(self, canvas):
    """绑定Canvas实现双向联动"""
    self.canvas = canvas
    # 连接Canvas选择变更信号
    if hasattr(canvas, 'selectionChanged'):
        canvas.selectionChanged.connect(self._on_canvas_selection_changed)

def _on_canvas_selection_changed(self, shapes):
    """响应Canvas选择变更"""
    # 找到选中的表格单元格
    from labelme.table_shape import is_table_cell
    selected_cells = [s for s in shapes if is_table_cell(s)]
    
    if selected_cells:
        # 高亮对应的逻辑视图单元格
        for cell in selected_cells:
            lloc = cell.get_logical_location()
            row = lloc["start_row"]
            col = lloc["start_col"]
            self.highlight_cell(row, col)

def _on_cell_clicked(self, row, col):
    """处理逻辑视图单元格点击"""
    super()._on_cell_clicked(row, col)
    
    # 通知Canvas高亮对应的物理单元格
    if self.canvas and hasattr(self.canvas, 'table_controller'):
        controller = self.canvas.table_controller
        if controller:
            controller.highlight_cells_by_logical_position(row, col)

# 3. 合并单元格支持
def _apply_cell_spans(self, grid_data):
    """应用合并单元格"""
    cells = grid_data.get('cells', [])
    
    # 清除现有的合并
    self.table_view.clearSpans()
    
    # 按逻辑位置处理合并
    for cell in cells:
        row = cell['row']
        col = cell['col']
        # 检查是否是合并单元格（暂时简化，未来可以支持真正的合并）
        self.table_view.setSpan(row, col, 1, 1)
```

**验证标准**：
- 边框样式在逻辑视图中正确显示
- 点击逻辑视图时Canvas对应单元格高亮
- 点击Canvas时逻辑视图对应单元格高亮
- 基础的合并单元格显示正常

---

### 第二阶段：核心架构组件开发

#### 阶段目标：实现多表格管理和数据处理的完整架构

### Xelawk开发线 - 多表格架构

#### 步骤X3：开发多表格协调器
**新建文件**：`labelme/widgets/multi_table_controller.py`（约500行）

与之前的计划相同，实现TableInstanceManager、TableSwitchManager等核心组件。

#### 步骤X4：开发智能数据同步管理器
**新建文件**：`labelme/utils/table_data_sync.py`（约400行）

与之前的计划相同，实现ChangeDetector、SyncStrategy等组件。

### OYY开发线 - 数据管理

#### 步骤Y3：开发表格数据统一管理器
**新建文件**：`labelme/utils/table_data_manager.py`（约450行）

与之前的计划相同，基于table_shape.py的序列化接口开发。

#### 步骤Y4：开发表格数据导出器
**新建文件**：`labelme/utils/table_exporter.py`（约350行）

与之前的计划相同，支持多种格式导出。

---

### 第三阶段：主应用开发和系统集成

#### 步骤Z1：开发表格专用主窗口
**新建文件**：`labelme/app_tableme.py`（约800行）

完全仿照app.py创建TableMainWindow，集成所有组件。

#### 步骤Z2：扩展启动机制和最终集成
**修改文件**：`labelme/__main__.py`

添加表格模式启动支持。

#### 步骤Z3：端到端测试和优化
完整系统测试，性能优化，bug修复。

---

## 3. 重新优化后的验证标准

### 3.1 第一阶段验证
- ✅ 现有所有组件功能完整可用
- ✅ TableController的对齐、网格生成、划线推理功能正常
- ✅ UI组件与控制器双向绑定正常
- ✅ 基础的表格标注流程可以完整运行

### 3.2 第二阶段验证
- ✅ 多表格管理架构完整
- ✅ 数据管理和同步机制有效
- ✅ 核心业务逻辑完全可用

### 3.3 第三阶段验证
- ✅ 独立表格应用完全可用
- ✅ 所有PRD功能实现
- ✅ 性能和稳定性达标

这个优化后的计划更加现实，充分利用了已有的优秀实现！~~