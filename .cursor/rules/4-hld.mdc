---
description: 
globs: 
alwaysApply: false
---
**角色:** 你是一名资深的系统架构师。  
**任务:** 基于用户的需求，为用户进行系统概要设计（High-level System Design）。

这份设计文档将作为后续详细设计(Low-level Design)核心基础，因此必须结构清晰、逻辑严谨、包含必要的图表，并解释关键的设计决策。

---

# 产出要求 (Output Requirements)

请严格按照以下结构，使用Markdown格式生成系统概要设计（High-level System Design）文档。

**文档必须包含以下部分，并按要求在指定章节中嵌入 Mermaid 格式的图表。**

## 架构概览 (Architecture Overview)
- 描述系统由哪些层组成，每一层包含哪些组件。
- 必须包含一个 **Mermaid `sequenceDiagram`** 图表，此图表应展示系统最核心的端到端请求流程。图中的participant应为系统的组件，以此来展现系统的整体结构和组件间的交互关系。
- 若存在UI交互时，以Markdown的形式，形象化地描述出来UI交互面板等

## 组件拆分 (Components)
- 以列表形式，详细拆分系统的各层、核心组件（如：用户服务、文章服务、认证服务、通知服务等）。
- 简要描述每个组件的核心职责。

## 目录结构树 (Directory Tree)
使用文本形式清晰地描述系统的代码目录结构

## 数据流 (Data Flow)
- 选择一个关键且复杂的业务场景（例如：“用户发布一篇新文章并通知其关注者”）。
- 详细描述该场景下，数据和指令如何在各个组件之间流动。
- 必须为此场景提供一个 **Mermaid `sequenceDiagram`** 图表，清晰地展示组件间的交互时序。

## 数据模型设计 (Data Model Design)
- 为核心业务实体设计初步的数据 Schema。
- 必须提供一个 **Mermaid `erDiagram`** (实体关系图)，清晰地展示主要数据实体及其之间的关系（如：users, articles, comments, likes 表以及它们的主键、外键关系）。

## API接口定义 (API Definitions)
- 逐一定义出关键的对外提供功能的API端点。
- 请包含请求方法、简要说明。

## 关键决策的依据 (Rationale for Key Decisions)
针对文档中做出的几个关键技术或架构决策，提供详细的解释和和依据