#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/3 16:30
# <AUTHOR> <EMAIL>
# @FileName: history_manager.py

"""
全局历史管理器

实现基于Command模式的全局撤销重做系统，管理所有编辑操作的历史记录。
"""

import time
from typing import List, Optional, Any
from PyQt5.QtCore import QObject, pyqtSignal

from ..commands.base_command import BaseCommand
from ..utils.log import get_logger

LOGGER = get_logger()


class GlobalHistoryManager(QObject):
    """
    全局历史管理器
    
    管理所有编辑操作的命令历史，提供撤销/重做功能。
    使用Command模式存储和回放编辑操作。
    """
    
    # 信号定义
    history_changed = pyqtSignal()  # 历史记录变化时发出
    command_executed = pyqtSignal(BaseCommand)  # 命令执行时发出
    command_undone = pyqtSignal(BaseCommand)    # 命令撤销时发出
    command_redone = pyqtSignal(BaseCommand)    # 命令重做时发出
    
    def __init__(self, max_history_size: int = 100):
        """
        初始化历史管理器
        
        Args:
            max_history_size: 最大历史记录数量，默认100个
        """
        super().__init__()
        
        self._history_stack: List[BaseCommand] = []
        self._current_index: int = -1  # 当前位置，-1表示没有历史
        self._max_history_size: int = max_history_size
        
        # 统计信息
        self._total_commands_executed: int = 0
        self._total_memory_used: int = 0
        
        LOGGER.debug(f"GlobalHistoryManager initialized (max_size: {max_history_size})")
        
    def execute_command(self, command: BaseCommand) -> bool:
        """
        执行命令并记录到历史
        
        Args:
            command: 要执行的命令
            
        Returns:
            bool: 执行成功返回True，失败返回False
        """
        if not isinstance(command, BaseCommand):
            LOGGER.error(f"Invalid command type: {type(command)}")
            return False
            
        try:
            # 执行命令
            success = command.execute()
            if not success:
                LOGGER.error(f"Command execution failed: {command}")
                return False
                
            # 清除当前位置之后的所有历史（重做栈）
            if self._current_index < len(self._history_stack) - 1:
                removed_commands = self._history_stack[self._current_index + 1:]
                self._history_stack = self._history_stack[:self._current_index + 1]
                LOGGER.debug(f"Cleared {len(removed_commands)} redo commands")
                
            # 添加新命令到历史
            self._history_stack.append(command)
            self._current_index += 1
            
            # 控制历史大小
            self._trim_history()
            
            # 更新统计信息
            self._total_commands_executed += 1
            self._update_memory_usage()
            
            # 发出信号
            self.command_executed.emit(command)
            self.history_changed.emit()
            
            LOGGER.debug(f"Command executed: {command}")
            return True
            
        except Exception as e:
            LOGGER.error(f"Exception during command execution: {e}")
            return False
            
    def undo(self) -> bool:
        """
        撤销上一个操作
        
        Returns:
            bool: 撤销成功返回True，失败返回False
        """
        if not self.can_undo():
            LOGGER.debug("No operation to undo")
            return False
            
        try:
            command = self._history_stack[self._current_index]
            success = command.undo()
            
            if success:
                self._current_index -= 1
                self.command_undone.emit(command)
                self.history_changed.emit()
                LOGGER.debug(f"Command undone: {command}")
                return True
            else:
                LOGGER.error(f"Command undo failed: {command}")
                return False
                
        except Exception as e:
            LOGGER.error(f"Exception during undo: {e}")
            return False
            
    def redo(self) -> bool:
        """
        重做下一个操作
        
        Returns:
            bool: 重做成功返回True，失败返回False
        """
        if not self.can_redo():
            LOGGER.debug("No operation to redo")
            return False
            
        try:
            self._current_index += 1
            command = self._history_stack[self._current_index]
            success = command.execute()
            
            if success:
                self.command_redone.emit(command)
                self.history_changed.emit()
                LOGGER.debug(f"Command redone: {command}")
                return True
            else:
                self._current_index -= 1  # 回滚索引
                LOGGER.error(f"Command redo failed: {command}")
                return False
                
        except Exception as e:
            LOGGER.error(f"Exception during redo: {e}")
            # 回滚索引
            if self._current_index > -1:
                self._current_index -= 1
            return False
            
    def can_undo(self) -> bool:
        """
        检查是否可以撤销
        
        Returns:
            bool: 可以撤销返回True
        """
        return self._current_index >= 0
        
    def can_redo(self) -> bool:
        """
        检查是否可以重做
        
        Returns:
            bool: 可以重做返回True
        """
        return self._current_index < len(self._history_stack) - 1
        
    def clear_history(self) -> None:
        """清空历史记录"""
        self._history_stack.clear()
        self._current_index = -1
        self._total_memory_used = 0
        self.history_changed.emit()
        LOGGER.debug("History cleared")
        
    def get_history_size(self) -> int:
        """
        获取当前历史记录数量
        
        Returns:
            int: 历史记录数量
        """
        return len(self._history_stack)
        
    def get_current_command(self) -> Optional[BaseCommand]:
        """
        获取当前执行的命令
        
        Returns:
            Optional[BaseCommand]: 当前命令，如果没有则返回None
        """
        if self.can_undo():
            return self._history_stack[self._current_index]
        return None
        
    def get_next_command(self) -> Optional[BaseCommand]:
        """
        获取下一个可重做的命令
        
        Returns:
            Optional[BaseCommand]: 下一个命令，如果没有则返回None
        """
        if self.can_redo():
            return self._history_stack[self._current_index + 1]
        return None
        
    def get_statistics(self) -> dict:
        """
        获取统计信息
        
        Returns:
            dict: 包含统计信息的字典
        """
        return {
            'current_size': len(self._history_stack),
            'max_size': self._max_history_size,
            'current_index': self._current_index,
            'can_undo': self.can_undo(),
            'can_redo': self.can_redo(),
            'total_executed': self._total_commands_executed,
            'memory_usage_bytes': self._total_memory_used,
        }
        
    def _trim_history(self) -> None:
        """修剪历史记录，保持在最大限制内"""
        if len(self._history_stack) > self._max_history_size:
            # 移除最早的命令
            removed_count = len(self._history_stack) - self._max_history_size
            self._history_stack = self._history_stack[removed_count:]
            self._current_index -= removed_count
            
            # 确保current_index不为负数
            if self._current_index < -1:
                self._current_index = -1
                
            LOGGER.debug(f"Trimmed {removed_count} old commands from history")
            
    def _update_memory_usage(self) -> None:
        """更新内存使用统计"""
        self._total_memory_used = sum(
            cmd.get_memory_usage() for cmd in self._history_stack
        )
        
    def _record_command_without_execute(self, command: BaseCommand) -> None:
        """
        记录命令但不执行（用于已经执行的操作）
        
        Args:
            command: 已执行的命令
            
        Note:
            这是一个内部方法，用于与现有Canvas系统的兼容性
        """
        command.executed = True
        
        # 清除重做栈
        if self._current_index < len(self._history_stack) - 1:
            self._history_stack = self._history_stack[:self._current_index + 1]
            
        # 添加到历史
        self._history_stack.append(command)
        self._current_index += 1
        
        # 控制历史大小
        self._trim_history()
        
        # 更新统计
        self._total_commands_executed += 1
        self._update_memory_usage()
        
        # 发出信号
        self.command_executed.emit(command)
        self.history_changed.emit()
        
        LOGGER.debug(f"Command recorded: {command}")


# 创建全局实例的工厂函数
def create_global_history_manager(max_size: int = 100) -> GlobalHistoryManager:
    """
    创建全局历史管理器实例
    
    Args:
        max_size: 最大历史记录数量
        
    Returns:
        GlobalHistoryManager: 历史管理器实例
    """
    return GlobalHistoryManager(max_size) 