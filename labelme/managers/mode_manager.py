#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/3 16:00
# <AUTHOR> <EMAIL>
# @FileName: mode_manager.py

"""
模式管理器 - 管理简化的2模式系统
将原有的5种模式简化为2种核心模式：选择模式和单元格模式
"""

from enum import Enum
from typing import Optional, Callable
from PyQt5 import QtCore

from ..utils.log import get_logger

LOGGER = get_logger()


class OperationMode(Enum):
    """操作模式枚举"""
    SELECT = "select"        # 选择模式 - 选择、移动、调整已有单元格
    CREATE_CELL = "create_cell"  # 单元格模式 - 拖拽绘制新的表格单元格
    GRID_DRAG = "grid_drag"  #  网格拖拽模式


class ModeManager(QtCore.QObject):
    """模式管理器 - 管理操作模式的状态和切换"""
    
    # 信号定义
    mode_changed = QtCore.pyqtSignal(object, object)  # (old_mode, new_mode)
    mode_state_updated = QtCore.pyqtSignal(object)    # (current_mode)
    
    def __init__(self):
        super().__init__()
        
        # 当前模式
        self._current_mode = OperationMode.SELECT
        
        # 模式配置
        self._mode_configs = {
            OperationMode.SELECT: {
                'name': '选择模式',
                'description': '选择、移动、调整已有单元格，支持多选',
                'cursor': 'default',
                'allow_create': False,
                'allow_select': True,
                'allow_multi_select': True,
                'allow_move': True,
                'allow_resize': True,
            },
            OperationMode.CREATE_CELL: {
                'name': '单元格模式', 
                'description': '拖拽绘制新的表格单元格',
                'cursor': 'crosshair',
                'allow_create': True,
                'allow_select': True,
                'allow_multi_select': False,
                'allow_move': False,
                'allow_resize': False,
            },
            OperationMode.GRID_DRAG: {
                'name': '网格拖拽模式',
                'description': '拖拽网格线调整表格布局',
                'cursor': 'default',
                'allow_create': False,
                'allow_select': False,
                'allow_multi_select': False,
                'allow_move': False,
                'allow_resize': False,
            }
        }
        
        # 模式切换回调函数
        self._mode_callbacks = {}
        
        LOGGER.debug("ModeManager初始化完成")

    @property
    def current_mode(self) -> OperationMode:
        """获取当前模式"""
        return self._current_mode

    def get_mode_config(self, mode: Optional[OperationMode] = None) -> dict:
        """获取模式配置
        
        Args:
            mode: 指定模式，为None时返回当前模式配置
            
        Returns:
            模式配置字典
        """
        target_mode = mode if mode is not None else self._current_mode
        return self._mode_configs.get(target_mode, {})

    def switch_mode(self, new_mode: OperationMode) -> bool:
        """切换操作模式
        
        Args:
            new_mode: 目标模式
            
        Returns:
            是否切换成功
        """
        if new_mode == self._current_mode:
            return True
            
        if new_mode not in self._mode_configs:
            LOGGER.error(f"不支持的模式: {new_mode}")
            return False
            
        old_mode = self._current_mode
        self._current_mode = new_mode
        
        # 发出模式切换信号
        self.mode_changed.emit(old_mode, new_mode)
        self.mode_state_updated.emit(new_mode)
        
        # 执行模式切换回调
        self._execute_mode_callback(new_mode)
        
        config = self.get_mode_config(new_mode)
        LOGGER.debug(f"模式切换: {old_mode.value} -> {new_mode.value} ({config['name']})")
        
        return True

    def register_mode_callback(self, mode: OperationMode, callback: Callable[[OperationMode], None]):
        """注册模式切换回调函数
        
        Args:
            mode: 目标模式
            callback: 回调函数，接收new_mode参数
        """
        if mode not in self._mode_callbacks:
            self._mode_callbacks[mode] = []
        self._mode_callbacks[mode].append(callback)
        LOGGER.debug(f"注册模式回调: {mode.value}")

    def _execute_mode_callback(self, mode: OperationMode):
        """执行模式切换回调"""
        callbacks = self._mode_callbacks.get(mode, [])
        for callback in callbacks:
            try:
                callback(mode)
            except Exception as e:
                LOGGER.error(f"模式回调执行失败: {e}")

    def is_mode_active(self, mode: OperationMode) -> bool:
        """检查指定模式是否为当前活动模式"""
        return self._current_mode == mode

    def can_create_shapes(self) -> bool:
        """当前模式是否允许创建形状"""
        config = self.get_mode_config()
        return config.get('allow_create', False)

    def can_select_shapes(self) -> bool:
        """当前模式是否允许选择形状"""
        config = self.get_mode_config()
        return config.get('allow_select', True)

    def can_multi_select(self) -> bool:
        """当前模式是否允许多选"""
        config = self.get_mode_config()
        return config.get('allow_multi_select', False)

    def can_move_shapes(self) -> bool:
        """当前模式是否允许移动形状"""
        config = self.get_mode_config()
        return config.get('allow_move', False)

    def can_resize_shapes(self) -> bool:
        """当前模式是否允许调整形状大小"""
        config = self.get_mode_config()
        return config.get('allow_resize', False)

    def get_cursor_style(self) -> str:
        """获取当前模式的鼠标样式"""
        config = self.get_mode_config()
        return config.get('cursor', 'default')

    def get_mode_description(self) -> str:
        """获取当前模式的描述"""
        config = self.get_mode_config()
        return config.get('description', '')

    def get_all_modes(self) -> list:
        """获取所有可用模式"""
        return list(self._mode_configs.keys())

    def reset_to_default(self):
        """重置到默认模式（选择模式）"""
        self.switch_mode(OperationMode.SELECT)

    def __str__(self) -> str:
        """字符串表示"""
        config = self.get_mode_config()
        return f"ModeManager(current={self._current_mode.value}, name='{config['name']}')" 