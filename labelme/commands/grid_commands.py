#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/3 20:30
# <AUTHOR> <EMAIL>
# @FileName: grid_commands.py

"""
网格拖拽命令类

实现基于Command模式的网格拖拽操作命令，支持撤销和重做。
包括：网格线平移、旋转、边拖拽、表格整体旋转等操作。
"""

import copy
from typing import List, Dict, Any, Optional
from PyQt5.QtCore import QPointF
from qtpy import QtCore

from .base_command import BaseCommand
from labelme.table_shape import TableCellShape
from labelme.utils.log import get_logger

LOGGER = get_logger()


class GridLineTranslateCommand(BaseCommand):
    """
    网格线平移命令
    
    封装网格线平移操作，支持撤销和重做。
    """
    
    def __init__(
        self, 
        canvas, 
        affected_cells: List[TableCellShape],
        old_positions: List[List[QPointF]], 
        new_positions: List[List[QPointF]],
        grid_line_info: Dict[str, Any],
        description: str = None
    ):
        """
        初始化网格线平移命令
        
        Args:
            canvas: Canvas画布对象
            affected_cells: 受影响的单元格列表
            old_positions: 每个单元格的原始位置点列表
            new_positions: 每个单元格的新位置点列表
            grid_line_info: 网格线信息 {'direction': 'vertical'/'horizontal', 'coordinate': float}
            description: 命令描述
        """
        if description is None:
            direction = grid_line_info.get('direction', 'unknown')
            coordinate = grid_line_info.get('coordinate', 0)
            description = f"平移{direction}网格线 (坐标: {coordinate:.1f})"
            
        super().__init__(description)
        
        self.canvas = canvas
        self.affected_cells = affected_cells
        self.old_positions = old_positions  # 每个cell的旧位置点列表
        self.new_positions = new_positions  # 每个cell的新位置点列表
        self.grid_line_info = grid_line_info
        
        # 验证数据完整性
        if len(affected_cells) != len(old_positions) or len(affected_cells) != len(new_positions):
            raise ValueError("affected_cells、old_positions、new_positions的长度必须一致")
            
        LOGGER.debug(f"🔧 [GRID_TRANSLATE] 创建平移命令: {description}")
        LOGGER.debug(f"   影响单元格: {len(affected_cells)}个")
        LOGGER.debug(f"   网格线: {grid_line_info}")

    def execute(self) -> bool:
        """
        执行网格线平移操作
        
        Returns:
            bool: 是否执行成功
        """
        try:
            LOGGER.debug(f"🎯 [GRID_TRANSLATE] 执行平移: {self.description}")
            
            # 应用新位置到所有受影响的单元格
            for i, cell in enumerate(self.affected_cells):
                if i < len(self.new_positions):
                    # 深拷贝新位置避免引用问题
                    new_points = [QtCore.QPointF(p.x(), p.y()) for p in self.new_positions[i]]
                    cell.points = new_points
                    LOGGER.debug(f"   ✅ 单元格 {cell.label} 位置已更新")
            
            # 🆕 同步更新网格线检测和视图
            self._sync_grid_detection_and_view()

            LOGGER.debug(f"✅ [GRID_TRANSLATE] 平移执行成功")
            return True
            
        except Exception as e:
            LOGGER.error(f"[GRID_TRANSLATE] 平移执行失败: {e}")
            return False

    def undo(self) -> bool:
        """
        撤销网格线平移操作
        
        Returns:
            bool: 是否撤销成功
        """
        try:
            LOGGER.debug(f"↩️ [GRID_TRANSLATE] 撤销平移: {self.description}")
            
            # 恢复所有单元格到原始位置
            for i, cell in enumerate(self.affected_cells):
                if i < len(self.old_positions):
                    # 深拷贝原始位置避免引用问题
                    old_points = [QtCore.QPointF(p.x(), p.y()) for p in self.old_positions[i]]
                    cell.points = old_points
                    LOGGER.debug(f"   ↩️ 单元格 {cell.label} 位置已恢复")
            
            # 🆕 同步更新网格线检测和视图
            self._sync_grid_detection_and_view()

            LOGGER.debug(f"✅ [GRID_TRANSLATE] 平移撤销成功")
            return True
            
        except Exception as e:
            LOGGER.error(f"[GRID_TRANSLATE] 平移撤销失败: {e}")
            return False

    def get_memory_usage(self) -> int:
        """估算内存使用量"""
        base_size = super().get_memory_usage()
        
        # 计算positions的内存使用
        old_positions_size = sum(len(pos_list) * 16 for pos_list in self.old_positions)  # 每个QPointF约16字节
        new_positions_size = sum(len(pos_list) * 16 for pos_list in self.new_positions)
        cells_size = len(self.affected_cells) * 8  # 引用大小
        grid_info_size = len(str(self.grid_line_info)) * 2  # 字典大小估算
        
        total_size = base_size + old_positions_size + new_positions_size + cells_size + grid_info_size
        return total_size

    def _sync_grid_detection_and_view(self):
        """🆕 同步更新网格线检测和Canvas视图"""
        try:
            # 1. 清理网格线检测器缓存
            if hasattr(self.canvas, 'grid_line_detector') and self.canvas.grid_line_detector:
                self.canvas.grid_line_detector.clear_cache()
                LOGGER.debug("🔄 [GRID_SYNC] 已清理网格线检测器缓存")

            # 2. 重新检测当前网格线
            if hasattr(self.canvas, '_detect_current_grid_lines'):
                self.canvas._detect_current_grid_lines()
                LOGGER.debug("🔄 [GRID_SYNC] 已重新检测网格线")

            # 3. 刷新Canvas显示
            if hasattr(self.canvas, 'update'):
                self.canvas.update()
                LOGGER.debug("🔄 [GRID_SYNC] 已刷新Canvas显示")

        except Exception as e:
            LOGGER.error(f"[GRID_SYNC] 网格线同步更新失败: {e}")
            # 至少确保Canvas更新
            if hasattr(self.canvas, 'update'):
                self.canvas.update()


class GridLineRotateCommand(BaseCommand):
    """
    网格线旋转命令
    
    封装网格线旋转操作，支持撤销和重做。
    """
    
    def __init__(
        self, 
        canvas, 
        affected_cells: List[TableCellShape],
        old_positions: List[List[QPointF]], 
        new_positions: List[List[QPointF]],
        rotation_info: Dict[str, Any],
        description: str = None
    ):
        """
        初始化网格线旋转命令
        
        Args:
            canvas: Canvas画布对象
            affected_cells: 受影响的单元格列表
            old_positions: 每个单元格的原始位置点列表
            new_positions: 每个单元格的新位置点列表
            rotation_info: 旋转信息 {'pivot_point': QPointF, 'angle': float, 'grid_lines': [...]}
            description: 命令描述
        """
        if description is None:
            angle = rotation_info.get('angle', 0)
            grid_lines_count = len(rotation_info.get('grid_lines', []))
            if grid_lines_count == 1:
                description = f"旋转网格线 (角度: {angle:.1f}°)"
            else:
                description = f"旋转{grid_lines_count}条网格线 (角度: {angle:.1f}°)"
            
        super().__init__(description)
        
        self.canvas = canvas
        self.affected_cells = affected_cells
        self.old_positions = old_positions
        self.new_positions = new_positions
        self.rotation_info = rotation_info
        
        # 验证数据完整性
        if len(affected_cells) != len(old_positions) or len(affected_cells) != len(new_positions):
            raise ValueError("affected_cells、old_positions、new_positions的长度必须一致")
            
        LOGGER.debug(f"🔧 [GRID_ROTATE] 创建旋转命令: {description}")
        LOGGER.debug(f"   影响单元格: {len(affected_cells)}个")
        LOGGER.debug(f"   旋转信息: {rotation_info}")

    def execute(self) -> bool:
        """
        执行网格线旋转操作
        
        Returns:
            bool: 是否执行成功
        """
        try:
            LOGGER.debug(f"🎯 [GRID_ROTATE] 执行旋转: {self.description}")
            
            # 应用新位置到所有受影响的单元格
            for i, cell in enumerate(self.affected_cells):
                if i < len(self.new_positions):
                    # 深拷贝新位置避免引用问题
                    new_points = [QtCore.QPointF(p.x(), p.y()) for p in self.new_positions[i]]
                    cell.points = new_points
                    LOGGER.debug(f"   ✅ 单元格 {cell.label} 位置已更新")
            
            # 🆕 同步更新网格线检测和视图
            self._sync_grid_detection_and_view()

            LOGGER.debug(f"✅ [GRID_ROTATE] 旋转执行成功")
            return True
            
        except Exception as e:
            LOGGER.error(f"[GRID_ROTATE] 旋转执行失败: {e}")
            return False

    def undo(self) -> bool:
        """
        撤销网格线旋转操作
        
        Returns:
            bool: 是否撤销成功
        """
        try:
            LOGGER.debug(f"↩️ [GRID_ROTATE] 撤销旋转: {self.description}")
            
            # 恢复所有单元格到原始位置
            for i, cell in enumerate(self.affected_cells):
                if i < len(self.old_positions):
                    # 深拷贝原始位置避免引用问题
                    old_points = [QtCore.QPointF(p.x(), p.y()) for p in self.old_positions[i]]
                    cell.points = old_points
                    LOGGER.debug(f"   ↩️ 单元格 {cell.label} 位置已恢复")
            
            # 🆕 同步更新网格线检测和视图
            self._sync_grid_detection_and_view()

            LOGGER.debug(f"✅ [GRID_ROTATE] 旋转撤销成功")
            return True
            
        except Exception as e:
            LOGGER.error(f"[GRID_ROTATE] 旋转撤销失败: {e}")
            return False

    def get_memory_usage(self) -> int:
        """估算内存使用量"""
        base_size = super().get_memory_usage()
        
        # 计算positions的内存使用
        old_positions_size = sum(len(pos_list) * 16 for pos_list in self.old_positions)
        new_positions_size = sum(len(pos_list) * 16 for pos_list in self.new_positions)
        cells_size = len(self.affected_cells) * 8
        rotation_info_size = len(str(self.rotation_info)) * 2
        
        total_size = base_size + old_positions_size + new_positions_size + cells_size + rotation_info_size
        return total_size

    def _sync_grid_detection_and_view(self):
        """🆕 同步更新网格线检测和Canvas视图"""
        try:
            # 1. 清理网格线检测器缓存
            if hasattr(self.canvas, 'grid_line_detector') and self.canvas.grid_line_detector:
                self.canvas.grid_line_detector.clear_cache()
                LOGGER.debug("🔄 [GRID_SYNC] 已清理网格线检测器缓存")

            # 2. 重新检测当前网格线
            if hasattr(self.canvas, '_detect_current_grid_lines'):
                self.canvas._detect_current_grid_lines()
                LOGGER.debug("🔄 [GRID_SYNC] 已重新检测网格线")

            # 3. 刷新Canvas显示
            if hasattr(self.canvas, 'update'):
                self.canvas.update()
                LOGGER.debug("🔄 [GRID_SYNC] 已刷新Canvas显示")

        except Exception as e:
            LOGGER.error(f"[GRID_SYNC] 网格线同步更新失败: {e}")
            # 至少确保Canvas更新
            if hasattr(self.canvas, 'update'):
                self.canvas.update()


class GridLineEdgeDragCommand(BaseCommand):
    """
    网格线边拖拽命令
    
    封装网格线边拖拽操作，支持撤销和重做。
    这与单元格边拖拽不同，是在网格拖拽模式下的边操作。
    """
    
    def __init__(
        self, 
        canvas, 
        affected_cells: List[TableCellShape],
        old_positions: List[List[QPointF]], 
        new_positions: List[List[QPointF]],
        edge_info: Dict[str, Any],
        description: str = None
    ):
        """
        初始化网格线边拖拽命令
        
        Args:
            canvas: Canvas画布对象
            affected_cells: 受影响的单元格列表
            old_positions: 每个单元格的原始位置点列表
            new_positions: 每个单元格的新位置点列表
            edge_info: 边信息 {'direction': 'vertical'/'horizontal', 'coordinate': float, 'edge_type': 'start'/'end'}
            description: 命令描述
        """
        if description is None:
            direction = edge_info.get('direction', 'unknown')
            edge_type = edge_info.get('edge_type', 'unknown')
            coordinate = edge_info.get('coordinate', 0)
            description = f"拖拽{direction}网格线{edge_type}边 (坐标: {coordinate:.1f})"
            
        super().__init__(description)
        
        self.canvas = canvas
        self.affected_cells = affected_cells
        self.old_positions = old_positions
        self.new_positions = new_positions
        self.edge_info = edge_info
        
        # 验证数据完整性
        if len(affected_cells) != len(old_positions) or len(affected_cells) != len(new_positions):
            raise ValueError("affected_cells、old_positions、new_positions的长度必须一致")
            
        LOGGER.debug(f"🔧 [GRID_EDGE_DRAG] 创建边拖拽命令: {description}")
        LOGGER.debug(f"   影响单元格: {len(affected_cells)}个")
        LOGGER.debug(f"   边信息: {edge_info}")

    def execute(self) -> bool:
        """
        执行网格线边拖拽操作
        
        Returns:
            bool: 是否执行成功
        """
        try:
            LOGGER.debug(f"🎯 [GRID_EDGE_DRAG] 执行边拖拽: {self.description}")
            
            # 应用新位置到所有受影响的单元格
            for i, cell in enumerate(self.affected_cells):
                if i < len(self.new_positions):
                    # 深拷贝新位置避免引用问题
                    new_points = [QtCore.QPointF(p.x(), p.y()) for p in self.new_positions[i]]
                    cell.points = new_points
                    LOGGER.debug(f"   ✅ 单元格 {cell.label} 位置已更新")
            
            # 🆕 同步更新网格线检测和视图
            self._sync_grid_detection_and_view()

            LOGGER.debug(f"✅ [GRID_EDGE_DRAG] 边拖拽执行成功")
            return True
            
        except Exception as e:
            LOGGER.error(f"[GRID_EDGE_DRAG] 边拖拽执行失败: {e}")
            return False

    def undo(self) -> bool:
        """
        撤销网格线边拖拽操作
        
        Returns:
            bool: 是否撤销成功
        """
        try:
            LOGGER.debug(f"↩️ [GRID_EDGE_DRAG] 撤销边拖拽: {self.description}")
            
            # 恢复所有单元格到原始位置
            for i, cell in enumerate(self.affected_cells):
                if i < len(self.old_positions):
                    # 深拷贝原始位置避免引用问题
                    old_points = [QtCore.QPointF(p.x(), p.y()) for p in self.old_positions[i]]
                    cell.points = old_points
                    LOGGER.debug(f"   ↩️ 单元格 {cell.label} 位置已恢复")
            
            # 🆕 同步更新网格线检测和视图
            self._sync_grid_detection_and_view()

            LOGGER.debug(f"✅ [GRID_EDGE_DRAG] 边拖拽撤销成功")
            return True
            
        except Exception as e:
            LOGGER.error(f"[GRID_EDGE_DRAG] 边拖拽撤销失败: {e}")
            return False

    def get_memory_usage(self) -> int:
        """估算内存使用量"""
        base_size = super().get_memory_usage()
        
        # 计算positions的内存使用
        old_positions_size = sum(len(pos_list) * 16 for pos_list in self.old_positions)
        new_positions_size = sum(len(pos_list) * 16 for pos_list in self.new_positions)
        cells_size = len(self.affected_cells) * 8
        edge_info_size = len(str(self.edge_info)) * 2
        
        total_size = base_size + old_positions_size + new_positions_size + cells_size + edge_info_size
        return total_size

    def _sync_grid_detection_and_view(self):
        """🆕 同步更新网格线检测和Canvas视图"""
        try:
            # 1. 清理网格线检测器缓存
            if hasattr(self.canvas, 'grid_line_detector') and self.canvas.grid_line_detector:
                self.canvas.grid_line_detector.clear_cache()
                LOGGER.debug("🔄 [GRID_SYNC] 已清理网格线检测器缓存")

            # 2. 重新检测当前网格线
            if hasattr(self.canvas, '_detect_current_grid_lines'):
                self.canvas._detect_current_grid_lines()
                LOGGER.debug("🔄 [GRID_SYNC] 已重新检测网格线")

            # 3. 刷新Canvas显示
            if hasattr(self.canvas, 'update'):
                self.canvas.update()
                LOGGER.debug("🔄 [GRID_SYNC] 已刷新Canvas显示")

        except Exception as e:
            LOGGER.error(f"[GRID_SYNC] 网格线同步更新失败: {e}")
            # 至少确保Canvas更新
            if hasattr(self.canvas, 'update'):
                self.canvas.update()


class TableRotateCommand(BaseCommand):
    """
    表格整体旋转命令

    封装表格整体旋转操作，支持撤销和重做。
    """

    def __init__(
        self,
        canvas,
        affected_cells: List[TableCellShape],
        old_positions: List[List[QPointF]],
        new_positions: List[List[QPointF]],
        rotation_info: Dict[str, Any],
        description: str = None
    ):
        """
        初始化表格旋转命令

        Args:
            canvas: Canvas画布对象
            affected_cells: 受影响的单元格列表
            old_positions: 每个单元格的原始位置点列表
            new_positions: 每个单元格的新位置点列表
            rotation_info: 旋转信息 {'center': QPointF, 'angle': float, 'table_id': int}
            description: 命令描述
        """
        if description is None:
            angle = rotation_info.get('angle', 0)
            table_id = rotation_info.get('table_id', 0)
            description = f"旋转表格{table_id} (角度: {angle:.1f}°)"

        super().__init__(description)

        self.canvas = canvas
        self.affected_cells = affected_cells
        self.old_positions = copy.deepcopy(old_positions)
        self.new_positions = copy.deepcopy(new_positions)
        self.rotation_info = copy.deepcopy(rotation_info)

        LOGGER.debug(f"🎯 [TABLE_ROTATE_CMD] 创建表格旋转命令: {description}")
        LOGGER.debug(f"   受影响单元格数量: {len(affected_cells)}")
        LOGGER.debug(f"   旋转角度: {rotation_info.get('angle', 0):.1f}°")
        LOGGER.debug(f"   旋转中心: ({rotation_info.get('center', QtCore.QPointF()).x():.1f}, {rotation_info.get('center', QtCore.QPointF()).y():.1f})")

    def execute(self) -> bool:
        """
        执行表格旋转操作

        Returns:
            bool: 是否执行成功
        """
        try:
            LOGGER.debug(f"🎯 [TABLE_ROTATE_CMD] 执行表格旋转: {self.description}")

            # 应用新位置到所有受影响的单元格
            for i, cell in enumerate(self.affected_cells):
                if i < len(self.new_positions):
                    # 深拷贝新位置避免引用问题
                    new_points = [QtCore.QPointF(p.x(), p.y()) for p in self.new_positions[i]]
                    cell.points = new_points
                    LOGGER.debug(f"   ✅ 单元格 {cell.label} 位置已更新")

            # 🆕 同步更新网格线检测和视图
            self._sync_grid_detection_and_view()

            LOGGER.debug(f"✅ [TABLE_ROTATE_CMD] 表格旋转执行成功")
            return True

        except Exception as e:
            LOGGER.error(f"[TABLE_ROTATE_CMD] 表格旋转执行失败: {e}")
            return False

    def undo(self) -> bool:
        """
        撤销表格旋转操作

        Returns:
            bool: 是否撤销成功
        """
        try:
            LOGGER.debug(f"↩️ [TABLE_ROTATE_CMD] 撤销表格旋转: {self.description}")

            # 恢复所有单元格到原始位置
            for i, cell in enumerate(self.affected_cells):
                if i < len(self.old_positions):
                    # 深拷贝原始位置避免引用问题
                    old_points = [QtCore.QPointF(p.x(), p.y()) for p in self.old_positions[i]]
                    cell.points = old_points
                    LOGGER.debug(f"   ↩️ 单元格 {cell.label} 位置已恢复")

            # 🆕 同步更新网格线检测和视图
            self._sync_grid_detection_and_view()

            LOGGER.debug(f"✅ [TABLE_ROTATE_CMD] 表格旋转撤销成功")
            return True

        except Exception as e:
            LOGGER.error(f"[TABLE_ROTATE_CMD] 表格旋转撤销失败: {e}")
            return False

    def get_memory_usage(self) -> int:
        """
        获取命令的内存使用量估算

        Returns:
            int: 内存使用量（字节）
        """
        # 基础对象大小
        base_size = 200  # 基础对象和字符串

        # 计算positions的内存使用
        old_positions_size = sum(len(pos_list) * 16 for pos_list in self.old_positions)
        new_positions_size = sum(len(pos_list) * 16 for pos_list in self.new_positions)
        cells_size = len(self.affected_cells) * 8
        rotation_info_size = len(str(self.rotation_info)) * 2

        total_size = base_size + old_positions_size + new_positions_size + cells_size + rotation_info_size
        return total_size

    def _sync_grid_detection_and_view(self):
        """🆕 同步更新网格线检测和Canvas视图"""
        try:
            # 1. 清理网格线检测器缓存
            if hasattr(self.canvas, 'grid_line_detector') and self.canvas.grid_line_detector:
                self.canvas.grid_line_detector.clear_cache()
                LOGGER.debug("🔄 [GRID_SYNC] 已清理网格线检测器缓存")

            # 2. 重新检测当前网格线
            if hasattr(self.canvas, '_detect_current_grid_lines'):
                self.canvas._detect_current_grid_lines()
                LOGGER.debug("🔄 [GRID_SYNC] 已重新检测网格线")

            # 3. 刷新Canvas显示
            if hasattr(self.canvas, 'update'):
                self.canvas.update()
                LOGGER.debug("🔄 [GRID_SYNC] 已刷新Canvas显示")

        except Exception as e:
            LOGGER.error(f"[GRID_SYNC] 网格线同步更新失败: {e}")
            # 至少确保Canvas更新
            if hasattr(self.canvas, 'update'):
                self.canvas.update()
