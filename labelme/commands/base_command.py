#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/3 16:30
# <AUTHOR> <EMAIL>
# @FileName: base_command.py

"""
命令模式基础类

定义所有编辑操作命令的统一接口，支持execute和undo操作。
"""

import time
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional


class BaseCommand(ABC):
    """
    命令模式基础抽象类
    
    所有编辑操作命令都必须继承此类，实现execute和undo方法。
    遵循Command模式，封装编辑操作的执行和撤销逻辑。
    """
    
    def __init__(self, description: str = ""):
        """
        初始化基础命令
        
        Args:
            description: 命令描述，用于调试和日志
        """
        self.command_type: str = self.__class__.__name__
        self.description: str = description or f"执行{self.command_type}"
        self.created_at: float = time.time()
        self.executed: bool = False
        self.backup_data: Dict[str, Any] = {}
        
    @abstractmethod
    def execute(self) -> bool:
        """
        执行命令
        
        Returns:
            bool: 执行成功返回True，失败返回False
            
        Note:
            子类必须实现此方法，执行具体的编辑操作
        """
        pass
        
    @abstractmethod
    def undo(self) -> bool:
        """
        撤销命令
        
        Returns:
            bool: 撤销成功返回True，失败返回False
            
        Note:
            子类必须实现此方法，撤销execute的操作
        """
        pass
        
    def can_merge_with(self, other: 'BaseCommand') -> bool:
        """
        检查是否可以与另一个命令合并
        
        Args:
            other: 另一个命令对象
            
        Returns:
            bool: 可以合并返回True，默认返回False
            
        Note:
            某些连续的小操作可以合并为一个命令，减少历史记录数量
        """
        return False
        
    def get_memory_usage(self) -> int:
        """
        获取命令占用的内存估算值
        
        Returns:
            int: 内存使用量（字节）
        """
        # 简单估算：对象本身 + backup_data
        base_size = 100  # 对象基础大小估算
        backup_size = len(str(self.backup_data)) * 2  # 备份数据估算
        return base_size + backup_size
        
    def __str__(self) -> str:
        """字符串表示"""
        status = "已执行" if self.executed else "未执行"
        return f"{self.command_type}({self.description}) - {status}"
        
    def __repr__(self) -> str:
        """调试字符串表示"""
        return f"<{self.command_type} at {hex(id(self))}: {self.description}>"


class NoOpCommand(BaseCommand):
    """
    空操作命令
    
    用于测试和占位，不执行任何实际操作。
    """
    
    def __init__(self, description: str = "空操作"):
        super().__init__(description)
        
    def execute(self) -> bool:
        """执行空操作"""
        self.executed = True
        return True
        
    def undo(self) -> bool:
        """撤销空操作"""
        self.executed = False
        return True 