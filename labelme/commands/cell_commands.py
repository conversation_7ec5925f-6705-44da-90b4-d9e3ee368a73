#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/3 16:45
# <AUTHOR> <EMAIL>
# @FileName: cell_commands.py

"""
单元格编辑命令类

实现基于Command模式的单元格创建和删除命令。
"""

import copy
from typing import List, Dict, Any, Optional
from PyQt5.QtCore import QPointF
from qtpy import QtCore

from .base_command import BaseCommand
from labelme.table_shape import TableCellShape
from labelme.utils.log import get_logger

LOGGER = get_logger()


class CreateCellCommand(BaseCommand):
    """
    创建单元格命令
    
    封装单元格创建操作，支持撤销和重做。
    """
    
    def __init__(
        self, 
        canvas, 
        points: List[QPointF], 
        label: str = "",
        table_properties: Optional[Dict[str, Any]] = None
    ):
        """
        初始化创建单元格命令
        
        Args:
            canvas: Canvas画布对象
            points: 单元格的四个角点坐标
            label: 单元格标签
            table_properties: 表格属性字典
        """
        super().__init__(f"创建单元格: {label or '未命名'}")
        
        self.canvas_ref = canvas
        self.points = [QPointF(p.x(), p.y()) for p in points]  # 深拷贝点坐标
        self.label = label
        self.table_properties = table_properties or {}
        self.created_cell = None

    def execute(self) -> bool:
        """
        执行创建单元格操作

        Returns:
            bool: 是否执行成功
        """
        try:
            from ..table_shape import TableCellShape

            # 1. 获取活动的TableController
            multi_controller = self.canvas_ref.multi_table_controller
            active_controller = multi_controller.get_active_controller()

            if active_controller:
                # 2. 创建临时的Shape对象用于转换
                class TempRectShape:
                    def __init__(self, points):
                        self.points = points

                temp_rect = TempRectShape(self.points)

                # 3. 调用转换方法获取完整的TableCellShape
                self.created_cell = active_controller._convert_rect_to_table_cell(temp_rect)

                # 4. 覆盖label（如果有自定义）
                if self.label:
                    self.created_cell.label = self.label

                # 5. 覆盖table_properties（如果有自定义）
                if self.table_properties:
                    self.created_cell.table_properties.update(self.table_properties)

            # 🔧 修复：直接添加到Canvas，避免触发MultiTableController重复处理
            if hasattr(self.canvas_ref, 'shapes'):
                self.canvas_ref.shapes.append(self.created_cell)

                # 设置可见性
                if hasattr(self.canvas_ref, 'visible'):
                    self.canvas_ref.visible[self.created_cell] = True

                # 🔧 关键修复：只存储状态，不触发update()避免重复处理
                if hasattr(self.canvas_ref, 'storeShapes'):
                    self.canvas_ref.storeShapes()

                # 在同步代码前添加：
            LOGGER.info(f"🔍 检查Canvas父对象:")
            LOGGER.info(f"  - canvas有_parent: {hasattr(self.canvas_ref, '_parent')}")
            if hasattr(self.canvas_ref, '_parent'):
                LOGGER.info(
                    f"  - _parent有multi_table_controller: {hasattr(self.canvas_ref._parent, 'multi_table_controller')}")
            # 🔧 关键修复：通过Canvas直接访问MultiTableController同步
            if hasattr(self.canvas_ref, 'multi_table_controller'):
                multi_controller = self.canvas_ref.multi_table_controller
                active_controller = multi_controller.get_active_controller()
                if active_controller and self.created_cell not in active_controller.table_cells:
                    active_controller.table_cells.append(self.created_cell)
                    LOGGER.info(
                        f"🔧 CreateCellCommand: 通过MultiTableController同步table_cells (ID: {id(self.created_cell)})")
            self.backup_data['created_cell'] = self.created_cell
            LOGGER.info(f"📝 命令已执行: {self.description}")
            # 🔍 调试：检查创建后的对象引用
            LOGGER.info(f"🔍 CreateCellCommand执行后:")
            LOGGER.info(f"  - 创建的单元格ID: {id(self.created_cell)}")  # 改为 self.created_cell
            LOGGER.info(
                f"  - 是否在canvas.shapes中: {self.created_cell in self.canvas_ref.shapes}")  # 改为 self.canvas_ref

            # 检查是否在table_controller中
            if hasattr(self.canvas_ref, 'multi_table_controller') and self.canvas_ref.multi_table_controller:
                active_controller = self.canvas_ref.multi_table_controller.get_active_controller()
                if active_controller:
                    in_table_cells = self.created_cell in active_controller.table_cells
                    LOGGER.info(f"  - 是否在table_cells中: {in_table_cells}")

                    # 显示当前table_cells和canvas.shapes的对象ID
                    table_cell_ids = [id(tc) for tc in active_controller.table_cells]
                    canvas_cell_ids = [id(cs) for cs in self.canvas_ref.shapes if hasattr(cs, 'table_properties')]
                    LOGGER.info(f"  - table_cells所有对象ID: {table_cell_ids}")
                    LOGGER.info(f"  - canvas.shapes所有表格单元格ID: {canvas_cell_ids}")
                    # 🔍 新增：检查canvas.shapes总体情况
                    LOGGER.info(f"🔍 Canvas.shapes总体分析:")
                    LOGGER.info(f"  - canvas.shapes总数量: {len(self.canvas_ref.shapes)}")
                    LOGGER.info(
                        f"  - canvas.shapes中所有对象类型: {[type(s).__name__ for s in self.canvas_ref.shapes]}")
                    LOGGER.info(f"  - canvas.shapes中所有对象ID: {[id(s) for s in self.canvas_ref.shapes]}")

                    # 🔍 额外：检查哪些不是TableCellShape
                    non_table_shapes = [s for s in self.canvas_ref.shapes if not hasattr(s, 'table_properties')]
                    if non_table_shapes:
                        LOGGER.error(f"🚨 发现非表格Shape: {len(non_table_shapes)}个")
                        for i, shape in enumerate(non_table_shapes):
                            LOGGER.error(f"  [{i}] 类型: {type(shape).__name__}, ID: {id(shape)}")
            return True

        except Exception as e:
            LOGGER.error(f"CreateCellCommand execute失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def undo(self) -> bool:
        """
        撤销创建单元格操作
        
        Returns:
            bool: 是否撤销成功
        """
        try:
            if not self.created_cell:
                return False
            
            # 从Canvas中移除
            if (hasattr(self.canvas_ref, 'shapes') and 
                self.created_cell in self.canvas_ref.shapes):
                self.canvas_ref.shapes.remove(self.created_cell)
                
                # 🔧 修复：同时清理可见性字典
                if (hasattr(self.canvas_ref, 'visible') and 
                    self.created_cell in self.canvas_ref.visible):
                    del self.canvas_ref.visible[self.created_cell]
                
                # 存储状态
                if hasattr(self.canvas_ref, 'storeShapes'):
                    self.canvas_ref.storeShapes()
            
            # 🔧 关键修复：从TableController.table_cells中移除
            if hasattr(self.canvas_ref, 'multi_table_controller'):
                multi_controller = self.canvas_ref.multi_table_controller
                active_controller = multi_controller.get_active_controller()
                if active_controller and self.created_cell in active_controller.table_cells:
                    active_controller.table_cells.remove(self.created_cell)
                    LOGGER.debug(f"已从table_cells中移除单元格，剩余: {len(active_controller.table_cells)}个")
            
            # 从选中列表中移除（如果存在）
            if (hasattr(self.canvas_ref, 'selectedShapes') and 
                self.created_cell in self.canvas_ref.selectedShapes):
                self.canvas_ref.selectedShapes.remove(self.created_cell)
            
            LOGGER.debug(f"↩️ 命令已撤销: {self.description}")
            return True
            
        except Exception as e:
            LOGGER.error(f"CreateCellCommand undo失败: {e}")
            return False
    
    def get_memory_usage(self) -> int:
        """估算内存使用量"""
        base_size = super().get_memory_usage()
        points_size = len(self.points) * 16  # 每个QPointF大约16字节
        properties_size = len(str(self.table_properties)) * 2  # 粗略估算
        return base_size + points_size + properties_size


class MoveCellCommand(BaseCommand):
    """🆕 移动单元格命令"""
    
    def __init__(self, canvas, shapes: List[TableCellShape], old_positions: List[List[QPointF]], 
                 new_positions: List[List[QPointF]], description: str = "移动单元格"):
        super().__init__(description or f"移动单元格: {len(shapes)}个")
        self.canvas = canvas
        self.shapes = shapes
        self.old_positions = old_positions  # 每个shape的旧位置点列表
        self.new_positions = new_positions  # 每个shape的新位置点列表
        
    def execute(self) -> bool:
        """执行移动单元格到新位置"""
        try:
            LOGGER.info(f"🎯 开始执行移动命令: {self.description}")
            
            # 🔍 调试：检查移动前的对象引用
            LOGGER.info(f"🔍 MoveCellCommand执行前:")
            LOGGER.info(f"  - 要移动的单元格数量: {len(self.shapes)}")
            for i, cell in enumerate(self.shapes[:2]):  # 只检查前2个
                LOGGER.info(f"  - 单元格{i} ID: {id(cell)}")
                LOGGER.info(f"  - 是否在canvas.shapes中: {cell in self.canvas.shapes}")

                # 检查是否在table_controller中
                if hasattr(self.canvas, 'multi_table_controller') and self.canvas.multi_table_controller:
                    active_controller = self.canvas.multi_table_controller.get_active_controller()
                    if active_controller:
                        in_table_cells = cell in active_controller.table_cells
                        LOGGER.info(f"  - 是否在table_cells中: {in_table_cells}")
                        
                        # 检查table_cells和canvas.shapes的对象ID对比
                        table_cell_ids = [id(tc) for tc in active_controller.table_cells]
                        canvas_cell_ids = [id(cs) for cs in self.canvas.shapes if hasattr(cs, 'table_properties')]
                        LOGGER.info(f"  - table_cells对象ID前3个: {table_cell_ids[:3]}")
                        LOGGER.info(f"  - canvas.shapes中表格单元格ID前3个: {canvas_cell_ids[:3]}")
            
            # 移动每个形状到新位置
            for i, shape in enumerate(self.shapes):
                if i < len(self.new_positions):
                    shape.points = self.new_positions[i].copy()
                    
            self.canvas.update()
            LOGGER.info(f"📝 命令已执行: {self.description}")
            # 🔍 调试：检查移动后的对象引用
            LOGGER.info(f"🔍 MoveCellCommand执行后:")
            LOGGER.info(f"  - 移动的单元格数量: {len(self.shapes)}")
            for i, cell in enumerate(self.shapes[:2]):  # 只检查前2个
                LOGGER.info(f"  - 单元格{i} ID: {id(cell)}")
                LOGGER.info(f"  - 是否在canvas.shapes中: {cell in self.canvas.shapes}")

                # 检查是否在table_controller中
                if hasattr(self.canvas, 'multi_table_controller') and self.canvas.multi_table_controller:
                    active_controller = self.canvas.multi_table_controller.get_active_controller()
                    if active_controller:
                        in_table_cells = cell in active_controller.table_cells
                        LOGGER.info(f"  - 是否在table_cells中: {in_table_cells}")

            return True
            
        except Exception as e:
            LOGGER.error(f"移动单元格命令执行失败: {e}")
            return False
    
    def undo(self) -> bool:
        """撤销移动单元格到原位置"""
        try:
            for i, shape in enumerate(self.shapes):
                if i < len(self.old_positions):
                    shape.points = self.old_positions[i].copy()
            
            self.canvas.update()
            LOGGER.debug(f"↩️ 命令已撤销: {self.description}")
            return True
            
        except Exception as e:
            LOGGER.error(f"撤销移动单元格命令失败: {e}")
            return False


class DeleteCellCommand(BaseCommand):
    """
    删除单元格命令
    
    封装单元格删除操作，支持撤销和重做。
    """
    
    def __init__(self, canvas, cells_to_delete: List):
        """
        初始化删除单元格命令
        
        Args:
            canvas: Canvas画布对象
            cells_to_delete: 要删除的单元格列表
        """
        super().__init__(f"删除 {len(cells_to_delete)} 个单元格")
        
        self.canvas_ref = canvas
        self.cells_to_delete = cells_to_delete[:]  # 浅拷贝列表
        self.deleted_cells_data = []  # 存储删除的单元格完整数据
        
    def execute(self) -> bool:
        """
        执行删除单元格操作
        
        Returns:
            bool: 是否执行成功
        """
        try:
            self.deleted_cells_data = []
            
            for cell in self.cells_to_delete:
                # 备份单元格数据（深拷贝）
                cell_backup = {
                    'cell_object': cell,
                    'points': [QPointF(p.x(), p.y()) for p in cell.points] if hasattr(cell, 'points') else [],
                    'label': getattr(cell, 'label', ''),
                    'table_properties': copy.deepcopy(getattr(cell, 'table_properties', {})),
                    'shape_attributes': {
                        'line_color': getattr(cell, 'line_color', None),
                        'fill_color': getattr(cell, 'fill_color', None),
                        'flags': copy.deepcopy(getattr(cell, 'flags', {})),
                        'group_id': getattr(cell, 'group_id', None),
                        'description': getattr(cell, 'description', ''),
                    }
                }
                self.deleted_cells_data.append(cell_backup)
                
                # 从Canvas中移除
                if (hasattr(self.canvas_ref, 'shapes') and 
                    cell in self.canvas_ref.shapes):
                    self.canvas_ref.shapes.remove(cell)
                
                # 从选中列表中移除
                if (hasattr(self.canvas_ref, 'selectedShapes') and 
                    cell in self.canvas_ref.selectedShapes):
                    self.canvas_ref.selectedShapes.remove(cell)
            
            # 存储状态供后续撤销使用
            if hasattr(self.canvas_ref, 'storeShapes'):
                self.canvas_ref.storeShapes()
            
            # 刷新界面
            if hasattr(self.canvas_ref, 'update'):
                self.canvas_ref.update()
            
            self.backup_data['deleted_cells_count'] = len(self.deleted_cells_data)
            return True
            
        except Exception as e:
            LOGGER.error(f"DeleteCellCommand execute失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def undo(self) -> bool:
        """
        撤销删除单元格操作（恢复删除的单元格）
        
        Returns:
            bool: 是否撤销成功
        """
        try:
            if not self.deleted_cells_data:
                return False
            
            from ..table_shape import TableCellShape
            
            # 重新创建并添加删除的单元格
            for cell_data in self.deleted_cells_data:
                # 重新创建TableCellShape
                restored_cell = TableCellShape(
                    label=cell_data['label'],
                    flags=cell_data['shape_attributes']['flags'],
                    group_id=cell_data['shape_attributes']['group_id'],
                    description=cell_data['shape_attributes']['description']
                )
                
                # 恢复点坐标
                restored_cell.points = [QPointF(p.x(), p.y()) for p in cell_data['points']]
                
                # 恢复表格属性
                restored_cell.table_properties.update(cell_data['table_properties'])
                
                # 恢复形状属性
                if cell_data['shape_attributes']['line_color']:
                    restored_cell.line_color = cell_data['shape_attributes']['line_color']
                if cell_data['shape_attributes']['fill_color']:
                    restored_cell.fill_color = cell_data['shape_attributes']['fill_color']
                
                # 关闭形状
                restored_cell.close()
                
                # 添加回Canvas
                if hasattr(self.canvas_ref, 'shapes'):
                    self.canvas_ref.shapes.append(restored_cell)
                
                # 🔧 关键修复：添加回TableController.table_cells
                if hasattr(self.canvas_ref, 'multi_table_controller'):
                    multi_controller = self.canvas_ref.multi_table_controller
                    active_controller = multi_controller.get_active_controller()
                    if active_controller:
                        active_controller.table_cells.append(restored_cell)
                        LOGGER.debug(f"已将恢复的单元格加回table_cells，总数: {len(active_controller.table_cells)}个")
            
            # 存储状态
            if hasattr(self.canvas_ref, 'storeShapes'):
                self.canvas_ref.storeShapes()
            
            # 刷新界面
            if hasattr(self.canvas_ref, 'update'):
                self.canvas_ref.update()
            
            return True
            
        except Exception as e:
            LOGGER.error(f"DeleteCellCommand undo失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def get_memory_usage(self) -> int:
        """估算内存使用量"""
        base_size = super().get_memory_usage()
        
        # 估算备份数据大小
        backup_size = 0
        for cell_data in self.deleted_cells_data:
            # 点坐标大小
            backup_size += len(cell_data['points']) * 16
            # 属性字典大小（粗略估算）
            backup_size += len(str(cell_data['table_properties'])) * 2
            backup_size += len(str(cell_data['shape_attributes'])) * 2
            # 标签和描述
            backup_size += len(cell_data['label']) * 2
        
        return base_size + backup_size


class BatchCreateCellCommand(BaseCommand):
    """
    批量创建单元格命令
    
    用于快速表格生成功能，一次性创建多个单元格。
    """
    
    def __init__(
        self, 
        canvas, 
        rows: int, 
        cols: int, 
        center_point: QPointF,
        cell_width: float = 80,
        cell_height: float = 40,
        table_id: int = 0
    ):
        """
        初始化批量创建命令
        
        Args:
            canvas: Canvas画布对象
            rows: 行数
            cols: 列数
            center_point: 表格中心点
            cell_width: 单元格宽度
            cell_height: 单元格高度
            table_id: 表格ID
        """
        super().__init__(f"批量创建表格: {rows}x{cols}")
        
        self.canvas_ref = canvas
        self.rows = rows
        self.cols = cols
        self.center_point = QPointF(center_point.x(), center_point.y())
        self.cell_width = cell_width
        self.cell_height = cell_height  
        self.table_id = table_id
        self.created_cells = []
        
    def execute(self) -> bool:
        """
        执行批量创建操作
        
        Returns:
            bool: 是否执行成功
        """
        try:
            from ..table_shape import TableCellShape
            
            # 计算表格总尺寸
            table_width = self.cols * self.cell_width
            table_height = self.rows * self.cell_height
            
            # 计算起始位置（使表格居中于center_point）
            start_x = self.center_point.x() - table_width / 2
            start_y = self.center_point.y() - table_height / 2
            
            self.created_cells = []
            
            # 创建网格单元格
            for row in range(self.rows):
                for col in range(self.cols):
                    # 计算单元格位置
                    x1 = start_x + col * self.cell_width
                    y1 = start_y + row * self.cell_height
                    x2 = x1 + self.cell_width
                    y2 = y1 + self.cell_height
                    
                    # 创建单元格
                    label = f"table_{self.table_id}_cell_{row}_{col}"
                    cell = TableCellShape(label=label)
                    
                    # 🔧 关键修复：设置单元格的四个角点坐标
                    cell.points = [
                        QPointF(x1, y1),  # 左上角
                        QPointF(x2, y1),  # 右上角
                        QPointF(x2, y2),  # 右下角
                        QPointF(x1, y2)   # 左下角
                    ]
                    
                    # 关闭形状（形成闭合多边形）
                    cell.close()
                    
                    # 🔧 正确设置逻辑位置（使用方法而不是直接设置属性）
                    cell.set_logical_location(row, row, col, col)
                    cell.set_confirmed(True)
                    
                    # 设置表格属性
                    cell.table_properties.update({
                        'table_id': self.table_id,
                        'table_type': 1,  # 有线表格
                        'header': row == 0,  # 第一行作为表头
                        'border': {
                            'top': 1,
                            'right': 1,
                            'bottom': 1,
                            'left': 1
                        }
                    })
                    
                    # 添加到Canvas
                    if hasattr(self.canvas_ref, 'shapes'):
                        self.canvas_ref.shapes.append(cell)
                    
                    self.created_cells.append(cell)
            
            # 存储状态
            if hasattr(self.canvas_ref, 'storeShapes'):
                self.canvas_ref.storeShapes()
            
            # 刷新界面
            if hasattr(self.canvas_ref, 'update'):
                self.canvas_ref.update()
            
            self.backup_data['created_cells_count'] = len(self.created_cells)

            return True
            
        except Exception as e:
            LOGGER.error(f"BatchCreateCellCommand execute失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def undo(self) -> bool:
        """
        撤销批量创建操作
        
        Returns:
            bool: 是否撤销成功
        """
        try:
            if not self.created_cells:
                return False

            # 移除所有创建的单元格
            for cell in self.created_cells:
                if (hasattr(self.canvas_ref, 'shapes') and
                        cell in self.canvas_ref.shapes):
                    self.canvas_ref.shapes.remove(cell)

                # 🔧 关键修复：从table_cells中移除（仿照CreateCellCommand.undo的方式）
                if hasattr(self.canvas_ref, 'multi_table_controller'):
                    multi_controller = self.canvas_ref.multi_table_controller
                    active_controller = multi_controller.get_active_controller()
                    if active_controller and cell in active_controller.table_cells:
                        active_controller.table_cells.remove(cell)

                # 从选中列表中移除
                if (hasattr(self.canvas_ref, 'selectedShapes') and
                        cell in self.canvas_ref.selectedShapes):
                    self.canvas_ref.selectedShapes.remove(cell)
            
            # 存储状态
            if hasattr(self.canvas_ref, 'storeShapes'):
                self.canvas_ref.storeShapes()
            
            # 刷新界面
            if hasattr(self.canvas_ref, 'update'):
                self.canvas_ref.update()
            
            return True
            
        except Exception as e:
            LOGGER.error(f"BatchCreateCellCommand undo失败: {e}")
            return False
    
    def get_memory_usage(self) -> int:
        """估算内存使用量"""
        base_size = super().get_memory_usage()
        # 每个单元格大约占用内存：4个点坐标(64字节) + 属性(约200字节) = 约264字节
        cells_memory = len(self.created_cells) * 264
        return base_size + cells_memory


class ResizeEdgeCommand(BaseCommand):
    """🆕 边拖动命令 - 支持撤销/重做的边调整操作"""

    def __init__(self, canvas, shapes: List[TableCellShape], edge_name: str,
                 old_positions: List[List[QPointF]], new_positions: List[List[QPointF]],
                 description: str = None):
        """
        初始化边拖动命令

        Args:
            canvas: Canvas引用
            shapes: 受影响的单元格列表
            edge_name: 边名称 ('top', 'bottom', 'left', 'right')
            old_positions: 每个shape的原始位置点列表
            new_positions: 每个shape的新位置点列表
            description: 命令描述
        """
        if description is None:
            if len(shapes) == 1:
                description = f"调整{edge_name}边"
            else:
                description = f"批量调整{edge_name}边: {len(shapes)}个单元格"

        super().__init__(description)
        self.canvas = canvas
        self.shapes = shapes
        self.edge_name = edge_name
        self.old_positions = old_positions  # 每个shape的旧位置点列表
        self.new_positions = new_positions  # 每个shape的新位置点列表

        # 验证数据完整性
        if len(shapes) != len(old_positions) or len(shapes) != len(new_positions):
            raise ValueError("shapes、old_positions、new_positions的长度必须一致")

        LOGGER.debug(f"🔧 [RESIZE_CMD] 创建边拖动命令: {description}")
        LOGGER.debug(f"   影响单元格: {[s.label for s in shapes]}")
        LOGGER.debug(f"   边名称: {edge_name}")

    def execute(self) -> bool:

            LOGGER.debug(f"🎯 [RESIZE_CMD] 执行边拖动: {self.description}")


            # 应用新位置到所有受影响的单元格
            for i, shape in enumerate(self.shapes):
                if i < len(self.new_positions):
                    # 深拷贝新位置避免引用问题
                    new_points = [QtCore.QPointF(p.x(), p.y()) for p in self.new_positions[i]]
                    shape.points = new_points
                    LOGGER.debug(f"   ✅ 单元格 {shape.label} 位置已更新")

            # 刷新Canvas显示
            if hasattr(self.canvas, 'update'):
                self.canvas.update()

            LOGGER.debug(f"✅ [RESIZE_CMD] 边拖动执行成功")
            return True

    def undo(self) -> bool:
        """
        撤销边拖动操作（恢复到原始位置）

        Returns:
            bool: 是否撤销成功
        """
        try:
            LOGGER.debug(f"↩️ [RESIZE_CMD] 撤销边拖动: {self.description}")

            # 恢复所有单元格到原始位置
            for i, shape in enumerate(self.shapes):
                if i < len(self.old_positions):
                    # 深拷贝原始位置避免引用问题
                    old_points = [QtCore.QPointF(p.x(), p.y()) for p in self.old_positions[i]]
                    shape.points = old_points
                    LOGGER.debug(f"   ↩️ 单元格 {shape.label} 位置已恢复")

            # 刷新Canvas显示
            if hasattr(self.canvas, 'update'):
                self.canvas.update()

            LOGGER.debug(f"✅ [RESIZE_CMD] 边拖动撤销成功")
            return True

        except Exception as e:
            LOGGER.error(f"[RESIZE_CMD] 边拖动撤销失败: {e}")
            return False

    def get_memory_usage(self) -> int:
        """估算内存使用量"""
        base_size = super().get_memory_usage()

        # 计算positions的内存使用
        old_positions_size = sum(len(pos_list) * 16 for pos_list in self.old_positions)  # 每个QPointF约16字节
        new_positions_size = sum(len(pos_list) * 16 for pos_list in self.new_positions)
        shapes_size = len(self.shapes) * 8  # 引用大小
        edge_name_size = len(self.edge_name) * 2  # 字符串大小估算

        total_size = base_size + old_positions_size + new_positions_size + shapes_size + edge_name_size

        return total_size

    def get_command_data(self) -> dict:
        """获取命令的详细数据"""
        return {
            'command_type': 'ResizeEdge',
            'edge_name': self.edge_name,
            'shapes_count': len(self.shapes),
            'shapes_labels': [s.label for s in self.shapes],
            'memory_usage': self.get_memory_usage()
        }


class ResizeVertexCommand(BaseCommand):
    """🆕 顶点拖动命令 - 支持撤销/重做的角点调整操作"""

    def __init__(self, canvas, shapes: List[TableCellShape], vertex_index: int,
                 old_positions: List[List[QPointF]], new_positions: List[List[QPointF]],
                 is_rectangular: bool = True, description: str = None):
        """
        初始化顶点拖动命令

        Args:
            canvas: Canvas引用
            shapes: 受影响的单元格列表
            vertex_index: 顶点索引 (0-3，对应四个角)
            old_positions: 每个shape的原始位置点列表
            new_positions: 每个shape的新位置点列表
            is_rectangular: 是否矩形约束模式（True=矩形约束，False=自由拖拽）
            description: 命令描述
        """
        if description is None:
            mode_text = "矩形约束" if is_rectangular else "自由拖拽"
            vertex_names = ["左上角", "右上角", "右下角", "左下角"]
            vertex_name = vertex_names[vertex_index] if 0 <= vertex_index < 4 else f"顶点{vertex_index}"
            
            if len(shapes) == 1:
                description = f"调整{vertex_name}({mode_text})"
            else:
                description = f"批量调整{vertex_name}({mode_text}): {len(shapes)}个单元格"

        super().__init__(description)
        self.canvas = canvas
        self.shapes = shapes
        self.vertex_index = vertex_index
        self.old_positions = old_positions  # 每个shape的旧位置点列表
        self.new_positions = new_positions  # 每个shape的新位置点列表
        self.is_rectangular = is_rectangular

        # 验证数据完整性
        if len(shapes) != len(old_positions) or len(shapes) != len(new_positions):
            raise ValueError("shapes、old_positions、new_positions的长度必须一致")

        LOGGER.debug(f"🔧 [VERTEX_CMD] 创建顶点拖动命令: {description}")
        LOGGER.debug(f"   影响单元格: {[s.label for s in shapes]}")
        LOGGER.debug(f"   顶点索引: {vertex_index}, 矩形约束: {is_rectangular}")

    def execute(self) -> bool:
        """
        执行顶点拖动操作

        Returns:
            bool: 是否执行成功
        """
        try:
            LOGGER.debug(f"🎯 [VERTEX_CMD] 执行顶点拖动: {self.description}")

            # 应用新位置到所有受影响的单元格
            for i, shape in enumerate(self.shapes):
                if i < len(self.new_positions):
                    # 深拷贝新位置避免引用问题
                    new_points = [QtCore.QPointF(p.x(), p.y()) for p in self.new_positions[i]]
                    shape.points = new_points
                    LOGGER.debug(f"   ✅ 单元格 {shape.label} 顶点位置已更新")

            # 刷新Canvas显示
            if hasattr(self.canvas, 'update'):
                self.canvas.update()

            LOGGER.debug(f"✅ [VERTEX_CMD] 顶点拖动执行成功")
            return True

        except Exception as e:
            LOGGER.error(f"[VERTEX_CMD] 顶点拖动执行失败: {e}")
            return False

    def undo(self) -> bool:
        """
        撤销顶点拖动操作（恢复到原始位置）

        Returns:
            bool: 是否撤销成功
        """
        try:
            LOGGER.debug(f"↩️ [VERTEX_CMD] 撤销顶点拖动: {self.description}")

            # 恢复所有单元格到原始位置
            for i, shape in enumerate(self.shapes):
                if i < len(self.old_positions):
                    # 深拷贝原始位置避免引用问题
                    old_points = [QtCore.QPointF(p.x(), p.y()) for p in self.old_positions[i]]
                    shape.points = old_points
                    LOGGER.debug(f"   ↩️ 单元格 {shape.label} 顶点位置已恢复")

            # 刷新Canvas显示
            if hasattr(self.canvas, 'update'):
                self.canvas.update()

            LOGGER.debug(f"✅ [VERTEX_CMD] 顶点拖动撤销成功")
            return True

        except Exception as e:
            LOGGER.error(f"[VERTEX_CMD] 顶点拖动撤销失败: {e}")
            return False

    def get_memory_usage(self) -> int:
        """估算内存使用量"""
        base_size = super().get_memory_usage()

        # 计算positions的内存使用
        old_positions_size = sum(len(pos_list) * 16 for pos_list in self.old_positions)  # 每个QPointF约16字节
        new_positions_size = sum(len(pos_list) * 16 for pos_list in self.new_positions)
        shapes_size = len(self.shapes) * 8  # 引用大小
        vertex_index_size = 4  # int大小
        is_rectangular_size = 1  # bool大小

        total_size = base_size + old_positions_size + new_positions_size + shapes_size + vertex_index_size + is_rectangular_size

        return total_size

    def get_command_data(self) -> dict:
        """获取命令的详细数据"""
        return {
            'command_type': 'ResizeVertex',
            'vertex_index': self.vertex_index,
            'is_rectangular': self.is_rectangular,
            'shapes_count': len(self.shapes),
            'shapes_labels': [s.label for s in self.shapes],
            'memory_usage': self.get_memory_usage()
        }


class ChangeCellTextCommand(BaseCommand):
    """单元格文本变更命令"""
    
    def __init__(self, canvas, cell_shape, old_text, new_text):
        super().__init__(f"修改单元格文本")
        self.canvas = canvas
        self.cell_shape = cell_shape
        self.old_text = old_text
        self.new_text = new_text
        
    def execute(self):
        """执行文本变更"""
        try:
            self.cell_shape.set_cell_text(self.new_text)
            self.canvas.update()
            
            LOGGER.debug(f"文本变更命令已执行: '{self.old_text}' -> '{self.new_text}'")
            return True
            
        except Exception as e:
            LOGGER.error(f"ChangeCellTextCommand execute失败: {e}")
            return False
    
    def undo(self):
        """撤销文本变更"""
        try:
            self.cell_shape.set_cell_text(self.old_text)
            self.canvas.update()
            
            LOGGER.debug(f"文本变更命令已撤销: '{self.new_text}' -> '{self.old_text}'")
            return True
            
        except Exception as e:
            LOGGER.error(f"ChangeCellTextCommand undo失败: {e}")
            return False

class MergeCellCommand(BaseCommand):
    """🆕 合并单元格命令"""

    def __init__(self, canvas, table_controller, cells_to_merge: List, description: str = None):
        if description is None:
            description = f"合并单元格: {len(cells_to_merge)}个"

        super().__init__(description)

        self.canvas = canvas
        self.table_controller = table_controller
        self.cells_to_merge = cells_to_merge[:]  # 深拷贝原始单元格列表
        self.backup_cells = []  # 保存原始单元格的完整备份
        self.merged_cell = None  # 保存创建的合并单元格

        # 验证输入
        if len(cells_to_merge) < 2:
            raise ValueError("合并至少需要2个单元格")

        print(f"🔧 [MERGE_CMD] 创建合并命令: {description}")
        print(f"   待合并单元格: {[c.label for c in cells_to_merge]}")

    def execute(self) -> bool:
        """执行合并操作"""
        try:
            print(f"🎯 [MERGE_CMD] 执行合并: {self.description}")

            # 1. 深拷贝备份原始单元格（确保可以完全恢复）
            from copy import deepcopy
            self.backup_cells = []
            for cell in self.cells_to_merge:
                backup_cell = deepcopy(cell)
                self.backup_cells.append(backup_cell)

            # 2. 验证能否合并
            from labelme.utils.table_merge_split import TableMergeEngine
            if not TableMergeEngine.are_cells_adjacent(self.cells_to_merge):
                print("❌ [MERGE_CMD] 选中的单元格不相邻，无法合并")
                return False

            # 3. 计算合并参数
            merged_bbox = TableMergeEngine.calculate_merge_bbox(self.cells_to_merge)
            merged_logical = TableMergeEngine.calculate_merge_logical_position(self.cells_to_merge)

            # 4. 创建合并单元格
            self.merged_cell = TableMergeEngine.create_merged_cell_data(
                self.cells_to_merge, merged_bbox, merged_logical
            )

            # 5. 从canvas和table_controller中移除原始单元格
            for cell in self.cells_to_merge:
                if cell in self.table_controller.table_cells:
                    self.table_controller.table_cells.remove(cell)
                if cell in self.canvas.shapes:
                    self.canvas.shapes.remove(cell)
                if cell in self.canvas.visible:
                    del self.canvas.visible[cell]

            self.merged_cell.table_properties['is_merged'] = True

            # 6. 添加合并后的单元格
            self.table_controller.table_cells.append(self.merged_cell)
            self.canvas.shapes.append(self.merged_cell)
            self.canvas.visible[self.merged_cell] = True

            # 7. 清除选择并刷新
            self.canvas.selectedShapes = []
           # self.canvas.update()
            self.canvas.repaint()
            print(f"✅ [MERGE_CMD] 合并执行成功")
            return True

        except Exception as e:
            print(f"❌ [MERGE_CMD] 合并执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def undo(self) -> bool:
        """撤销合并操作（拆分回原始单元格）"""
        try:
            print(f"↩️ [MERGE_CMD] 撤销合并: {self.description}")

            # 1. 移除合并后的单元格
            if self.merged_cell:
                # 🔧 查找实际存在的合并单元格（处理deepcopy导致的对象不匹配）
                actual_merged_cell = None
                for cell in list(self.table_controller.table_cells):  # 使用list()避免迭代时修改
                    if (hasattr(cell, 'label') and hasattr(self.merged_cell, 'label') and
                            cell.label == self.merged_cell.label):
                        actual_merged_cell = cell
                        break

                if actual_merged_cell:
                    self.table_controller.table_cells.remove(actual_merged_cell)
                    if actual_merged_cell in self.canvas.shapes:
                        self.canvas.shapes.remove(actual_merged_cell)
                    if actual_merged_cell in self.canvas.visible:
                        del self.canvas.visible[actual_merged_cell]

            # 2. 恢复原始单元格
            for backup_cell in self.backup_cells:
                from copy import deepcopy
                restored_cell = deepcopy(backup_cell)

                self.table_controller.table_cells.append(restored_cell)
                self.canvas.shapes.append(restored_cell)
                self.canvas.visible[restored_cell] = True

                # 🔧 修复：确保恢复的单元格不处于选中状态
                if hasattr(restored_cell, 'selected'):
                    restored_cell.selected = False

            # 3. 清除选择并刷新
            self.canvas.selectedShapes = []
            self.canvas.update()

            print(f"✅ [MERGE_CMD] 合并撤销成功")
            return True

        except Exception as e:
            print(f"❌ [MERGE_CMD] 合并撤销失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def get_memory_usage(self) -> int:
        """估算内存使用量"""
        base_size = super().get_memory_usage()

        # 计算备份单元格的内存使用
        backup_cells_size = len(self.backup_cells) * 500  # 每个单元格约500字节
        cells_to_merge_size = len(self.cells_to_merge) * 8  # 引用大小
        merged_cell_size = 500 if self.merged_cell else 0

        total_size = base_size + backup_cells_size + cells_to_merge_size + merged_cell_size
        return total_size

    def get_command_data(self) -> dict:
        """获取命令的详细数据"""
        return {
            'command_type': 'MergeCell',
            'cells_count': len(self.cells_to_merge),
            'cells_labels': [c.label for c in self.cells_to_merge],
            'merged_label': self.merged_cell.label if self.merged_cell else None,
            'memory_usage': self.get_memory_usage()
        }


class SplitCellCommand(BaseCommand):
    """🆕 拆分单元格命令"""

    def __init__(self, canvas, table_controller, merged_cell, description: str = None):
        if description is None:
            description = f"拆分单元格: {merged_cell.label}"

        super().__init__(description)

        self.canvas = canvas
        self.table_controller = table_controller
        self.merged_cell = merged_cell
        self.split_cells = []
        self.merged_cell_backup = None

        # 验证输入
        from labelme.utils.table_merge_split import TableSplitEngine
        if not TableSplitEngine.can_split_cell(merged_cell):
            raise ValueError("该单元格不是合并单元格，无法拆分")

        LOGGER.debug(f"🔧 [SPLIT_CMD] 创建拆分命令: {description}")
        LOGGER.debug(f"   合并单元格: {merged_cell.label}")

    def execute(self) -> bool:
        """执行拆分操作"""
        try:
            LOGGER.debug(f"🎯 [SPLIT_CMD] 执行拆分: {self.description}")

            # 1. 深拷贝备份合并单元格
            from copy import deepcopy
            self.merged_cell_backup = deepcopy(self.merged_cell)

            # 2. 使用TableSplitEngine创建拆分后的单元格
            from labelme.utils.table_merge_split import TableSplitEngine
            self.split_cells = TableSplitEngine.create_split_cells(self.merged_cell)

            # 3. 从canvas和table_controller中移除合并单元格
            if self.merged_cell in self.table_controller.table_cells:
                self.table_controller.table_cells.remove(self.merged_cell)
            if self.merged_cell in self.canvas.shapes:
                self.canvas.shapes.remove(self.merged_cell)
            if self.merged_cell in self.canvas.visible:
                del self.canvas.visible[self.merged_cell]

            # 4. 添加拆分后的单元格
            for split_cell in self.split_cells:
                self.table_controller.table_cells.append(split_cell)
                self.canvas.shapes.append(split_cell)
                self.canvas.visible[split_cell] = True

            # 5. 清除选择并刷新
            self.canvas.selectedShapes = []
            self.canvas.update()

            LOGGER.debug(f"✅ [SPLIT_CMD] 拆分执行成功，创建了 {len(self.split_cells)} 个单元格")
            return True

        except Exception as e:
            LOGGER.error(f"❌ [SPLIT_CMD] 拆分执行失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def undo(self) -> bool:
        """撤销拆分操作（重新合并为原单元格）"""
        try:
            LOGGER.debug(f"↩️ [SPLIT_CMD] 撤销拆分: {self.description}")

            # 1. 移除拆分后的单元格
            for split_cell in self.split_cells:
                if split_cell in self.table_controller.table_cells:
                    self.table_controller.table_cells.remove(split_cell)
                if split_cell in self.canvas.shapes:
                    self.canvas.shapes.remove(split_cell)
                if split_cell in self.canvas.visible:
                    del self.canvas.visible[split_cell]

            # 2. 恢复原合并单元格
            if self.merged_cell_backup:
                from copy import deepcopy
                restored_cell = deepcopy(self.merged_cell_backup)
                self.table_controller.table_cells.append(restored_cell)
                self.canvas.shapes.append(restored_cell)
                self.canvas.visible[restored_cell] = True

                # 🔧 修复：确保恢复的合并单元格不处于选中状态
                if hasattr(restored_cell, 'selected'):
                    restored_cell.selected = False

            # 3. 清除选择并刷新
            self.canvas.selectedShapes = []
            self.canvas.update()

            LOGGER.debug(f"✅ [SPLIT_CMD] 拆分撤销成功")
            return True

        except Exception as e:
            LOGGER.error(f"❌ [SPLIT_CMD] 拆分撤销失败: {e}")
            return False

class SplitCellByRowCommand(BaseCommand):
    """🆕 按行拆分单元格命令 - 在单元格中间添加水平分割线"""

    def __init__(self, canvas, table_controller, cell, split_position_ratio=0.5, description: str = None):
        if description is None:
            description = f"按行拆分单元格: {cell.label}"

        super().__init__(description)

        self.canvas = canvas
        self.table_controller = table_controller
        self.cell = cell
        self.split_position_ratio = split_position_ratio  # 分割位置比例 (0.5 = 中间)
        self.original_cell_backup = None
        self.new_cells = []

        LOGGER.debug(f"🔧 [SPLIT_ROW_CMD] 创建按行拆分命令: {description}")
        LOGGER.debug(f"   分割比例: {split_position_ratio}")

    def execute(self) -> bool:
        """执行按行拆分操作"""
        try:
            LOGGER.debug(f"🎯 [SPLIT_ROW_CMD] 执行按行拆分: {self.description}")

            # 1. 备份原始单元格
            from copy import deepcopy
            self.original_cell_backup = deepcopy(self.cell)

            # 2. 创建按行拆分后的单元格
            self.new_cells = self._create_row_split_cells()

            # 3. 移除原始单元格
            self._remove_cell_from_canvas_and_controller(self.cell)

            # 4. 添加新单元格
            for new_cell in self.new_cells:
                self.table_controller.table_cells.append(new_cell)
                self.canvas.shapes.append(new_cell)
                self.canvas.visible[new_cell] = True

            # 5. 清理状态并刷新
            self.canvas.selectedShapes = []
            self.canvas.selectedShapesCopy = []
            self.canvas.repaint()

            LOGGER.debug(f"✅ [SPLIT_ROW_CMD] 按行拆分成功，创建了 {len(self.new_cells)} 个单元格")
            return True

        except Exception as e:
            LOGGER.error(f"❌ [SPLIT_ROW_CMD] 按行拆分失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def undo(self) -> bool:
        """撤销按行拆分操作"""
        try:
            LOGGER.debug(f"↩️ [SPLIT_ROW_CMD] 撤销按行拆分: {self.description}")

            # 1. 移除新创建的单元格
            for new_cell in self.new_cells:
                self._remove_cell_from_canvas_and_controller(new_cell)

            # 2. 恢复原始单元格
            if self.original_cell_backup:
                from copy import deepcopy
                restored_cell = deepcopy(self.original_cell_backup)
                self.table_controller.table_cells.append(restored_cell)
                self.canvas.shapes.append(restored_cell)
                self.canvas.visible[restored_cell] = True
            # 🔧 添加：确保恢复的单元格不处于选中状态
                if hasattr(restored_cell, 'selected'):
                    restored_cell.selected = False
            # 3. 清理状态并刷新
            self.canvas.selectedShapes = []
            self.canvas.selectedShapesCopy = []
            self.canvas.repaint()

            LOGGER.debug(f"✅ [SPLIT_ROW_CMD] 按行拆分撤销成功")
            return True

        except Exception as e:
            LOGGER.error(f"❌ [SPLIT_ROW_CMD] 按行拆分撤销失败: {e}")
            return False

    def _create_row_split_cells(self):
        """创建按行拆分后的单元格 - 简化版物理拆分"""
        from qtpy.QtCore import QPointF
        from labelme.table_shape import TableCellShape

        # 获取原单元格的边界
        bbox = self.cell.boundingRect()

        # 计算分割位置（纯物理）
        split_y = bbox.y() + bbox.height() * self.split_position_ratio

        # 获取逻辑位置信息
        logical_pos = self.cell.get_logical_location()
        start_row = logical_pos["start_row"]
        end_row = logical_pos["end_row"]
        start_col = logical_pos["start_col"]
        end_col = logical_pos["end_col"]

        new_cells = []

        # 🔧 修复：总是创建上半部分单元格
        upper_points = [
            QPointF(bbox.x(), bbox.y()),
            QPointF(bbox.x() + bbox.width(), bbox.y()),
            QPointF(bbox.x() + bbox.width(), split_y),
            QPointF(bbox.x(), split_y)
        ]

        upper_cell = self._create_split_cell(
            f"Cell_R{start_row}C{start_col}_Upper",
            upper_points,
            start_row, start_row, start_col, end_col  # 上半部分保持原逻辑行
        )
        new_cells.append(upper_cell)

        # 🔧 修复：总是创建下半部分单元格
        lower_points = [
            QPointF(bbox.x(), split_y),
            QPointF(bbox.x() + bbox.width(), split_y),
            QPointF(bbox.x() + bbox.width(), bbox.y() + bbox.height()),
            QPointF(bbox.x(), bbox.y() + bbox.height())
        ]

        # 下半部分使用下一个逻辑行（如果原来是单行，则分配给下一行）
        lower_row = end_row + 1 if start_row == end_row else end_row

        lower_cell = self._create_split_cell(
            f"Cell_R{lower_row}C{start_col}_Lower",
            lower_points,
            lower_row, lower_row, start_col, end_col
        )
        new_cells.append(lower_cell)

        return new_cells
    def _create_split_cell(self, label, points, start_row, end_row, start_col, end_col):
        """创建分割后的单元格"""
        from labelme.table_shape import TableCellShape
        
        # 创建新单元格，继承原单元格属性
        split_cell = TableCellShape(
            label=label,
            line_color=self.cell.line_color,
            flags=self.cell.flags,
            group_id=self.cell.group_id,
            description=self.cell.description
        )
        split_cell.points = points
        
        # 继承视觉属性
        split_cell.fill = self.cell.fill
        split_cell.selected = False
        
        # 继承表格属性
        split_cell.set_table_type(self.cell.get_table_type())
        split_cell.set_border_style(**self.cell.get_border_style())
        
        # 设置逻辑位置
        split_cell.set_logical_location(
            start_row=start_row,
            end_row=end_row,
            start_col=start_col,
            end_col=end_col
        )
        
        # 设置单元格ID和文本
        split_cell.table_properties['cell_id'] = f"cell_{start_row}_{start_col}"
        split_cell.set_cell_text("")  # 清空文本内容
        
        return split_cell

    def _remove_cell_from_canvas_and_controller(self, cell):
        """从canvas和controller中移除单元格"""
        if cell in self.table_controller.table_cells:
            self.table_controller.table_cells.remove(cell)
        if cell in self.canvas.shapes:
            self.canvas.shapes.remove(cell)
        if cell in self.canvas.visible:
            del self.canvas.visible[cell]
        # 清理选择状态
        if cell in self.canvas.selectedShapes:
            self.canvas.selectedShapes.remove(cell)
        if cell in self.canvas.selectedShapesCopy:
            self.canvas.selectedShapesCopy.remove(cell)


class SplitCellByColumnCommand(BaseCommand):
    """🆕 按列拆分单元格命令 - 在单元格中间添加垂直分割线"""

    def __init__(self, canvas, table_controller, cell, split_position_ratio=0.5, description: str = None):
        if description is None:
            description = f"按列拆分单元格: {cell.label}"

        super().__init__(description)

        self.canvas = canvas
        self.table_controller = table_controller
        self.cell = cell
        self.split_position_ratio = split_position_ratio
        self.original_cell_backup = None
        self.new_cells = []

        LOGGER.debug(f"🔧 [SPLIT_COL_CMD] 创建按列拆分命令: {description}")

    def execute(self) -> bool:
        """执行按列拆分操作"""
        try:
            LOGGER.debug(f"🎯 [SPLIT_COL_CMD] 执行按列拆分: {self.description}")

            # 1. 备份原始单元格
            from copy import deepcopy
            self.original_cell_backup = deepcopy(self.cell)

            # 2. 创建按列拆分后的单元格
            self.new_cells = self._create_column_split_cells()

            # 3. 移除原始单元格
            self._remove_cell_from_canvas_and_controller(self.cell)

            # 4. 添加新单元格
            for new_cell in self.new_cells:
                self.table_controller.table_cells.append(new_cell)
                self.canvas.shapes.append(new_cell)
                self.canvas.visible[new_cell] = True

            # 5. 清理状态并刷新
            self.canvas.selectedShapes = []
            self.canvas.selectedShapesCopy = []
            self.canvas.repaint()

            LOGGER.debug(f"✅ [SPLIT_COL_CMD] 按列拆分成功，创建了 {len(self.new_cells)} 个单元格")
            return True

        except Exception as e:
            LOGGER.error(f"❌ [SPLIT_COL_CMD] 按列拆分失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def undo(self) -> bool:
        """撤销按列拆分操作"""
        try:
            LOGGER.debug(f"↩️ [SPLIT_COL_CMD] 撤销按列拆分: {self.description}")

            # 1. 移除新创建的单元格
            for new_cell in self.new_cells:
                self._remove_cell_from_canvas_and_controller(new_cell)

            # 2. 恢复原始单元格
            if self.original_cell_backup:
                from copy import deepcopy
                restored_cell = deepcopy(self.original_cell_backup)
                self.table_controller.table_cells.append(restored_cell)
                self.canvas.shapes.append(restored_cell)
                self.canvas.visible[restored_cell] = True
            # 🔧 添加：确保恢复的单元格不处于选中状态
                if hasattr(restored_cell, 'selected'):
                    restored_cell.selected = False
            # 3. 清理状态并刷新
            self.canvas.selectedShapes = []
            self.canvas.selectedShapesCopy = []
            self.canvas.repaint()

            LOGGER.debug(f"✅ [SPLIT_COL_CMD] 按列拆分撤销成功")
            return True

        except Exception as e:
            LOGGER.error(f"❌ [SPLIT_COL_CMD] 按列拆分撤销失败: {e}")
            return False

    def _create_column_split_cells(self):
        """创建按列拆分后的单元格 - 简化版物理拆分"""
        from qtpy.QtCore import QPointF
        from labelme.table_shape import TableCellShape

        # 获取原单元格的边界
        bbox = self.cell.boundingRect()

        # 计算分割位置（纯物理）
        split_x = bbox.x() + bbox.width() * self.split_position_ratio

        # 获取逻辑位置信息
        logical_pos = self.cell.get_logical_location()
        start_row = logical_pos["start_row"]
        end_row = logical_pos["end_row"]
        start_col = logical_pos["start_col"]
        end_col = logical_pos["end_col"]

        new_cells = []

        # 🔧 修复：总是创建左半部分单元格
        left_points = [
            QPointF(bbox.x(), bbox.y()),
            QPointF(split_x, bbox.y()),
            QPointF(split_x, bbox.y() + bbox.height()),
            QPointF(bbox.x(), bbox.y() + bbox.height())
        ]

        left_cell = self._create_split_cell(
            f"Cell_R{start_row}C{start_col}_Left",
            left_points,
            start_row, end_row, start_col, start_col  # 左半部分保持原逻辑列
        )
        new_cells.append(left_cell)

        # 🔧 修复：总是创建右半部分单元格
        right_points = [
            QPointF(split_x, bbox.y()),
            QPointF(bbox.x() + bbox.width(), bbox.y()),
            QPointF(bbox.x() + bbox.width(), bbox.y() + bbox.height()),
            QPointF(split_x, bbox.y() + bbox.height())
        ]

        # 右半部分使用下一个逻辑列（如果原来是单列，则分配给下一列）
        right_col = end_col + 1 if start_col == end_col else end_col

        right_cell = self._create_split_cell(
            f"Cell_R{start_row}C{right_col}_Right",
            right_points,
            start_row, end_row, right_col, right_col
        )
        new_cells.append(right_cell)

        return new_cells
    def _create_split_cell(self, label, points, start_row, end_row, start_col, end_col):
        """创建分割后的单元格"""
        from labelme.table_shape import TableCellShape
        
        # 创建新单元格，继承原单元格属性
        split_cell = TableCellShape(
            label=label,
            line_color=self.cell.line_color,
            flags=self.cell.flags,
            group_id=self.cell.group_id,
            description=self.cell.description
        )
        split_cell.points = points
        
        # 继承视觉属性
        split_cell.fill = self.cell.fill
        split_cell.selected = False
        
        # 继承表格属性
        split_cell.set_table_type(self.cell.get_table_type())
        split_cell.set_border_style(**self.cell.get_border_style())
        
        # 设置逻辑位置
        split_cell.set_logical_location(
            start_row=start_row,
            end_row=end_row,
            start_col=start_col,
            end_col=end_col
        )
        
        # 设置单元格ID和文本
        split_cell.table_properties['cell_id'] = f"cell_{start_row}_{start_col}"
        split_cell.set_cell_text("")  # 清空文本内容
        
        return split_cell

    def _remove_cell_from_canvas_and_controller(self, cell):
        """从canvas和controller中移除单元格"""
        if cell in self.table_controller.table_cells:
            self.table_controller.table_cells.remove(cell)
        if cell in self.canvas.shapes:
            self.canvas.shapes.remove(cell)
        if cell in self.canvas.visible:
            del self.canvas.visible[cell]
        # 清理选择状态
        if cell in self.canvas.selectedShapes:
            self.canvas.selectedShapes.remove(cell)
        if cell in self.canvas.selectedShapesCopy:
            self.canvas.selectedShapesCopy.remove(cell)


class BatchSplitCellCommand(BaseCommand):
    """🆕 批量拆分单元格命令"""

    def __init__(self, canvas, table_controller, cells, split_type="row", split_position_ratio=0.5, description: str = None):
        if description is None:
            split_type_name = "行" if split_type == "row" else "列"
            description = f"批量按{split_type_name}拆分 {len(cells)} 个单元格"

        super().__init__(description)

        self.canvas = canvas
        self.table_controller = table_controller
        self.cells = cells
        self.split_type = split_type  # "row" 或 "column"
        self.split_position_ratio = split_position_ratio
        self.split_commands = []  # 存储各个单独的拆分命令

        LOGGER.debug(f"🔧 [BATCH_SPLIT_CMD] 创建批量拆分命令: {description}")
        LOGGER.debug(f"   拆分类型: {split_type}, 拆分比例: {split_position_ratio}")

    def execute(self) -> bool:
        """执行批量拆分操作"""
        try:
            LOGGER.debug(f"🎯 [BATCH_SPLIT_CMD] 执行批量拆分: {self.description}")

            self.split_commands = []
            success_count = 0

            for cell in self.cells:
                try:
                    # 为每个单元格创建相应的拆分命令
                    if self.split_type == "row":
                        split_cmd = SplitCellByRowCommand(
                            self.canvas, 
                            self.table_controller, 
                            cell, 
                            self.split_position_ratio
                        )
                    else:
                        split_cmd = SplitCellByColumnCommand(
                            self.canvas, 
                            self.table_controller, 
                            cell, 
                            self.split_position_ratio
                        )

                    # 执行拆分命令
                    if split_cmd.execute():
                        self.split_commands.append(split_cmd)
                        success_count += 1
                    else:
                        LOGGER.warning(f"⚠️ [BATCH_SPLIT_CMD] 单元格 {cell.label} 拆分失败")

                except Exception as e:
                    LOGGER.error(f"❌ [BATCH_SPLIT_CMD] 处理单元格 {cell.label} 时出错: {e}")

            # 最终刷新
            self.canvas.selectedShapes = []
            self.canvas.selectedShapesCopy = []
            self.canvas.repaint()

            LOGGER.debug(f"✅ [BATCH_SPLIT_CMD] 批量拆分完成，成功处理 {success_count}/{len(self.cells)} 个单元格")
            return success_count > 0

        except Exception as e:
            LOGGER.error(f"❌ [BATCH_SPLIT_CMD] 批量拆分失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def undo(self) -> bool:
        """撤销批量拆分操作"""
        try:
            LOGGER.debug(f"↩️ [BATCH_SPLIT_CMD] 撤销批量拆分: {self.description}")

            success_count = 0
            # 逆序撤销所有拆分命令
            for split_cmd in reversed(self.split_commands):
                try:
                    if split_cmd.undo():
                        success_count += 1
                    else:
                        LOGGER.warning(f"⚠️ [BATCH_SPLIT_CMD] 撤销拆分命令失败")
                except Exception as e:
                    LOGGER.error(f"❌ [BATCH_SPLIT_CMD] 撤销拆分命令时出错: {e}")

            # 最终刷新
            self.canvas.selectedShapes = []
            self.canvas.selectedShapesCopy = []
            self.canvas.repaint()

            LOGGER.debug(f"✅ [BATCH_SPLIT_CMD] 批量拆分撤销完成，成功撤销 {success_count}/{len(self.split_commands)} 个命令")
            return success_count > 0

        except Exception as e:
            LOGGER.error(f"❌ [BATCH_SPLIT_CMD] 批量拆分撤销失败: {e}")
            return False

# 工具函数
# 工具函数

def create_cell_command(canvas, points: List[QPointF], label: str = "", **properties) -> CreateCellCommand:
    """
    创建单元格命令的便捷工厂函数
    
    Args:
        canvas: Canvas对象
        points: 单元格角点坐标
        label: 单元格标签
        **properties: 其他表格属性
    
    Returns:
        CreateCellCommand: 创建命令对象
    """
    return CreateCellCommand(canvas, points, label, properties)


def create_delete_command(canvas, cells: List) -> DeleteCellCommand:
    """
    删除单元格命令的便捷工厂函数
    
    Args:
        canvas: Canvas对象
        cells: 要删除的单元格列表
    
    Returns:
        DeleteCellCommand: 删除命令对象
    """
    return DeleteCellCommand(canvas, cells)


def create_batch_command(
    canvas, 
    rows: int, 
    cols: int, 
    center: QPointF, 
    **options
) -> BatchCreateCellCommand:
    """
    批量创建命令的便捷工厂函数
    
    Args:
        canvas: Canvas对象
        rows: 行数
        cols: 列数  
        center: 中心点
        **options: 其他选项（cell_width, cell_height, table_id等）
    
    Returns:
        BatchCreateCellCommand: 批量创建命令对象
    """
    return BatchCreateCellCommand(canvas, rows, cols, center, **options) 