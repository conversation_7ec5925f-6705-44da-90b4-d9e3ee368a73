# labelme/table_shape.py
"""表格单元格形状类"""

import copy
from qtpy import QtCore, QtGui
from labelme.shape import Shape
from labelme.utils.edge_detection import EdgeDetectionHelper, EdgeDetectionResult


class TableCellShape(Shape):
    """表格单元格形状类，正确使用table_cell类型"""

    def __init__(self, label=None, line_color=None, flags=None, group_id=None, description=None):
        super().__init__(
            label=label,
            line_color=line_color,
            shape_type="table_cell",  # 我们自己的类型！
            flags=flags,
            group_id=group_id,
            description=description
        )
        
        # 🔧 强制启用填充，确保TableCellShape总是可以显示选中状态
        self.fill = True
        # 🔧 关键修复：初始化table_properties字典
        self.shape_type = "table_cell"
        self.table_properties = {
            'lloc': {
                'start_row': 0,
                'end_row': 0,
                'start_col': 0,
                'end_col': 0
            },
            'border': {
                'style': {
                    'top': 1,
                    'right': 1,
                    'bottom': 1,
                    'left': 1
                }
            },
            'table_type': 1,  # 默认有线表格
            'cell_text': '',  # 空文本内容
            'is_confirmed': False,  # 未确认状态
            'header': False , # 不是表头
            'is_merged': False,  # 🆕 标识是否是合并单元格
            'merge_info': None  # 🆕 合并信息（如果需要的话）
        }
        # 确保颜色属性有默认值
        if self.line_color is None:
            self.line_color = Shape.line_color or QtGui.QColor(0, 255, 0)  # ✅ 使用Shape设置的绿色
        if self.fill_color is None:
            self.fill_color = Shape.fill_color or QtGui.QColor(0, 255, 0, 50)  # 绿色半透明填充
        if self.select_line_color is None:
            self.select_line_color = Shape.select_line_color or QtGui.QColor(255, 0, 0)
        if self.select_fill_color is None:
            self.select_fill_color = QtGui.QColor(255, 255, 0, 120)  # 黄色选中填充，半透明
        if self.vertex_fill_color is None:
            self.vertex_fill_color = QtGui.QColor(255, 255, 255)  # 白色顶点填充
        if self.hvertex_fill_color is None:
            self.hvertex_fill_color = QtGui.QColor(255, 0, 0)  # 红色高亮顶点

        # 🔧 确保table_properties包含header字段
        if 'header' not in self.table_properties:
            self.table_properties['header'] = False
        
        # 🆕 悬停状态属性
        self._is_hovered = False
        # 表格特有属性
        self.table_properties = {
            # 逻辑位置（行列索引）
            "lloc": {
                "start_row": 0,
                "end_row": 0,
                "start_col": 0,
                "end_col": 0
            },
            # 边框样式
            "border": {
                "top": 1,  # 0=无边框, 1=实线边框
                "right": 1,
                "bottom": 1,
                "left": 1
            },
            # 表格类型
            "table_type": 1,  # 0=纯文本, 1=有线表格, 2=无线表格
            # 表格ID（支持一图多表）
            "table_id": 0,  # 表格索引，从0开始
            # 单元格文本内容
            "cell_text": "",
            # 确认状态
            "is_confirmed": False
        }

    def get_header(self):
        """获取表头状态"""
        return self.table_properties.get('header', False)

    def set_header(self, is_header):
        """设置表头状态

        Args:
            is_header (bool): 是否为表头
        """
        self.table_properties['header'] = bool(is_header)
    def copy(self):
        """复制表格单元格（包含表格属性）"""
        new_shape = super().copy()
        # 深拷贝表格属性
        new_shape.table_properties = copy.deepcopy(self.table_properties)
        return new_shape

    def is_merged_cell(self):
        """检查是否是合并单元格"""
        return self.table_properties.get('is_merged', False)

    def set_merged(self, is_merged, merge_info=None):
        """设置合并状态"""
        self.table_properties['is_merged'] = is_merged
        self.table_properties['merge_info'] = merge_info

    def paint(self, painter):
        """绘制TableCellShape"""
        if len(self.points) < 4:
            return

        # 直接调用现有的正确绘制方法
        self._paint_table_cell(painter)

        # 🆕 绘制逻辑坐标（如果启用）
        if self.table_properties.get('show_logical_coords', False):
            self._paint_logical_coordinates(painter)

        # 绘制顶点（如果选中）
        if self.selected:
            self._paint_vertices(painter)
    # def paint(self, painter):
    #     """绘制TableCellShape，正确处理缩放变换"""
    #     if len(self.points) < 4:
    #         LOGGER.debug("❌ [TableCell] points不足4个，无法绘制矩形")
    #         return
    #
    #     # 🔧 根据选中状态设置颜色
    #     if self.selected:
    #         line_color = QtGui.QColor(255, 0, 0, 255)  # 红色边框
    #         fill_color = QtGui.QColor(255, 0, 0, 100)  # 红色半透明填充
    #         line_width = 3
    #     else:
    #         line_color = self.line_color if self.line_color else QtGui.QColor(0, 0, 255, 255)
    #         fill_color = self.fill_color if self.fill_color else QtGui.QColor(135, 206, 250, 100)
    #         line_width = 1
    #
    #     # 🔧 关键修复：使用_scale_point处理缩放变换
    #     path = QtGui.QPainterPath()
    #     if self.points:
    #         # 第一个点移动到
    #         path.moveTo(self._scale_point(self.points[0]))
    #         # 连接其他点
    #         for point in self.points[1:]:
    #             path.lineTo(self._scale_point(point))
    #         # 闭合路径
    #         path.closeSubpath()
    #
    #     # 绘制填充
    #     if self.fill and fill_color:
    #         painter.fillPath(path, fill_color)
    #
    #     # 绘制边框
    #     pen = QtGui.QPen(line_color, line_width, QtCore.Qt.SolidLine)
    #     painter.setPen(pen)
    #     painter.drawPath(path)
    #
    #     # 绘制顶点（如果选中）
    #     if self.selected:
    #         self._paint_vertices(painter)
    # 在TableCellShape类中添加
    def containsPoint(self, point):
        """检查点是否在TableCellShape内部 - 优化版本"""
        if not self.points or len(self.points) < 4:
            return False

        # 🔧 优化1：使用更精确的多边形包含算法，而不是简单的边界框检测
        # 这样可以处理不规则的四边形单元格
        path = self.makePath()
        if path and not path.isEmpty():
            return path.contains(point)
        
        # 🔧 备用方案：如果路径创建失败，使用优化的边界框检测
        x_coords = [p.x() for p in self.points]
        y_coords = [p.y() for p in self.points]

        min_x = min(x_coords)
        max_x = max(x_coords)
        min_y = min(y_coords)
        max_y = max(y_coords)

        # 🔧 优化2：添加小的容错边距，提高点击成功率
        margin = 2.0  # 2像素的容错边距
        return (min_x - margin <= point.x() <= max_x + margin) and (min_y - margin <= point.y() <= max_y + margin)
    def _paint_table_cell(self, painter):
        """绘制表格单元格主体"""
        if len(self.points) < 4:
            return

        # 创建多边形路径
        path = QtGui.QPainterPath()
        path.moveTo(self._scale_point(self.points[0]))
        for point in self.points[1:]:
            path.lineTo(self._scale_point(point))
        if self.isClosed():
            path.lineTo(self._scale_point(self.points[0]))

        # 🔧 TableCellShape填充逻辑：透明->黄色悬停->蓝色选中
        if self.selected:
            # 选中状态：强制绘制蓝色填充
            fill_color = self.select_fill_color
            if fill_color is not None:
                painter.fillPath(path, fill_color)
            else:
                # 使用默认的蓝色选中填充
                default_select_fill = QtGui.QColor(0, 128, 255, 155)  # 蓝色半透明
                painter.fillPath(path, default_select_fill)
        elif hasattr(self, '_is_hovered') and self._is_hovered:
            # 悬停状态：绘制透明黄色填充
            hover_fill = QtGui.QColor(255, 255, 0, 80)  # 透明黄色
            painter.fillPath(path, hover_fill)
        else:
            # 未选中状态：不绘制填充，保持透明
            pass  # 不绘制任何填充，保持透明

        # 绘制表格边框
        self._paint_table_borders(painter)

    def _paint_table_borders(self, painter):
        """绘制表格边框样式"""
        if len(self.points) < 4:
            return
        self.line_color = QtGui.QColor(0, 255, 0)  # 强制绿色

        table_type = self.table_properties["table_type"]
        border = self.table_properties["border"]

        # 定义四条边：上、右、下、左
        edges = [
            (self.points[0], self.points[1], border["top"]),  # 上边
            (self.points[1], self.points[2], border["right"]),  # 右边
            (self.points[2], self.points[3], border["bottom"]),  # 下边
            (self.points[3], self.points[0], border["left"])  # 左边
        ]

        # 🔧 修改：统一边框显示逻辑 - 所有表格类型都按照border属性来显示
        # 不再区分表格类型，统一使用原无线表的显示逻辑：
        # - 有边框(border=1)：显示实线
        # - 无边框(border=0)：显示淡虚线
        # 绘制每条边
        for start_point, end_point, has_border in edges:
            # 应用缩放
            start_scaled = self._scale_point(start_point)
            end_scaled = self._scale_point(end_point)

            # 统一的边框显示逻辑：根据border属性精确控制
            if has_border:
                # 有边框：显示实线
                pen = QtGui.QPen(self.line_color, 2, QtCore.Qt.SolidLine)
            else:
                # 无边框：显示淡虚线
                dash_color = QtGui.QColor(self.line_color)
                dash_color.setAlpha(100)  # 更淡的颜色
                pen = QtGui.QPen(dash_color, 1, QtCore.Qt.CustomDashLine)
                dash_pattern = [6, 8]  # 6像素线段 + 8像素间隔
                pen.setDashPattern(dash_pattern)

            painter.setPen(pen)
            painter.drawLine(start_scaled, end_scaled)

    def _paint_logical_coordinates(self, painter):
        """在单元格中心绘制逻辑坐标"""
        if len(self.points) < 4:
            return

        # 获取逻辑位置信息
        lloc = self.table_properties.get("lloc", {})
        start_row = lloc.get("start_row", 0)
        end_row = lloc.get("end_row", 0)
        start_col = lloc.get("start_col", 0)
        end_col = lloc.get("end_col", 0)

        # 格式化逻辑坐标文本
        if start_row == end_row and start_col == end_col:
            # 普通单元格：显示为 (row,col)
            coord_text = f"({start_row},{start_col})"
        else:
            # 合并单元格：显示为 (start-end,start-end)
            row_display = f"{start_row}-{end_row}" if start_row != end_row else str(start_row)
            col_display = f"{start_col}-{end_col}" if start_col != end_col else str(start_col)
            coord_text = f"({row_display},{col_display})"

        # 计算单元格中心点
        center_x = sum(p.x() for p in self.points) / len(self.points)
        center_y = sum(p.y() for p in self.points) / len(self.points)
        center_point = self._scale_point(QtCore.QPointF(center_x, center_y))

        # 设置文本绘制样式
        font = QtGui.QFont("Arial", 12, QtGui.QFont.Bold)
        painter.setFont(font)

        # 设置文本颜色（红色，确保可见）
        text_color = QtGui.QColor(255, 0, 0, 255)  # 红色
        painter.setPen(text_color)

        # 计算文本边界框
        font_metrics = QtGui.QFontMetrics(font)
        text_rect = font_metrics.boundingRect(coord_text)

        # 调整文本位置到中心
        text_x = center_point.x() - text_rect.width() / 2
        text_y = center_point.y() + text_rect.height() / 4  # 稍微向下偏移

        # 绘制文本背景（白色半透明）
        bg_rect = QtCore.QRectF(
            text_x - 2, text_y - text_rect.height() + 2,
            text_rect.width() + 4, text_rect.height() + 2
        )
        bg_color = QtGui.QColor(255, 255, 255, 200)  # 白色半透明背景
        painter.fillRect(bg_rect, bg_color)

        # 绘制文本
        painter.drawText(QtCore.QPointF(text_x, text_y), coord_text)

    def _paint_vertices(self, painter):
        """绘制顶点（选中时）"""
        vertex_path = QtGui.QPainterPath()

        for i in range(len(self.points)):
            self.drawVertex(vertex_path, i)

        if vertex_path.length() > 0:
            # 使用选中颜色绘制顶点
            color = self.select_line_color if self.selected else self.line_color
            painter.setPen(QtGui.QPen(color, self.PEN_WIDTH))
            painter.drawPath(vertex_path)

            # 设置顶点填充色
            vertex_fill = self.hvertex_fill_color if self._highlightIndex is not None else self.vertex_fill_color
            painter.fillPath(vertex_path, vertex_fill)

    def makePath(self):
        """创建表格单元格的路径（用于碰撞检测等）"""
        if len(self.points) < 3:
            return QtGui.QPainterPath()

        path = QtGui.QPainterPath(self.points[0])
        for p in self.points[1:]:
            path.lineTo(p)

        # 如果是闭合的，连接回起点
        if self.isClosed():
            path.lineTo(self.points[0])

        return path

    def get_table_data(self):
        """获取表格数据（用于JSON序列化）"""
        return {
            "shape_type": self.shape_type,  # "table_cell"
            "label": self.label,
            "points": [[p.x(), p.y()] for p in self.points],
            "table_properties": self.table_properties,
            "flags": self.flags,
            "group_id": self.group_id,
            "description": self.description
        }

    @classmethod
    def from_table_data(cls, data):
        """从表格数据创建形状（用于JSON反序列化）"""
        shape = cls(
            label=data.get("label"),
            flags=data.get("flags"),
            group_id=data.get("group_id"),
            description=data.get("description")
        )

        # 设置点坐标
        if "points" in data:
            shape.points = [QtCore.QPointF(p[0], p[1]) for p in data["points"]]

        # 设置表格属性
        if "table_properties" in data:
            shape.table_properties.update(data["table_properties"])

        return shape

    @classmethod
    def from_multi_table_cell(cls, cell_data: dict, table_id: int, table_type: int) -> 'TableCellShape':
        """从MultiTableController导出格式创建TableCellShape
        
        Args:
            cell_data: 单元格数据字典，包含cell_ind, header, content, bbox, lloc, border
            table_id: 表格ID
            table_type: 表格类型
            
        Returns:
            TableCellShape对象
            
        Raises:
            ValueError: 当数据格式无效时
        """
        if not isinstance(cell_data, dict) or "bbox" not in cell_data:
            raise ValueError("MultiTable格式数据无效：缺少必要字段")
        
        # 创建基础形状对象
        cell_ind = cell_data.get("cell_ind", 0)
        label = f"table_{table_id}_cell_{cell_ind}"
        
        shape = cls(label=label)
        
        # 转换bbox：{"p1": (x,y), "p2": (x,y), "p3": (x,y), "p4": (x,y)} → points
        bbox = cell_data["bbox"]
        if isinstance(bbox, dict) and all(f"p{i}" in bbox for i in range(1, 5)):
            shape.points = [
                QtCore.QPointF(bbox["p1"][0], bbox["p1"][1]),
                QtCore.QPointF(bbox["p2"][0], bbox["p2"][1]),
                QtCore.QPointF(bbox["p3"][0], bbox["p3"][1]),
                QtCore.QPointF(bbox["p4"][0], bbox["p4"][1])
            ]
        else:
            raise ValueError("MultiTable格式bbox数据无效")
        
        # 设置表格属性
        shape.table_properties.update({
            "table_id": table_id,
            "table_type": table_type,
            "header": cell_data.get("header", False),
            "cell_index": cell_ind
        })
        
        # 设置逻辑位置
        if "lloc" in cell_data and isinstance(cell_data["lloc"], dict):
            shape.table_properties["lloc"] = cell_data["lloc"]
        
        # 设置边框样式
        if "border" in cell_data and isinstance(cell_data["border"], dict):
            border_style = cell_data["border"].get("style", {})
            if border_style:
                shape.table_properties["border"] = border_style  # ✅ 正确字段

        # 设置文本内容
        if "content" in cell_data and isinstance(cell_data["content"], list):
            # 提取第一个content项的text作为文本内容
            content_list = cell_data["content"]
            if content_list and isinstance(content_list[0], dict):
                text_content = content_list[0].get("text", "")
                shape.table_properties["cell_text"] = text_content  # ✅ 修复：使用正确的字段名
        
        return shape
    
    @classmethod  
    def from_public_dataset_cell(cls, cell_data: dict, cell_index: int, filename: str = "unknown") -> 'TableCellShape':
        """从公共数据集格式创建TableCellShape
        
        Args:
            cell_data: 单元格数据字典，包含tokens和bbox
            cell_index: 单元格索引
            filename: 数据集文件名
            
        Returns:
            TableCellShape对象
            
        Raises:
            ValueError: 当数据格式无效时
        """
        if not isinstance(cell_data, dict):
            raise ValueError("公共数据集格式数据无效：不是字典类型")
        
        # 创建基础形状对象
        label = f"dataset_cell_{cell_index}"
        shape = cls(label=label)
        
        # 转换bbox：[x0, y0, x1, y1] → 4个点坐标
        if "bbox" in cell_data and isinstance(cell_data["bbox"], list):
            bbox = cell_data["bbox"]
            if len(bbox) == 4:
                x0, y0, x1, y1 = bbox
                # 构建矩形的4个角点
                shape.points = [
                    QtCore.QPointF(x0, y0),  # 左上
                    QtCore.QPointF(x1, y0),  # 右上  
                    QtCore.QPointF(x1, y1),  # 右下
                    QtCore.QPointF(x0, y1)   # 左下
                ]
            else:
                raise ValueError("公共数据集bbox格式无效：应为[x0,y0,x1,y1]")
        else:
            # 空单元格没有bbox，设置默认点
            shape.points = [QtCore.QPointF(0, 0), QtCore.QPointF(0, 0), QtCore.QPointF(0, 0), QtCore.QPointF(0, 0)]
        
        # 设置文本内容：tokens数组 → cell_text字符串
        tokens = cell_data.get("tokens", [])
        if isinstance(tokens, list):
            text_content = " ".join(str(token) for token in tokens)
            shape.table_properties["cell_text"] = text_content  # ✅ 修复：使用正确的字段名
        
        # 设置默认逻辑位置（按顺序排列）
        # 简单策略：假设每行最多10列
        max_cols = 10
        row = cell_index // max_cols
        col = cell_index % max_cols
        shape.table_properties["lloc"] = {
            "start_row": row,
            "end_row": row,
            "start_col": col, 
            "end_col": col
        }
        
        # 设置默认属性
        shape.table_properties.update({
            "table_id": 0,  # 默认表格ID
            "table_type": 1,  # 默认有线表格
            "header": False,  # 默认非表头
            "cell_index": cell_index,
            "source_filename": filename,
            "border_style": {"top": True, "bottom": True, "left": True, "right": True}  # 默认全边框
        })
        
        return shape

    def set_logical_location(self, start_row, end_row, start_col, end_col):
        """设置逻辑位置（行列索引）"""
        self.table_properties["lloc"] = {
            "start_row": start_row,
            "end_row": end_row,
            "start_col": start_col,
            "end_col": end_col
        }

    def get_logical_location(self):
        """获取逻辑位置"""
        return self.table_properties["lloc"]

    def set_border_style(self, top=1, right=1, bottom=1, left=1):
        """设置边框样式"""
        self.table_properties["border"] = {
            "top": top,
            "right": right,
            "bottom": bottom,
            "left": left
        }

    def get_border_style(self):
        """获取边框样式"""
        return self.table_properties["border"]

    def set_table_type(self, table_type):
        """设置表格类型 - 仅用于标识，不影响边框显示逻辑"""
        if table_type in [0, 1, 2]:
            self.table_properties["table_type"] = table_type
            # 🔧 修改：表格类型现在仅用于标识，不影响边框显示
            # 所有表格类型都按照border属性来显示边框：
            # - border=1: 显示实线
            # - border=0: 显示淡虚线
            # 表格类型含义：0=纯文本, 1=有线表格, 2=无线表格

    def get_table_type(self):
        """获取表格类型"""
        return self.table_properties["table_type"]

    def set_table_id(self, table_id):
        """设置表格ID"""
        self.table_properties["table_id"] = table_id

    def get_table_id(self):
        """获取表格ID"""
        return self.table_properties["table_id"]

    def set_cell_text(self, text):
        """设置单元格文本"""
        self.table_properties["cell_text"] = text

    def get_cell_text(self):
        """获取单元格文本"""
        return self.table_properties.get("cell_text", "")

    def is_confirmed(self):
        """检查是否已确认"""
        return self.table_properties.get("is_confirmed", False)

    def set_confirmed(self, confirmed=True):
        """设置确认状态"""
        self.table_properties["is_confirmed"] = confirmed

    def detect_nearest_edge(self, pos, threshold=None, require_selected=True):
        """检测鼠标位置最接近的边

        Args:
            pos: 鼠标位置 (QtCore.QPointF)
            threshold: 检测阈值，None使用默认值
            require_selected: 是否要求单元格处于选中状态才进行边检测

        Returns:
            EdgeType枚举值或None（如果不接近任何边或未选中）
        """
        if len(self.points) != 4:
            return None

        # 🔧 关键：只有选中状态的单元格才进行边检测
        if require_selected and not self.selected:
            return None

        # 使用边检测工具类
        return EdgeDetectionHelper.detect_nearest_edge(self.points, pos, threshold)

    def get_edge_vertex_indices(self, edge_type):
        """获取指定边对应的顶点索引

        Args:
            edge_type: EdgeType枚举值

        Returns:
            该边的两个顶点索引 (index1, index2)
        """
        return EdgeDetectionHelper.get_edge_vertex_indices(edge_type)

    def get_edge_center(self, edge_type):
        """获取指定边的中心点坐标

        Args:
            edge_type: EdgeType枚举值

        Returns:
            边中心点坐标 (QtCore.QPointF)
        """
        if len(self.points) != 4:
            return QtCore.QPointF()

        return EdgeDetectionHelper.calculate_edge_center(self.points, edge_type)

    def get_detailed_edge_detection(self, pos, threshold=None):
        """获取详细的边检测结果

        Args:
            pos: 鼠标位置
            threshold: 检测阈值

        Returns:
            EdgeDetectionResult对象或None
        """
        edge_type = self.detect_nearest_edge(pos, threshold)
        if edge_type is None:
            return None

        # 计算距离
        if threshold is None:
            threshold = EdgeDetectionHelper.DEFAULT_THRESHOLD

        vertex_indices = self.get_edge_vertex_indices(edge_type)
        edge_center = self.get_edge_center(edge_type)

        # 计算实际距离
        distance = ((pos.x() - edge_center.x()) ** 2 + (pos.y() - edge_center.y()) ** 2) ** 0.5

        return EdgeDetectionResult(edge_type, distance, vertex_indices)

    def is_near_edge(self, pos, threshold=None):
        """快速检查是否接近任何边

        Args:
            pos: 鼠标位置
            threshold: 检测阈值

        Returns:
            bool - 是否接近边
        """
        return self.detect_nearest_edge(pos, threshold) is not None

    def debug_edge_detection(self, pos, threshold=None):
        """调试边检测功能，输出详细信息

        Args:
            pos: 鼠标位置
            threshold: 检测阈值
        """
        detection_result = self.get_detailed_edge_detection(pos, threshold)

        if detection_result:
            LOGGER.debug(f"🎯 [边检测] {detection_result}")
            LOGGER.debug(f"   - 坐标轴: {detection_result.coordinate_axis}")
            LOGGER.debug(f"   - 边中心: {self.get_edge_center(detection_result.edge_type)}")
        else:
            LOGGER.debug(f"❌ [边检测] 位置 ({pos.x():.1f}, {pos.y():.1f}) 不接近任何边")

# 工具函数
def create_table_cell_from_rect(x1, y1, x2, y2, label=""):
    """从矩形坐标创建表格单元格"""
    shape = TableCellShape(label=label)

    # 创建矩形的四个顶点（顺时针）
    shape.points = [
        QtCore.QPointF(x1, y1),  # 左上
        QtCore.QPointF(x2, y1),  # 右上
        QtCore.QPointF(x2, y2),  # 右下
        QtCore.QPointF(x1, y2)  # 左下
    ]

    # 关闭形状
    shape.close()

    return shape


def create_table_cell_from_points(points, label=""):
    """从点列表创建表格单元格"""
    shape = TableCellShape(label=label)
    shape.points = [QtCore.QPointF(p[0], p[1]) for p in points]

    # 如果是4个点，关闭形状
    if len(shape.points) >= 3:
        shape.close()

    return shape


def is_table_cell(shape):
    """检查一个形状是否是表格单元格"""
    return hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell'


def get_table_cells_from_shapes(shapes):
    """从形状列表中筛选出表格单元格"""
    return [shape for shape in shapes if is_table_cell(shape)]