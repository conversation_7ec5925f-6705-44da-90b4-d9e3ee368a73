#!/usr/bin/python3
# -*- coding: utf-8 -*-
# Time: 2025-01-03 18:00
# Author: <EMAIL>
# FileName: textin_converter.py

"""
TextIn API响应转换器

将TextIn通用表格识别API的响应数据转换为TableLabelMe标注协议格式。
"""

import time
from typing import Dict, Any, List, Tuple

from .base_converter import BaseAPIResponseConverter, ConvertError
from ...utils.log import get_logger

LOGGER = get_logger()


class TextInResponseConverter(BaseAPIResponseConverter):
    """
    TextIn API响应转换器
    
    负责将TextIn表格识别API的响应数据转换为TableLabelMe可用的格式。
    支持多页文档、多表格、合并单元格等复杂场景。
    """
    
    def convert(self, api_response: Dict[str, Any], original_image_size: Tuple[int, int]) -> Dict[str, Any]:
        """
        转换TextIn API响应为TableLabelMe标注协议格式
        
        Args:
            api_response: TextIn API原始响应数据
            original_image_size: 原始图像尺寸 (width, height)
            
        Returns:
            dict: 标准的TableLabelMe标注格式数据
            
        Raises:
            ConvertError: 转换失败时抛出
        """
        try:
            # 1. 验证响应数据结构
            if 'result' not in api_response:
                raise ConvertError("API响应缺少result字段")
            
            result = api_response['result']
            if 'pages' not in result:
                raise ConvertError("API响应缺少pages字段")
            
            # 2. 获取缩放比例
            metadata = api_response.get('_metadata', {})
            scale_ratio = metadata.get('scale_ratio', 1.0)
            
            LOGGER.debug(f"开始转换TextIn响应，缩放比例: {scale_ratio}")
            
            # 3. 处理所有页面的表格
            all_tables = []
            table_id = 0
            
            for page_idx, page in enumerate(result['pages']):
                page_tables = self._process_page_tables(page, page_idx, table_id, scale_ratio)
                all_tables.extend(page_tables)
                table_id += len(page_tables)
            
            LOGGER.debug(f"转换完成，共识别到 {len(all_tables)} 个表格")
            
            # 4. 构建TableDataManager支持的MultiTableController格式
            # 转换为MultiTableController导出格式: [{table_ind: 0, cells: [...]}]
            multi_table_format = []
            
            for table in all_tables:
                # 转换单元格格式为TableDataManager期望的格式
                converted_cells = []
                for cell in table.get('cells', []):
                    # 提取TableDataManager需要的单元格字段
                    table_props = cell.get('table_properties', {})
                    lloc = table_props.get('lloc', {})
                    
                    # 🔧 修复：转换bbox格式为MultiTableController期望的格式
                    # 从points列表转换为{"p1": (x,y), ...}格式
                    cell_points = cell.get('points', [[0, 0], [50, 0], [50, 20], [0, 20]])
                    if len(cell_points) >= 4:
                        # 确保有4个点：左上、右上、右下、左下
                        bbox_dict = {
                            "p1": tuple(cell_points[0]) if isinstance(cell_points[0], list) else cell_points[0],  # 左上
                            "p2": tuple(cell_points[1]) if isinstance(cell_points[1], list) else cell_points[1],  # 右上
                            "p3": tuple(cell_points[2]) if isinstance(cell_points[2], list) else cell_points[2],  # 右下
                            "p4": tuple(cell_points[3]) if isinstance(cell_points[3], list) else cell_points[3]   # 左下
                        }
                    elif len(cell_points) == 2:
                        # 如果只有2个点（左上角和右下角），构建完整的矩形
                        x1, y1 = cell_points[0]
                        x2, y2 = cell_points[1]
                        bbox_dict = {
                            "p1": (x1, y1),  # 左上
                            "p2": (x2, y1),  # 右上
                            "p3": (x2, y2),  # 右下
                            "p4": (x1, y2)   # 左下
                        }
                    else:
                        # 默认bbox格式
                        bbox_dict = {
                            "p1": (0, 0),
                            "p2": (50, 0), 
                            "p3": (50, 20),
                            "p4": (0, 20)
                        }

                    converted_cell = {
                        # 单元格索引
                        "cell_ind": len(converted_cells),
                        
                        # 基础信息
                        "text": table_props.get('cell_text', ''),
                        "row": lloc.get('start_row', 0),
                        "col": lloc.get('start_col', 0),
                        "row_span": lloc.get('end_row', 0) - lloc.get('start_row', 0) + 1,
                        "col_span": lloc.get('end_col', 0) - lloc.get('start_col', 0) + 1,
                        
                        # 🔧 修复：使用正确的bbox格式
                        "bbox": bbox_dict,
                        
                        # 逻辑位置信息
                        "lloc": lloc,
                        
                        # 样式信息
                        "border": table_props.get('border', {
                            "style": {
                                "top": 1,
                                "right": 1,
                                "bottom": 1,
                                "left": 1
                            },
                            "width": None,
                            "color": None
                        }),
                        "header": table_props.get('header', False),
                        "table_type": table_props.get('table_type', 1),
                        "is_confirmed": table_props.get('is_confirmed', True),
                        
                        # 内容信息（MultiTableController格式）
                        "content": [{"text": table_props.get('cell_text', '')}],
                        
                        # API元数据
                        "api_source": "textin"
                    }
                    converted_cells.append(converted_cell)
                
                # 构建表格数据
                table_data = {
                    "table_ind": table.get('table_id', 0),
                    "type": table.get('table_type', 1),
                    "cells": converted_cells,
                    "table_bbox": table.get('table_bbox', [0, 0, 100, 100]),
                    "metadata": table.get('metadata', {})
                }
                multi_table_format.append(table_data)
            
            LOGGER.debug(f"生成MultiTableController格式，表格数量: {len(multi_table_format)}")
            return multi_table_format
            
        except ConvertError:
            # 重新抛出ConvertError
            raise
        except Exception as e:
            raise ConvertError("转换TextIn API响应时发生错误", str(e))
    
    def _process_page_tables(self, page_data: Dict[str, Any], page_idx: int, 
                           start_table_id: int, scale_ratio: float) -> List[Dict[str, Any]]:
        """
        处理单个页面的表格数据
        
        Args:
            page_data: 页面数据
            page_idx: 页面索引
            start_table_id: 起始表格ID
            scale_ratio: 坐标缩放比例
            
        Returns:
            list: 表格数据列表
        """
        page_tables = []
        
        if 'tables' not in page_data:
            LOGGER.warning(f"页面 {page_idx} 不包含表格数据")
            return page_tables
        
        tables = page_data['tables']
        for table_idx, table_data in enumerate(tables):
            # TextIn API不需要检查表格类型
            # 所有返回的数据都是表格
            
            try:
                converted_table = self._convert_single_table(
                    table_data, 
                    start_table_id + table_idx, 
                    scale_ratio
                )
                if converted_table:
                    page_tables.append(converted_table)
                    
            except Exception as e:
                LOGGER.error(f"转换表格 {table_idx} 失败: {e}")
                continue
        
        return page_tables
    
    def _convert_single_table(self, table_data: Dict[str, Any], table_id: int, 
                            scale_ratio: float) -> Dict[str, Any]:
        """
        转换单个表格数据
        
        Args:
            table_data: TextIn表格数据
            table_id: 表格ID
            scale_ratio: 坐标缩放比例
            
        Returns:
            dict: 转换后的表格数据
        """
        # 1. 处理表格边界
        table_position = table_data.get('position', [])
        if len(table_position) >= 8:
            # TextIn返回8个坐标点，转换为矩形边界
            x_coords = [table_position[i] for i in range(0, 8, 2)]
            y_coords = [table_position[i] for i in range(1, 8, 2)]
            
            # 缩放坐标
            if scale_ratio != 1.0:
                x_coords = [int(x / scale_ratio) for x in x_coords]
                y_coords = [int(y / scale_ratio) for y in y_coords]
            
            table_bbox = [
                min(x_coords),  # x1
                min(y_coords),  # y1
                max(x_coords),  # x2
                max(y_coords)   # y2
            ]
        else:
            LOGGER.warning(f"表格 {table_id} 位置信息不完整")
            table_bbox = [0, 0, 100, 100]  # 默认边界
        
        # 2. 处理单元格数据
        cells = table_data.get('table_cells', [])  # 使用table_cells而不是cells
        converted_cells = []
        
        for cell in cells:
            converted_cell = self._convert_single_cell(cell, table_id, scale_ratio)
            if converted_cell:
                converted_cells.append(converted_cell)
        
        # 3. 构建表格数据结构
        table_result = {
            "table_id": table_id,
            "table_bbox": table_bbox,
            "cells": converted_cells,
            "table_type": self._detect_table_type(table_data),  # 直接传入table_data
            "metadata": {
                "source": "textin_api",
                "original_cells_count": len(cells),
                "converted_cells_count": len(converted_cells),
                "converted_at": time.time()
            }
        }
        
        LOGGER.debug(f"表格 {table_id} 转换完成，单元格数量: {len(converted_cells)}")
        
        return table_result
    
    def _convert_single_cell(self, cell_data: Dict[str, Any], table_id: int, 
                           scale_ratio: float) -> Dict[str, Any]:
        """
        转换单个单元格数据
        
        Args:
            cell_data: TextIn单元格数据
            table_id: 所属表格ID
            scale_ratio: 坐标缩放比例
            
        Returns:
            dict: 转换后的单元格数据
        """
        try:
            # 1. 获取基础信息
            start_row = cell_data.get('start_row', 0)
            start_col = cell_data.get('start_col', 0)
            end_row = cell_data.get('end_row', start_row)
            end_col = cell_data.get('end_col', start_col)
            
            # 从lines中获取文本
            lines = cell_data.get('lines', [])
            text = ''
            if lines:
                text = ' '.join(line.get('text', '').strip() for line in lines)
            text = text.strip()
            
            # 2. 处理单元格位置
            position = cell_data.get('position', [])
            if len(position) >= 8:  # TextIn返回8个坐标点
                # 提取边界框坐标
                x_coords = [position[i] for i in range(0, 8, 2)]
                y_coords = [position[i] for i in range(1, 8, 2)]
                
                # 缩放坐标
                if scale_ratio != 1.0:
                    x_coords = [int(x / scale_ratio) for x in x_coords]
                    y_coords = [int(y / scale_ratio) for y in y_coords]
                
                # 计算边界框
                cell_bbox = [
                    min(x_coords),  # x1
                    min(y_coords),  # y1
                    max(x_coords),  # x2
                    max(y_coords)   # y2
                ]
            else:
                LOGGER.warning(f"单元格 ({start_row}, {start_col}) 位置信息不完整")
                cell_bbox = [0, 0, 50, 20]  # 默认大小
            
            # 3. 处理边框信息
            borders_data = cell_data.get('borders', {})
            border = self._convert_borders(borders_data)
            
            # 4. 构建TableLabelMe单元格数据结构
            converted_cell = {
                # 基础标注信息
                "label": f"cell_{table_id}_{start_row}_{start_col}",
                "shape_type": "table_cell",
                "flags": {},
                "group_id": None,
                "description": "",
                
                # 🔧 修复：物理位置信息 - 生成完整的4个顶点坐标
                "points": [
                    [cell_bbox[0], cell_bbox[1]],  # 左上角
                    [cell_bbox[2], cell_bbox[1]],  # 右上角
                    [cell_bbox[2], cell_bbox[3]],  # 右下角
                    [cell_bbox[0], cell_bbox[3]]   # 左下角
                ],
                
                # 表格特有属性
                "table_properties": {
                    "table_id": table_id,
                    "cell_text": text,
                    "table_type": 1,  # 默认为有线表格
                    "is_confirmed": True,  # API识别的结果默认确认
                    
                    # 逻辑位置信息
                    "lloc": {
                        "start_row": start_row,
                        "end_row": end_row,
                        "start_col": start_col,
                        "end_col": end_col
                    },
                    
                    # 边框样式 - 使用完整的border结构
                    "border": border,
                    
                    # 表头检测（基于简单规则）
                    "header": self._is_header_cell(start_row, text)
                },
                
                # API来源元数据
                "api_metadata": {
                    "source": "textin",
                    "original_row": start_row,
                    "original_col": start_col,
                    "row_span": end_row - start_row + 1,
                    "col_span": end_col - start_col + 1,
                    "confidence": 1.0  # TextIn API不提供单元格置信度
                }
            }
            
            return converted_cell
            
        except Exception as e:
            LOGGER.error(f"转换单元格失败: {e}")
            return None
    
    def _convert_borders(self, borders_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        转换边框信息
        
        Args:
            borders_data: TextIn边框数据
            
        Returns:
            dict: TableLabelMe边框样式
        """
        # 初始化目标格式
        border = {
            "style": {
                "top": 1,
                "right": 1,
                "bottom": 1,
                "left": 1
            },
            "width": None,
            "color": None
        }
        
        # 如果API提供了具体的边框信息，进行转换
        if borders_data:
            LOGGER.debug(f"转换边框信息: 输入={borders_data}")
            for side in ["top", "right", "bottom", "left"]:
                if side in borders_data:
                    # API中0表示无边框，1表示有边框，与目标格式一致
                    border["style"][side] = borders_data[side]
            LOGGER.debug(f"转换边框信息: 输出={border}")
        
        return border
    
    def _detect_table_type(self, table_data: Dict[str, Any]) -> int:
        """
        检测表格类型
        
        Args:
            table_data: TextIn表格数据
            
        Returns:
            int: 表格类型 (0=纯文本, 1=有线表格, 2=无线表格)
        """
        # 从API响应中获取表格类型
        api_type = table_data.get('type', '')
        
        # 映射API类型到TableLabelMe类型
        if api_type == 'plain':
            return 0  # 纯文本
        elif api_type == 'table_with_line':
            return 1  # 有线表格
        elif api_type == 'table_without_line':
            return 2  # 无线表格
        
        # 如果无法获取API类型或类型未知，使用默认逻辑
        cells = table_data.get('table_cells', [])
        if len(cells) > 1:
            return 1  # 有线表格
        elif len(cells) == 1:
            return 0  # 纯文本
        else:
            return 2  # 无线表格
    
    def _is_header_cell(self, row: int, text: str) -> bool:
        """
        判断是否为表头单元格
        
        Args:
            row: 行号
            text: 单元格文本
            
        Returns:
            bool: 是否为表头
        """
        # 简单规则：第一行通常是表头
        if row == 0:
            return True
        
        # 其他表头识别规则可以在这里添加
        # 比如：特定关键词、格式特征等
        
        return False
    
    def get_converter_info(self) -> Dict[str, str]:
        """
        获取TextIn转换器信息
        
        Returns:
            dict: 转换器信息字典
        """
        info = super().get_converter_info()
        info.update({
            "source_api": "TextIn",
            "api_version": "v1.0.17",
            "supports_multipage": "true",
            "supports_merged_cells": "true"
        })
        return info 