#!/usr/bin/python3
# -*- coding: utf-8 -*-
# Time: 2025-01-03 18:00
# Author: xela<PERSON><EMAIL>
# FileName: base_converter.py

"""
基础API响应转换器抽象类

定义所有API响应转换器的统一接口，负责将不同API的响应格式转换为TableLabelMe标注协议。
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Tuple

from ...utils.log import get_logger

LOGGER = get_logger()


class BaseAPIResponseConverter(ABC):
    """
    API响应转换器基类
    
    定义将第三方API响应转换为TableLabelMe标注协议的统一接口。
    子类需要实现具体的转换逻辑。
    """
    
    def __init__(self):
        """初始化基础转换器"""
        self.name = self.__class__.__name__
        LOGGER.debug(f"初始化API响应转换器: {self.name}")
    
    @abstractmethod
    def convert(self, api_response: Dict[str, Any], original_image_size: Tuple[int, int]) -> List[Dict[str, Any]]:
        """
        转换API响应为TableLabelMe标注协议格式（子类必须实现）
        
        Args:
            api_response: API原始响应数据
            original_image_size: 原始图像尺寸 (width, height)
            
        Returns:
            list: 表格数据列表，每个表格包含单元格信息
            
        Raises:
            ConvertError: 转换失败时抛出
        """
        pass
    
    def scale_coordinates(self, coordinates: List[int], scale_ratio: float) -> List[int]:
        """
        缩放坐标点
        
        当API调用前对图像进行了缩放时，需要将API返回的坐标按比例恢复到原始尺寸。
        
        Args:
            coordinates: 坐标点列表 [x1, y1, x2, y2, ...]
            scale_ratio: 缩放比例（原始尺寸/API处理尺寸）
            
        Returns:
            list: 缩放后的坐标点列表
        """
        if scale_ratio == 1.0:
            return coordinates
        
        # 按缩放比例调整坐标
        scaled_coords = []
        for coord in coordinates:
            scaled_coords.append(int(coord / scale_ratio))
        
        return scaled_coords
    
    def validate_cell_data(self, cell_data: Dict[str, Any]) -> bool:
        """
        验证单元格数据有效性
        
        Args:
            cell_data: 单元格数据字典
            
        Returns:
            bool: 数据有效返回True
        """
        required_fields = ['position', 'text', 'row', 'col']
        
        for field in required_fields:
            if field not in cell_data:
                LOGGER.warning(f"单元格数据缺少必需字段: {field}")
                return False
        
        # 检查坐标数据
        position = cell_data['position']
        if not isinstance(position, list) or len(position) < 4:
            LOGGER.warning(f"无效的单元格位置数据: {position}")
            return False
        
        return True
    
    def create_table_annotation_format(self, tables: List[Dict[str, Any]], 
                                     image_path: str, 
                                     image_size: Tuple[int, int]) -> Dict[str, Any]:
        """
        创建TableLabelMe标注协议格式的完整数据结构
        
        Args:
            tables: 表格数据列表
            image_path: 图像文件路径
            image_size: 图像尺寸 (width, height)
            
        Returns:
            dict: 完整的标注数据，符合TableLabelMe协议
        """
        annotation_data = {
            "version": "5.8.1",
            "flags": {},
            "shapes": [],
            "imagePath": image_path.split('/')[-1],  # 只保留文件名
            "imageData": None,  # 通常为None，图像数据单独存储
            "imageHeight": image_size[1],
            "imageWidth": image_size[0],
            "text": "",
            "lineColor": [0, 255, 0],
            "fillColor": [255, 0, 0],
            
            # TableLabelMe扩展字段
            "table_annotation": {
                "tables": tables,
                "total_tables": len(tables),
                "annotation_source": "api_auto_labeling",
                "api_metadata": {
                    "converter": self.name,
                    "converted_at": None  # 将在具体转换器中设置
                }
            }
        }
        
        return annotation_data
    
    def get_converter_info(self) -> Dict[str, str]:
        """
        获取转换器信息
        
        Returns:
            dict: 转换器信息字典
        """
        return {
            "name": self.name,
            "target_format": "TableLabelMe",
            "version": "1.0.0"
        }


class ConvertError(Exception):
    """转换异常类"""
    
    def __init__(self, message: str, details: str = ""):
        """
        初始化转换异常
        
        Args:
            message: 错误消息
            details: 详细错误信息
        """
        super().__init__(message)
        self.message = message
        self.details = details
        
    def __str__(self):
        if self.details:
            return f"{self.message}: {self.details}"
        return self.message 