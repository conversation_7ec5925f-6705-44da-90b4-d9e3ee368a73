#!/usr/bin/python3
# -*- coding: utf-8 -*-
# Time: 2025-01-03 18:00
# Author: <EMAIL>
# FileName: base_client.py

"""
基础API客户端抽象类

定义所有第三方API客户端的统一接口，提供异常处理和基础功能。
"""

import os
from abc import ABC, abstractmethod
from typing import Dict, Any, Tuple, Optional
from PIL import Image

from ...utils.log import get_logger

LOGGER = get_logger()


class APIError(Exception):
    """API调用异常类"""
    
    def __init__(self, message: str, details: str = "", error_code: int = None):
        """
        初始化API异常
        
        Args:
            message: 错误消息
            details: 详细错误信息
            error_code: 错误码
        """
        super().__init__(message)
        self.message = message
        self.details = details
        self.error_code = error_code
        
    def __str__(self):
        error_str = self.message
        if self.error_code:
            error_str = f"[{self.error_code}] {error_str}"
        if self.details:
            error_str = f"{error_str}: {self.details}"
        return error_str


class BaseAPIClient(ABC):
    """
    API客户端基类
    
    定义所有第三方API客户端必须实现的接口方法。
    提供图像预处理、错误处理等通用功能。
    """
    
    MAX_IMAGE_SIZE = 2048  # 最大图像尺寸限制（按最长边）
    
    def __init__(self):
        """初始化基础客户端"""
        self.name = self.__class__.__name__
        LOGGER.debug(f"初始化API客户端: {self.name}")
        
    @abstractmethod
    def recognize_table(self, image_path: str) -> Dict[str, Any]:
        """
        表格识别接口（子类必须实现）
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            dict: API响应结果
            
        Raises:
            APIError: API调用失败时抛出
        """
        pass
        
    def preprocess_image(self, image_path: str) -> Tuple[str, Tuple[int, int], float]:
        """
        图像预处理
        
        对输入图像进行尺寸检查和缩放处理，确保符合API要求。
        如果图像超过最大尺寸限制，会等比缩放并保存临时文件。
        
        Args:
            image_path: 原始图像路径
            
        Returns:
            tuple: (处理后图像路径, 原始尺寸, 缩放比例)
            
        Raises:
            APIError: 图像处理失败时抛出
        """
        try:
            # 打开图像获取尺寸
            with Image.open(image_path) as img:
                original_size = img.size  # (width, height)
                max_dimension = max(original_size)
                
                # 检查是否需要缩放
                if max_dimension <= self.MAX_IMAGE_SIZE:
                    # 不需要缩放，直接返回原始路径
                    LOGGER.debug(f"图像尺寸符合要求: {original_size}")
                    return image_path, original_size, 1.0
                
                # 计算缩放比例
                scale_ratio = self.MAX_IMAGE_SIZE / max_dimension
                new_size = (
                    int(original_size[0] * scale_ratio),
                    int(original_size[1] * scale_ratio)
                )
                
                LOGGER.debug(f"图像需要缩放: {original_size} -> {new_size}, 比例: {scale_ratio:.3f}")
                
                # 缩放图像
                resized_img = img.resize(new_size, Image.Resampling.LANCZOS)
                
                # 生成临时文件路径
                base_name = os.path.splitext(image_path)[0]
                temp_path = f"{base_name}_api_temp.jpg"
                
                # 保存缩放后的图像
                if resized_img.mode in ('RGBA', 'LA', 'P'):
                    # 转换为RGB模式以支持JPEG保存
                    rgb_img = Image.new('RGB', resized_img.size, (255, 255, 255))
                    if resized_img.mode == 'P':
                        resized_img = resized_img.convert('RGBA')
                    rgb_img.paste(resized_img, mask=resized_img.split()[-1] if resized_img.mode in ('RGBA', 'LA') else None)
                    resized_img = rgb_img
                
                resized_img.save(temp_path, 'JPEG', quality=95)
                
                LOGGER.debug(f"已保存缩放图像到: {temp_path}")
                return temp_path, original_size, scale_ratio
                
        except Exception as e:
            raise APIError(f"图像预处理失败", str(e))
            
    def cleanup_temp_files(self, temp_path: str) -> None:
        """
        清理临时文件
        
        Args:
            temp_path: 临时文件路径
        """
        try:
            if temp_path and os.path.exists(temp_path) and "_api_temp" in temp_path:
                os.remove(temp_path)
                LOGGER.debug(f"已清理临时文件: {temp_path}")
        except Exception as e:
            LOGGER.warning(f"清理临时文件失败: {e}")
            
    def validate_image_file(self, image_path: str) -> bool:
        """
        验证图像文件有效性
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            bool: 文件有效返回True
            
        Raises:
            APIError: 文件无效时抛出
        """
        if not os.path.exists(image_path):
            raise APIError(f"图像文件不存在: {image_path}")
            
        if not os.path.isfile(image_path):
            raise APIError(f"路径不是文件: {image_path}")
            
        # 检查文件大小（10MB限制）
        file_size = os.path.getsize(image_path) / (1024 * 1024)  # MB
        if file_size > 10:
            raise APIError(f"文件过大: {file_size:.1f}MB，超过10MB限制")
            
        # 检查是否为有效图像
        try:
            with Image.open(image_path) as img:
                img.verify()
            return True
        except Exception as e:
            raise APIError(f"无效的图像文件: {image_path}", str(e))
            
    def get_client_info(self) -> Dict[str, str]:
        """
        获取客户端信息
        
        Returns:
            dict: 客户端信息字典
        """
        return {
            "name": self.name,
            "max_image_size": str(self.MAX_IMAGE_SIZE),
            "supported_formats": "jpg, png, bmp, tiff, gif"
        } 