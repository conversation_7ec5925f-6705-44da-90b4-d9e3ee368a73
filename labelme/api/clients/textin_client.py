#!/usr/bin/python3
# -*- coding: utf-8 -*-
# Time: 2025-01-03 18:00
# Author: <EMAIL>
# FileName: textin_client.py

"""
TextIn API客户端实现

实现TextIn通用表格识别API的具体调用逻辑。
"""

import os
import json
from typing import Dict, Any

import requests

from .base_client import BaseAPIClient, APIError
from ...utils.log import get_logger

LOGGER = get_logger()


class TextInAPIClient(BaseAPIClient):
    """
    TextIn表格识别API客户端
    
    基于TextIn通用表格识别API v1.0.17实现的客户端。
    支持图像预处理、坐标映射和完整的错误处理。
    """
    
    API_URL = "https://api.textin.com/ai/service/v2/recognize/table/multipage"
    
    def __init__(self, app_id: str, secret_code: str):
        """
        初始化TextIn API客户端
        
        Args:
            app_id: TextIn应用ID
            secret_code: TextIn密钥
            
        Raises:
            APIError: 密钥验证失败时抛出
        """
        super().__init__()
        
        if not app_id or not secret_code:
            raise APIError("TextIn API密钥不能为空", "请提供有效的app_id和secret_code")
        
        self.app_id = app_id
        self.secret_code = secret_code
        
        LOGGER.debug(f"TextInAPIClient初始化完成，app_id: {app_id[:8]}...")
        
    def recognize_table(self, image_path: str) -> Dict[str, Any]:
        """
        调用TextIn表格识别API
        
        Args:
            image_path: 图像文件路径
            
        Returns:
            dict: API响应结果，包含表格识别数据
            
        Raises:
            APIError: API调用失败时抛出
        """
        temp_path = None
        
        try:
            # 1. 验证图像文件
            # self.validate_image_file(image_path)
            
            # 2. 图像预处理
            processed_path, original_size, scale_ratio = self.preprocess_image(image_path)
            if processed_path != image_path:
                temp_path = processed_path
            
            # 3. 读取图像数据
            with open(processed_path, 'rb') as f:
                image_data = f.read()
            
            LOGGER.debug(f"准备调用TextIn API，图像大小: {len(image_data)} bytes")
            
            # 4. 构建请求
            headers = {
                'x-ti-app-id': self.app_id,
                'x-ti-secret-code': self.secret_code,
                'Content-Type': 'application/octet-stream'
            }
            
            # 5. 设置API参数（优化表格识别）
            params = {
                'output_order': 'table_only',  # 只保留表格区域
                'table_type_hint': 'automatic',  # 自动检测表格类型
                'character': 0,  # 不需要字符级详细信息
                'straighten': 0  # 保持原始坐标系
            }
            
            # 6. 发送API请求
            LOGGER.debug(f"发送请求到: {self.API_URL}")
            response = requests.post(
                self.API_URL,
                headers=headers,
                params=params,
                data=image_data,
                timeout=30  # 30秒超时
            )
            
            # 7. 检查HTTP状态码
            if response.status_code != 200:
                raise APIError(
                    f"API请求失败，状态码: {response.status_code}",
                    response.text,
                    response.status_code
                )
            
            # 8. 解析响应JSON
            try:
                result = response.json()
            except json.JSONDecodeError as e:
                raise APIError("API响应JSON解析失败", str(e))
            
            # 9. 检查API错误码
            api_code = result.get('code', 0)
            if api_code != 200:  # TextIn API成功时返回200，不是0
                error_message = result.get('message', '未知错误')
                raise APIError(
                    f"TextIn API错误: {error_message}",
                    self._get_error_description(api_code),
                    api_code
                )
            
            # 10. 验证响应数据结构
            if 'result' not in result:
                raise APIError("API响应缺少result字段")
            
            # 11. 添加元数据到响应
            result['_metadata'] = {
                'original_image_size': original_size,
                'scale_ratio': scale_ratio,
                'api_client': self.name,
                'api_version': 'v1.0.17'
            }
            
            LOGGER.debug(f"TextIn API调用成功，识别到 {len(result['result'].get('pages', []))} 页")
            
            return result
            
        except requests.exceptions.Timeout:
            raise APIError("API请求超时", "请检查网络连接或稍后重试")
        except requests.exceptions.ConnectionError:
            raise APIError("API连接失败", "请检查网络连接")
        except requests.exceptions.RequestException as e:
            raise APIError("API请求异常", str(e))
        except APIError:
            # 重新抛出APIError
            raise
        except Exception as e:
            raise APIError("调用TextIn API时发生未知错误", str(e))
        finally:
            # 12. 清理临时文件
            if temp_path:
                self.cleanup_temp_files(temp_path)
    
    def _get_error_description(self, error_code: int) -> str:
        """
        获取错误码对应的详细描述
        
        Args:
            error_code: TextIn API错误码
            
        Returns:
            str: 错误描述
        """
        error_descriptions = {
            40101: "x-ti-app-id 或 x-ti-secret-code 为空",
            40102: "x-ti-app-id 或 x-ti-secret-code 无效，验证失败",
            40103: "客户端IP不在白名单",
            40003: "余额不足，请充值后再使用",
            40004: "参数错误，请查看技术文档，检查传参",
            40301: "文件类型不支持",
            40302: "上传文件大小不符，文件大小不超过10M",
            40304: "图片尺寸不符，图像宽高须介于20和10000（像素）之间",
            40305: "识别文件未上传",
            40400: "无效的请求链接，请检查链接是否正确",
            30203: "基础服务故障，请稍后重试",
            500: "服务器内部错误"
        }
        
        return error_descriptions.get(error_code, f"未知错误码: {error_code}")
    
    @classmethod
    def from_env(cls) -> 'TextInAPIClient':
        """
        从环境变量创建TextIn API客户端
        
        环境变量：
        - TEXTIN_APP_ID: TextIn应用ID
        - TEXTIN_SECRET_CODE: TextIn密钥
        
        Returns:
            TextInAPIClient: 初始化的客户端实例
            
        Raises:
            APIError: 环境变量未设置时抛出
        """
        app_id = os.environ.get('TEXTIN_APP_ID')
        secret_code = os.environ.get('TEXTIN_SECRET_CODE')
        
        if not app_id or not secret_code:
            raise APIError(
                "未配置TextIn API密钥",
                "请设置环境变量 TEXTIN_APP_ID 和 TEXTIN_SECRET_CODE"
            )
        
        return cls(app_id, secret_code)
    
    def get_client_info(self) -> Dict[str, str]:
        """
        获取TextIn客户端信息
        
        Returns:
            dict: 客户端信息字典
        """
        info = super().get_client_info()
        info.update({
            "api_url": self.API_URL,
            "api_version": "v1.0.17",
            "provider": "TextIn",
            "app_id": self.app_id[:8] + "..." if len(self.app_id) > 8 else self.app_id
        })
        return info 