#!/usr/bin/python3
# -*- coding: utf-8 -*-
# Time: 2025-01-03 18:00
# Author: <EMAIL>
# FileName: __init__.py

"""
API模块 - 第三方API集成功能

提供第三方表格识别API的集成接口，支持多种API服务商的表格识别功能。
采用策略模式设计，便于扩展新的API服务。
"""

from .clients.base_client import BaseAPIClient, APIError
from .clients.textin_client import TextInAPIClient
from .converters.base_converter import BaseAPIResponseConverter, ConvertError
from .converters.textin_converter import TextInResponseConverter

__all__ = [
    'BaseAPIClient',
    'APIError', 
    'TextInAPIClient',
    'BaseAPIResponseConverter',
    'ConvertError',
    'TextInResponseConverter'
] 