#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
表格控制器 - 重构版本
采用拦截式设计，充分利用Canvas现有机制
"""

from qtpy import QtCore, QtGui, QtWidgets
from typing import List, Dict, Optional, Tuple, Any
import copy

from labelme.table_shape import TableCellShape
from labelme.utils.table_analyzer import TableAnalyzer
from labelme.utils.log import get_logger

from labelme.utils.table_merge_split import TableMergeEngine

LOGGER = get_logger()


class TableController:
    """表格标注控制器 - 拦截式设计"""

    def __init__(self, canvas):
        """初始化表格控制器

        Args:
            canvas: 关联的Canvas对象
        """
        self.canvas = canvas

        # === 模式控制 ===
        self.mode = "none"  # "none" | "cell" | "table_build"
        self.internal_flag = None  # 内部处理标记

        # === 表格数据 ===
        self.current_table_grid = None  # 当前分析的表格网格
        self.table_cells = []  # 当前标注的所有单元格
        self.highlighted_cells = []  # 当前高亮的单元格

        # === 表格级别属性 ===
        self.table_type = 1  # 表格类型：0=纯文本, 1=有线表格, 2=无线表格
        self.auto_determine_enabled = True  # 🆕 保存时自动推导表格类型开关（默认启用）

        # === 状态记录 ===
        self.last_analysis_result = None  # 最后一次分析结果

        self.alignment_analysis_result = None    # 存储分析结果
        self.alignment_tolerance = 5.0           # 对齐容差（像素）
        self.alignment_is_valid = False          # 分析结果是否有效

    # ===== 模式管理 =====

    def enter_cell_mode(self):
        """进入单元格标注模式"""
        LOGGER.debug(f"进入单元格模式...")

        self.mode = "cell"
        self.internal_flag = "drawing_cell"

        # 关键：使用Canvas的标准rectangle模式
        self.canvas.createMode = "rectangle"
        self.canvas.setEditing(False)  # 进入CREATE模式

        # 确保_crosshair配置正确
        if 'rectangle' not in self.canvas._crosshair:
            self.canvas._crosshair['rectangle'] = True

        self._clear_temporary_state()
        LOGGER.debug(f"单元格模式设置完成 - Canvas.createMode: {self.canvas.createMode}")

    def enter_table_build_mode(self):
        """进入表格构建模式"""
        LOGGER.debug(f"进入表格构建模式...")

        self.mode = "table_build"
        self.internal_flag = "selecting_area"

        # 同样使用rectangle模式，通过内部标记区分行为
        self.canvas.createMode = "rectangle"
        self.canvas.setEditing(False)  # 进入CREATE模式

        self._clear_temporary_state()
        LOGGER.debug(f"表格构建模式设置完成 - Canvas.createMode: {self.canvas.createMode}")

    def exit_all_modes(self):
        """退出所有表格模式"""
        LOGGER.debug(f"退出表格模式...")

        # 🆕 处理批量选中模式
        if self.mode == "batch_select":
            self.exit_batch_select_mode()
            return  # exit_batch_select_mode已经处理了所有清理工作

        # 处理其他模式
        self.mode = "none"
        self.internal_flag = None

        # 恢复Canvas到EDIT模式
        self.canvas.setEditing(True)

        self._clear_temporary_state()
        self._clear_highlights()
        LOGGER.debug(f"已退出表格标注模式")

    def enter_batch_select_mode(self):
        """进入批量选中模式"""
        LOGGER.debug(f"进入批量选中模式...")

        self.mode = "batch_select"
        self.internal_flag = "selecting_batch"

        # 设置Canvas为编辑模式
        self.canvas.setEditing(True)

        # 🆕 设置十字光标（选区模式）
        self.canvas.setBatchSelectCursor()

        # 设置自己为选择处理器
        self.canvas.selection_handler = self

        # 🔧 修复：安全初始化选择框状态
        try:
            self._batch_select_state = {
                "active": False,
                "start_pos": None,
                "current_pos": None
            }
        except Exception as e:
            LOGGER.error(f"初始化批量选择状态失败: {e}")
            self._batch_select_state = None

        LOGGER.debug(f"批量选中模式设置完成，光标切换为十字选区模式")
        return True

    def exit_batch_select_mode(self):
        """退出批量选中模式"""
        LOGGER.debug(f"退出批量选中模式...")

        self.mode = "none"
        self.internal_flag = None

        # 🆕 恢复默认光标
        self.canvas.restoreCursor()

        # 清除选择处理器
        if self.canvas.selection_handler == self:
            self.canvas.selection_handler = None

        # 清除选择框
        self.canvas._selection_box = None
        self._batch_select_state = None

        LOGGER.debug(f"已退出批量选中模式，光标已恢复")
    def _clear_temporary_state(self):
        """清理临时状态"""
        self.last_analysis_result = None

    def _clear_highlights(self):
        """清除单元格高亮"""
        for cell in self.highlighted_cells:
            cell.selected = False  # 使用Shape基类的选中属性
            # 恢复原始边框颜色（如果有保存的话）
            if hasattr(cell, '_original_line_color'):
                cell.line_color = cell._original_line_color
                delattr(cell, '_original_line_color')
        self.highlighted_cells.clear()
        self.canvas.update()

    # ===== Canvas拦截处理 (核心) =====
    # 添加选择框处理方法（约在第150行之后）：
    def handle_selection_start(self, pos, event):
        """处理选择开始事件"""
        if self.mode != "batch_select":
            return False

        LOGGER.debug(f"开始批量选择，起始位置: ({pos.x():.1f}, {pos.y():.1f})")

        # 记录选择框状态
        self._batch_select_state["active"] = True
        self._batch_select_state["start_pos"] = pos
        self._batch_select_state["current_pos"] = pos

        # 设置Canvas选择框
        self.canvas._selection_box = {"start": pos, "current": pos}

        return True  # 拦截事件

    def handle_selection_move(self, pos, event):
        """处理选择移动事件"""
        if not self._batch_select_state or not self._batch_select_state["active"]:
            return False

        # 更新当前位置
        self._batch_select_state["current_pos"] = pos
        self.canvas._selection_box["current"] = pos

        # 触发重绘
        self.canvas.update()

        return True  # 拦截事件

    def handle_selection_end(self, event):
        """处理选择结束事件"""
        if not self._batch_select_state or not self._batch_select_state["active"]:
            return False

        LOGGER.debug(f"🎯 完成批量选择")

        try:
            # 🔧 修复：安全获取选择框范围
            start_pos = self._batch_select_state.get("start_pos")
            end_pos = self._batch_select_state.get("current_pos")
            
            if not start_pos or not end_pos:
                LOGGER.debug("⚠️ 选择框位置数据无效")
                return self._cleanup_batch_select()

            # 查找相交的TableCellShape
            selected_cells = self._find_cells_in_selection_box(start_pos, end_pos)

            LOGGER.debug(f"🎯 框选到 {len(selected_cells)} 个单元格")

            # 🔧 修复：安全设置选中状态
            if selected_cells:
                # 确保canvas和selectShapes方法存在
                if hasattr(self.canvas, 'selectShapes') and callable(self.canvas.selectShapes):
                    self.canvas.selectShapes(selected_cells)
                else:
                    LOGGER.debug("⚠️ Canvas.selectShapes 方法不可用")
            else:
                # 如果没有框选到单元格，清除选择
                if hasattr(self.canvas, 'deSelectShape') and callable(self.canvas.deSelectShape):
                    self.canvas.deSelectShape()

        except Exception as e:
            LOGGER.error(f"批量选择处理错误: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 🔧 修复：统一的清理流程
            return self._cleanup_batch_select()

    def _cleanup_batch_select(self):
        """统一的批量选择清理流程"""
        try:
            # 清理Canvas状态
            if hasattr(self.canvas, '_selection_box'):
                self.canvas._selection_box = None
            
            # 退出模式
            self.exit_batch_select_mode()
            
            # 通知主窗口
            self._notify_batch_select_completed()
            
            # 触发重绘
            if hasattr(self.canvas, 'update') and callable(self.canvas.update):
                self.canvas.update()
                
        except Exception as e:
            LOGGER.error(f"清理批量选择状态时出错: {e}")
            
        return True  # 总是拦截事件

    def _notify_batch_select_completed(self):
        """通知主窗口批量选中已完成"""
        # 通过Canvas找到主窗口并取消按钮选中状态
        if hasattr(self.canvas, 'parent') and self.canvas.parent():
            main_window = self.canvas.parent()
            # 向上查找主窗口
            while main_window and not hasattr(main_window, 'action_batch_select_mode'):
                main_window = main_window.parent()
            
            if main_window and hasattr(main_window, 'action_batch_select_mode'):
                main_window.action_batch_select_mode.setChecked(False)
                LOGGER.debug("🔄 已取消主窗口批量选中按钮的选中状态")

    def _find_cells_in_selection_box(self, start_pos, end_pos):
        """查找选择框内的TableCellShape"""
        try:
            from qtpy import QtCore

            # 🔧 修复：安全的位置参数检查
            if not start_pos or not end_pos:
                LOGGER.debug("⚠️ 选择框位置参数无效")
                return []
            
            if not hasattr(start_pos, 'x') or not hasattr(start_pos, 'y'):
                LOGGER.debug("⚠️ start_pos 不是有效的点对象")
                return []
                
            if not hasattr(end_pos, 'x') or not hasattr(end_pos, 'y'):
                LOGGER.debug("⚠️ end_pos 不是有效的点对象")
                return []

            # 计算选择框矩形
            x1, y1 = start_pos.x(), start_pos.y()
            x2, y2 = end_pos.x(), end_pos.y()

            # 确保左上角和右下角
            left = min(x1, x2)
            top = min(y1, y2)
            right = max(x1, x2)
            bottom = max(y1, y2)

            # 🔧 修复：检查选择框大小，避免过小的选择框
            width = right - left
            height = bottom - top
            
            if width < 1 or height < 1:
                LOGGER.debug(f"⚠️ 选择框太小: {width}x{height}")
                return []

            selection_rect = QtCore.QRectF(left, top, width, height)

            LOGGER.debug(f"🔍 选择框: ({left:.1f}, {top:.1f}, {width:.1f}, {height:.1f})")

            # 🔧 修复：安全检查canvas和shapes
            if not hasattr(self.canvas, 'shapes'):
                LOGGER.debug("⚠️ Canvas没有shapes属性")
                return []
                
            if not isinstance(self.canvas.shapes, (list, tuple)):
                LOGGER.debug("⚠️ Canvas.shapes不是列表类型")
                return []

            # 查找相交的TableCellShape
            selected_cells = []
            
            for i, shape in enumerate(self.canvas.shapes):
                try:
                    # 🔧 修复：更严格的TableCellShape检查
                    if not hasattr(shape, 'shape_type'):
                        continue
                        
                    if shape.shape_type != 'table_cell':
                        continue
                        
                    # 检查shape是否有有效的boundingRect方法
                    if not hasattr(shape, 'boundingRect') or not callable(shape.boundingRect):
                        LOGGER.debug(f"⚠️ 形状 {i} 没有 boundingRect 方法")
                        continue
                        
                    # 获取shape的边界框
                    shape_rect = shape.boundingRect()
                    
                    # 检查边界框是否有效
                    if not hasattr(shape_rect, 'intersects'):
                        LOGGER.debug(f"⚠️ 形状 {i} 的边界框无效")
                        continue
                        
                    # 检查选择框与形状边界框是否相交
                    if selection_rect.intersects(shape_rect):
                        selected_cells.append(shape)
                        shape_label = getattr(shape, 'label', f'Cell_{i}')
                        LOGGER.debug(f"✅ 选中单元格: {shape_label}")
                        
                except Exception as e:
                    LOGGER.error(f"处理形状 {i} 时出错: {e}")
                    continue

            return selected_cells
            
        except Exception as e:
            LOGGER.error(f"查找选择框内单元格时出错: {e}")
            import traceback
            traceback.print_exc()
            return []
    def handle_before_finalise(self, current_shape) -> Dict[str, Any]:
        """Canvas finalise前的拦截处理

        这是与Canvas交互的核心方法

        Args:
            current_shape: Canvas当前正在完成的形状

        Returns:
            dict: {
                'intercepted': bool,      # 是否拦截了标准处理
                'action': str,           # 执行的动作类型
                'result_data': Any       # 处理结果数据
            }
        """
        LOGGER.debug(f"🔧 [DEBUG] TableController收到形状: {type(current_shape)}")
        LOGGER.debug(f"🔧 [DEBUG] 当前模式: {self.mode}, 标记: {self.internal_flag}")
        LOGGER.debug(f"🔧 [DEBUG] 形状点数: {len(current_shape.points) if hasattr(current_shape, 'points') else 'N/A'}")

        if self.mode == "none":
            LOGGER.debug("⚠️ [WARN] 控制器处于none模式")
            return {'intercepted': False}


        if self.mode == "none":
            return {'intercepted': False}

        LOGGER.debug(f"🔗 拦截Canvas finalise - 模式: {self.mode}, 标记: {self.internal_flag}")

        if self.mode == "cell" and self.internal_flag == "drawing_cell":
            return self._handle_cell_creation(current_shape)

        elif self.mode == "table_build" and self.internal_flag == "selecting_area":
            return self._handle_table_area_selection(current_shape)

        elif self.mode == "line_inference" and self.internal_flag == "drawing_guide_line":
            return self._handle_guide_line_creation(current_shape)

        return {'intercepted': False}

    def handle_after_finalise(self):
        """Canvas finalise后的通知处理"""
        if self.mode == "none":
            return

        LOGGER.debug(f"🔗 Canvas finalise完成 - 模式: {self.mode}")

        # 可以在这里做一些后续处理，比如：
        # - 更新统计信息
        # - 触发界面刷新信号
        # - 保存状态等

    # ===== 单元格创建处理 =====
    def _handle_cell_creation(self, rect_shape) -> Dict[str, Any]:
        """处理单元格创建 - 桥梁方案：调用CreateCellCommand"""
        try:
            LOGGER.info(f"🔗 _handle_cell_creation 桥梁调用: {type(rect_shape)}, ID: {id(rect_shape)}")

            # 检查形状有效性
            if not rect_shape or not hasattr(rect_shape, 'points') or len(rect_shape.points) < 2:
                LOGGER.error(f"无效的矩形形状")
                return {'intercepted': False}

            # 🔗 桥梁方案：通过CreateCellCommand创建
            if hasattr(self.canvas, 'history_manager') and self.canvas.history_manager:
                from labelme.commands import CreateCellCommand

                create_cmd = CreateCellCommand(
                    canvas=self.canvas,
                    points=rect_shape.points[:],  # 拷贝点列表
                    label=f"cell_{len(self.table_cells) + 1}",
                    table_properties={}
                )

                # 执行命令
                success = self.canvas.history_manager.execute_command(create_cmd)

                if success:
                    LOGGER.info(f"✅ 通过CreateCellCommand成功创建单元格")
                    return {
                        'intercepted': True,
                        'action': 'create_table_cell',
                        'result_data': {'table_cell_shape': create_cmd.created_cell}
                    }
                else:
                    LOGGER.error(f"CreateCellCommand执行失败")
                    return {'intercepted': False}
            else:
                LOGGER.warning(f"无history_manager，无法使用命令系统")
                return {'intercepted': False}

        except Exception as e:
            LOGGER.error(f"桥梁调用失败: {e}")
            return {'intercepted': False}
    def _convert_rect_to_table_cell(self, rect_shape) -> TableCellShape:
        """将Rectangle形状转换为TableCellShape"""

        # 创建TableCellShape
        cell = TableCellShape(label=f"cell_{len(self.table_cells) + 1}")

        # 🔧 关键修复：将2点矩形转换为4点矩形
        if len(rect_shape.points) == 2:
            # Canvas rectangle模式创建的是2个对角点
            p1, p2 = rect_shape.points[0], rect_shape.points[1]
            x1, y1 = p1.x(), p1.y()
            x2, y2 = p2.x(), p2.y()

            # 确保坐标顺序正确
            left = min(x1, x2)
            right = max(x1, x2)
            top = min(y1, y2)
            bottom = max(y1, y2)

            # 创建4个顶点（顺时针）
            from PyQt5.QtCore import QPointF
            cell.points = [
                QPointF(left, top),  # 左上
                QPointF(right, top),  # 右上
                QPointF(right, bottom),  # 右下
                QPointF(left, bottom)  # 左下
            ]

            LOGGER.debug(f"🔧 [CONVERT] 2点转4点: ({left},{top}) -> ({right},{bottom})")
            LOGGER.debug(f"🔧 [CONVERT] 4个顶点: {[(p.x(), p.y()) for p in cell.points]}")
        else:
            # 如果已经有4个点，直接复制
            cell.points = rect_shape.points.copy()

        cell.shape_type = "table_cell"

        # 设置默认样式
        cell.fill = True
        cell.fill_color = QtGui.QColor(255, 255, 255, 64)  # 半透明白色填充
        cell.line_color = QtGui.QColor(0, 0, 0, 255)  # 黑色边框

        # 设置表格特有属性
        cell.set_table_type(1)  # 默认有线表格
        cell.set_cell_text("")  # 空文本
        cell.set_confirmed(False)  # 未确认状态

        # 设置默认边框样式
        cell.set_border_style(top=1, right=1, bottom=1, left=1)

        # 闭合形状
        cell.close()

        return cell
    # ===== 表格构建处理 =====

    def _handle_table_area_selection(self, rect_shape) -> Dict[str, Any]:
        """处理表格区域选择

        无论是否找到单元格，都要保留表格区域框
        """
        try:
            LOGGER.debug("🔍 处理表格区域选择...")

            # 🔧 新增：将框选区域转换为表格边界框并保留
            table_boundary = self._create_table_boundary_shape(rect_shape)

            # 1. 找到选择区域内的单元格
            selected_cells = self._find_cells_in_selection(rect_shape)

            if len(selected_cells) == 0:
                LOGGER.debug("⚠️ 选择区域内没有找到单元格，但保留表格区域框")
                return {
                    'intercepted': True,
                    'action': 'create_table_boundary',  # 新动作
                    'result_data': {
                        'table_boundary_shape': table_boundary,
                        'message': '表格区域已创建，请在此区域内绘制单元格'
                    }
                }

            LOGGER.debug(f"🔍 找到 {len(selected_cells)} 个单元格")

            # 2-5. 原有的分析逻辑
            self._highlight_selected_cells(selected_cells)
            analysis_result = TableAnalyzer.analyze_cells_to_grid(selected_cells)
            self._apply_analysis_result(analysis_result)
            self.current_table_grid = analysis_result
            self.last_analysis_result = analysis_result

            LOGGER.debug(f"✅ 表格分析完成: {analysis_result['grid']['rows']}行 x {analysis_result['grid']['cols']}列")

            return {
                'intercepted': True,
                'action': 'analyze_table_area',
                'result_data': {
                    'table_boundary_shape': table_boundary,  # 同时包含边界框
                    'analysis_result': analysis_result,
                    'selected_cells': selected_cells,
                    'grid_size': (analysis_result['grid']['rows'], analysis_result['grid']['cols'])
                }
            }

        except Exception as e:
            LOGGER.error(f"表格分析失败: {e}")
            return {
                'intercepted': True,
                'action': 'analysis_failed',
                'result_data': {'error': str(e)}
            }

    def _create_table_boundary_shape(self, rect_shape):
        """创建表格边界框形状（兼容Shape类的rectangle格式）"""
        from labelme.shape import Shape

        # 创建边界框形状
        boundary = Shape(label="表格区域", shape_type="rectangle")

        # 🔧 修复：Shape类的rectangle只需要2个对角点
        if len(rect_shape.points) == 2:
            # 直接使用原始的2个点
            boundary.points = rect_shape.points.copy()
        else:
            # 如果有4个点，提取对角点
            boundary.points = [rect_shape.points[0], rect_shape.points[2]]

        # 设置表格边界的样式（虚线边框）
        from PyQt5.QtGui import QColor
        boundary.line_color = QColor(0, 0, 255, 180)  # 蓝色边框
        boundary.fill_color = QColor(0, 0, 255, 20)  # 极淡蓝色填充
        boundary.fill = True

        # 🔧 关键：不要调用close()，让Shape类自己处理rectangle
        # boundary.close()  # 移除这行

        LOGGER.debug(f"🔧 [BOUNDARY] 创建表格边界框，points数量: {len(boundary.points)}")
        LOGGER.debug(f"🔧 [BOUNDARY] 点坐标: {[(p.x(), p.y()) for p in boundary.points]}")

        return boundary
    def _find_cells_in_selection(self, selection_rect) -> List[TableCellShape]:
        """在选择矩形中找到表格单元格"""

        # 计算选择区域的边界框
        if len(selection_rect.points) < 2:
            return []

        x_coords = [p.x() for p in selection_rect.points]
        y_coords = [p.y() for p in selection_rect.points]

        sel_x1, sel_x2 = min(x_coords), max(x_coords)
        sel_y1, sel_y2 = min(y_coords), max(y_coords)

        selected_cells = []

        # 遍历Canvas中的所有形状
        for shape in self.canvas.shapes:
            # 检查是否是表格单元格
            if not (hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell'):
                continue

            # 检查是否与选择区域相交
            if self._is_cell_in_selection(shape, sel_x1, sel_y1, sel_x2, sel_y2):
                selected_cells.append(shape)

        return selected_cells

    def _is_cell_in_selection(self, cell: TableCellShape,
                              sel_x1: float, sel_y1: float,
                              sel_x2: float, sel_y2: float) -> bool:
        """判断单元格是否在选择区域内"""

        if not cell.points or len(cell.points) < 2:
            return False

        # 计算单元格边界框
        cell_x_coords = [p.x() for p in cell.points]
        cell_y_coords = [p.y() for p in cell.points]

        cell_x1, cell_x2 = min(cell_x_coords), max(cell_x_coords)
        cell_y1, cell_y2 = min(cell_y_coords), max(cell_y_coords)

        # 计算重叠区域
        overlap_x1 = max(sel_x1, cell_x1)
        overlap_y1 = max(sel_y1, cell_y1)
        overlap_x2 = min(sel_x2, cell_x2)
        overlap_y2 = min(sel_y2, cell_y2)

        # 检查是否有有效重叠
        if overlap_x1 >= overlap_x2 or overlap_y1 >= overlap_y2:
            return False

        # 计算重叠比例
        overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
        cell_area = (cell_x2 - cell_x1) * (cell_y2 - cell_y1)

        if cell_area <= 0:
            return False

        overlap_ratio = overlap_area / cell_area

        # 重叠超过30%才算选中
        return overlap_ratio > 0.3

    def _highlight_selected_cells(self, cells: List[TableCellShape]):
        """高亮选中的单元格"""

        # 清除之前的高亮
        self._clear_highlights()

        # 使用Shape的标准选中机制
        for cell in cells:
            cell.selected = True  # 使用Shape基类的选中属性
            # 可选：临时修改边框颜色加强视觉效果
            cell._original_line_color = cell.line_color  # 保存原始颜色
            cell.line_color = QtGui.QColor(255, 0, 0, 255)  # 红色高亮

        self.highlighted_cells = cells.copy()
        self.canvas.update()


    def _on_merge_cells_requested(self, selected_data):
        """处理来自结构视图的合并请求 - 支持空单元格"""
        LOGGER.debug(f"🔗 收到合并请求，选中数据: {len(selected_data)} 个单元格")

        try:
            # 1. 计算选中的逻辑区域
            logical_region = self._calculate_logical_region(selected_data)
            LOGGER.debug(f"🔧 逻辑区域: {logical_region}")

            # 2. 查找该区域内的现有物理单元格
            existing_cells = self._find_cells_in_logical_region(logical_region)
            LOGGER.debug(f"🔧 区域内现有单元格: {len(existing_cells)} 个")

            if len(existing_cells) == 0:
                # 没有物理单元格，提示用户先绘制
                LOGGER.debug("⚠️ 选中区域内没有物理单元格，请先在该区域绘制一个单元格")
                return

            elif len(existing_cells) == 1:
                # 有一个物理单元格，扩展其逻辑位置
                success = self._expand_cell_to_logical_region(existing_cells[0], logical_region)
                if success:
                    LOGGER.debug("✅ 单元格逻辑位置扩展成功")
                    self._refresh_structure_widget()
                else:
                    LOGGER.error(f"单元格逻辑位置扩展失败")

            else:
                # 有多个物理单元格，进行传统合并
                success = self.merge_selected_cells_by_data(existing_cells)
                if success:
                    LOGGER.debug("✅ 多个单元格合并成功")
                    self._refresh_structure_widget()
                else:
                    LOGGER.error(f"多个单元格合并失败")

        except Exception as e:
            LOGGER.error(f"处理合并请求错误: {e}")
            import traceback
            traceback.print_exc()

    def _calculate_logical_region(self, selected_data):
        """计算选中的逻辑区域"""
        if not selected_data:
            return None

        rows = [data['row'] for data in selected_data]
        cols = [data['col'] for data in selected_data]

        return {
            'start_row': min(rows),
            'end_row': max(rows),
            'start_col': min(cols),
            'end_col': max(cols)
        }

    def _find_cells_in_logical_region(self, logical_region):
        """查找逻辑区域内的现有物理单元格"""
        cells_in_region = []

        for cell in self.table_cells:
            if hasattr(cell, 'get_logical_location'):
                lloc = cell.get_logical_location()

                # 检查单元格是否与逻辑区域有交集
                if (lloc.get('start_row') <= logical_region['end_row'] and
                        lloc.get('end_row') >= logical_region['start_row'] and
                        lloc.get('start_col') <= logical_region['end_col'] and
                        lloc.get('end_col') >= logical_region['start_col']):
                    cells_in_region.append(cell)

        return cells_in_region

    def _expand_cell_to_logical_region(self, cell, logical_region):
        """将单个物理单元格的逻辑位置扩展到指定区域"""
        try:
            # 存储到撤销栈
            self.canvas.storeShapes()

            # 更新单元格的逻辑位置
            cell.set_logical_location(
                start_row=logical_region['start_row'],
                end_row=logical_region['end_row'],
                start_col=logical_region['start_col'],
                end_col=logical_region['end_col']
            )

            # 更新标签以反映新的逻辑位置
            if hasattr(cell, 'label'):
                row_span = logical_region['end_row'] - logical_region['start_row'] + 1
                col_span = logical_region['end_col'] - logical_region['start_col'] + 1
                if row_span > 1 or col_span > 1:
                    cell.label = f"merged_{logical_region['start_row']}_{logical_region['start_col']}({row_span}x{col_span})"
                else:
                    cell.label = f"cell_{logical_region['start_row']}_{logical_region['start_col']}"

            # 标记为合并单元格
            if hasattr(cell, 'table_properties'):
                cell.table_properties['is_merged'] = row_span > 1 or col_span > 1
                cell.table_properties['cell_id'] = f"merged_{logical_region['start_row']}_{logical_region['start_col']}"

            # 更新显示
            self.canvas.update()

            LOGGER.debug(
                f"🔧 单元格逻辑位置已扩展: ({logical_region['start_row']}, {logical_region['start_col']}) -> ({logical_region['end_row']}, {logical_region['end_col']})")
            return True

        except Exception as e:
            LOGGER.error(f"扩展单元格逻辑位置错误: {e}")
            return False
    def _on_split_cells_requested(self, selected_data):
        """处理来自结构视图的拆分请求"""
        LOGGER.debug(f"🔗 收到拆分请求，选中数据: {len(selected_data)} 个单元格")

        try:
            if len(selected_data) != 1:
                LOGGER.error(f"拆分操作只能选择一个合并单元格")
                return

            cell_data = selected_data[0]
            # 找到对应的物理单元格
            physical_cell = self._find_physical_cell_by_logical_data(cell_data)

            if physical_cell:
                success = self.split_merged_cell(physical_cell)
                if success:
                    LOGGER.debug("✅ 拆分成功")
                    # 刷新结构视图
                    self._refresh_structure_widget()
                else:
                    LOGGER.error(f"拆分失败")
            else:
                LOGGER.error(f"未找到对应的物理单元格")

        except Exception as e:
            LOGGER.error(f"处理拆分请求错误: {e}")

    def _find_physical_cells_by_logical_data(self, logical_data_list):
        """根据逻辑数据找到对应的物理单元格"""
        physical_cells = []

        # 🔍 调试：检查table_cells和canvas.shapes的引用一致性
        from labelme.table_shape import is_table_cell
        canvas_table_cells = [s for s in self.canvas.shapes if is_table_cell(s)]

        LOGGER.info(f"🔍 查找物理单元格:")
        LOGGER.info(f"  - self.table_cells数量: {len(self.table_cells)}")
        LOGGER.info(f"  - canvas中表格单元格数量: {len(canvas_table_cells)}")
        LOGGER.info(f"  - self.table_cells对象ID: {[id(cell) for cell in self.table_cells[:3]]}")  # 只显示前3个
        LOGGER.info(f"  - canvas.shapes对象ID: {[id(cell) for cell in canvas_table_cells[:3]]}")  # 只显示前3个

        for data in logical_data_list:
            row = data['row']
            col = data['col']

            # 在table_cells中查找对应的单元格
            found_in_table_cells = False
            for cell in self.table_cells:
                if hasattr(cell, 'get_logical_location'):
                    lloc = cell.get_logical_location()
                    if (lloc.get('start_row') <= row <= lloc.get('end_row') and
                            lloc.get('start_col') <= col <= lloc.get('end_col')):
                        physical_cells.append(cell)
                        found_in_table_cells = True
                        LOGGER.info(f"  ✅ 在table_cells中找到({row},{col}): {id(cell)}")
                        break

            if not found_in_table_cells:
                LOGGER.info(f"  ❌ 在table_cells中未找到({row},{col})")

                # 🔍 尝试在canvas.shapes中查找
                for cell in canvas_table_cells:
                    if hasattr(cell, 'get_logical_location'):
                        lloc = cell.get_logical_location()
                        if (lloc.get('start_row') <= row <= lloc.get('end_row') and
                                lloc.get('start_col') <= col <= lloc.get('end_col')):
                            LOGGER.info(f"  🔍 但在canvas.shapes中找到了({row},{col}): {id(cell)}")
                            break

        return physical_cells
    def _find_physical_cell_by_logical_data(self, logical_data):
        """根据逻辑数据找到对应的物理单元格"""
        cells = self._find_physical_cells_by_logical_data([logical_data])
        return cells[0] if cells else None

    def merge_selected_cells_by_data(self, physical_cells):
        """根据物理单元格数据进行合并"""
        if len(physical_cells) < 2:
            return False

        # 复用现有的合并逻辑
        return self.merge_selected_cells_from_list(physical_cells)



    def merge_selected_cells_from_list(self, cells):
        """合并指定的单元格列表（支持撤销）"""
        if len(cells) < 2:
            LOGGER.error(f"需要选择至少2个单元格进行合并")
            return False

        # 🔧 检查是否有历史管理器支持命令系统
        if (hasattr(self.canvas, 'main_window') and
                hasattr(self.canvas.main_window, 'history_manager')):

            history_manager = self.canvas.main_window.history_manager

            # 🔧 使用命令系统执行合并
            try:
                from labelme.commands.cell_commands import MergeCellCommand

                merge_command = MergeCellCommand(
                    canvas=self.canvas,
                    table_controller=self,
                    cells_to_merge=cells,
                    description=f"合并 {len(cells)} 个单元格"
                )

                success = history_manager.execute_command(merge_command)

                if success:
                    LOGGER.debug(f"✅ 成功合并 {len(cells)} 个单元格")
                    return True
                else:
                    LOGGER.error("❌ 合并命令执行失败")
                    return False

            except Exception as e:
                LOGGER.error(f"❌ 命令系统执行异常: {e}")
                import traceback
                traceback.print_exc()
                return False
        else:
            # 🔧 回退：没有命令系统时使用原有逻辑（但记录警告）
            LOGGER.warning("⚠️ 未找到命令系统，使用传统方式（不支持撤销）")

            # 原有的合并逻辑（为了兼容性保留）
            if not TableMergeEngine.are_cells_adjacent(cells):
                LOGGER.error("❌ 选中的单元格不相邻，无法合并")
                return False

            merged_bbox = TableMergeEngine.calculate_merge_bbox(cells)
            merged_logical = TableMergeEngine.calculate_merge_logical_position(cells)

            merged_cell = TableMergeEngine.create_merged_cell_data(cells, merged_bbox, merged_logical)

            self.canvas.storeShapes()

            for cell in cells:
                if cell in self.table_cells:
                    self.table_cells.remove(cell)
                if cell in self.canvas.shapes:
                    self.canvas.shapes.remove(cell)
                if cell in self.canvas.visible:
                    del self.canvas.visible[cell]

            self.table_cells.append(merged_cell)
            self.canvas.shapes.append(merged_cell)
            self.canvas.visible[merged_cell] = True

            self.canvas.selectedShapes = []
            self.canvas.update()
            LOGGER.debug(f"✅ 成功合并 {len(cells)} 个单元格（传统模式）")
            return True

    def split_merged_cell(self, merged_cell):
        """拆分合并的单元格"""
        if not hasattr(merged_cell, 'get_logical_location'):
            LOGGER.error("❌ 单元格缺少逻辑位置信息")
            return False

        # 验证是否可拆分
        from labelme.utils.table_merge_split import TableSplitEngine
        if not TableSplitEngine.can_split_cell(merged_cell):
            LOGGER.error("❌ 该单元格不是合并单元格，无法拆分")
            return False

        # 检查命令系统支持
        if not (hasattr(self.canvas, 'main_window') and
                hasattr(self.canvas.main_window, 'history_manager')):
            LOGGER.error("❌ 命令系统不可用，无法执行拆分操作")
            return False

        history_manager = self.canvas.main_window.history_manager

        try:
            from labelme.commands.cell_commands import SplitCellCommand

            split_command = SplitCellCommand(
                canvas=self.canvas,
                table_controller=self,
                merged_cell=merged_cell,
                description=f"拆分单元格: {merged_cell.label}"
            )

            success = history_manager.execute_command(split_command)

            if success:
                LOGGER.debug(f"✅ 成功拆分单元格: {merged_cell.label}")
                return True
            else:
                LOGGER.error("❌ 拆分命令执行失败")
                return False

        except Exception as e:
            LOGGER.error(f"❌ 拆分操作异常: {e}")
            import traceback
            traceback.print_exc()
            return False
    def _apply_analysis_result(self, analysis_result: Dict):
        """将分析结果应用到单元格对象 - 修复版"""

        cell_positions = analysis_result['cell_positions']

        # 存储到撤销栈（labelme方式）
        self.canvas.storeShapes()

        for cell_id, position_info in cell_positions.items():
            cell = position_info['cell_object']

            # 🔧 修复：获取完整的逻辑位置信息
            start_row = position_info['row']
            start_col = position_info['col']
            row_span = position_info['row_span']
            col_span = position_info['col_span']

            # 🔧 关键修复：计算end_row和end_col
            end_row = start_row + row_span - 1
            end_col = start_col + col_span - 1

            # 🔧 调试输出：验证应用过程
            if hasattr(self.__class__, 'debug_output') and self.__class__.debug_output:
                LOGGER.debug(f"🔧 [APPLY] Cell {str(cell_id)[-4:]}: "
                      f"设置逻辑位置 ({start_row},{start_col},{end_row},{end_col}) "
                      f"跨度({row_span}x{col_span})")

            # 🔧 正确设置逻辑位置，包含跨度信息
            if hasattr(cell, 'set_logical_location'):
                cell.set_logical_location(
                    start_row=start_row,
                    end_row=end_row,
                    start_col=start_col,
                    end_col=end_col
                )
            else:
                LOGGER.debug(f"⚠️ Cell {cell_id} 没有 set_logical_location 方法")

            # 更新单元格标签以反映合并状态
            if hasattr(cell, 'label'):
                if row_span > 1 or col_span > 1:
                    cell.label = f"merged_{start_row}_{start_col}({row_span}x{col_span})"
                else:
                    cell.label = f"cell_{start_row}_{start_col}"

            # 🔧 设置合并标识（可选，用于后续识别）
            if hasattr(cell, 'table_properties'):
                cell.table_properties['is_merged'] = row_span > 1 or col_span > 1
                cell.table_properties['row_span'] = row_span
                cell.table_properties['col_span'] = col_span

        # 更新显示
        self.canvas.update()

        # 🔧 关键：刷新结构视图，确保界面显示更新
        if hasattr(self, 'structure_widget'):
            self._refresh_structure_widget()

        LOGGER.debug(f"✅ 分析结果已应用到 {len(cell_positions)} 个单元格")
    # ===== 边框样式管理 =====

    def set_cell_border_style(self, cell: TableCellShape, top=None, right=None, bottom=None, left=None):
        """设置单个单元格的边框样式

        Args:
            cell: 目标单元格
            top/right/bottom/left: 边框样式，None表示不修改，0=虚线，1=实线
        """
        if not isinstance(cell, TableCellShape):
            LOGGER.error(f"不是有效的表格单元格")
            return False

        current_style = cell.get_border_style()

        # 只修改指定的边框
        new_style = {
            'top': top if top is not None else current_style['top'],
            'right': right if right is not None else current_style['right'],
            'bottom': bottom if bottom is not None else current_style['bottom'],
            'left': left if left is not None else current_style['left']
        }

        cell.set_border_style(**new_style)

        # 🆕 相邻单元格边框联动调整
        self._update_adjacent_borders(cell, new_style)

        self.canvas.update()
        LOGGER.debug(f"✅ 已更新单元格边框样式: {new_style}")
        return True

    def toggle_cell_border(self, cell: TableCellShape, border_side: str):
        """切换单个单元格的某个边框（0↔1）

        Args:
            cell: 目标单元格
            border_side: 'top'|'right'|'bottom'|'left'
        """
        if border_side not in ['top', 'right', 'bottom', 'left']:
            LOGGER.error(f"无效的边框位置: {border_side}")
            return False

        current_style = cell.get_border_style()
        current_value = current_style[border_side]
        new_value = 1 - current_value  # 0→1, 1→0

        kwargs = {border_side: new_value}
        return self.set_cell_border_style(cell, **kwargs)

    def _update_adjacent_borders(self, cell: TableCellShape, updated_border_style: dict):
        """更新相邻单元格的边框以保持一致性

        Args:
            cell: 被修改的单元格
            updated_border_style: 新的边框样式
        """
        try:
            # 获取当前单元格的逻辑位置
            logical_location = cell.get_logical_location()
            if not logical_location:
                LOGGER.warning("单元格缺少逻辑位置信息，跳过相邻边框更新")
                return

            # 查找相邻单元格并更新对应边框
            adjacent_updates = self._find_adjacent_border_updates(cell, updated_border_style)

            for adjacent_cell, border_updates in adjacent_updates:
                if border_updates:
                    # 获取相邻单元格当前边框样式
                    current_border = adjacent_cell.get_border_style()
                    # 应用更新
                    current_border.update(border_updates)
                    adjacent_cell.set_border_style(**current_border)
                    LOGGER.debug(f"已更新相邻单元格边框: {border_updates}")

        except Exception as e:
            LOGGER.error(f"更新相邻边框失败: {e}")

    def _find_adjacent_border_updates(self, cell: TableCellShape, updated_border_style: dict) -> List[Tuple]:
        """查找需要更新边框的相邻单元格

        Args:
            cell: 当前单元格
            updated_border_style: 当前单元格的新边框样式

        Returns:
            List[Tuple[TableCellShape, dict]]: (相邻单元格, 需要更新的边框样式)
        """
        adjacent_updates = []

        try:
            # 获取当前单元格的逻辑位置
            logical_location = cell.get_logical_location()
            current_row = logical_location['start_row']
            current_col = logical_location['start_col']
            current_end_row = logical_location['end_row']
            current_end_col = logical_location['end_col']

            # 遍历所有单元格查找相邻的
            for other_cell in self.table_cells:
                if other_cell == cell:
                    continue

                other_location = other_cell.get_logical_location()
                if not other_location:
                    continue

                other_row = other_location['start_row']
                other_col = other_location['start_col']
                other_end_row = other_location['end_row']
                other_end_col = other_location['end_col']

                border_updates = {}

                # 计算当前单元格和相邻单元格的尺寸
                current_height = current_end_row - current_row + 1
                current_width = current_end_col - current_col + 1
                other_height = other_end_row - other_row + 1
                other_width = other_end_col - other_col + 1

                # 🔧 严格方向限制：水平边框只影响水平边框，垂直边框只影响垂直边框

                # 水平方向的边框同步（left ↔ right）
                horizontal_updates = {}

                # 检查右相邻：当前单元格的右边框 → 相邻单元格的左边框
                if (current_end_col + 1 == other_col and
                    self._ranges_overlap(current_row, current_end_row, other_row, other_end_row)):
                    if 'right' in updated_border_style:
                        # 检查边长度，如果相邻边更长则跳过
                        if other_height <= current_height:
                            horizontal_updates['left'] = updated_border_style['right']
                        else:
                            LOGGER.debug(f"跳过右相邻边框同步：相邻单元格高度({other_height}) > 当前单元格高度({current_height})")

                # 检查左相邻：当前单元格的左边框 → 相邻单元格的右边框
                if (other_end_col + 1 == current_col and
                    self._ranges_overlap(current_row, current_end_row, other_row, other_end_row)):
                    if 'left' in updated_border_style:
                        # 检查边长度，如果相邻边更长则跳过
                        if other_height <= current_height:
                            horizontal_updates['right'] = updated_border_style['left']
                        else:
                            LOGGER.debug(f"跳过左相邻边框同步：相邻单元格高度({other_height}) > 当前单元格高度({current_height})")

                # 垂直方向的边框同步（top ↔ bottom）
                vertical_updates = {}

                # 检查下相邻：当前单元格的下边框 → 相邻单元格的上边框
                if (current_end_row + 1 == other_row and
                    self._ranges_overlap(current_col, current_end_col, other_col, other_end_col)):
                    if 'bottom' in updated_border_style:
                        # 检查边长度，如果相邻边更长则跳过
                        if other_width <= current_width:
                            vertical_updates['top'] = updated_border_style['bottom']
                        else:
                            LOGGER.debug(f"跳过下相邻边框同步：相邻单元格宽度({other_width}) > 当前单元格宽度({current_width})")

                # 检查上相邻：当前单元格的上边框 → 相邻单元格的下边框
                if (other_end_row + 1 == current_row and
                    self._ranges_overlap(current_col, current_end_col, other_col, other_end_col)):
                    if 'top' in updated_border_style:
                        # 检查边长度，如果相邻边更长则跳过
                        if other_width <= current_width:
                            vertical_updates['bottom'] = updated_border_style['top']
                        else:
                            LOGGER.debug(f"跳过上相邻边框同步：相邻单元格宽度({other_width}) > 当前单元格宽度({current_width})")

                # 🔧 关键：严格分离水平和垂直边框更新
                # 只有在明确的方向性操作时才应用对应的边框更新
                has_horizontal_input = 'left' in updated_border_style or 'right' in updated_border_style
                has_vertical_input = 'top' in updated_border_style or 'bottom' in updated_border_style

                if has_horizontal_input and horizontal_updates:
                    border_updates.update(horizontal_updates)
                    LOGGER.debug(f"应用水平边框同步: {horizontal_updates}")

                if has_vertical_input and vertical_updates:
                    border_updates.update(vertical_updates)
                    LOGGER.debug(f"应用垂直边框同步: {vertical_updates}")

                if border_updates:
                    adjacent_updates.append((other_cell, border_updates))

        except Exception as e:
            LOGGER.error(f"查找相邻单元格失败: {e}")

        return adjacent_updates

    def _ranges_overlap(self, start1: int, end1: int, start2: int, end2: int) -> bool:
        """检查两个范围是否有重叠

        Args:
            start1, end1: 第一个范围
            start2, end2: 第二个范围

        Returns:
            bool: 是否有重叠
        """
        return not (end1 < start2 or end2 < start1)

    def set_selected_cells_border_style(self, top=None, right=None, bottom=None, left=None):
        """批量设置当前高亮单元格的边框样式，包括相邻边联动"""
        if not self.highlighted_cells:
            LOGGER.error(f"没有选中的单元格")
            return False

        LOGGER.debug(f"🔧 批量设置边框样式，选中单元格数量: {len(self.highlighted_cells)}")

        # 🔧 修复：批量操作时避免连锁反应
        # 第一步：收集所有需要更新的边框（包括相邻边框）
        all_border_updates = {}  # {cell: {border_side: value}}

        # 先为所有选中的单元格收集直接的边框更新
        for cell in self.highlighted_cells:
            if cell not in all_border_updates:
                all_border_updates[cell] = {}

            if top is not None:
                all_border_updates[cell]['top'] = top
            if right is not None:
                all_border_updates[cell]['right'] = right
            if bottom is not None:
                all_border_updates[cell]['bottom'] = bottom
            if left is not None:
                all_border_updates[cell]['left'] = left

        # 第二步：为每个选中单元格收集相邻边框更新
        for cell in self.highlighted_cells:
            cell_updates = all_border_updates[cell]
            if cell_updates:
                LOGGER.debug(f"🔍 处理单元格 {cell.label if hasattr(cell, 'label') else 'Unknown'}，更新: {cell_updates}")

                # 查找相邻单元格需要更新的边框
                adjacent_updates = self._find_adjacent_border_updates(cell, cell_updates)

                for adjacent_cell, border_updates in adjacent_updates:
                    # 只有当相邻单元格不在选中列表中时，才添加到更新列表
                    if adjacent_cell not in self.highlighted_cells:
                        if adjacent_cell not in all_border_updates:
                            all_border_updates[adjacent_cell] = {}

                        # 🔧 关键调试：检查是否意外添加了上下边框
                        for border_side, value in border_updates.items():
                            if border_side in ['top', 'bottom'] and 'right' in cell_updates and 'left' in cell_updates:
                                LOGGER.debug(f"⚠️ 意外的上下边框更新！单元格: {adjacent_cell.label if hasattr(adjacent_cell, 'label') else 'Unknown'}, 边框: {border_side}={value}")
                            elif border_side in ['left', 'right'] and 'top' in cell_updates and 'bottom' in cell_updates:
                                LOGGER.debug(f"⚠️ 意外的左右边框更新！单元格: {adjacent_cell.label if hasattr(adjacent_cell, 'label') else 'Unknown'}, 边框: {border_side}={value}")

                        all_border_updates[adjacent_cell].update(border_updates)
                        LOGGER.debug(f"收集相邻边框更新: 单元格 {adjacent_cell.label if hasattr(adjacent_cell, 'label') else 'Unknown'} → {border_updates}")

        # 第三步：统一应用所有边框更新（不触发进一步的相邻边框同步）
        for cell, border_updates in all_border_updates.items():
            if border_updates:
                current_border = cell.get_border_style()
                current_border.update(border_updates)
                cell.set_border_style(**current_border)
                LOGGER.debug(f"应用边框更新到单元格: {border_updates}")

        self.canvas.update()

        # 🔧 修复：添加逻辑结构更新，传递变更信号（避免全量刷新）
        if hasattr(self, 'structure_widget'):
            # 构建边框变更信息
            border_changes = {}
            if top is not None:
                border_changes['top'] = top
            if right is not None:
                border_changes['right'] = right
            if bottom is not None:
                border_changes['bottom'] = bottom
            if left is not None:
                border_changes['left'] = left

            # 使用局部更新，只传递变更的边框信息
            if border_changes:
                self._update_structure_border_styles_only(list(all_border_updates.keys()), border_changes)
                LOGGER.debug(f"🔧 已更新逻辑结构边框样式: {border_changes}")

        LOGGER.debug(f"✅ 已批量更新 {len(all_border_updates)} 个单元格的边框样式（避免连锁反应）")
        return True

    def copy_border_style(self, source_cell: TableCellShape, target_cells: List[TableCellShape]):
        """复制边框样式到其他单元格"""
        if not isinstance(source_cell, TableCellShape):
            LOGGER.error(f"源单元格无效")
            return False

        source_style = source_cell.get_border_style()

        for target_cell in target_cells:
            if isinstance(target_cell, TableCellShape):
                target_cell.set_border_style(**source_style)

        self.canvas.update()
        LOGGER.debug(f"✅ 已复制边框样式到 {len(target_cells)} 个单元格")
        return True

    # ===== 数据获取接口 =====

    def get_current_table_grid(self) -> Optional[Dict]:
        """获取当前表格网格数据"""
        return copy.deepcopy(self.current_table_grid) if self.current_table_grid else None

    def get_table_cells(self) -> List[TableCellShape]:
        """获取所有表格单元格"""
        return self.table_cells.copy()

    def add_cell_shape(self, cell: TableCellShape):
        """添加单元格到表格控制器
        
        Args:
            cell: 要添加的单元格
        """
        if cell not in self.table_cells:
            self.table_cells.append(cell)
            LOGGER.debug(f"✅ 添加单元格到TableController: {cell.label}")
        else:
            LOGGER.debug(f"⚠️ 单元格已存在: {cell.label}")

    def get_highlighted_cells(self) -> List[TableCellShape]:
        """获取当前高亮的单元格"""
        return self.highlighted_cells.copy()

    def clear_table_data(self):
        """清除表格数据"""
        self.current_table_grid = None
        self.table_cells.clear()
        self._clear_highlights()
        LOGGER.debug("🗑️ 表格数据已清除")

    def get_table_stats(self) -> Dict:
        """获取表格统计信息"""
        stats = {
            'total_cells': len(self.table_cells),
            'highlighted_cells': len(self.highlighted_cells),
            'current_mode': self.mode,
            'internal_flag': self.internal_flag,
            'has_analyzed_table': self.current_table_grid is not None
        }

        if self.current_table_grid:
            grid = self.current_table_grid['grid']
            stats.update({
                'grid_rows': grid['rows'],
                'grid_cols': grid['cols'],
                'grid_cells': grid['rows'] * grid['cols']
            })

        # 添加边框样式统计
        if self.table_cells:
            border_stats = self._analyze_border_styles()
            stats['border_styles'] = border_stats

        return stats

    def _analyze_border_styles(self) -> Dict:
        """分析当前所有单元格的边框样式分布"""
        border_count = {'top': 0, 'right': 0, 'bottom': 0, 'left': 0}
        total_cells = len(self.table_cells)

        for cell in self.table_cells:
            if isinstance(cell, TableCellShape):
                style = cell.get_border_style()
                for side, has_border in style.items():
                    if has_border:
                        border_count[side] += 1

        # 计算百分比
        border_percentage = {}
        for side, count in border_count.items():
            border_percentage[side] = (count / total_cells * 100) if total_cells > 0 else 0

        return {
            'total_cells': total_cells,
            'border_count': border_count,
            'border_percentage': border_percentage
        }

    def are_all_cells_fully_bordered(self) -> bool:
        """判断是否所有单元格都是完全带框的（四边都有边框）

        Returns:
            bool: True表示所有单元格都是完全带框的，False表示至少有一个单元格不是完全带框的
        """
        if not self.table_cells:
            # 如果没有单元格，默认返回False（无线表）
            return False

        for cell in self.table_cells:
            if isinstance(cell, TableCellShape):
                border_style = cell.get_border_style()
                # 检查四边是否都有边框（值为1）
                if not all(border_style.get(side, 0) == 1 for side in ['top', 'right', 'bottom', 'left']):
                    return False

        return True

    def auto_determine_table_type(self) -> int:
        """根据单元格边框状态自动判断表格类型

        规则：
        - 如果没有单元格，则为文本（table_type=0）
        - 如果所有单元格都是完全带框的，则为有线表（table_type=1）
        - 否则为无线表（table_type=2）

        Returns:
            int: 表格类型 (0=文本, 1=有线表格, 2=无线表格)
        """
        # 🆕 优先检查：如果没有单元格，设置为文本类型
        if not self.table_cells:
            LOGGER.debug("🔍 自动判断：没有表格单元格 → 文本")
            return 0  # 文本

        if self.are_all_cells_fully_bordered():
            LOGGER.debug("🔍 自动判断：所有单元格都完全带框 → 有线表")
            return 1  # 有线表格
        else:
            LOGGER.debug("🔍 自动判断：存在不完全带框的单元格 → 无线表")
            return 2  # 无线表格

    # ===== 调试和状态检查 =====

    def is_in_table_mode(self) -> bool:
        """检查是否在表格模式中"""
        return self.mode != "none"

    def get_current_mode_info(self) -> Dict:
        """获取当前模式信息"""
        return {
            'mode': self.mode,
            'internal_flag': self.internal_flag,
            'canvas_create_mode': self.canvas.createMode,
            'canvas_drawing': self.canvas.drawing(),
            'canvas_editing': self.canvas.editing()
        }

    # ===== 对齐工具算法 =====

    def align_selected_cells_top(self):
        """顶端对齐：将选中的单元格顶部对齐到最上方的位置"""
        selected_cells = self._get_selected_table_cells()
        if len(selected_cells) < 2:
            LOGGER.error(f"需要选择至少2个单元格进行对齐")
            return False

        # 找到最小Y坐标（最上方）
        min_y = float('inf')
        for cell in selected_cells:
            if cell.points and len(cell.points) >= 2:
                cell_min_y = min(p.y() for p in cell.points)
                min_y = min(min_y, cell_min_y)

        if min_y == float('inf'):
            LOGGER.error(f"无法确定对齐基准")
            return False

        # 存储到撤销栈
        self.canvas.storeShapes()

        # 调整所有选中单元格的Y坐标
        for cell in selected_cells:
            if cell.points and len(cell.points) >= 2:
                current_min_y = min(p.y() for p in cell.points)
                dy = min_y - current_min_y

                # 移动所有点
                for point in cell.points:
                    point.setY(point.y() + dy)

        self.canvas.update()
        LOGGER.debug(f"✅ 已对齐 {len(selected_cells)} 个单元格到顶端")
        return True

    def align_selected_cells_left(self):
        """左对齐：将选中的单元格左边对齐到最左侧位置"""
        selected_cells = self._get_selected_table_cells()
        if len(selected_cells) < 2:
            LOGGER.error(f"需要选择至少2个单元格进行对齐")
            return False

        # 找到最小X坐标（最左侧）
        min_x = float('inf')
        for cell in selected_cells:
            if cell.points and len(cell.points) >= 2:
                cell_min_x = min(p.x() for p in cell.points)
                min_x = min(min_x, cell_min_x)

        if min_x == float('inf'):
            LOGGER.error(f"无法确定对齐基准")
            return False

        # 存储到撤销栈
        self.canvas.storeShapes()

        # 调整所有选中单元格的X坐标
        for cell in selected_cells:
            if cell.points and len(cell.points) >= 2:
                current_min_x = min(p.x() for p in cell.points)
                dx = min_x - current_min_x

                # 移动所有点
                for point in cell.points:
                    point.setX(point.x() + dx)

        self.canvas.update()
        LOGGER.debug(f"✅ 已对齐 {len(selected_cells)} 个单元格到左侧")
        return True

    def align_selected_cells_bottom(self):
        """底端对齐：将选中的单元格底部对齐到最下方位置"""
        selected_cells = self._get_selected_table_cells()
        if len(selected_cells) < 2:
            LOGGER.error(f"需要选择至少2个单元格进行对齐")
            return False

        # 找到最大Y坐标（最下方）
        max_y = float('-inf')
        for cell in selected_cells:
            if cell.points and len(cell.points) >= 2:
                cell_max_y = max(p.y() for p in cell.points)
                max_y = max(max_y, cell_max_y)

        if max_y == float('-inf'):
            LOGGER.error(f"无法确定对齐基准")
            return False

        # 存储到撤销栈
        self.canvas.storeShapes()

        # 调整所有选中单元格的Y坐标
        for cell in selected_cells:
            if cell.points and len(cell.points) >= 2:
                current_max_y = max(p.y() for p in cell.points)
                dy = max_y - current_max_y

                # 移动所有点
                for point in cell.points:
                    point.setY(point.y() + dy)

        self.canvas.update()
        LOGGER.debug(f"✅ 已对齐 {len(selected_cells)} 个单元格到底端")
        return True

    def align_selected_cells_right(self):
        """右对齐：将选中的单元格右边对齐到最右侧位置"""
        selected_cells = self._get_selected_table_cells()
        if len(selected_cells) < 2:
            LOGGER.error(f"需要选择至少2个单元格进行对齐")
            return False

        # 找到最大X坐标（最右侧）
        max_x = float('-inf')
        for cell in selected_cells:
            if cell.points and len(cell.points) >= 2:
                cell_max_x = max(p.x() for p in cell.points)
                max_x = max(max_x, cell_max_x)

        if max_x == float('-inf'):
            LOGGER.error(f"无法确定对齐基准")
            return False

        # 存储到撤销栈
        self.canvas.storeShapes()

        # 调整所有选中单元格的X坐标
        for cell in selected_cells:
            if cell.points and len(cell.points) >= 2:
                current_max_x = max(p.x() for p in cell.points)
                dx = max_x - current_max_x

                # 移动所有点
                for point in cell.points:
                    point.setX(point.x() + dx)

        self.canvas.update()
        LOGGER.debug(f"✅ 已对齐 {len(selected_cells)} 个单元格到右侧")
        return True

    def _get_selected_table_cells(self) -> List[TableCellShape]:
        """获取当前选中的表格单元格"""
        selected_cells = []
        for shape in self.canvas.selectedShapes:
            if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell':
                selected_cells.append(shape)
        return selected_cells

    # ===== 批量网格生成算法 =====

    def auto_decompose_region_to_grid(self, rows=None, cols=None, target_region=None):
        """将表格区域分解为均匀网格
        
        Args:
            rows: 行数，None时自动估算
            cols: 列数，None时自动估算  
            target_region: 目标区域(x1,y1,x2,y2)，None时使用最后选择的区域
            
        Returns:
            List[TableCellShape]: 生成的单元格列表
        """
        # 确定目标区域
        if target_region is None:
            # 优先使用直接设置的table_region属性
            if hasattr(self, 'table_region') and self.table_region:
                target_region = self.table_region
            # 否则尝试从最后的分析结果获取
            elif self.last_analysis_result:
                # 从分析结果中计算边界框
                cells = self.last_analysis_result.get('cell_positions', {})
                if cells:
                    # 计算所有单元格的边界框
                    all_points = []
                    for cell_info in cells.values():
                        cell = cell_info['cell_object']
                        if cell.points:
                            all_points.extend([(p.x(), p.y()) for p in cell.points])
                    
                    if all_points:
                        x_coords = [p[0] for p in all_points]
                        y_coords = [p[1] for p in all_points]
                        target_region = (min(x_coords), min(y_coords), max(x_coords), max(y_coords))
            
            # 如果仍然没有区域，报错返回
            if target_region is None:
                LOGGER.error(f"没有指定表格区域，请先选择表格区域")
                return []

        x1, y1, x2, y2 = target_region

        # 智能估算行列数（如果未指定）
        if rows is None or cols is None:
            area = (x2 - x1) * (y2 - y1)
            estimated_cells = max(4, int(area / 10000))  # 基于面积估算单元格数量
            
            if rows is None and cols is None:
                # 两者都未指定，创建接近正方形的网格
                rows = int(estimated_cells ** 0.5)
                cols = (estimated_cells + rows - 1) // rows  # 向上取整
            elif rows is None:
                rows = (estimated_cells + cols - 1) // cols
            elif cols is None:
                cols = (estimated_cells + rows - 1) // rows

        # 确保最小尺寸
        rows = max(1, rows)
        cols = max(1, cols)

        LOGGER.debug(f"🔧 生成 {rows}x{cols} 网格，区域: ({x1:.1f},{y1:.1f}) -> ({x2:.1f},{y2:.1f})")

        # 计算单元格尺寸
        cell_width = (x2 - x1) / cols
        cell_height = (y2 - y1) / rows

        created_cells = []
        
        # 存储到撤销栈
        self.canvas.storeShapes()

        # 批量创建单元格
        for row in range(rows):
            for col in range(cols):
                # 计算单元格位置
                cx1 = x1 + col * cell_width
                cy1 = y1 + row * cell_height
                cx2 = cx1 + cell_width
                cy2 = cy1 + cell_height

                # 创建TableCellShape
                from labelme.table_shape import create_table_cell_from_rect
                cell = create_table_cell_from_rect(
                    cx1, cy1, cx2, cy2,
                    label=f"cell_{row}_{col}"
                )

                # 设置逻辑位置
                cell.set_logical_location(row, row, col, col)
                cell.set_confirmed(True)

                # 添加到Canvas和管理列表
                self.canvas.shapes.append(cell)
                self.table_cells.append(cell)
                created_cells.append(cell)

        # 更新Canvas显示
        self.canvas.update()
        
        LOGGER.debug(f"✅ 成功创建 {rows}x{cols} 网格，共 {len(created_cells)} 个单元格")
        return created_cells

    # ===== 划线推理模式 =====

    def enter_line_inference_mode(self):
        """进入划线推理模式"""
        LOGGER.debug("🔧 进入划线推理模式...")

        self.mode = "line_inference"
        self.internal_flag = "drawing_guide_line"

        # 使用Canvas的line模式
        self.canvas.createMode = "line"
        self.canvas.setEditing(False)  # 进入CREATE模式

        self._clear_temporary_state()
        LOGGER.debug(f"✅ 划线推理模式设置完成 - Canvas.createMode: {self.canvas.createMode}")

    def handle_shapes_deleted(self, deleted_shapes):
        """处理形状被删除的事件

        Args:
            deleted_shapes: 被删除的TableCellShape列表
        """
        LOGGER.debug(f"🗑️ TableController处理删除事件: {len(deleted_shapes)} 个单元格")

        # 从table_cells列表中移除
        for shape in deleted_shapes:
            if shape in self.table_cells:
                self.table_cells.remove(shape)

        # 刷新结构视图
        self._refresh_structure_widget()

        # 如果没有单元格了，清理状态
        if not self.table_cells:
            self.current_table_grid = None
            self.last_analysis_result = None

        LOGGER.debug(f"✅ 删除处理完成，剩余单元格: {len(self.table_cells)}")
    def _handle_guide_line_creation(self, line_shape):
        """处理用户划线，重新分配单元格逻辑坐标"""
        try:
            LOGGER.debug("📏 处理划线推理...")

            # 分析线条方向
            direction = self._analyze_line_direction(line_shape)
            line_position = self._get_line_position(line_shape)

            LOGGER.debug(f"📏 线条方向: {direction}, 位置: {line_position:.1f}")

            # 找到受影响的单元格
            affected_cells = self._find_cells_affected_by_line(line_position, direction)

            if not affected_cells:
                LOGGER.error(f"线条附近没有找到单元格")
                return {
                    'intercepted': True,
                    'action': 'no_cells_affected',
                    'result_data': {'message': '线条附近没有单元格'}
                }

            LOGGER.debug(f"📏 找到 {len(affected_cells)} 个受影响的单元格")

            # 重新分配逻辑坐标
            if direction == "horizontal":
                self._split_cells_horizontally(affected_cells, line_position)
            elif direction == "vertical":
                self._split_cells_vertically(affected_cells, line_position)

            # 重新分析表格结构
            analysis_result = TableAnalyzer.analyze_cells_to_grid(self.table_cells)
            self._apply_analysis_result(analysis_result)
            self.current_table_grid = analysis_result

            LOGGER.debug(f"✅ 划线推理完成，影响 {len(affected_cells)} 个单元格")

            return {
                'intercepted': True,
                'action': 'line_inference_applied',
                'result_data': {
                    'direction': direction,
                    'affected_cells': len(affected_cells),
                    'line_position': line_position
                }
            }

        except Exception as e:
            LOGGER.error(f"划线推理失败: {e}")
            return {
                'intercepted': True,
                'action': 'line_inference_failed',
                'result_data': {'error': str(e)}
            }

    def _analyze_line_direction(self, line_shape):
        """分析线条方向"""
        if not line_shape.points or len(line_shape.points) < 2:
            return "unknown"

        start, end = line_shape.points[0], line_shape.points[1]
        dx = abs(end.x() - start.x())
        dy = abs(end.y() - start.y())

        # 判断主要方向
        return "horizontal" if dx > dy else "vertical"

    def _get_line_position(self, line_shape):
        """获取线条的位置坐标"""
        if not line_shape.points or len(line_shape.points) < 2:
            return 0

        start, end = line_shape.points[0], line_shape.points[1]
        direction = self._analyze_line_direction(line_shape)

        if direction == "horizontal":
            # 水平线，返回Y坐标的平均值
            return (start.y() + end.y()) / 2
        else:
            # 垂直线，返回X坐标的平均值
            return (start.x() + end.x()) / 2

    def _find_cells_affected_by_line(self, line_position, direction, tolerance=20):
        """找到被线条影响的单元格"""
        affected_cells = []

        for cell in self.table_cells:
            if not cell.points or len(cell.points) < 2:
                continue

            # 计算单元格中心
            x_coords = [p.x() for p in cell.points]
            y_coords = [p.y() for p in cell.points]
            center_x = sum(x_coords) / len(x_coords)
            center_y = sum(y_coords) / len(y_coords)

            # 判断是否受线条影响
            if direction == "horizontal":
                # 水平线影响垂直方向上的单元格
                if abs(center_y - line_position) < tolerance:
                    affected_cells.append(cell)
            else:
                # 垂直线影响水平方向上的单元格
                if abs(center_x - line_position) < tolerance:
                    affected_cells.append(cell)

        return affected_cells

    def _split_cells_horizontally(self, cells, line_y):
        """基于水平线重新分配单元格的行坐标"""
        # 按Y坐标排序单元格
        cells_with_y = []
        for cell in cells:
            y_coords = [p.y() for p in cell.points]
            center_y = sum(y_coords) / len(y_coords)
            cells_with_y.append((center_y, cell))

        cells_with_y.sort(key=lambda x: x[0])

        # 重新分配行号
        current_row = 0
        for center_y, cell in cells_with_y:
            if center_y > line_y:
                current_row += 1

            lloc = cell.get_logical_location()
            cell.set_logical_location(
                start_row=current_row,
                end_row=current_row,
                start_col=lloc["start_col"],
                end_col=lloc["end_col"]
            )

    def _split_cells_vertically(self, cells, line_x):
        """基于垂直线重新分配单元格的列坐标"""
        # 按X坐标排序单元格
        cells_with_x = []
        for cell in cells:
            x_coords = [p.x() for p in cell.points]
            center_x = sum(x_coords) / len(x_coords)
            cells_with_x.append((center_x, cell))

        cells_with_x.sort(key=lambda x: x[0])

        # 重新分配列号
        current_col = 0
        for center_x, cell in cells_with_x:
            if center_x > line_x:
                current_col += 1

            lloc = cell.get_logical_location()
            cell.set_logical_location(
                start_row=lloc["start_row"],
                end_row=lloc["end_row"],
                start_col=current_col,
                end_col=current_col
            )

    # ===== UI组件双向绑定 =====

    def bind_properties_widget(self, properties_widget):
        """绑定属性面板，实现双向数据同步"""
        self.properties_widget = properties_widget
        
        # 连接属性变更信号
        if hasattr(properties_widget, 'property_changed'):
            properties_widget.property_changed.connect(self._on_properties_changed)
            
        LOGGER.debug("✅ 已绑定属性面板")

    def bind_structure_widget(self, structure_widget):
        """绑定逻辑结构视图，实现双向数据同步"""
        self.structure_widget = structure_widget

        # 连接单元格点击信号
        if hasattr(structure_widget, 'cellClicked'):
            structure_widget.cellClicked.connect(self._on_structure_cell_clicked)

        # 🆕 连接多选变更信号
        if hasattr(structure_widget, 'selectionChanged'):
            structure_widget.selectionChanged.connect(self._on_structure_selection_changed)

        # 🆕 连接合并请求信号
        if hasattr(structure_widget, 'merge_cells_requested'):
            structure_widget.merge_cells_requested.connect(self._on_merge_cells_requested)

        # 🆕 连接拆分请求信号
        if hasattr(structure_widget, 'split_cells_requested'):
            structure_widget.split_cells_requested.connect(self._on_split_cells_requested)

        LOGGER.debug("✅ 已绑定结构视图（包含多选信号）")

    def _on_structure_selection_changed(self, selected_logical_data):
        """处理逻辑结构视图的多选变更事件

        Args:
            selected_logical_data: 格式为 [{'row': int, 'col': int, 'text': str, ...}, ...]
        """
        LOGGER.debug(f"🔗 收到逻辑结构多选变更: {len(selected_logical_data)} 个单元格")

        if not selected_logical_data:
            # 空选择，清除所有选中
            self.canvas.selectShapes([])  # 🔧 使用正确的Canvas方法
            return

        # 映射逻辑坐标到物理单元格
        physical_cells = self._find_physical_cells_by_logical_data(selected_logical_data)

        # 统计信息
        empty_cells_count = len(selected_logical_data) - len(physical_cells)

        if physical_cells:
            # 🔧 关键修复：使用Canvas标准选中方法触发完整事件链
            self.canvas.selectShapes(physical_cells)
            LOGGER.debug(f"✅ 已选中 {len(physical_cells)} 个物理单元格")

            if empty_cells_count > 0:
                LOGGER.debug(f"⚠️ {empty_cells_count} 个逻辑单元格在物理界面不存在")
        else:
            # 全部都是空单元格
            self.canvas.selectShapes([])
            LOGGER.debug("⚠️ 选中的都是空单元格，清除物理选择")
    def _on_properties_changed(self, property_name, property_value):
        """处理属性面板的变更通知"""
        selected_cells = self._get_selected_table_cells()
        if not selected_cells:
            return

        LOGGER.debug(f"🔄 属性变更: {property_name} = {property_value}")

        # 存储到撤销栈
        self.canvas.storeShapes()

        # 🆕 处理合并/拆分请求
        if property_name == "merge_cells_requested":
            LOGGER.debug("🔗 TableController收到合并请求")
            if len(selected_cells) >= 2:
                success = self.merge_selected_cells_from_list(selected_cells)
                if success:
                    LOGGER.debug("✅ 合并成功")
                    self._refresh_structure_widget()
                else:
                    LOGGER.error(f"合并失败")
            return
        elif property_name == "split_cells_requested":
            LOGGER.debug("🔗 TableController收到拆分请求")
            if len(selected_cells) == 1:
                success = self.split_merged_cell(selected_cells[0])
                if success:
                    LOGGER.debug("✅ 拆分成功")
                    self._refresh_structure_widget()
                else:
                    LOGGER.error(f"拆分失败")
            return

        # 🆕 处理具体的边框切换操作
        if property_name == "border_toggle":
            LOGGER.debug(f"🔧 处理边框切换操作，选中单元格数量: {len(selected_cells)}")

            side = property_value.get('side')
            value = property_value.get('value')

            if side and value is not None:
                # 构建边框更新参数，只包含被切换的边框
                border_kwargs = {side: value}

                # 临时设置高亮单元格为当前选中的单元格
                original_highlighted = self.highlighted_cells.copy()
                self.highlighted_cells = selected_cells.copy()

                try:
                    # 使用批量边框设置方法，只更新指定的边框
                    success = self.set_selected_cells_border_style(**border_kwargs)
                    if success:
                        LOGGER.debug(f"✅ 边框切换完成: {side} = {value}")
                    else:
                        LOGGER.warning(f"⚠️ 边框切换失败: {side} = {value}")
                finally:
                    # 恢复原始高亮状态
                    self.highlighted_cells = original_highlighted

                # 🔧 修复：使用局部边框样式更新，避免全量结构刷新
                if hasattr(self, 'structure_widget'):
                    self._update_structure_border_styles_only(selected_cells, {side: value})
                return

        # 🔧 修复：特殊处理边框样式变更，确保相邻边框同步（保持兼容性）
        elif property_name == "border_style":
            LOGGER.debug(f"🔧 处理边框样式变更，选中单元格数量: {len(selected_cells)}")

            # 对于边框样式变更，使用批量设置方法以确保相邻边框同步
            # 提取边框值
            top = property_value.get('top')
            right = property_value.get('right')
            bottom = property_value.get('bottom')
            left = property_value.get('left')

            # 临时设置高亮单元格为当前选中的单元格
            original_highlighted = self.highlighted_cells.copy()
            self.highlighted_cells = selected_cells.copy()

            try:
                # 使用批量边框设置方法，这会自动处理相邻边框同步
                success = self.set_selected_cells_border_style(top=top, right=right, bottom=bottom, left=left)
                if success:
                    LOGGER.debug("✅ 边框样式变更完成，包含相邻边框同步")
                else:
                    LOGGER.warning("⚠️ 边框样式变更失败")
            finally:
                # 恢复原始高亮状态
                self.highlighted_cells = original_highlighted

            # 🔧 修复：使用局部边框样式更新，避免全量结构刷新
            if hasattr(self, 'structure_widget'):
                border_changes = {'top': top, 'right': right, 'bottom': bottom, 'left': left}
                # 过滤掉None值
                actual_changes = {k: v for k, v in border_changes.items() if v is not None}
                self._update_structure_border_styles_only(selected_cells, actual_changes)
            return

        # 🆕 处理控制器级别的属性变更（不需要应用到单元格）
        if property_name == "auto_determine_enabled":
            self.set_auto_determine_enabled(property_value)
            return

        # 应用其他属性变更到选中的单元格
        for cell in selected_cells:
            if property_name == "table_type":
                cell.set_table_type(property_value)
                # 同时更新控制器级别的表格类型
                self.set_table_type(property_value)
            elif property_name == "cell_text":
                cell.set_cell_text(property_value)
            elif property_name == "logical_location":
                cell.set_logical_location(**property_value)

        # 更新显示
        self.canvas.update()
        if hasattr(self, 'structure_widget'):
            self._refresh_structure_widget()

    def handle_property_change(self, property_name, property_value):
        """处理来自主窗口的属性变更通知（兼容性方法）
        
        这个方法用于处理来自app_tableme.py的属性变更通知，
        确保与现有的_on_properties_changed方法保持兼容性。
        
        Args:
            property_name: 属性名称
            property_value: 属性值
        """
        LOGGER.debug(f"🔄 [COMPAT] 收到属性变更通知: {property_name} = {property_value}")
        
        # 检查是否有选中的单元格
        selected_cells = self._get_selected_table_cells()
        
        if selected_cells:
            # 有选中单元格时，使用现有的属性处理方法
            self._on_properties_changed(property_name, property_value)
        else:
            # 没有选中单元格时，记录但不报错
            LOGGER.debug(f"⚠️ TableController收到属性变更但无选中单元格: {property_name} = {property_value}")
            
            # 对于某些全局属性，需要保存到控制器级别
            if property_name == "table_type":
                # 首先保存到控制器级别（支持空表格场景）
                self.set_table_type(property_value)

                # 如果有单元格，也应用到所有单元格
                if self.table_cells:
                    LOGGER.debug(f"🔧 应用table_type到所有单元格: {property_value}")
                    for cell in self.table_cells:
                        if hasattr(cell, 'set_table_type'):
                            cell.set_table_type(property_value)
                else:
                    LOGGER.debug(f"🔧 应用table_type到控制器级别（无单元格）: {property_value}")

                # 更新显示
                self.canvas.update()
                if hasattr(self, 'structure_widget'):
                    self._refresh_structure_widget()
            elif property_name == "auto_determine_enabled":
                # 🆕 处理自动推导开关（控制器级别属性）
                self.set_auto_determine_enabled(property_value)
            else:
                LOGGER.debug(f"✅ 属性变更已记录但跳过处理: {property_name}")

    def _on_structure_cell_clicked(self, row, col):
        """处理逻辑结构视图的单元格点击"""
        LOGGER.debug(f"🖱️ 结构视图点击: ({row}, {col})")

        # 找到对应的物理单元格
        target_cells = []
        for cell in self.table_cells:
            lloc = cell.get_logical_location()
            if (lloc["start_row"] <= row <= lloc["end_row"] and 
                lloc["start_col"] <= col <= lloc["end_col"]):
                target_cells.append(cell)

        if target_cells:
            # 清除现有选择
            for shape in self.canvas.shapes:
                shape.selected = False

            # 选中目标单元格
            for cell in target_cells:
                cell.selected = True

            # 🔧 关键修复：使用Canvas标准选中方法
            self.canvas.selectShapes(target_cells)
            LOGGER.debug(f"✅ 已选中 {len(target_cells)} 个物理单元格")
        else:
            # 清除选择
            self.canvas.selectShapes([])
            LOGGER.debug("⚠️ 点击的是空单元格，清除物理选择")

    def _refresh_structure_widget(self):
        """刷新结构视图显示"""
        if not hasattr(self, 'structure_widget'):
            LOGGER.debug("⚠️ 无结构视图组件")
            return

        try:
            # 动态构造结构视图需要的数据格式
            grid_data = self._build_grid_data_for_structure_view()
            if grid_data:
                LOGGER.debug(
                    f"🔗 刷新结构视图，数据: rows={grid_data['rows']}, cols={grid_data['cols']}, cells={len(grid_data['cells'])}")
                self.structure_widget.update_structure_view(grid_data)
            else:
                LOGGER.debug("⚠️ 无法构造结构视图数据")
        except Exception as e:
            LOGGER.error(f"刷新结构视图错误: {e}")

    def _update_structure_border_styles_only(self, affected_cells, border_changes):
        """仅更新结构视图中的边框样式，不重建整个结构

        Args:
            affected_cells: 受影响的单元格列表
            border_changes: 边框变化字典，如 {'left': 0, 'right': 1}
        """
        if not hasattr(self, 'structure_widget') or not affected_cells:
            return

        try:
            LOGGER.debug(f"[STRUCTURE] 局部更新边框样式: {len(affected_cells)}个单元格, 变化: {border_changes}")

            # 收集需要更新的单元格及其新边框样式
            cells_with_borders = []

            for cell in affected_cells:
                # 获取单元格当前的完整边框样式
                current_border = cell.get_border_style()

                # 应用边框变化
                updated_border = current_border.copy()
                updated_border.update(border_changes)

                cells_with_borders.append((cell, updated_border))

            # 调用结构视图的局部更新方法
            if hasattr(self.structure_widget, 'update_cell_border_styles'):
                self.structure_widget.update_cell_border_styles(cells_with_borders)
                LOGGER.debug(f"[STRUCTURE] 边框样式局部更新完成")
            else:
                # 降级：如果没有局部更新方法，使用全量刷新
                LOGGER.warning("[STRUCTURE] 结构视图不支持局部更新，使用全量刷新")
                self._refresh_structure_widget()

        except Exception as e:
            LOGGER.error(f"局部更新结构视图边框样式失败: {e}")
            # 降级：出错时使用全量刷新
            self._refresh_structure_widget()

    def _build_grid_data_for_structure_view(self):
        """构造结构视图需要的数据格式 - 包含完整逻辑位置信息"""
        if not self.table_cells:
            return None

        # 分析所有单元格，计算表格尺寸
        max_row = 0
        max_col = 0
        cells_data = []

        for cell in self.table_cells:
            try:
                # 🎯 关键：获取完整的逻辑位置
                lloc = cell.get_logical_location()
                if not lloc:
                    continue

                # 更新最大行列数
                max_row = max(max_row, lloc.get('end_row', 0))
                max_col = max(max_col, lloc.get('end_col', 0))

                # 🔧 优雅解决方案：在数据中直接包含逻辑位置信息
                cell_data = {
                    'row': lloc.get('start_row', 0),
                    'col': lloc.get('start_col', 0),
                    'text': cell.get_cell_text() if hasattr(cell, 'get_cell_text') else '',
                    'border': cell.get_border_style() if hasattr(cell, 'get_border_style') else {},
                    'cell_id': cell.table_properties.get('cell_id', ''),
                    # 🆕 新增：包含完整的逻辑位置信息
                    'logical_location': {
                        'start_row': lloc.get('start_row', 0),
                        'end_row': lloc.get('end_row', 0),
                        'start_col': lloc.get('start_col', 0),
                        'end_col': lloc.get('end_col', 0)
                    },
                    # ✨ 新增：包含表头信息
                    'table_properties': {
                        'header': cell.get_header() if hasattr(cell, 'get_header') else False,
                        'table_type': cell.get_table_type() if hasattr(cell, 'get_table_type') else 0,
                        'cell_id': cell.table_properties.get('cell_id', '')
                    }
                }
                cells_data.append(cell_data)

            except Exception as e:
                LOGGER.debug(f"⚠️ 处理单元格时出错: {e}")
                continue

        if not cells_data:
            return None

        return {
            'rows': max_row + 1,
            'cols': max_col + 1,
            'cells': cells_data
        }
    def _refresh_properties_widget(self, selected_cells):
        """刷新属性面板显示"""
        if not hasattr(self, 'properties_widget'):
            return

        # 通知属性面板更新选中的单元格
        if hasattr(self.properties_widget, 'update_from_selection'):
            self.properties_widget.update_from_selection(selected_cells)

    def handle_selection_changed(self, selected_shapes):
        """处理Canvas选择变更通知"""
        # 过滤出表格单元格
        from labelme.table_shape import is_table_cell
        selected_table_cells = [shape for shape in selected_shapes if is_table_cell(shape)]
        
        # 更新内部状态
        self.highlighted_cells = selected_table_cells.copy()
        
        # 通知属性面板更新
        if hasattr(self, 'properties_widget'):
            self._refresh_properties_widget(selected_table_cells)
        
        # 如果有表格单元格被选中，在结构视图中高亮对应位置
        if selected_table_cells and hasattr(self, 'structure_widget'):
            # 高亮第一个选中单元格的逻辑位置
            first_cell = selected_table_cells[0]
            lloc = first_cell.get_logical_location()
            
            # 通知结构视图高亮该位置
            if hasattr(self.structure_widget, 'highlight_cell'):
                self.structure_widget.highlight_cell(lloc["start_row"], lloc["start_col"])
        
        LOGGER.debug(f"🔄 选择变更: {len(selected_table_cells)} 个表格单元格被选中")

    def is_cell_header(self, row: int, col: int) -> bool:
        """检查指定逻辑位置的单元格是否为表头
        
        Args:
            row: 逻辑行号
            col: 逻辑列号
            
        Returns:
            bool: 是否为表头
        """
        # 方法1：通过逻辑位置查找单元格
        cell = self.find_cell_by_logical_position(row, col)
        if cell and hasattr(cell, 'get_header'):
            return cell.get_header()
            
        # 方法2：通过分析结果查找
        if self.current_table_grid:
            cells = self.current_table_grid.get('cells', [])
            for cell_data in cells:
                if (cell_data.get('row') == row and 
                    cell_data.get('col') == col):
                    # 检查单元格的表头属性
                    table_properties = cell_data.get('table_properties', {})
                    return table_properties.get('header', False)
                    
        return False

    def find_cell_by_logical_position(self, row: int, col: int) -> Optional[TableCellShape]:
        """根据逻辑位置查找表格单元格
        
        Args:
            row: 逻辑行号
            col: 逻辑列号
            
        Returns:
            TableCellShape或None: 找到的单元格
        """
        for cell in self.table_cells:
            if isinstance(cell, TableCellShape):
                logical_loc = cell.get_logical_location()
                if logical_loc:
                    # 检查单元格是否包含这个逻辑位置
                    if (logical_loc.get('start_row', -1) <= row <= logical_loc.get('end_row', -1) and
                        logical_loc.get('start_col', -1) <= col <= logical_loc.get('end_col', -1)):
                        return cell
        return None

    def set_cells_header_status(self, cells: List[TableCellShape], is_header: bool):
        """设置多个单元格的表头状态
        
        Args:
            cells: 要设置的单元格列表
            is_header: 表头状态
        """
        affected_count = 0
        for cell in cells:
            if isinstance(cell, TableCellShape) and hasattr(cell, 'set_header'):
                cell.set_header(is_header)
                affected_count += 1
                
        if affected_count > 0:
            # 刷新结构视图
            self._refresh_structure_widget()
            self.canvas.update()
            
        return affected_count

    # ===== 表格级别属性管理 =====

    def get_table_type(self):
        """获取表格类型

        Returns:
            int: 表格类型 (0=纯文本, 1=有线表格, 2=无线表格)
        """
        return self.table_type

    def set_table_type(self, table_type):
        """设置表格类型

        Args:
            table_type (int): 表格类型 (0=纯文本, 1=有线表格, 2=无线表格)
        """
        if table_type in [0, 1, 2]:
            self.table_type = table_type
            LOGGER.debug(f"设置表格类型为: {table_type}")
        else:
            LOGGER.warning(f"无效的表格类型: {table_type}")

    def get_auto_determine_enabled(self):
        """获取自动推导表格类型开关状态

        Returns:
            bool: True表示启用自动推导，False表示使用用户设置
        """
        return self.auto_determine_enabled

    def set_auto_determine_enabled(self, enabled):
        """设置自动推导表格类型开关状态

        Args:
            enabled (bool): True表示启用自动推导，False表示使用用户设置
        """
        self.auto_determine_enabled = bool(enabled)
        LOGGER.debug(f"设置自动推导表格类型开关为: {self.auto_determine_enabled}")

    # ===== 🆕 表格对齐修正功能 =====
    def analyze_logical_structure(self) -> Dict[str, Any]:
        """分析逻辑结构 - 核心功能

        执行表格分析并生成理想网格和对齐报告

        Returns:
            dict: {
                'status': str,              # 'success' 或 'error'
                'message': str,             # 状态描述信息
                'grid_analysis': dict,      # 基础网格分析结果
                'alignment_analysis': dict, # 对齐分析结果
                'summary': {
                    'total_cells': int,
                    'grid_size': str,       # 例如 "3×4"
                    'misaligned_count': int,
                    'tolerance_used': float
                }
            }
        """
        LOGGER.debug(f"🔍 开始分析逻辑结构（容差={self.alignment_tolerance}px）...")

        # 1. 前置验证
        if not hasattr(self, 'table_cells') or not self.table_cells:
            error_msg = "无法分析：当前没有表格单元格"
            LOGGER.error(f"{error_msg}")
            return {
                'status': 'error',
                'message': error_msg,
                'grid_analysis': None,
                'alignment_analysis': None,
                'summary': {
                    'total_cells': 0,
                    'grid_size': '0×0',
                    'misaligned_count': 0,
                    'tolerance_used': self.alignment_tolerance
                }
            }

        total_cells = len(self.table_cells)
        LOGGER.debug(f"   📊 处理 {total_cells} 个单元格")

        try:
            # 2. 执行基础网格分析（复用现有功能）
            grid_analysis = TableAnalyzer.analyze_cells_to_grid(self.table_cells)
            boundaries = grid_analysis.get('boundaries', {'rows': [], 'cols': []})

            if not boundaries['rows'] or not boundaries['cols']:
                error_msg = "分析失败：无法检测到有效的表格边界"
                LOGGER.error(f"{error_msg}")
                return {
                    'status': 'error',
                    'message': error_msg,
                    'grid_analysis': grid_analysis,
                    'alignment_analysis': None,
                    'summary': {
                        'total_cells': total_cells,
                        'grid_size': '0×0',
                        'misaligned_count': 0,
                        'tolerance_used': self.alignment_tolerance
                    }
                }

            # 3. 生成理想网格
            ideal_grid = TableAnalyzer.generate_ideal_grid_from_boundaries(
                boundaries, self.alignment_tolerance
            )
            LOGGER.debug(
                f"   📐 生成理想网格：{len(ideal_grid['ideal_horizontal_lines'])}行 × {len(ideal_grid['ideal_vertical_lines'])}列")

            # 4. 计算对齐报告
            alignment_report = TableAnalyzer.calculate_alignment_report(
                self.table_cells, ideal_grid, self.alignment_tolerance
            )

            misaligned_count = alignment_report['total_misalignment_count']
            LOGGER.debug(f"   🔍 对齐检查：发现 {misaligned_count} 个不对齐单元格")

            # 5. 存储分析结果
            self.alignment_analysis_result = {
                'ideal_grid': ideal_grid,
                'alignment_report': alignment_report,
                'created_at': None  # 简化版不记录时间
            }
            self.alignment_is_valid = True

            # 6. 返回结果
            grid_size = f"{grid_analysis['grid']['rows']}×{grid_analysis['grid']['cols']}"
            success_msg = f"分析完成：{grid_size} 网格，{misaligned_count} 个单元格需要对齐"
            LOGGER.debug(f"✅ {success_msg}")

            return {
                'status': 'success',
                'message': success_msg,
                'grid_analysis': grid_analysis,
                'alignment_analysis': self.alignment_analysis_result,
                'summary': {
                    'total_cells': total_cells,
                    'grid_size': grid_size,
                    'misaligned_count': misaligned_count,
                    'tolerance_used': self.alignment_tolerance
                }
            }

        except Exception as e:
            error_msg = f"分析过程中发生错误: {e}"
            LOGGER.error(f"{error_msg}")
            raise RuntimeError(error_msg)

    def correct_table_alignment_simple(self, tolerance: Optional[float] = None) -> Dict[str, Any]:
        """修正表格对齐 - 核心功能

        基于已有的分析结果修正单元格对齐

        Args:
            tolerance: 对齐容差（可选，默认使用实例设置）

        Returns:
            dict: {
                'status': str,              # 'success' 或 'error'
                'message': str,             # 操作结果描述
                'summary': {
                    'total_cells': int,     # 总单元格数
                    'adjusted_count': int,  # 调整的单元格数
                    'conflict_count': int,  # 冲突的单元格数
                    'tolerance_used': float # 使用的容差值
                }
            }
        """
        LOGGER.debug("🔧 开始表格对齐修正...")

        # 1. 前置检查：必须先有有效的分析结果
        if not self.has_valid_analysis_result():
            error_msg = "请先运行'分析逻辑结构'以生成修正方案"
            LOGGER.error(f"{error_msg}")
            return {
                'status': 'error',
                'message': error_msg,
                'summary': {
                    'total_cells': len(self.table_cells) if hasattr(self, 'table_cells') else 0,
                    'adjusted_count': 0,
                    'conflict_count': 0,
                    'tolerance_used': tolerance or self.alignment_tolerance
                }
            }

        # 2. 验证单元格存在
        if not hasattr(self, 'table_cells') or not self.table_cells:
            raise RuntimeError("TableController状态异常：table_cells为空，但有有效分析结果")

        total_cells = len(self.table_cells)
        used_tolerance = tolerance or self.alignment_tolerance
        LOGGER.debug(f"   📊 修正 {total_cells} 个单元格，容差={used_tolerance}px")

        try:
            # 3. 获取理想网格数据
            ideal_grid = self.alignment_analysis_result['ideal_grid']
            alignment_report = self.alignment_analysis_result['alignment_report']
            LOGGER.debug(f"   📋 使用分析结果：{alignment_report['total_misalignment_count']}个不对齐单元格")

            # 4. 导入对齐修正引擎
            from labelme.utils.table_alignment_engine import TableAlignmentEngine

            # 5. 应用对齐修正
            correction_result = TableAlignmentEngine.apply_alignment_correction(
                self.table_cells,
                ideal_grid
            )

            # 6. 应用位置变更到实际单元格
            adjusted_count = self._apply_position_adjustments(correction_result['adjusted_cells'])
            conflict_count = len(correction_result.get('conflicting_cells', []))

            # 7. 使分析结果失效（因为单元格位置已改变）
            self.invalidate_analysis_result("修正完成")

            # 8. 返回结果摘要
            success_msg = f"修正完成：调整了 {adjusted_count} 个单元格"
            if conflict_count > 0:
                success_msg += f"，{conflict_count} 个单元格存在冲突"

            LOGGER.debug(f"✅ {success_msg}")

            return {
                'status': 'success',
                'message': success_msg,
                'summary': {
                    'total_cells': total_cells,
                    'adjusted_count': adjusted_count,
                    'conflict_count': conflict_count,
                    'tolerance_used': used_tolerance
                }
            }

        except ImportError as e:
            error_msg = f"模块导入失败: {e}"
            LOGGER.error(f"{error_msg}")
            raise RuntimeError(error_msg)
        except Exception as e:
            error_msg = f"对齐修正过程中发生错误: {e}"
            LOGGER.error(f"{error_msg}")
            raise RuntimeError(error_msg)

    def _apply_position_adjustments(self, adjusted_cells: List[Dict[str, Any]]) -> int:
        """应用位置调整到实际单元格"""
        if not adjusted_cells:
            return 0

        adjusted_count = 0
        for adjustment in adjusted_cells:
            try:
                cell_id = adjustment['cell_id']
                new_position = adjustment['new_position']

                # 找到对应的单元格对象
                target_cell = None
                for cell in self.table_cells:
                    if id(cell) == cell_id:  # 使用id匹配
                        target_cell = cell
                        break

                if target_cell is None:
                    LOGGER.debug(f"⚠️ 未找到ID为 {cell_id} 的单元格")
                    continue

                # 应用新位置
                if 'points' in new_position and new_position['points']:
                    target_cell.points = new_position['points']
                    adjusted_count += 1

                    # 触发Canvas更新
                    if self.canvas and hasattr(self.canvas, 'update'):
                        self.canvas.update()

            except Exception as e:
                LOGGER.debug(f"⚠️ 调整单元格时出错: {e}")
                continue

        return adjusted_count

    def has_valid_analysis_result(self) -> bool:
        """检查是否有有效的分析结果"""
        return (hasattr(self, 'alignment_is_valid') and
                self.alignment_is_valid and
                hasattr(self, 'alignment_analysis_result') and
                self.alignment_analysis_result is not None)

    def invalidate_analysis_result(self, reason: str = "未知原因") -> None:
        """使分析结果失效"""
        if hasattr(self, 'alignment_is_valid'):
            self.alignment_is_valid = False
        if hasattr(self, 'alignment_analysis_result'):
            self.alignment_analysis_result = None
        LOGGER.debug(f"🔄 分析结果已失效: {reason}")

    def set_alignment_tolerance(self, tolerance: float) -> None:
        """设置对齐容差"""
        if tolerance <= 0:
            raise ValueError("容差值必须大于0")

        old_tolerance = getattr(self, 'alignment_tolerance', 5.0)
        self.alignment_tolerance = tolerance

        # 如果容差改变，使现有分析结果失效
        if old_tolerance != tolerance:
            self.invalidate_analysis_result("容差参数变更")

        LOGGER.debug(f"🔧 对齐容差已设置为 {tolerance}px")


    def apply_edge_movement_to_selected(self, edge_name, delta_movement, reference_shape):
        """对所有选中的单元格应用边移动

        Args:
            edge_name: 边名称 ('top', 'bottom', 'left', 'right')
            delta_movement: 移动距离
            reference_shape: 参考形状（用于确定移动方向）

        Returns:
            int: 成功处理的单元格数量
        """
        LOGGER.debug(f"🔧 [BATCH_EDGE] 批量边移动: {edge_name}边, 距离: {delta_movement:.2f}")

        # 获取所有选中的表格单元格
        selected_cells = self._get_selected_table_cells()
        if not selected_cells:
            LOGGER.error(f"[BATCH_EDGE] 没有选中的表格单元格")
            return 0

        # 🔧 为撤销操作保存状态
       # self.canvas.storeShapes()

        success_count = 0

        for cell in selected_cells:
            if self._apply_edge_movement_to_cell(cell, edge_name, delta_movement):
                success_count += 1
            else:
                LOGGER.debug(f"⚠️ [BATCH_EDGE] 单元格 {cell.label} 边移动失败")

        # 刷新显示
        self.canvas.update()

        LOGGER.debug(f"✅ [BATCH_EDGE] 成功处理 {success_count}/{len(selected_cells)} 个单元格")
        return success_count


    def _apply_edge_movement_to_cell(self, cell, edge_name, delta_movement):
        """对单个单元格应用边移动

        Args:
            cell: 目标单元格
            edge_name: 边名称
            delta_movement: 移动距离

        Returns:
            bool: 是否成功
        """
        if len(cell.points) != 4:
            return False

        try:
            # 🔧 边名称到顶点索引的映射
            edge_vertex_mapping = {
                'top': [0, 1],  # 上边：左上(0), 右上(1)
                'right': [1, 2],  # 右边：右上(1), 右下(2)
                'bottom': [2, 3],  # 下边：右下(2), 左下(3)
                'left': [0, 3]  # 左边：左上(0), 左下(3)
            }

            if edge_name not in edge_vertex_mapping:
                LOGGER.error(f"[CELL_EDGE] 无效边名称: {edge_name}")
                return False

            vertex_indices = edge_vertex_mapping[edge_name]

            # 🔧 复制顶点避免引用问题
            new_points = [QtCore.QPointF(p.x(), p.y()) for p in cell.points]

            # 🔧 计算新的坐标
            if edge_name in ['top', 'bottom']:
                # 水平边：移动Y坐标
                for vertex_index in vertex_indices:
                    new_y = new_points[vertex_index].y() + delta_movement
                    new_points[vertex_index].setY(new_y)
            else:
                # 垂直边：移动X坐标
                for vertex_index in vertex_indices:
                    new_x = new_points[vertex_index].x() + delta_movement
                    new_points[vertex_index].setX(new_x)

            # 🔧 尺寸约束检查
            if not self._validate_cell_size_constraints(new_points):
                LOGGER.debug(f"⚠️ [CELL_EDGE] 单元格 {cell.label} 尺寸约束检查失败")
                return False

            # 🔧 原子性更新
            cell.points = new_points

            LOGGER.debug(f"✅ [CELL_EDGE] 已更新单元格 {cell.label} 的{edge_name}边")
            return True

        except Exception as e:
            LOGGER.error(f"[CELL_EDGE] 单元格 {cell.label} 边移动异常: {e}")
            return False


    def _validate_cell_size_constraints(self, points):
        """验证单元格尺寸约束

        Args:
            points: 新的顶点坐标列表

        Returns:
            bool: 是否满足约束
        """
        if len(points) != 4:
            return False

        # 计算尺寸
        x_coords = [p.x() for p in points]
        y_coords = [p.y() for p in points]

        width = max(x_coords) - min(x_coords)
        height = max(y_coords) - min(y_coords)

        # 最小尺寸约束
        min_size = 0.1

        if width < min_size or height < min_size:
            LOGGER.debug(f"⚠️ [SIZE_CONSTRAINT] 尺寸过小: {width:.1f} × {height:.1f} (最小: {min_size})")
            return False

        return True

    def split_selected_cells_by_row(self, split_ratio=0.5):
        """按行拆分选中的单元格（简单一分为二）"""
        if not self.highlighted_cells:
            LOGGER.warning("没有选中的单元格")
            return False

        # 检查命令系统可用性
        if not (hasattr(self.canvas, 'main_window') and
                hasattr(self.canvas.main_window, 'history_manager')):
            LOGGER.error("❌ 命令系统不可用，无法执行按行拆分操作")
            return False

        history_manager = self.canvas.main_window.history_manager
        
        from labelme.commands.cell_commands import SplitCellByRowCommand

        success_count = 0
        for cell in self.highlighted_cells:
            try:
                cmd = SplitCellByRowCommand(
                    self.canvas,
                    self,
                    cell,
                    split_ratio
                )
                if history_manager.execute_command(cmd):
                    success_count += 1
            except Exception as e:
                LOGGER.error(f"按行拆分单元格 {cell.label} 失败: {e}")

        return success_count > 0

    def split_selected_cells_by_column(self, split_ratio=0.5):
        """按列拆分选中的单元格（简单一分为二）"""
        if not self.highlighted_cells:
            LOGGER.warning("没有选中的单元格")
            return False

        # 检查命令系统可用性
        if not (hasattr(self.canvas, 'main_window') and
                hasattr(self.canvas.main_window, 'history_manager')):
            LOGGER.error("❌ 命令系统不可用，无法执行按列拆分操作")
            return False

        history_manager = self.canvas.main_window.history_manager
        
        from labelme.commands.cell_commands import SplitCellByColumnCommand

        success_count = 0
        for cell in self.highlighted_cells:
            try:
                cmd = SplitCellByColumnCommand(
                    self.canvas,
                    self,
                    cell,
                    split_ratio
                )
                if history_manager.execute_command(cmd):
                    success_count += 1
            except Exception as e:
                LOGGER.error(f"按列拆分单元格 {cell.label} 失败: {e}")

        return success_count > 0
        
    def get_edge_movement_info(self, shape, edge_index, target_pos):
        """获取边移动信息（供Canvas调用） - 修复版本"""
        if not hasattr(shape, 'shape_type') or shape.shape_type != 'table_cell':
            return None

        if len(shape.points) != 4:
            return None

        # 🔧 修复：基于用户反馈重新定义边索引映射
        # 用户反馈：点击左边时上边在移动，说明边索引映射错误
        # 重新定义映射（可能需要根据实际效果调整）

        LOGGER.debug(f"🔍 [EDGE_DEBUG] 原始边索引: {edge_index}")

        # 🔧 方案1：尝试逆时针映射
        edge_index_to_name_v1 = {
            0: 'left',  # 如果用户点击左边得到索引0
            1: 'top',  # 那么上边可能是索引1
            2: 'right',  # 右边是索引2
            3: 'bottom'  # 下边是索引3
        }

        # 🎯 使用方案1先尝试（基于用户反馈推测）
        edge_index_to_name = edge_index_to_name_v1

        if edge_index not in edge_index_to_name:
            LOGGER.error(f"[EDGE_DEBUG] 无效边索引: {edge_index}")
            return None

        edge_name = edge_index_to_name[edge_index]
        LOGGER.debug(f"🔍 [EDGE_DEBUG] 边索引 {edge_index} → 边名称 '{edge_name}'")

        # 计算当前边位置和移动距离
        current_pos = self._get_edge_current_position(shape, edge_name)

        if edge_name in ['top', 'bottom']:
            delta_movement = target_pos.y() - current_pos
            LOGGER.debug(
                f"🔍 [EDGE_DEBUG] 水平边移动: 目标Y={target_pos.y():.1f}, 当前Y={current_pos:.1f}, 差值={delta_movement:.1f}")
        else:  # left, right
            delta_movement = target_pos.x() - current_pos
            LOGGER.debug(
                f"🔍 [EDGE_DEBUG] 垂直边移动: 目标X={target_pos.x():.1f}, 当前X={current_pos:.1f}, 差值={delta_movement:.1f}")

        result = {
            'edge_name': edge_name,
            'delta_movement': delta_movement,
            'current_pos': current_pos,
            'target_pos': target_pos,
            'original_edge_index': edge_index  # 保留原始索引用于调试
        }

        LOGGER.debug(f"🔍 [EDGE_DEBUG] 边移动信息: {result}")
        return result

    def calculate_new_edge_position(self, cell, edge_name, delta_movement):
        """计算单元格边移动后的新位置（不直接修改单元格）

        Args:
            cell: 目标单元格
            edge_name: 边名称 ('top', 'bottom', 'left', 'right')
            delta_movement: 移动距离

        Returns:
            List[QtCore.QPointF]: 新的顶点坐标列表，失败返回None
        """
        if len(cell.points) != 4:
            LOGGER.error(f"[CALC_EDGE] 单元格 {cell.label} 顶点数不正确: {len(cell.points)}")
            return None

        try:
            # 🔧 边名称到顶点索引的映射
            edge_vertex_mapping = {
                'top': [0, 1],  # 上边：左上(0), 右上(1)
                'right': [1, 2],  # 右边：右上(1), 右下(2)
                'bottom': [2, 3],  # 下边：右下(2), 左下(3)
                'left': [0, 3]  # 左边：左上(0), 左下(3)
            }

            if edge_name not in edge_vertex_mapping:
                LOGGER.error(f"[CALC_EDGE] 无效边名称: {edge_name}")
                return None

            vertex_indices = edge_vertex_mapping[edge_name]

            # 🔧 复制原始坐标，避免修改原单元格
            new_points = [QtCore.QPointF(p.x(), p.y()) for p in cell.points]

            # 🔧 计算新的坐标
            if edge_name in ['top', 'bottom']:
                # 水平边：移动Y坐标
                for vertex_index in vertex_indices:
                    new_y = new_points[vertex_index].y() + delta_movement
                    new_points[vertex_index].setY(new_y)
            else:
                # 垂直边：移动X坐标
                for vertex_index in vertex_indices:
                    new_x = new_points[vertex_index].x() + delta_movement
                    new_points[vertex_index].setX(new_x)

            # 🔧 尺寸约束检查
            if not self._validate_cell_size_constraints(new_points):
                LOGGER.debug(f"⚠️ [CALC_EDGE] 单元格 {cell.label} 尺寸约束检查失败")
                return None

            LOGGER.debug(f"✅ [CALC_EDGE] 单元格 {cell.label} 新位置计算成功")
            return new_points

        except Exception as e:
            LOGGER.error(f"[CALC_EDGE] 单元格 {cell.label} 位置计算异常: {e}")
            return None

    def batch_calculate_new_edge_positions(self, cells, edge_name, delta_movement):
        """批量计算单元格边移动后的新位置

        Args:
            cells: 单元格列表
            edge_name: 边名称
            delta_movement: 移动距离

        Returns:
            List[List[QtCore.QPointF]]: 所有单元格的新位置列表，失败的单元格对应None
        """
        LOGGER.debug(f"🔧 [BATCH_CALC] 批量计算新位置: {edge_name}边, 距离: {delta_movement:.2f}")

        new_positions = []
        success_count = 0

        for i, cell in enumerate(cells):
            new_pos = self.calculate_new_edge_position(cell, edge_name, delta_movement)
            new_positions.append(new_pos)

            if new_pos:
                success_count += 1
                LOGGER.debug(f"   ✅ 单元格{i} ({cell.label}) 计算成功")
            else:
                LOGGER.error(f"单元格{i} ({cell.label}) 计算失败")

        LOGGER.debug(f"🔧 [BATCH_CALC] 批量计算完成: {success_count}/{len(cells)} 成功")

        # 如果有任何一个失败，整个批量操作失败
        if success_count != len(cells):
            LOGGER.error(f"[BATCH_CALC] 批量计算失败，有单元格计算失败")
            return None

        return new_positions
    def _get_edge_current_position(self, shape, edge_name):
        """获取边的当前位置坐标"""
        points = shape.points

        edge_position_mapping = {
            'top': points[0].y(),  # 上边的Y坐标
            'bottom': points[2].y(),  # 下边的Y坐标
            'left': points[0].x(),  # 左边的X坐标
            'right': points[1].x()  # 右边的X坐标
        }

        return edge_position_mapping.get(edge_name, 0.0)

    def handle_cell_text_editing(self, cell_shape):
        """处理单元格文本编辑请求"""
        # 检查是否是表格单元格
        if not (hasattr(cell_shape, 'shape_type') and cell_shape.shape_type == 'table_cell'):
            LOGGER.warning("尝试编辑非表格单元格的文本")
            return False
            
        # 检查单元格是否属于当前表格
        if cell_shape not in self.table_cells:
            LOGGER.warning("尝试编辑不属于当前表格的单元格")
            return False
            
        # 触发文本编辑（由Canvas处理具体的编辑器显示）
        LOGGER.info(f"开始编辑单元格文本: {cell_shape.label}")
        return True
    
    def handle_cell_text_changed(self, cell_shape, old_text, new_text):
        """处理单元格文本变更，添加到历史记录"""
        if old_text == new_text:
            return  # 无变化
            
        # 创建文本变更命令
        try:
            from labelme.commands.cell_commands import ChangeCellTextCommand
            
            change_cmd = ChangeCellTextCommand(
                canvas=self.canvas,
                cell_shape=cell_shape,
                old_text=old_text,
                new_text=new_text
            )
            
            # 执行命令（添加到历史记录）
            if hasattr(self.canvas, 'history_manager') and self.canvas.history_manager:
                success = self.canvas.history_manager.execute_command(change_cmd)
                if success:
                    LOGGER.info(f"单元格文本变更已记录到历史: '{old_text}' -> '{new_text}'")
                    
                    # 刷新相关组件
                    self._refresh_structure_widget()
                    self._refresh_properties_widget([cell_shape])
                else:
                    LOGGER.error("文本变更命令执行失败")
            else:
                LOGGER.warning("历史管理器不可用，直接应用文本变更")
                cell_shape.set_cell_text(new_text)
                self.canvas.update()
                
        except ImportError:
            LOGGER.warning("ChangeCellTextCommand不可用，直接应用文本变更")
            cell_shape.set_cell_text(new_text)
            self.canvas.update()

# ===== 工具函数 =====

def create_table_controller_for_canvas(canvas) -> TableController:
    """为Canvas创建表格控制器的便捷函数"""
    controller = TableController(canvas)
    canvas.table_controller = controller
    return controller