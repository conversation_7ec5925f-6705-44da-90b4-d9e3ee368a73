"""
网格线多选器 - 专门处理网格线的框选和多选逻辑

功能：
1. 支持Shift+拖拽框选网格线
2. 自动筛选同方向的网格线
3. 与现有旋转功能集成

作者：AI Assistant
版本：1.0
"""

from typing import List, Optional, Dict, Any
from PyQt5 import QtCore, QtGui
from labelme.utils.log import get_logger
from labelme.utils.grid_line_detector import GridLine

LOGGER = get_logger()


class GridLineMultiSelector(QtCore.QObject):
    """网格线多选器 - 处理网格线的框选和多选逻辑"""
    
    # 信号定义
    selection_changed = QtCore.pyqtSignal(list)  # 网格线选择变更信号
    
    def __init__(self, canvas):
        super().__init__()
        self.canvas = canvas
        
        # 选择状态
        self._is_box_selecting = False
        self._selection_box = None  # {"start": QPointF, "current": QPointF}
        self._selected_grid_lines = []  # 当前选中的网格线
        
        LOGGER.debug("GridLineMultiSelector初始化完成")
    
    def is_box_selecting(self) -> bool:
        """检查是否正在框选"""
        return self._is_box_selecting
    
    def get_selection_box(self) -> Optional[Dict]:
        """获取当前选择框"""
        return self._selection_box
    
    def get_selected_lines(self) -> List[GridLine]:
        """获取当前选中的网格线"""
        return self._selected_grid_lines.copy()
    
    def start_box_selection(self, pos: QtCore.QPointF) -> bool:
        """开始框选网格线
        
        Args:
            pos: 起始位置
            
        Returns:
            True表示开始成功
        """
        # 只在网格拖拽模式下且点击空白区域时开始框选
        if not self.canvas.is_grid_drag_mode():
            LOGGER.debug("[GRID_MULTI_SELECT] 不在网格拖拽模式下，无法开始框选")
            return False

        # 检查是否点击在网格线上
        highlighted_line = getattr(self.canvas, 'highlighted_grid_line', None)
        if highlighted_line:
            LOGGER.debug(f"[GRID_MULTI_SELECT] 点击在网格线上，不开始框选: {highlighted_line}")
            return False  # 点击在网格线上，不开始框选
        
        self._selection_box = {
            "start": pos,
            "current": pos
        }
        self._is_box_selecting = True

        LOGGER.info(f"[GRID_MULTI_SELECT] ✅ 成功开始框选网格线，起始位置: ({pos.x():.1f}, {pos.y():.1f})")
        LOGGER.info(f"[GRID_MULTI_SELECT] 框选状态: is_box_selecting={self._is_box_selecting}")
        return True
    
    def update_box_selection(self, pos: QtCore.QPointF) -> bool:
        """更新框选位置

        Args:
            pos: 当前鼠标位置

        Returns:
            True表示更新成功
        """
        if not self._is_box_selecting or not self._selection_box:
            LOGGER.debug(f"[GRID_MULTI_SELECT] 无法更新框选: is_box_selecting={self._is_box_selecting}, selection_box={self._selection_box is not None}")
            return False

        self._selection_box["current"] = pos
        LOGGER.debug(f"[GRID_MULTI_SELECT] 更新框选位置: ({pos.x():.1f}, {pos.y():.1f})")
        return True
    
    def complete_box_selection(self) -> bool:
        """完成框选
        
        Returns:
            True表示完成成功
        """
        if not self._is_box_selecting or not self._selection_box:
            LOGGER.debug(f"[GRID_MULTI_SELECT] 无法完成框选: is_box_selecting={self._is_box_selecting}, selection_box={self._selection_box is not None}")
            return False

        try:
            # 计算选择矩形
            start = self._selection_box["start"]
            end = self._selection_box["current"]

            min_x = min(start.x(), end.x())
            max_x = max(start.x(), end.x())
            min_y = min(start.y(), end.y())
            max_y = max(start.y(), end.y())

            selection_rect = QtCore.QRectF(min_x, min_y, max_x - min_x, max_y - min_y)
            LOGGER.info(f"[GRID_MULTI_SELECT] 框选区域: ({min_x:.1f}, {min_y:.1f}) - ({max_x:.1f}, {max_y:.1f})")

            # 查找框选范围内的网格线
            grid_lines_in_box = self._find_grid_lines_in_rect(selection_rect)
            LOGGER.info(f"[GRID_MULTI_SELECT] 框选范围内找到 {len(grid_lines_in_box)} 条网格线")

            # 筛选同方向的网格线
            filtered_lines = self._filter_same_direction_lines(grid_lines_in_box)
            LOGGER.info(f"[GRID_MULTI_SELECT] 筛选后剩余 {len(filtered_lines)} 条网格线")

            # 更新选择
            self._update_grid_line_selection(filtered_lines)

            LOGGER.info(f"[GRID_MULTI_SELECT] ✅ 框选完成，最终选中 {len(filtered_lines)} 条网格线")
            
        except Exception as e:
            LOGGER.error(f"[GRID_MULTI_SELECT] 框选完成失败: {e}")
            return False
        finally:
            # 清理框选状态
            self._selection_box = None
            self._is_box_selecting = False
        
        return True
    
    def cancel_box_selection(self):
        """取消框选"""
        self._selection_box = None
        self._is_box_selecting = False
        LOGGER.debug("[GRID_MULTI_SELECT] 框选已取消")
    
    def clear_selection(self):
        """清空选择"""
        self._selected_grid_lines.clear()
        self.selection_changed.emit([])
        LOGGER.debug("[GRID_MULTI_SELECT] 已清空网格线选择")
    
    def _find_grid_lines_in_rect(self, rect: QtCore.QRectF) -> List[GridLine]:
        """查找选择框内的网格线
        
        Args:
            rect: 选择矩形
            
        Returns:
            框选范围内的网格线列表
        """
        selected_lines = []
        
        # 获取当前检测到的网格线
        if not hasattr(self.canvas, 'grid_line_detector') or not self.canvas.grid_line_detector:
            LOGGER.warning("[GRID_MULTI_SELECT] 网格线检测器未初始化")
            return selected_lines

        detected_lines = getattr(self.canvas.grid_line_detector, 'detected_grid_lines', [])
        LOGGER.info(f"[GRID_MULTI_SELECT] 检测器中共有 {len(detected_lines)} 条网格线")

        for i, line in enumerate(detected_lines):
            LOGGER.debug(f"[GRID_MULTI_SELECT] 检查网格线 {i}: {line.direction}, 坐标={line.coordinate:.1f}, 范围={line.start:.1f}-{line.end:.1f}")
            if self._grid_line_intersects_rect(line, rect):
                selected_lines.append(line)
                LOGGER.debug(f"[GRID_MULTI_SELECT] ✅ 网格线 {i} 与框选区域相交")
            else:
                LOGGER.debug(f"[GRID_MULTI_SELECT] ❌ 网格线 {i} 不与框选区域相交")

        LOGGER.info(f"[GRID_MULTI_SELECT] 框选范围内找到 {len(selected_lines)} 条网格线")
        return selected_lines
    
    def _grid_line_intersects_rect(self, line: GridLine, rect: QtCore.QRectF) -> bool:
        """检查网格线是否与矩形相交
        
        Args:
            line: 网格线
            rect: 选择矩形
            
        Returns:
            True表示相交
        """
        if line.direction == "horizontal":
            # 水平线：检查Y坐标是否在矩形范围内，且线段与矩形X范围有重叠
            if rect.top() <= line.coordinate <= rect.bottom():
                line_left = line.start
                line_right = line.end
                rect_left = rect.left()
                rect_right = rect.right()
                
                # 检查线段与矩形X范围是否有重叠
                return not (line_right < rect_left or line_left > rect_right)
        else:  # vertical
            # 垂直线：检查X坐标是否在矩形范围内，且线段与矩形Y范围有重叠
            if rect.left() <= line.coordinate <= rect.right():
                line_top = line.start
                line_bottom = line.end
                rect_top = rect.top()
                rect_bottom = rect.bottom()
                
                # 检查线段与矩形Y范围是否有重叠
                return not (line_bottom < rect_top or line_top > rect_bottom)
        
        return False
    
    def _filter_same_direction_lines(self, lines: List[GridLine]) -> List[GridLine]:
        """筛选同方向的网格线
        
        Args:
            lines: 输入的网格线列表
            
        Returns:
            筛选后的同方向网格线列表
        """
        if not lines:
            return []
        
        # 按方向分组
        horizontal_lines = [line for line in lines if line.direction == "horizontal"]
        vertical_lines = [line for line in lines if line.direction == "vertical"]
        
        # 返回数量最多的方向，如果数量相等则优先返回水平线
        if len(horizontal_lines) >= len(vertical_lines):
            selected_lines = horizontal_lines
            direction_name = "水平"
        else:
            selected_lines = vertical_lines
            direction_name = "垂直"
        
        LOGGER.info(f"[GRID_MULTI_SELECT] 筛选结果: {len(selected_lines)} 条{direction_name}网格线")
        return selected_lines
    
    def _update_grid_line_selection(self, lines: List[GridLine]):
        """更新网格线选择
        
        Args:
            lines: 要选中的网格线列表
        """
        self._selected_grid_lines = lines.copy()
        
        # 更新Canvas的选择状态（复用现有的旋转选择逻辑）
        if hasattr(self.canvas, 'selected_rotation_lines'):
            self.canvas.selected_rotation_lines = lines.copy()
            LOGGER.info(f"[GRID_MULTI_SELECT] ✅ 已更新Canvas的selected_rotation_lines: {len(lines)}条线")
        else:
            # 如果没有这个属性，创建它
            self.canvas.selected_rotation_lines = lines.copy()
            LOGGER.info(f"[GRID_MULTI_SELECT] ✅ 已创建Canvas的selected_rotation_lines: {len(lines)}条线")

        # 发出选择变更信号
        self.selection_changed.emit(lines)
        
        # 更新提示信息
        if lines:
            line_count = len(lines)
            line_type = "横线" if lines[0].direction == "horizontal" else "竖线"
            self.canvas.setToolTip(f"已框选 {line_count} 条{line_type} - 点击端点选择旋转中心")
        else:
            self.canvas.setToolTip("统一网格线拖动模式已激活 - 将鼠标悬停在网格线上开始拖动")
        
        # 触发重绘
        self.canvas.update()
