#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/3 16:30
# <AUTHOR> <EMAIL>
# @FileName: multi_selection_handler.py

"""
多选处理器 - 处理Canvas中的批量选择功能
支持Ctrl+点击和框选两种多选方式
"""

from PyQt5 import QtCore, QtGui
from typing import List, Optional, Set
from labelme.shape import Shape
from labelme.table_shape import TableCellShape

from ..utils.log import get_logger

LOGGER = get_logger()


class MultiSelectionHandler(QtCore.QObject):
    """多选处理器 - 管理Canvas中的批量选择操作"""
    
    # 信号定义
    selection_changed = QtCore.pyqtSignal(list)  # 选择变更信号
    selection_box_changed = QtCore.pyqtSignal()   # 选择框变更信号
    
    def __init__(self, canvas):
        super().__init__()
        self.canvas = canvas
        
        # 选择框状态
        self._selection_box = None  # {"start": QPointF, "current": QPointF}
        self._is_selecting = False
        
        # 选择模式
        self._ctrl_pressed = False
        self._selection_enabled = True
        
        LOGGER.debug("MultiSelectionHandler初始化完成")

    def set_selection_enabled(self, enabled: bool):
        """设置选择功能是否启用"""
        self._selection_enabled = enabled

    def is_selection_enabled(self) -> bool:
        """检查选择功能是否启用"""
        return self._selection_enabled

    def handle_selection_start(self, pos: QtCore.QPointF, event: QtGui.QMouseEvent) -> bool:
        """处理选择开始事件
        
        Args:
            pos: 鼠标位置
            event: 鼠标事件
            
        Returns:
            True表示已处理，False表示未处理
        """
        if not self._selection_enabled:
            return False
            
        # 检查修饰键
        modifiers = event.modifiers()
        self._ctrl_pressed = bool(modifiers & QtCore.Qt.ControlModifier)
        
        # 检查是否点击在形状上
        clicked_shape = self._get_shape_at_point(pos)
        
        if clicked_shape:
            # 点击在形状上，处理单个选择/多选
            return self._handle_shape_click(clicked_shape)
        else:
            # 点击在空白区域，开始框选
            return self._start_box_selection(pos)

    def handle_selection_move(self, pos: QtCore.QPointF) -> bool:
        """处理选择移动事件（拖拽过程中）
        
        Args:
            pos: 当前鼠标位置
            
        Returns:
            True表示已处理，False表示未处理
        """
        if not self._selection_enabled or not self._is_selecting:
            return False
            
        if self._selection_box:
            # 更新选择框
            self._selection_box["current"] = pos
            self.selection_box_changed.emit()
            return True
            
        return False

    def handle_selection_end(self, event: QtGui.QMouseEvent) -> bool:
        """处理选择结束事件
        
        Args:
            event: 鼠标事件
            
        Returns:
            True表示已处理，False表示未处理
        """
        if not self._selection_enabled or not self._is_selecting:
            return False
            
        if self._selection_box:
            # 完成框选
            self._complete_box_selection()
            return True
            
        return False

    def _get_shape_at_point(self, pos: QtCore.QPointF) -> Optional[Shape]:
        """获取指定位置的形状"""
        for shape in reversed(self.canvas.shapes):
            if self.canvas.isVisible(shape) and shape.containsPoint(pos):
                return shape
        return None

    def _handle_shape_click(self, shape: Shape) -> bool:
        """处理形状点击
        
        Args:
            shape: 被点击的形状
            
        Returns:
            True表示已处理
        """
        if self._ctrl_pressed:
            # Ctrl+点击：添加/移除选择
            if shape in self.canvas.selectedShapes:
                self._remove_from_selection(shape)
            else:
                self._add_to_selection(shape)
        else:
            # 普通点击：单选
            self._select_single_shape(shape)
            
        return False  # 让Canvas继续处理其他逻辑

    def _start_box_selection(self, pos: QtCore.QPointF) -> bool:
        """开始框选

        Args:
            pos: 起始位置

        Returns:
            True表示已处理
        """
        # 只有在空白区域且不按Ctrl时才开始框选
        if not self._ctrl_pressed:
            self._selection_box = {
                "start": pos,
                "current": pos
            }
            self._is_selecting = True
            return True

        return False

    def _complete_box_selection(self):
        """完成框选"""
        if not self._selection_box:
            return
            
        # 计算选择矩形
        start = self._selection_box["start"]
        end = self._selection_box["current"]
        
        min_x = min(start.x(), end.x())
        max_x = max(start.x(), end.x())
        min_y = min(start.y(), end.y())
        max_y = max(start.y(), end.y())
        
        selection_rect = QtCore.QRectF(min_x, min_y, max_x - min_x, max_y - min_y)
        
        # 查找框选范围内的形状
        selected_shapes = []
        for shape in self.canvas.shapes:
            if self.canvas.isVisible(shape) and self._shape_in_rect(shape, selection_rect):
                selected_shapes.append(shape)
        
        # 更新选择
        if selected_shapes:
            if self._ctrl_pressed:
                # Ctrl+框选：添加到当前选择
                for shape in selected_shapes:
                    if shape not in self.canvas.selectedShapes:
                        self._add_to_selection(shape)
            else:
                # 普通框选：替换当前选择
                self._select_multiple_shapes(selected_shapes)
        else:
            # 没有选中任何形状，清空选择
            if not self._ctrl_pressed:
                self._clear_selection()
        
        # 清理框选状态
        self._selection_box = None
        self._is_selecting = False
        self.selection_box_changed.emit()

    def _shape_in_rect(self, shape: Shape, rect: QtCore.QRectF) -> bool:
        """检查形状是否在矩形范围内
        
        Args:
            shape: 要检查的形状
            rect: 选择矩形
            
        Returns:
            True表示形状在矩形内
        """
        # 检查形状的边界框是否与选择矩形相交
        shape_rect = shape.boundingRect()
        return rect.intersects(shape_rect)

    def _select_single_shape(self, shape: Shape):
        """单选形状"""
        # 清空现有选择
        self._clear_selection()
        # 添加新选择
        self._add_to_selection(shape)

    def _select_multiple_shapes(self, shapes: List[Shape]):
        """多选形状"""
        # 清空现有选择
        self._clear_selection()

        # 🚀 批量添加优化：避免频繁信号发射
        for shape in shapes:
            if shape not in self.canvas.selectedShapes:
                shape.selected = True
                self.canvas.selectedShapes.append(shape)

        # 最后一次性发射信号
        self._emit_selection_changed()

    def _add_to_selection(self, shape: Shape):
        """添加形状到选择"""
        if shape not in self.canvas.selectedShapes:
            shape.selected = True
            self.canvas.selectedShapes.append(shape)
            self._emit_selection_changed()

    def _remove_from_selection(self, shape: Shape):
        """从选择中移除形状"""
        if shape in self.canvas.selectedShapes:
            shape.selected = False
            self.canvas.selectedShapes.remove(shape)
            self._emit_selection_changed()

    def _clear_selection(self):
        """清空选择"""
        for shape in self.canvas.selectedShapes:
            shape.selected = False
        self.canvas.selectedShapes.clear()
        self._emit_selection_changed()

    def _emit_selection_changed(self):
        """发出选择变更信号"""
        self.canvas.selectionChanged.emit(self.canvas.selectedShapes.copy())
        self.canvas.update()

    def get_selection_box(self) -> Optional[dict]:
        """获取当前选择框"""
        return self._selection_box

    def is_selecting(self) -> bool:
        """检查是否正在框选"""
        return self._is_selecting

    def clear_selection_box(self):
        """清空选择框"""
        self._selection_box = None
        self._is_selecting = False
        self.selection_box_changed.emit()

    def __str__(self) -> str:
        """字符串表示"""
        return f"MultiSelectionHandler(enabled={self._selection_enabled}, selecting={self._is_selecting})" 