import functools
import time
from typing import Literal

import imgviz
import numpy as np
import osam
from loguru import logger
from PyQt5 import QtCore
from PyQt5 import QtGui
from PyQt5 import QtWidgets

import labelme.utils
from labelme._automation import polygon_from_mask
from labelme.shape import Shape
from labelme.table_shape import TableCellShape
from labelme.utils.log import get_logger

LOGGER = get_logger()

# TODO(unknown):
# - [maybe] Find optimal epsilon value.


CURSOR_DEFAULT = QtCore.Qt.ArrowCursor  # type: ignore[attr-defined]
CURSOR_POINT = QtCore.Qt.PointingHandCursor  # type: ignore[attr-defined]
CURSOR_DRAW = QtCore.Qt.CrossCursor  # type: ignore[attr-defined]
CURSOR_MOVE = QtCore.Qt.ClosedHandCursor  # type: ignore[attr-defined]
CURSOR_GRAB = QtCore.Qt.OpenHandCursor  # type: ignore[attr-defined]
CURSOR_SELECT = QtCore.Qt.CrossCursor  # type: ignore[attr-defined]  # 🆕 批量选中模式使用十字光标
CURSOR_RESIZE_HORIZONTAL = QtCore.Qt.SizeHorCursor    # 水平调整（左右边）
CURSOR_RESIZE_VERTICAL = QtCore.Qt.SizeVerCursor      # 垂直调整（上下边）
CURSOR_HAND_DRAG = QtCore.Qt.OpenHandCursor  # 🆕 图像拖动准备状态
CURSOR_HAND_DRAGGING = QtCore.Qt.ClosedHandCursor  # 🆕 图像拖动中状态
CURSOR_TABLE_ROTATE = QtCore.Qt.PointingHandCursor  # 🆕 表格旋转光标
MOVE_SPEED = 5.0


class Canvas(QtWidgets.QWidget):
    zoomRequest = QtCore.pyqtSignal(int, QtCore.QPoint)
    scrollRequest = QtCore.pyqtSignal(int, int)
    newShape = QtCore.pyqtSignal()
    selectionChanged = QtCore.pyqtSignal(list)
    shapeMoved = QtCore.pyqtSignal()
    drawingPolygon = QtCore.pyqtSignal(bool)
    vertexSelected = QtCore.pyqtSignal(bool)
    mouseMoved = QtCore.pyqtSignal(QtCore.QPointF)

    CREATE, EDIT = 0, 1

    # polygon, rectangle, line, or point
    _createMode = "polygon"

    _fill_drawing = False

    def __init__(self, *args, **kwargs):
        self.epsilon = kwargs.pop("epsilon", 10.0)
        self.double_click = kwargs.pop("double_click", "close")
        if self.double_click not in [None, "close"]:
            raise ValueError(
                "Unexpected value for double_click event: {}".format(self.double_click)
            )
        self.num_backups = kwargs.pop("num_backups", 10)
        self._crosshair = kwargs.pop(
            "crosshair",
            {
                "polygon": False,
                "rectangle": True,
                "circle": False,
                "line": False,
                "point": False,
                "linestrip": False,
                "ai_polygon": False,
                "ai_mask": False,
                "table_cell": True,  # 新增：表格单元格显示十字光标
                "quick_table_region": True,  # 🆕 快速表格选区显示十字光标
            },
        )
        super(Canvas, self).__init__(*args, **kwargs)
        # Initialise local state.
        self.mode = self.EDIT
        self.shapes = []
        self.shapesBackups = []
        self.current = None
        self.selectedShapes = []  # save the selected shapes here
        self.selectedShapesCopy = []
        # self.line represents:
        #   - createMode == 'polygon': edge from last point to current
        #   - createMode == 'rectangle': diagonal line of the rectangle
        #   - createMode == 'line': the line
        #   - createMode == 'point': the point
        self.line = Shape()
        self.prevPoint = QtCore.QPoint()
        self.prevMovePoint = QtCore.QPoint()
        self.offsets = QtCore.QPoint(), QtCore.QPoint()
        self.scale = 1.0
        self.pixmap = QtGui.QPixmap()
        self.visible = {}
        self._hideBackround = False
        self.hideBackround = False
        self.hShape = None
        self.prevhShape = None
        self.hVertex = None
        self.prevhVertex = None
        self.hEdge = None
        self.prevhEdge = None
        self.movingShape = False
        self.snapping = True
        self.hShapeIsSelected = False
        self._painter = QtGui.QPainter()
        self._cursor = CURSOR_DEFAULT
        # 🆕 TableCellShape悬停状态管理
        self._hovered_table_cell = None  # 当前悬停的TableCellShape
        # 🆕 多选处理器
        self.multi_selection_handler = None  # 多选处理器
        self._init_multi_selection_handler()

        # 🆕 网格线多选器
        self.grid_line_multi_selector = None  # 网格线多选器
        self._init_grid_line_multi_selector()

        # 🆕 选择框绘制状态（通过多选处理器管理）
        self._selection_box = None  # 当前选择框: {"start": QPointF, "current": QPointF}
        
        # 🆕 图像拖动状态管理
        self._image_dragging = False  # 是否正在拖动图像
        self._image_drag_start_pos = None  # 拖动开始位置

        # 表格控制器，由外部设置
        self.table_controller = None
        self.multi_table_controller = None
        # Menus:
        # 0: right-click without selection and dragging of shapes
        # 1: right-click with selection and dragging of shapes
        self.menus = (QtWidgets.QMenu(), QtWidgets.QMenu())
        # Set widget options.
        self.setMouseTracking(True)
        self.setFocusPolicy(QtCore.Qt.WheelFocus)  # type: ignore[attr-defined]

        self._ai_model_name: str = "sam2:latest"

        self._vertex_drag_info = None  # 记录顶点拖拽信息：{shapes, vertex_index, old_positions, is_rectangular}
        self._edge_drag_info = None    # 记录边拖拽信息：{shapes, edge_index, old_positions}
        
        # 统一网格线拖动模式状态
        self.unified_grid_drag_mode = False  # 是否处于统一网格线拖动模式
        self.grid_line_detector = None  # 网格线检测器（将在需要时初始化）

        # 🆕 表格整体旋转相关状态
        self.table_rotation_mode = False  # 表格旋转模式标志
        self.table_rotation_center = None  # 表格旋转中心点
        self.table_rotation_start_angle = 0  # 旋转起始角度
        self.rotating_table_cells = []  # 参与旋转的所有表格单元格
        self.table_rotation_original_positions = {}  # 表格旋转前的原始位置
        self.table_rotation_hover_mode = False  # 表格旋转悬停模式标志

        # 导入网格线检测器（延迟导入避免循环依赖）
        from labelme.utils.grid_line_detector import GridLineDetector
        self._GridLineDetector = GridLineDetector

    def _init_multi_selection_handler(self):
        """初始化多选处理器"""
        from labelme.widgets.multi_selection_handler import MultiSelectionHandler
        self.multi_selection_handler = MultiSelectionHandler(self)
        
        # 连接信号
        self.multi_selection_handler.selection_box_changed.connect(self._on_selection_box_changed)
        
        LOGGER.debug("Canvas多选处理器初始化完成")

    def _init_grid_line_multi_selector(self):
        """初始化网格线多选器"""
        from labelme.widgets.grid_line_multi_selector import GridLineMultiSelector
        self.grid_line_multi_selector = GridLineMultiSelector(self)

        # 连接信号
        self.grid_line_multi_selector.selection_changed.connect(self._on_grid_line_selection_changed)

        LOGGER.debug("Canvas网格线多选器初始化完成")

    def _on_grid_line_selection_changed(self, selected_lines):
        """网格线选择变更事件处理"""
        LOGGER.info(f"[GRID_MULTI_SELECT] 网格线选择变更: {len(selected_lines)} 条线")
        # 触发重绘以显示选中状态
        self.update()

    def _on_selection_box_changed(self):
        """选择框变更事件处理"""
        # 获取当前选择框并触发重绘
        if self.multi_selection_handler:
            self._selection_box = self.multi_selection_handler.get_selection_box()
            self.update()

    def set_multi_selection_enabled(self, enabled: bool):
        """设置多选功能是否启用"""
        if self.multi_selection_handler:
            self.multi_selection_handler.set_selection_enabled(enabled)

    def fillDrawing(self):
        return self._fill_drawing

    def setFillDrawing(self, value):
        self._fill_drawing = value

    @property
    def createMode(self):
        return self._createMode

    @createMode.setter
    def createMode(self, value):
        if value not in [
            "polygon",
            "rectangle",
            "circle",
            "line",
            "point",
            "linestrip",
            "ai_polygon",
            "ai_mask",
            "table_cell",  # 新增：支持表格单元格创建
            "quick_table_region",  # 🆕 P1阶段：快速生成表格的选区模式
        ]:
            raise ValueError("Unsupported createMode: %s" % value)
        self._createMode = value

    def set_ai_model_name(self, model_name: str) -> None:
        logger.debug("Setting AI model to {!r}", model_name)
        self._ai_model_name = model_name

    def storeShapes(self):
        shapesBackup = []
        for shape in self.shapes:
            shapesBackup.append(shape.copy())
        if len(self.shapesBackups) > self.num_backups:
            self.shapesBackups = self.shapesBackups[-self.num_backups - 1:]
        self.shapesBackups.append(shapesBackup)

    @property
    def isShapeRestorable(self):
        # We save the state AFTER each edit (not before) so for an
        # edit to be undoable, we expect the CURRENT and the PREVIOUS state
        # to be in the undo stack.
        if len(self.shapesBackups) < 2:
            return False
        return True

    def restoreShape(self):
        # This does _part_ of the job of restoring shapes.
        # The complete process is also done in app.py::undoShapeEdit
        # and app.py::loadShapes and our own Canvas::loadShapes function.
        if not self.isShapeRestorable:
            return
        self.shapesBackups.pop()  # latest

        # The application will eventually call Canvas.loadShapes which will
        # push this right back onto the stack.
        shapesBackup = self.shapesBackups.pop()
        self.shapes = shapesBackup
        self.selectedShapes = []
        for shape in self.shapes:
            shape.selected = False
        self.update()

    def enterEvent(self, ev):
        # 🆕 网格拖拽模式下使用默认光标
        if not self.is_grid_drag_mode():
            self.overrideCursor(self._cursor)

    def leaveEvent(self, ev):
        self.unHighlight()
        self.restoreCursor()
        # 🆕 清除TableCellShape悬停状态
        self._clear_table_cell_hover()
        # 🆕 网格拖拽模式下清除网格线高亮
        if self.is_grid_drag_mode():
            if hasattr(self, 'highlighted_grid_line'):
                self.highlighted_grid_line = None
            self.update()
    def focusOutEvent(self, ev):
        self.restoreCursor()

    def isVisible(self, shape):  # type: ignore[override]
        return self.visible.get(shape, True)

    def drawing(self):
        return self.mode == self.CREATE

    def editing(self):
        return self.mode == self.EDIT

    def setEditing(self, value=True):
        self.mode = self.EDIT if value else self.CREATE
        if self.mode == self.EDIT:
            # CREATE -> EDIT
            self.repaint()  # clear crosshair
        else:
            # EDIT -> CREATE
            self.unHighlight()
            self.deSelectShape()

    def unHighlight(self):
        if self.hShape:
            self.hShape.highlightClear()
            self.update()
        self.prevhShape = self.hShape
        self.prevhVertex = self.hVertex
        self.prevhEdge = self.hEdge
        self.hShape = self.hVertex = self.hEdge = None

    def selectedVertex(self):
        return self.hVertex is not None

    def selectedEdge(self):
        return self.hEdge is not None

    def mouseMoveEvent(self, ev):
        if self.mouseMoveEvent_grid_enhancement(ev):
            return  # 被拦截，不继续处理
        """Update line with last point and current coordinates."""
        try:
            pos = self.transformPos(ev.localPos())
        except AttributeError:
            return

        self.mouseMoved.emit(pos)
        # 🆕 在网格拖拽模式下，跳过所有其他鼠标移动处理
        if self.is_grid_drag_mode():
            return

        # 🆕 图像拖动处理：完全重新设计的简化方案
        if (getattr(self, '_image_dragging', False) and 
            getattr(self, '_image_drag_start_pos', None) is not None and
            QtCore.Qt.LeftButton & ev.buttons()):
            
            current_pos = ev.pos()
            
            # 计算相对于上一次的移动增量
            if not hasattr(self, '_image_last_pos'):
                self._image_last_pos = self._image_drag_start_pos
            
            delta = current_pos - self._image_last_pos
            self._image_last_pos = current_pos
            
            LOGGER.info(f"[CANVAS] 图像拖动中: 当前位置={current_pos}, 增量=({delta.x()}, {delta.y()})")
            
            # 🔧 完全新的方案：直接获取父窗口的滚动区域，手动滚动
            try:
                # 查找父级滚动区域
                parent = self.parent()
                while parent and not isinstance(parent, QtWidgets.QScrollArea):
                    parent = parent.parent()
                
                if parent and isinstance(parent, QtWidgets.QScrollArea):
                    # 直接操作滚动条
                    h_scroll = parent.horizontalScrollBar()
                    v_scroll = parent.verticalScrollBar()
                    
                    # 简单的像素移动，速度适中
                    speed_factor = 0.75  # 提高速度因子
                    
                    if abs(delta.x()) > 0:
                        new_h_value = h_scroll.value() - int(delta.x() * speed_factor)  # 注意方向
                        new_h_value = max(h_scroll.minimum(), min(h_scroll.maximum(), new_h_value))
                        h_scroll.setValue(new_h_value)
                        LOGGER.info(f"[CANVAS] 水平滚动: {h_scroll.value()}")
                    
                    if abs(delta.y()) > 0:
                        new_v_value = v_scroll.value() - int(delta.y() * speed_factor)  # 注意方向
                        new_v_value = max(v_scroll.minimum(), min(v_scroll.maximum(), new_v_value))
                        v_scroll.setValue(new_v_value)
                        LOGGER.info(f"[CANVAS] 垂直滚动: {v_scroll.value()}")
                        
                else:
                    LOGGER.warning("[CANVAS] 未找到滚动区域")
                    
            except Exception as e:
                LOGGER.error(f"[CANVAS] 图像拖动失败: {e}")
            
            return  # 图像拖动时不处理其他鼠标移动逻辑

        self.prevMovePoint = pos
        self.restoreCursor()
        
        # 🆕 多选处理器：选择框移动处理
        if self.multi_selection_handler and self.multi_selection_handler.handle_selection_move(pos):
            return  # 被多选处理器拦截
            
        is_shift_pressed = ev.modifiers() & QtCore.Qt.ShiftModifier
        # 🆕 检测Alt键用于切换矩形/自由调整模式
        is_alt_pressed = ev.modifiers() & QtCore.Qt.AltModifier

        # 🎯 最小化拖拽检测：仅在已选中且移动距离足够时处理
        if (hasattr(self, 'press_start_pos') and self.press_start_pos is not None and
                hasattr(self, 'drag_started') and not self.drag_started and
                self.selectedShapes):
            # 检查是否开始拖拽（简单的距离判断）
            distance = ((pos.x() - self.press_start_pos.x()) ** 2 +
                        (pos.y() - self.press_start_pos.y()) ** 2) ** 0.5
            if distance > 8:  # 8像素阈值，避免误触
                # 🔧 关键修复：只有在非边检测和非顶点检测时才启动形状拖动
                if not self.selectedEdge() and not self.selectedVertex():
                    self.drag_started = True
                    LOGGER.debug(f"[CANVAS] 开始拖拽形状，距离: {distance:.1f}")
                else:
                    LOGGER.debug(f"[CANVAS] 跳过形状拖动：边或顶点拖动优先，距离: {distance:.1f}")

            # 🎯 修复：如果已经开始拖拽且有选中形状，处理拖拽移动
            # 但要确保不干扰边拖动和顶点拖动
        if (hasattr(self, 'drag_started') and self.drag_started and
                self.selectedShapes and not self.selectedVertex() and not self.selectedEdge()):
            # 只处理形状移动，不处理顶点调整和边调整
            LOGGER.debug(f"[CANVAS] 执行形状移动")
            if self.boundedMoveShapes(self.selectedShapes, pos):
                self.repaint()
            return

        # Polygon drawing.
        if self.drawing():
            if self.createMode in ["ai_polygon", "ai_mask"]:
                self.line.shape_type = "points"
            elif self.createMode == "quick_table_region":
                # 🆕 快速表格选区模式使用rectangle类型
                self.line.shape_type = "rectangle"
            else:
                self.line.shape_type = self.createMode

            self.overrideCursor(CURSOR_DRAW)
            if not self.current:
                self.repaint()  # draw crosshair
                return

            if self.outOfPixmap(pos):
                # Don't allow the user to draw outside the pixmap.
                # Project the point to the pixmap's edges.
                pos = self.intersectionPoint(self.current[-1], pos)
            elif (
                    self.snapping
                    and len(self.current) > 1
                    and self.createMode == "polygon"
                    and self.closeEnough(pos, self.current[0])
            ):
                # Attract line to starting point and
                # colorise to alert the user.
                pos = self.current[0]
                self.overrideCursor(CURSOR_POINT)
                self.current.highlightVertex(0, Shape.NEAR_VERTEX)
            if self.createMode in ["polygon", "linestrip"]:
                self.line.points = [self.current[-1], pos]
                self.line.point_labels = [1, 1]
            elif self.createMode in ["ai_polygon", "ai_mask"]:
                self.line.points = [self.current.points[-1], pos]
                self.line.point_labels = [
                    self.current.point_labels[-1],
                    0 if is_shift_pressed else 1,
                ]
            elif self.createMode == "rectangle":
                self.line.points = [self.current[0], pos]
                self.line.point_labels = [1, 1]
                self.line.close()
            elif self.createMode == "quick_table_region":
                # 🆕 快速表格选区模式，行为类似rectangle，但使用灰色
                self.line.points = [self.current[0], pos]
                self.line.point_labels = [1, 1]
                self.line.close()
                # ⚠️ 注意：shape_type已经在上面统一设置为rectangle了
                # 设置选区为灰色
                self.line.line_color = QtGui.QColor(128, 128, 128, 180)  # 灰色边框
                self.line.fill_color = QtGui.QColor(128, 128, 128, 60)   # 灰色填充
            elif self.createMode == "circle":
                self.line.points = [self.current[0], pos]
                self.line.point_labels = [1, 1]
                self.line.shape_type = "circle"
            elif self.createMode == "line":
                self.line.points = [self.current[0], pos]
                self.line.point_labels = [1, 1]
                self.line.close()
            elif self.createMode == "point":
                self.line.points = [self.current[0]]
                self.line.point_labels = [1]
                self.line.close()
            assert len(self.line.points) == len(self.line.point_labels)
            self.repaint()
            self.current.highlightClear()
            return

        # Polygon copy moving.
        if QtCore.Qt.RightButton & ev.buttons():  # type: ignore[attr-defined]
            if self.selectedShapesCopy and self.prevPoint:
                self.overrideCursor(CURSOR_MOVE)
                self.boundedMoveShapes(self.selectedShapesCopy, pos)
                self.repaint()
            elif self.selectedShapes:
                self.selectedShapesCopy = [s.copy() for s in self.selectedShapes]
                self.repaint()
            return

        # Polygon/Vertex moving.
        if QtCore.Qt.LeftButton & ev.buttons():  # type: ignore[attr-defined]
                if self.selectedVertex():
                    # 🎯 优先级1：顶点调整（保持现有逻辑）
                    LOGGER.debug(f"[DRAG] 顶点调整模式 - 顶点索引: {self.hVertex}")

                    if is_alt_pressed and hasattr(self.hShape, 'shape_type') and self.hShape.shape_type == 'table_cell':
                        # Alt+拖拽：自由调整顶点模式
                        self.boundedMoveVertex(pos)
                        self.setToolTip("自由调整模式 - 可调整为不规则形状")
                    elif hasattr(self.hShape, 'shape_type') and self.hShape.shape_type == 'table_cell':
                        # 普通拖拽：矩形约束模式
                        self.boundedMoveVertexRectangular(pos)
                        self.setToolTip("矩形调整模式 - 保持矩形形状 (Alt+拖拽切换到自由模式)")
                    else:
                        # 非表格单元格：使用原有逻辑
                        self.boundedMoveVertex(pos)

                    self.repaint()
                    self.movingShape = True

                elif self.selectedEdge():
                    # 🆕 优先级2：边拖动处理（新增的核心逻辑）
                    LOGGER.debug(f"[DRAG] 边拖动模式 - 边索引: {self.hEdge}, 形状: {type(self.hShape).__name__}")

                    if hasattr(self.hShape, 'shape_type') and self.hShape.shape_type == 'table_cell':
                        # TableCellShape的边拖动
                        if self.boundedMoveEdge(pos):
                            LOGGER.debug("[DRAG] 边拖动成功")
                            self.repaint()
                            self.movingShape = True
                        else:
                            LOGGER.error("[DRAG] 边拖动失败")
                    else:
                        LOGGER.warning("[DRAG] 非TableCellShape不支持边拖动")

                elif self.selectedShapes and self.prevPoint:
                    # 🎯 优先级3：形状移动（保持现有逻辑，但添加调试信息）
                    LOGGER.debug(f"[DRAG] 形状移动模式 - 选中形状数: {len(self.selectedShapes)}")

                    self.overrideCursor(CURSOR_MOVE)
                    self.boundedMoveShapes(self.selectedShapes, pos)
                    self.repaint()
                    self.movingShape = True
                return

        # 🎯 重新设计的交互逻辑：动机驱动的分层检测
        # 核心原则：
        # 1. TableCellShape内部点击 → 立即选中（最高优先级）
        # 2. 角点检测 → 仅在选中状态下敏感检测
        # 3. 拖动判断 → 按住+移动距离确认
        
        # 🆕 检测Ctrl/Cmd键状态，显示合适的光标提示
        ctrl_or_cmd_pressed = (ev.modifiers() & QtCore.Qt.ControlModifier) or (ev.modifiers() & QtCore.Qt.MetaModifier)
        if ctrl_or_cmd_pressed and self.editing():
            self.setToolTip("拖动可移动图像")
            self.overrideCursor(CURSOR_HAND_DRAG)
        else:
            self.setToolTip("滚轮滚动图像 | Ctrl/Cmd+拖动移动图像")
        
        # 🎯 第一优先级：TableCellShape形状内部检测
        table_cell_found = False
        for shape in reversed([s for s in self.shapes if self.isVisible(s)]):
            if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell':
                if shape.containsPoint(pos):
                    self._handle_table_cell_hover(shape, pos, is_alt_pressed)
                    table_cell_found = True
                    return
        
        # 🆕 如果没有检测到TableCellShape，清除悬停状态
        if not table_cell_found:
            self._clear_table_cell_hover()
        
        # 🎯 第二优先级：其他形状检测（保持原有逻辑）
        cursor_changed = False
        base_epsilon = max(self.epsilon / self.scale, 8.0)  # 动态精度调整
        
        for shape in reversed([s for s in self.shapes if self.isVisible(s)]):
            # 跳过已经处理过的表格单元格
            if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell':
                continue
                    
                # 🔧 非表格单元格：使用原有检测逻辑但优化精度
                # Look for a nearby vertex to highlight. If that fails,
                # check if we happen to be inside a shape.
                index = shape.nearestVertex(pos, base_epsilon)
                index_edge = shape.nearestEdge(pos, base_epsilon)
                if index is not None:
                    if self.selectedVertex():
                        self.hShape.highlightClear()  # type: ignore[union-attr]
                    self.prevhVertex = self.hVertex = index
                    self.prevhShape = self.hShape = shape
                    self.prevhEdge = self.hEdge
                    self.hEdge = None
                    shape.highlightVertex(index, shape.MOVE_VERTEX)
                    self.overrideCursor(CURSOR_POINT)
                    self.setToolTip(
                        self.tr(
                            "Click & Drag to move point\n"
                            "ALT + SHIFT + Click to delete point"
                        )
                    )
                    self.setStatusTip(self.toolTip())
                    self.update()
                    cursor_changed = True
                    break
                elif index_edge is not None and shape.canAddPoint():
                    if self.selectedVertex():
                        self.hShape.highlightClear()  # type: ignore[union-attr]
                    self.prevhVertex = self.hVertex
                    self.hVertex = None
                    self.prevhShape = self.hShape = shape
                    self.prevhEdge = self.hEdge = index_edge
                    self.overrideCursor(CURSOR_POINT)
                    self.setToolTip(self.tr("ALT + Click to create point"))
                    self.setStatusTip(self.toolTip())
                    self.update()
                    cursor_changed = True
                    break
                elif shape.containsPoint(pos):
                    if self.selectedVertex():
                        self.hShape.highlightClear()  # type: ignore[union-attr]
                    self.prevhVertex = self.hVertex
                    self.hVertex = None
                    self.prevhShape = self.hShape = shape
                    self.prevhEdge = self.hEdge
                    self.hEdge = None
                    self.setToolTip(
                        self.tr("Click & drag to move shape '%s'") % shape.label
                    )
                    self.setStatusTip(self.toolTip())
                    self.overrideCursor(CURSOR_GRAB)
                    self.update()
                    cursor_changed = True
                    break
                    
        if not cursor_changed:
            # Nothing found, clear highlights, reset state.
            self.unHighlight()
            # 🆕 在多选模式下保持合适的光标样式
            if self.multi_selection_handler and self.multi_selection_handler.is_selecting():
                self.overrideCursor(CURSOR_SELECT)
                
        self.vertexSelected.emit(self.hVertex is not None)

    def boundedMoveEdge(self, pos):
        """边拖动的核心实现方法（命令系统集成版本）"""
        if not self.selectedEdge() or not self.hShape:
            LOGGER.error(f"[EDGE_DRAG] 无效状态: selectedEdge={self.selectedEdge()}, hShape={self.hShape}")
            return False

        # 🔧 检查是否为TableCellShape
        if not (hasattr(self.hShape, 'shape_type') and self.hShape.shape_type == 'table_cell'):
            LOGGER.error("[EDGE_DRAG] 非TableCellShape不支持边拖动")
            return False

        LOGGER.debug(f"🔧 [EDGE_DRAG] 开始边拖动: hShape={self.hShape.label}, selectedShapes数量={len(self.selectedShapes)}")
        # 🆕 初始化边拖动状态（只在第一次调用时）
        if self._edge_drag_info is None:
            LOGGER.debug(f"🔧 [EDGE_DRAG] 初始化拖动状态: hShape={self.hShape.label}")
            
            # 🔧 获取所有受影响的单元格
            affected_cells = []
            if len(self.selectedShapes) > 1:
                # 多选：所有选中的TableCellShape
                affected_cells = [shape for shape in self.selectedShapes
                                  if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell']
            else:
                # 单选：当前悬停的形状
                affected_cells = [self.hShape]
            
            # 🔧 记录原始位置
            old_positions = []
            for cell in affected_cells:
                old_pos = [QtCore.QPointF(p.x(), p.y()) for p in cell.points]
                old_positions.append(old_pos)
            
            self._edge_drag_info = {
                'shapes': affected_cells,
                'edge_index': self.hEdge,
                'old_positions': old_positions
            }
        # 边界检查
        if self.outOfPixmap(pos):
            pos = self.intersectionPoint(self.hShape.points[0], pos)
            LOGGER.warning(f"[EDGE_DRAG] 位置被约束到画布内: ({pos.x():.1f}, {pos.y():.1f})")



        # 🔧 查找TableController获取边移动信息
        table_controller = None
        if hasattr(self, 'multi_table_controller') and self.multi_table_controller:
            table_controller = self.multi_table_controller.get_active_controller()
        elif hasattr(self, 'table_controller') and self.table_controller:
            table_controller = self.table_controller

        if not table_controller:
            LOGGER.error("[EDGE_DRAG] 未找到TableController")
            return False

        # 🔧 获取边移动信息
        edge_info = table_controller.get_edge_movement_info(self.hShape, self.hEdge, pos)
        if not edge_info:
            LOGGER.error("[EDGE_DRAG] 无法获取边移动信息")
            return False

        LOGGER.debug(f"🔧 [EDGE_DRAG] 边移动信息: {edge_info}")

        try:
            # 直接更新所有受影响单元格的显示位置（仅用于预览）
            for cell in self._edge_drag_info['shapes']:
                new_pos = table_controller.calculate_new_edge_position(
                    cell, edge_info['edge_name'], edge_info['delta_movement']
                )
                if new_pos:
                    # 直接更新单元格的points（仅预览，不记录到历史）
                    cell.points = new_pos
                else:
                    LOGGER.error(f"[EDGE_DRAG] 无法计算单元格 {cell.label} 的新位置")
                    return False
            
            LOGGER.debug(f"✅ [EDGE_DRAG] 预览更新成功")
            return True
            
        except Exception as e:
            LOGGER.error(f"[EDGE_DRAG] 预览更新失败: {e}")
            return False

    def _handle_table_cell_hover(self, shape, pos, is_alt_pressed):
        """🎯 处理TableCellShape悬停逻辑 - 复用Canvas现有的边检测系统"""

        # 🆕 设置悬停状态
        self._set_table_cell_hover(shape)

        # 计算动态角点检测阈值
        corner_threshold = self._calculate_corner_threshold(shape)

        # 🎯 优先级1：角点检测（保持现有逻辑）
        vertex_index = shape.nearestVertex(pos, corner_threshold)

        if vertex_index is not None and (shape in self.selectedShapes or shape == self.hShape):
            # 🎯 靠近角点：使用现有逻辑
            if self.selectedVertex():
                self.hShape.highlightClear()  # type: ignore[union-attr]

            self.prevhVertex = self.hVertex = vertex_index
            self.prevhShape = self.hShape = shape
            self.prevhEdge = self.hEdge
            self.hEdge = None  # 清除边检测状态

            shape.highlightVertex(vertex_index, shape.MOVE_VERTEX)
            self.overrideCursor(CURSOR_POINT)

            if is_alt_pressed:
                self.setToolTip("Alt+拖拽：自由调整形状")
            else:
                self.setToolTip("拖拽：矩形调整 (Alt切换自由模式)")

            self.setStatusTip(self.toolTip())
            self.update()
            return

        # 🎯 优先级2：边检测（仅在选中状态下进行）
        # 🔧 关键：复用Canvas现有的边检测系统
        if shape in self.selectedShapes:
            base_epsilon = max(1.5 / self.scale, 8.0)  # 使用Canvas现有的阈值计算
            edge_index = shape.nearestEdge(pos, base_epsilon)

            if edge_index is not None:
                # 🎯 靠近边缘：使用Canvas现有的边检测状态管理
                if self.selectedVertex():
                    self.hShape.highlightClear()  # type: ignore[union-attr]

                # 🔧 关键：使用Canvas现有的边检测状态变量
                self.prevhVertex = self.hVertex
                self.hVertex = None
                self.prevhShape = self.hShape = shape
                self.prevhEdge = self.hEdge
                self.hEdge = edge_index  # 使用Canvas现有的hEdge变量

                # 🆕 根据边的方向设置不同的光标（这是唯一的新增逻辑）
                # 由于Canvas现有的nearestEdge返回的是边索引，我们需要判断这是哪条边
                if self._is_horizontal_edge(shape, edge_index):
                    # 水平边（上边、下边）→ 使用水平调整光标 ↔
                    self.overrideCursor(CURSOR_RESIZE_HORIZONTAL)
                    direction = "水平调整"
                else:
                    # 垂直边（左边、右边）→ 使用垂直调整光标 ↕
                    self.overrideCursor(CURSOR_RESIZE_VERTICAL)
                    direction = "垂直调整"

                self.setToolTip(f"拖拽调整边框 ({direction})")
                self.setStatusTip(self.toolTip())
                self.update()
                return

        # 🎯 优先级3：形状内部检测（保持现有逻辑）
        if self.selectedVertex():
            self.hShape.highlightClear()  # type: ignore[union-attr]

        self.prevhVertex = self.hVertex
        self.hVertex = None
        self.prevhShape = self.hShape = shape
        self.prevhEdge = self.hEdge
        self.hEdge = None  # 清除边检测状态

        self.overrideCursor(CURSOR_GRAB)
        self.setToolTip(f"点击选择 '{shape.label}' (拖拽移动)")
        self.setStatusTip(self.toolTip())
        self.update()

    # 同时在 _is_horizontal_edge 方法中也更新映射
    def _is_horizontal_edge(self, shape, edge_index):
        """判断边索引对应的是否为水平边 - 修复版本"""
        # 🔧 基于新的边索引映射更新
        horizontal_edges = [1, 3]

        result = edge_index in horizontal_edges
        return not result
    def _set_table_cell_hover(self, shape):
        """设置TableCellShape悬停状态"""
        # 清除之前的悬停状态
        if self._hovered_table_cell and self._hovered_table_cell != shape:
            if hasattr(self._hovered_table_cell, '_is_hovered'):
                self._hovered_table_cell._is_hovered = False
        
        # 设置新的悬停状态
        if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell':
            if not shape.selected:  # 只有未选中的单元格才显示悬停效果
                shape._is_hovered = True
                self._hovered_table_cell = shape
            else:
                # 选中的单元格不显示悬停效果
                shape._is_hovered = False
                if self._hovered_table_cell == shape:
                    self._hovered_table_cell = None

    def _clear_table_cell_hover(self):
        """清除所有TableCellShape悬停状态"""
        if self._hovered_table_cell:
            if hasattr(self._hovered_table_cell, '_is_hovered'):
                self._hovered_table_cell._is_hovered = False
            self._hovered_table_cell = None

    def _calculate_corner_threshold(self, shape):
        """计算角点检测阈值"""
        # 基础阈值：像素
        base_threshold = 1.5
        
        # 根据缩放级别调整
        scale_adjusted = max(base_threshold / self.scale, 8.0)
        
        # 根据形状大小调整
        shape_rect = shape.boundingRect()
        shape_size = min(shape_rect.width(), shape_rect.height())
        
        if shape_size < 50:  # 小形状，增加敏感度
            scale_adjusted *= 1.5
        elif shape_size > 200:  # 大形状，减少敏感度
            scale_adjusted *= 0.8
        
        return scale_adjusted

    def _should_start_drag(self, current_pos):
        """🎯 智能拖动检测：按住不放 + 像素级移动距离阈值"""
        if not hasattr(self, 'press_start_pos') or self.press_start_pos is None:
            return False
            
        # 计算移动距离
        dx = current_pos.x() - self.press_start_pos.x()
        dy = current_pos.y() - self.press_start_pos.y()
        distance = (dx * dx + dy * dy) ** 0.5
        
        # 动态阈值：根据缩放级别调整
        drag_threshold = max(8.0 / self.scale, 5.0)  # 最小5像素，缩放时调整
        
        return distance > drag_threshold

    def _handle_drag_detection(self, pos, is_alt_pressed):
        """🎯 处理拖动检测逻辑 - 按住+移动距离确认"""
        
        # 计算移动距离
        move_distance = ((pos.x() - self.press_start_pos.x()) ** 2 + 
                        (pos.y() - self.press_start_pos.y()) ** 2) ** 0.5
        
        # 拖动确认阈值
        drag_threshold = 0.1  # 0.1像素移动距离（精细拖动）
        
        if move_distance > drag_threshold and not hasattr(self, 'drag_confirmed'):
            # 🎯 确认拖动意图
            self.drag_confirmed = True
            
            if self.selectedVertex():
                # 在角点：进入调整模式
                if is_alt_pressed:
                    self.setToolTip("自由调整模式 - 可调整为不规则形状")
                else:
                    self.setToolTip("矩形调整模式 - 保持矩形形状")
            elif self.hShape:
                # 在形状内部：进入拖动模式
                self.overrideCursor(CURSOR_MOVE)
                self.setToolTip(f"拖动移动 '{self.hShape.label}'")
        
        # 执行实际的移动逻辑
        if hasattr(self, 'drag_confirmed') and self.drag_confirmed:
            if self.selectedVertex():
                # 角点调整
                if is_alt_pressed and hasattr(self.hShape, 'shape_type') and self.hShape.shape_type == 'table_cell':
                    self.boundedMoveVertex(pos)
                elif hasattr(self.hShape, 'shape_type') and self.hShape.shape_type == 'table_cell':
                    self.boundedMoveVertexRectangular(pos)
                else:
                    self.boundedMoveVertex(pos)
                self.repaint()
                self.movingShape = True
            elif self.selectedShapes and self.prevPoint:
                # 形状拖动
                self.boundedMoveShapes(self.selectedShapes, pos)
                self.repaint()
                self.movingShape = True

    def addPointToEdge(self):
        shape = self.prevhShape
        index = self.prevhEdge
        point = self.prevMovePoint
        if shape is None or index is None or point is None:
            return
        shape.insertPoint(index, point)
        shape.highlightVertex(index, shape.MOVE_VERTEX)
        self.hShape = shape
        self.hVertex = index
        self.hEdge = None
        self.movingShape = True

    def removeSelectedPoint(self):
        shape = self.prevhShape
        index = self.prevhVertex
        if shape is None or index is None:
            return
        shape.removePoint(index)
        shape.highlightClear()
        self.hShape = shape
        self.prevhVertex = None
        self.movingShape = True  # Save changes

    def mousePressEvent_grid_enhancement(self, ev):
        """增强的鼠标按下事件处理 - 添加拖拽开始和旋转模式"""
        if not self.unified_grid_drag_mode:
            return False  # 不处理

        if ev.button() == QtCore.Qt.LeftButton:
            is_shift_pressed = ev.modifiers() & QtCore.Qt.ShiftModifier
            is_ctrl_pressed = ev.modifiers() & QtCore.Qt.ControlModifier
            is_alt_pressed = ev.modifiers() & QtCore.Qt.AltModifier
            # 🆕 检测Ctrl+Alt组合键
            is_ctrl_alt_pressed = is_ctrl_pressed and is_alt_pressed

            LOGGER.debug(f"🔄 [GRID_PRESS] 左键点击，Shift: {is_shift_pressed}, Ctrl+Alt: {is_ctrl_alt_pressed}")
            LOGGER.debug(f"   rotation_mode_line: {hasattr(self, 'rotation_mode_line') and self.rotation_mode_line is not None}")
            LOGGER.debug(f"   highlighted_grid_line: {hasattr(self, 'highlighted_grid_line') and self.highlighted_grid_line is not None}")

            # 🆕 处理表格旋转开始（最高优先级）
            if is_ctrl_alt_pressed:
                pos = self.transformPos(ev.localPos())
                if self._start_table_rotation(pos):
                    LOGGER.info("🔄 [TABLE_ROTATE] 成功开始表格旋转")
                    return True  # 拦截事件

            # 🆕 处理旋转模式的两步选择
            if is_shift_pressed and hasattr(self, 'highlighted_grid_line') and self.highlighted_grid_line:
                LOGGER.debug("🔄 [GRID_PRESS] 处理旋转模式选择")
                return self._handle_rotation_mode_click(ev)

            # 🆕 处理Shift+拖拽框选网格线（第一优先级）
            if is_shift_pressed and self.grid_line_multi_selector:
                pos = self.transformPos(ev.localPos())
                LOGGER.debug(f"🔄 [GRID_PRESS] 尝试开始框选，highlighted_grid_line: {getattr(self, 'highlighted_grid_line', None)}")
                if self.grid_line_multi_selector.start_box_selection(pos):
                    LOGGER.info("🔄 [GRID_PRESS] 成功开始网格线框选")
                    return True  # 拦截事件
                else:
                    LOGGER.debug("🔄 [GRID_PRESS] 框选开始失败，继续处理其他逻辑")

            # 🆕 处理旋转中心点选择
            if hasattr(self, 'selected_rotation_lines') and self.selected_rotation_lines:
                LOGGER.debug("🔄 [GRID_PRESS] 处理端点选择")
                return self._handle_pivot_selection_click(ev)

            # 🆕 检查网格线边拖拽（在网格拖拽模式下）
            grid_edge_result = self._check_grid_edge_drag_start(ev)
            if grid_edge_result:
                LOGGER.debug("🔄 [GRID_PRESS] 开始网格线边拖拽")
                return True  # 拦截事件

            # 原有逻辑：普通网格线拖拽
            if hasattr(self, 'highlighted_grid_line') and self.highlighted_grid_line:
                LOGGER.debug("🔄 [GRID_PRESS] 处理普通网格线拖拽")
                self._start_grid_line_drag(ev)
                return True  # 拦截事件

        # 🆕 点击空白处：清除多选状态
        if hasattr(self, 'selected_rotation_lines') and self.selected_rotation_lines:
            LOGGER.info("🔄 [MULTI_ROTATION] 点击空白处，清除所有选择")
            self.selected_rotation_lines = []
            self.setToolTip("已清除选择 - Shift+点击网格线重新选择")
            self.update()
            return True

        # 在网格拖拽模式下，禁用所有其他鼠标点击操作
        LOGGER.debug("🔒 网格拖拽模式：禁用常规鼠标点击")
        return True  # 拦截事件

    def mouseMoveEvent_grid_enhancement(self, ev):
        """增强的鼠标移动事件处理 - 集成拖拽更新"""
        if not self.unified_grid_drag_mode:
            return False  # 不处理

        pos = self.transformPos(ev.localPos())
        # 🆕 检测修饰键用于切换拖拽模式
        is_alt_pressed = ev.modifiers() & QtCore.Qt.AltModifier
        is_shift_pressed = ev.modifiers() & QtCore.Qt.ShiftModifier
        is_ctrl_pressed = ev.modifiers() & QtCore.Qt.ControlModifier
        # 🆕 检测Ctrl+Alt组合键用于表格旋转
        is_ctrl_alt_pressed = is_ctrl_pressed and is_alt_pressed

        # 🆕 处理网格线框选移动
        if self.grid_line_multi_selector and self.grid_line_multi_selector.is_box_selecting():
            if self.grid_line_multi_selector.update_box_selection(pos):
                # 更新选择框显示
                self._selection_box = self.grid_line_multi_selector.get_selection_box()
                self.update()
                return True  # 拦截事件

        if hasattr(self, 'dragging_grid_line') and self.dragging_grid_line:
            # 🎯 正在拖拽：根据拖拽类型选择处理模式
            if hasattr(self, 'dragging_grid_edge') and self.dragging_grid_edge:
                # 🆕 边拖拽模式
                self._update_grid_edge_drag_position(pos)
                self.setToolTip("网格线边拖拽模式 - 拖拽网格线端点")
            elif hasattr(self, 'rotation_pivot_point') and self.rotation_pivot_point:
                # 🆕 旋转模式：已选择旋转中心，进行旋转拖拽
                self._update_grid_drag_position_rotate(pos)
                self.setToolTip("精细旋转模式 - 最大±15°，限制在另一端点同侧移动")
            elif is_alt_pressed:
                # 🆕 Alt+拖拽：自由移动模式
                self._update_grid_drag_position_free(pos)
                self.setToolTip("自由拖拽模式 - 可任意方向移动网格线")
            else:
                # 原有：平行移动模式
                self._update_grid_drag_position(pos)
                self.setToolTip("平行拖拽模式 - 保持网格线方向 (Alt切换自由模式, Shift选择旋转)")
        elif self.table_rotation_mode:
            # 🆕 表格旋转模式：执行表格旋转
            self._update_table_rotation(pos)
        else:
            # 悬停检测：查找并高亮网格线
            if is_ctrl_alt_pressed:
                # 🆕 Ctrl+Alt+悬停：表格旋转悬停模式
                self._handle_table_rotation_hover(pos)
            elif is_shift_pressed:
                # Shift+悬停：显示旋转模式提示
                self._update_grid_hover_highlight_with_rotation_hints(pos)
            else:
                # 🆕 退出表格旋转悬停模式（如果不再按住Ctrl+Alt）
                self._exit_table_rotation_hover_mode()

                # 🔧 新增：检查是否有选中的线段，如果有则检测端点悬停
                if hasattr(self, 'selected_rotation_lines') and self.selected_rotation_lines:
                    self._update_rotation_endpoint_hover(pos)
                else:
                    # 普通悬停
                    self._update_grid_hover_highlight(pos)

        # 🔧 关键：触发重绘以显示实时更新
        self.update()
        return True  # 拦截事件

    def _start_grid_line_drag(self, ev):
        """开始网格线拖拽"""
        pos = self.transformPos(ev.localPos())

        # 🎯 设置拖拽状态
        self.dragging_grid_line = self.highlighted_grid_line
        self.drag_start_pos = pos
        self.drag_start_coordinate = self.highlighted_grid_line.coordinate

        # 🔧 记录原始网格线用于恢复
        self.original_grid_line = self.highlighted_grid_line

        # 🆕 保存所有受影响单元格的原始位置（用于自由拖拽）
        self._save_original_cell_positions()

        # 清除悬停高亮，避免同时显示两条线
        self.highlighted_grid_line = None

        LOGGER.info(f"🎯 开始拖拽{self.dragging_grid_line.direction}网格线，起始坐标={self.drag_start_coordinate:.1f}")

    def _save_original_cell_positions(self):
        """🆕 保存所有受影响单元格的原始位置"""
        if not self.original_grid_line or not self.original_grid_line.affected_cells:
            self.original_cell_positions = {}
            return

        self.original_cell_positions = {}
        for cell in self.original_grid_line.affected_cells:
            if hasattr(cell, 'points'):
                # 深拷贝原始位置
                self.original_cell_positions[id(cell)] = [
                    QtCore.QPointF(p.x(), p.y()) for p in cell.points
                ]

        LOGGER.debug(f"💾 [FREE_DRAG] 已保存 {len(self.original_cell_positions)} 个单元格的原始位置")

    def _update_grid_drag_position(self, pos):
        """更新拖拽位置 - 实时更新dragging_grid_line的坐标"""
        if not self.dragging_grid_line:
            return

        # 🎯 计算新坐标
        if self.dragging_grid_line.direction == "vertical":
            new_coordinate = pos.x()
            # 可选：添加约束，防止拖拽到画布外
            new_coordinate = max(0, min(self.pixmap.width(), new_coordinate))
        else:  # horizontal
            new_coordinate = pos.y()
            # 可选：添加约束，防止拖拽到画布外
            new_coordinate = max(0, min(self.pixmap.height(), new_coordinate))

        # 🔧 创建新的网格线对象，更新坐标（因为GridLine是NamedTuple，需要_replace）
        self.dragging_grid_line = self.dragging_grid_line._replace(coordinate=new_coordinate)

        LOGGER.info(f"📍 拖拽位置更新: {new_coordinate:.1f}")

    def _update_grid_drag_position_free(self, pos):
        """🆕 自由拖拽模式：允许网格线任意方向移动"""
        if not self.dragging_grid_line:
            return

        # 🎯 计算移动向量（相对于拖拽起始位置）
        movement_vector = QtCore.QPointF(
            pos.x() - self.drag_start_pos.x(),
            pos.y() - self.drag_start_pos.y()
        )

        LOGGER.debug(f"🔄 [FREE_DRAG] 移动向量: ({movement_vector.x():.1f}, {movement_vector.y():.1f})")

        # 🔧 应用自由移动到网格线上的所有单元格（仅预览，不保存）
        self._apply_free_movement_to_grid_cells(movement_vector)

    def _update_grid_drag_position_rotate(self, pos):
        """🆕 旋转拖拽模式：简单直接的旋转逻辑"""
        if not self.dragging_grid_line or not hasattr(self, 'rotation_pivot_point'):
            return

        # 🎯 核心思路：直接计算鼠标移动对应的角度变化，无复杂限制

        # 计算当前鼠标相对于旋转中心的向量
        current_dx = pos.x() - self.rotation_pivot_point.x()
        current_dy = pos.y() - self.rotation_pivot_point.y()

        # 计算起始位置相对于旋转中心的向量
        start_dx = self.drag_start_pos.x() - self.rotation_pivot_point.x()
        start_dy = self.drag_start_pos.y() - self.rotation_pivot_point.y()

        # 使用向量叉积和点积计算角度差（稳定且连续）
        import math
        cross_product = start_dx * current_dy - start_dy * current_dx
        dot_product = start_dx * current_dx + start_dy * current_dy

        # 计算角度差（弧度）
        angle_diff_radians = math.atan2(cross_product, dot_product)
        angle_diff_degrees = math.degrees(angle_diff_radians)

        # 🎯 简单的灵敏度和角度限制
        sensitivity = 0.4  # 降低灵敏度，让旋转更精细
        controlled_angle = angle_diff_degrees * sensitivity

        # 限制最大旋转角度
        max_angle = 15.0
        controlled_angle = max(-max_angle, min(max_angle, controlled_angle))

        LOGGER.debug(f"🔄 [ROTATION] 角度差: {angle_diff_degrees:.1f}°, 控制后: {controlled_angle:.1f}°")

        # 应用旋转
        self._apply_rotation_to_grid_cells(controlled_angle, self.rotation_pivot_point)

    def _update_grid_edge_drag_position(self, pos):
        """🆕 更新网格线边拖拽位置"""
        if not hasattr(self, 'dragging_grid_edge') or not self.dragging_grid_edge:
            return

        if not hasattr(self, 'dragging_grid_line') or not self.dragging_grid_line:
            return

        # 计算移动距离
        dx = pos.x() - self.drag_start_pos.x()
        dy = pos.y() - self.drag_start_pos.y()

        # 根据网格线方向和边索引确定移动方向
        grid_line = self.dragging_grid_line
        edge_index = self.dragging_edge_index

        if grid_line.direction == "vertical":
            # 垂直网格线：只允许水平移动
            movement = dx
            new_coordinate = grid_line.coordinate + movement
        else:
            # 水平网格线：只允许垂直移动
            movement = dy
            new_coordinate = grid_line.coordinate + movement

        # 应用边拖拽到受影响的单元格
        self._apply_grid_edge_movement(new_coordinate, edge_index)

        LOGGER.debug(f"🔄 [GRID_EDGE] 边拖拽更新: 移动 {movement:.1f}px, 新坐标: {new_coordinate:.1f}")

    def _apply_grid_edge_movement(self, new_coordinate, edge_index):
        """🆕 应用网格线边移动到受影响的单元格"""
        if not self.original_grid_line or not self.original_grid_line.affected_cells:
            return

        grid_line = self.original_grid_line
        affected_cells = grid_line.affected_cells

        # 计算移动距离
        movement = new_coordinate - grid_line.coordinate

        for cell in affected_cells:
            # 获取单元格的原始位置
            cell_id = id(cell)
            if cell_id not in self.original_cell_positions:
                continue

            original_points = self.original_cell_positions[cell_id]
            new_points = []

            for point in original_points:
                new_point = QtCore.QPointF(point.x(), point.y())

                if grid_line.direction == "vertical":
                    # 垂直网格线：根据边索引决定哪些点需要移动
                    if self._should_move_point_for_vertical_edge(cell, point, edge_index, grid_line.coordinate):
                        new_point.setX(point.x() + movement)
                else:
                    # 水平网格线：根据边索引决定哪些点需要移动
                    if self._should_move_point_for_horizontal_edge(cell, point, edge_index, grid_line.coordinate):
                        new_point.setY(point.y() + movement)

                new_points.append(new_point)

            # 更新单元格位置
            cell.points = new_points

    def _should_move_point_for_vertical_edge(self, cell, point, edge_index, grid_coordinate):
        """🆕 判断垂直网格线边拖拽时是否应该移动该点"""
        tolerance = 5.0  # 容差

        # 检查点是否在网格线上
        if abs(point.x() - grid_coordinate) <= tolerance:
            # edge_index 0: 起始边（上边），1: 结束边（下边）
            if edge_index == 0:
                # 移动上边：只移动网格线上方的点
                return self._is_point_above_grid_line(cell, point, grid_coordinate)
            else:
                # 移动下边：只移动网格线下方的点
                return self._is_point_below_grid_line(cell, point, grid_coordinate)
        return False

    def _should_move_point_for_horizontal_edge(self, cell, point, edge_index, grid_coordinate):
        """🆕 判断水平网格线边拖拽时是否应该移动该点"""
        tolerance = 5.0  # 容差

        # 检查点是否在网格线上
        if abs(point.y() - grid_coordinate) <= tolerance:
            # edge_index 0: 起始边（左边），1: 结束边（右边）
            if edge_index == 0:
                # 移动左边：只移动网格线左侧的点
                return self._is_point_left_of_grid_line(cell, point, grid_coordinate)
            else:
                # 移动右边：只移动网格线右侧的点
                return self._is_point_right_of_grid_line(cell, point, grid_coordinate)
        return False

    def _is_point_above_grid_line(self, cell, point, grid_coordinate):
        """🆕 判断点是否在垂直网格线上方"""
        # 简化实现：检查单元格中心是否在网格线上方
        center_y = sum(p.y() for p in cell.points) / len(cell.points)
        return center_y < grid_coordinate

    def _is_point_below_grid_line(self, cell, point, grid_coordinate):
        """🆕 判断点是否在垂直网格线下方"""
        center_y = sum(p.y() for p in cell.points) / len(cell.points)
        return center_y > grid_coordinate

    def _is_point_left_of_grid_line(self, cell, point, grid_coordinate):
        """🆕 判断点是否在水平网格线左侧"""
        center_x = sum(p.x() for p in cell.points) / len(cell.points)
        return center_x < grid_coordinate

    def _is_point_right_of_grid_line(self, cell, point, grid_coordinate):
        """🆕 判断点是否在水平网格线右侧"""
        center_x = sum(p.x() for p in cell.points) / len(cell.points)
        return center_x > grid_coordinate

    def _calculate_angle_from_pivot(self, pos, pivot_point):
        """🆕 计算从旋转中心到指定点的角度（度数）"""
        import math

        dx = pos.x() - pivot_point.x()
        dy = pos.y() - pivot_point.y()

        # 使用atan2计算角度，转换为度数
        angle_radians = math.atan2(dy, dx)
        angle_degrees = math.degrees(angle_radians)

        return angle_degrees

    def _is_mouse_position_valid_for_rotation(self, current_pos):
        """🆕 检查鼠标位置是否在允许的旋转区域内"""
        if not hasattr(self, 'rotation_pivot_point'):
            return True

        # 获取旋转中心点
        pivot_point = self.rotation_pivot_point

        # 🎯 找到线的另一个端点（非旋转中心的端点）
        other_endpoint = self._get_other_endpoint_for_rotation()
        if not other_endpoint:
            return True  # 如果找不到另一端点，允许移动

        # 🔧 使用半平面限制：鼠标必须与另一端点在旋转中心的同一侧
        is_valid = self._is_mouse_on_same_side(current_pos, pivot_point, other_endpoint)

        if not is_valid:
            LOGGER.debug(f"🚫 [ROTATION] 鼠标在禁止区域: 当前({current_pos.x():.1f}, {current_pos.y():.1f}), 中心({pivot_point.x():.1f}, {pivot_point.y():.1f}), 参考端点({other_endpoint.x():.1f}, {other_endpoint.y():.1f})")

        return is_valid



    def _get_other_endpoint_for_rotation(self):
        """🆕 获取当前旋转线的另一个端点（非旋转中心的端点）"""
        # 🆕 支持多线旋转模式
        if hasattr(self, 'selected_rotation_lines') and self.selected_rotation_lines:
            # 多线模式：使用主拖拽线
            reference_line = self.dragging_grid_line
        else:
            # 单线模式
            reference_line = self.dragging_grid_line

        if not reference_line:
            return None

        # 获取线的两个端点
        endpoints = self._get_grid_line_endpoints(reference_line)
        if len(endpoints) != 2:
            return None

        # 找到非旋转中心的端点
        pivot_point = self.rotation_pivot_point
        tolerance = 10.0  # 容差

        for endpoint in endpoints:
            distance = ((endpoint.x() - pivot_point.x()) ** 2 +
                       (endpoint.y() - pivot_point.y()) ** 2) ** 0.5
            if distance > tolerance:
                # 这是另一个端点
                LOGGER.debug(f"🎯 [ROTATION] 找到另一端点: ({endpoint.x():.1f}, {endpoint.y():.1f})")
                return endpoint

        LOGGER.debug(f"🔍 [ROTATION] 未找到另一端点")
        return None

    def _is_mouse_on_same_side(self, current_pos, pivot_point, reference_endpoint):
        """🆕 检查鼠标是否与参考端点在旋转中心的同一侧"""
        # 计算参考端点相对于旋转中心的向量
        ref_vector_x = reference_endpoint.x() - pivot_point.x()
        ref_vector_y = reference_endpoint.y() - pivot_point.y()

        # 计算当前鼠标相对于旋转中心的向量
        cur_vector_x = current_pos.x() - pivot_point.x()
        cur_vector_y = current_pos.y() - pivot_point.y()

        # 🔧 使用叉积判断是否在同一侧
        # 叉积 = ref_vector × cur_vector = ref_x * cur_y - ref_y * cur_x
        cross_product = ref_vector_x * cur_vector_y - ref_vector_y * cur_vector_x

        # 🎯 同一侧：叉积符号相同或为0
        # 这里我们允许在参考端点的同一侧或共线
        return cross_product >= -1.0  # 添加小容差避免边界抖动



    def _apply_rotation_sensitivity_control(self, raw_angle, current_pos, pivot_point):
        """🆕 应用旋转灵敏度控制，实现细颗粒度旋转（修复累积误差）"""

        # 🔧 修复：使用角度归一化，避免累积误差
        normalized_angle = self._normalize_angle(raw_angle)

        # 🎯 方法1：基础灵敏度缩放
        # 降低旋转灵敏度，让鼠标移动更大距离才产生相同角度
        sensitivity_factor = 0.3  # 降低到30%的灵敏度
        controlled_angle = normalized_angle * sensitivity_factor

        # 🎯 方法2：角度限制
        # 限制旋转角度在合理范围内（表格通常只需要小角度调整）
        max_rotation = 15.0  # 最大±15度
        controlled_angle = max(-max_rotation, min(max_rotation, controlled_angle))

        # 🎯 方法3：距离敏感调整
        # 根据鼠标距离旋转中心的远近进一步调整灵敏度
        distance_to_pivot = ((current_pos.x() - pivot_point.x()) ** 2 +
                            (current_pos.y() - pivot_point.y()) ** 2) ** 0.5

        # 距离越近，灵敏度越低（避免小距离时的抖动）
        min_distance = 50.0  # 最小有效距离
        if distance_to_pivot < min_distance:
            distance_factor = distance_to_pivot / min_distance
            controlled_angle *= distance_factor

        # 🎯 方法4：角度量化（可选）
        # 将角度量化到0.1度的精度，减少微小抖动
        quantized_angle = round(controlled_angle, 1)

        return quantized_angle

    def _normalize_angle(self, angle):
        """🔧 角度归一化，将角度限制在-180到180度之间"""
        while angle > 180:
            angle -= 360
        while angle < -180:
            angle += 360
        return angle

    def _apply_rotation_to_grid_cells(self, rotation_angle, pivot_point):
        """🆕 对网格线上的所有单元格应用旋转变换（支持多线旋转）"""
        if not hasattr(self, 'original_cell_positions') or not self.original_cell_positions:
            LOGGER.warning("[ROTATION] 没有保存的原始位置")
            return

        # 🆕 支持多线旋转
        if hasattr(self, 'selected_rotation_lines') and self.selected_rotation_lines:
            # 多线旋转模式
            self._apply_multi_line_rotation(rotation_angle)
        else:
            # 单线旋转模式（保持向后兼容）
            self._apply_single_line_rotation(rotation_angle, pivot_point)

    def _apply_multi_line_rotation(self, rotation_angle):
        """🆕 应用多线旋转"""
        if not hasattr(self, 'rotation_pivot_points') or not self.rotation_pivot_points:
            LOGGER.warning("[MULTI_ROTATION] 没有旋转中心点")
            return

        line_count = len(self.selected_rotation_lines)
        LOGGER.debug(f"🔄 [MULTI_ROTATION] 应用旋转: {line_count}条线, 角度: {rotation_angle:.1f}°")

        # 对每条选中的线应用旋转
        for line in self.selected_rotation_lines:
            line_id = id(line)
            if line_id not in self.rotation_pivot_points:
                continue

            line_pivot = self.rotation_pivot_points[line_id]

            # 对该线的所有受影响单元格应用旋转
            for cell in line.affected_cells:
                cell_id = id(cell)
                if cell_id not in self.original_cell_positions:
                    continue

                original_points = self.original_cell_positions[cell_id]
                if len(original_points) != len(cell.points):
                    continue

                # 找到该单元格上位于当前网格线上的顶点
                affected_points = self._find_grid_line_points_from_original(
                    original_points, line.direction, line.coordinate
                )

                # 应用旋转（只处理还没处理过的点）
                for i, original_point in enumerate(original_points):
                    if self._is_point_near_pivot(original_point, line_pivot):
                        # 旋转中心点保持不变
                        cell.points[i] = QtCore.QPointF(original_point.x(), original_point.y())
                    elif i in affected_points:
                        # 对网格线上的其他顶点应用旋转
                        rotated_point = self._rotate_point(original_point, line_pivot, rotation_angle)
                        cell.points[i] = rotated_point

        LOGGER.debug(f"✅ [MULTI_ROTATION] 多线旋转预览完成")

    def _apply_single_line_rotation(self, rotation_angle, pivot_point):
        """🆕 应用单线旋转（保持向后兼容）"""
        if not self.original_grid_line or not self.original_grid_line.affected_cells:
            LOGGER.warning("[ROTATION] 没有受影响的单元格")
            return

        affected_cells = self.original_grid_line.affected_cells
        direction = self.original_grid_line.direction
        old_coordinate = self.drag_start_coordinate

        LOGGER.debug(f"🔄 [ROTATION] 应用单线旋转: {len(affected_cells)}个单元格, 角度: {rotation_angle:.1f}°")

        for cell in affected_cells:
            cell_id = id(cell)
            if cell_id not in self.original_cell_positions:
                continue

            original_points = self.original_cell_positions[cell_id]
            if len(original_points) != len(cell.points):
                continue

            affected_points = self._find_grid_line_points_from_original(
                original_points, direction, old_coordinate
            )

            for i, original_point in enumerate(original_points):
                if self._is_point_near_pivot(original_point, pivot_point):
                    cell.points[i] = QtCore.QPointF(original_point.x(), original_point.y())
                elif i in affected_points:
                    rotated_point = self._rotate_point(original_point, pivot_point, rotation_angle)
                    cell.points[i] = rotated_point
                else:
                    cell.points[i] = QtCore.QPointF(original_point.x(), original_point.y())

        LOGGER.debug(f"✅ [ROTATION] 单线旋转预览完成")

    def _rotate_point(self, point, pivot, angle_degrees):
        """🆕 绕指定中心点旋转一个点"""
        import math

        # 转换为弧度
        angle_radians = math.radians(angle_degrees)

        # 平移到原点
        dx = point.x() - pivot.x()
        dy = point.y() - pivot.y()

        # 应用旋转矩阵
        cos_angle = math.cos(angle_radians)
        sin_angle = math.sin(angle_radians)

        rotated_x = dx * cos_angle - dy * sin_angle
        rotated_y = dx * sin_angle + dy * cos_angle

        # 平移回原位置
        final_x = rotated_x + pivot.x()
        final_y = rotated_y + pivot.y()

        return QtCore.QPointF(final_x, final_y)

    def _is_point_near_pivot(self, point, pivot_point, tolerance=5.0):
        """🆕 检查点是否接近旋转中心（用于保护旋转中心不移动）"""
        distance = ((point.x() - pivot_point.x()) ** 2 +
                   (point.y() - pivot_point.y()) ** 2) ** 0.5
        is_near = distance <= tolerance

        LOGGER.debug(f"   检查点是否为旋转中心: 点({point.x():.1f}, {point.y():.1f}) vs 中心({pivot_point.x():.1f}, {pivot_point.y():.1f}), 距离: {distance:.2f}, 是否中心: {is_near}")

        return is_near

    def _update_grid_hover_highlight(self, pos):
        """更新网格线悬停高亮"""
        if not hasattr(self, 'grid_line_detector') or not self.grid_line_detector:
            LOGGER.warning("🔍 grid_line_detector未初始化")
            return

        detected_lines = getattr(self.grid_line_detector, 'detected_grid_lines', [])
        LOGGER.info(f"🔍 当前检测到的网格线数量: {len(detected_lines)}")

        # 查找鼠标位置的网格线
        hovered_line = self.grid_line_detector.find_line_at_position(pos, detection_radius=8.0)
        if hovered_line:
            LOGGER.debug(f"🔍 检测到悬停网格线: {hovered_line.direction}, 坐标={hovered_line.coordinate:.1f}")
        else:
            LOGGER.debug(f"🔍 鼠标位置({pos.x():.1f}, {pos.y():.1f})没有检测到网格线")
        # 更新高亮状态
        old_highlighted = getattr(self, 'highlighted_grid_line', None)
        self.highlighted_grid_line = hovered_line

        if hovered_line != old_highlighted:
            if hovered_line:
                LOGGER.info(f"✅ 设置高亮网格线: {hovered_line.direction}, 坐标={hovered_line.coordinate:.1f}")
            else:
                LOGGER.info("✅ 清除高亮网格线")
        # 更新光标
        if hovered_line:
            if hovered_line.direction == "vertical":
                self.setCursor(QtCore.Qt.SizeHorCursor)  # ↔ 光标
            else:  # horizontal
                self.setCursor(QtCore.Qt.SizeVerCursor)  # ↕ 光标

            # 如果是新的高亮线，输出调试信息
            if hovered_line != old_highlighted:
                LOGGER.debug(f"🔍 高亮{hovered_line.direction}网格线: 坐标={hovered_line.coordinate:.1f}")
        else:
            self.setCursor(QtCore.Qt.ArrowCursor)  # 恢复默认光标

    def _update_grid_hover_highlight_with_rotation_hints(self, pos):
        """🆕 带旋转提示的悬停高亮"""
        # 先执行普通的悬停检测
        self._update_grid_hover_highlight(pos)

        # 如果有高亮的网格线，设置旋转提示
        if hasattr(self, 'highlighted_grid_line') and self.highlighted_grid_line:
            self.setToolTip("Shift+点击选择此网格线进行旋转")

    def _update_rotation_endpoint_hover(self, pos):
        """🆕 更新旋转端点悬停状态"""
        if not hasattr(self, 'selected_rotation_lines') or not self.selected_rotation_lines:
            return

        # 检查是否悬停在任何端点上
        hovered_endpoint = None
        hovered_line = None

        for line in self.selected_rotation_lines:
            endpoints = self._get_grid_line_endpoints(line)
            for endpoint in endpoints:
                distance = ((pos.x() - endpoint.x()) ** 2 +
                           (pos.y() - endpoint.y()) ** 2) ** 0.5

                if distance <= 25:  # 端点检测容差
                    hovered_endpoint = endpoint
                    hovered_line = line
                    break

            if hovered_endpoint:
                break

        # 更新悬停状态
        old_hovered = getattr(self, 'hovered_rotation_endpoint', None)
        self.hovered_rotation_endpoint = hovered_endpoint
        self.hovered_rotation_line = hovered_line

        # 设置光标和工具提示
        if hovered_endpoint:
            # 🎯 设置旋转光标
            self.setCursor(QtCore.Qt.PointingHandCursor)
            self.setToolTip("点击设置旋转中心点")

            # 如果是新的悬停端点，记录日志
            if hovered_endpoint != old_hovered:
                LOGGER.debug(f"🎯 [ENDPOINT_HOVER] 悬停在端点: ({hovered_endpoint.x():.1f}, {hovered_endpoint.y():.1f})")
        else:
            # 恢复默认状态
            self.setCursor(QtCore.Qt.ArrowCursor)
            self.setToolTip("已选择网格线 - 点击端点设置旋转中心，或点击空白处清除选择")

        # 如果悬停状态改变，触发重绘
        if hovered_endpoint != old_hovered:
            self.update()

    def mousePressEvent(self, ev):
        # 🆕 网格拖拽模式事件拦截
        if self.mousePressEvent_grid_enhancement(ev):
            return  # 被拦截，不继续处理

        pos = self.transformPos(ev.localPos())

        is_shift_pressed = ev.modifiers() & QtCore.Qt.ShiftModifier  # type: ignore[attr-defined]
        # 检测Ctrl/Cmd键（在macOS上是Command键）——已弃用
        ctrl_or_cmd_pressed = (ev.modifiers() & QtCore.Qt.ControlModifier) or (ev.modifiers() & QtCore.Qt.MetaModifier)

        # 🎯 最小化拖拽状态：只记录起始位置，不做复杂判断
        self.press_start_pos = pos
        self.drag_started = False  # 🆕 简单的拖拽标志

        if ev.button() == QtCore.Qt.LeftButton:  # type: ignore[attr-defined]
            # 🆕 图像拖动：按住Shift键时启动图像拖动模式
            if is_shift_pressed and self.editing():
                self._image_dragging = True
                self._image_drag_start_pos = ev.pos()  # 使用屏幕坐标
                
                # 设置拖动光标
                self.overrideCursor(CURSOR_HAND_DRAGGING)
                LOGGER.info(f"[CANVAS] 开始图像拖动模式，起始位置: {self._image_drag_start_pos}, Shift: {bool(is_shift_pressed)}")
                
                # 🔧 重要修复：设置prevPoint，确保mouseMoveEvent能正常工作
                self.prevPoint = pos
                return  # 不执行其他鼠标按下逻辑
            if self.drawing():
                if self.current:
                    # Add point to existing shape.
                    if self.createMode == "polygon":
                        self.current.addPoint(self.line[1])
                        self.line[0] = self.current[-1]
                        if self.current.isClosed():
                            self.finalise()
                    elif self.createMode in ["rectangle", "circle", "line", "quick_table_region"]:
                        assert len(self.current.points) == 1
                        self.current.points = self.line.points
                        self.finalise()
                    elif self.createMode == "linestrip":
                        self.current.addPoint(self.line[1])
                        self.line[0] = self.current[-1]
                        if int(ev.modifiers()) == QtCore.Qt.ControlModifier:  # type: ignore[attr-defined]
                            self.finalise()
                    elif self.createMode in ["ai_polygon", "ai_mask"]:
                        self.current.addPoint(
                            self.line.points[1],
                            label=self.line.point_labels[1],
                        )
                        self.line.points[0] = self.current.points[-1]
                        self.line.point_labels[0] = self.current.point_labels[-1]
                        if ev.modifiers() & QtCore.Qt.ControlModifier:  # type: ignore[attr-defined]
                            self.finalise()
                elif not self.outOfPixmap(pos):
                    # Create new shape.
                    # 🆕 修复：quick_table_region模式使用rectangle类型
                    if self.createMode in ["ai_polygon", "ai_mask"]:
                        shape_type = "points"
                    elif self.createMode == "quick_table_region":
                        shape_type = "rectangle"
                    else:
                        shape_type = self.createMode
                    
                    self.current = Shape(shape_type=shape_type)
                    self.current.addPoint(pos, label=0 if is_shift_pressed else 1)
                    if self.createMode == "point":
                        self.finalise()
                    elif (
                            self.createMode in ["ai_polygon", "ai_mask"]
                            and ev.modifiers() & QtCore.Qt.ControlModifier  # type: ignore[attr-defined]
                    ):
                        self.finalise()
                    else:
                        if self.createMode == "circle":
                            self.current.shape_type = "circle"
                        self.line.points = [pos, pos]
                        if (
                                self.createMode in ["ai_polygon", "ai_mask"]
                                and is_shift_pressed
                        ):
                            self.line.point_labels = [0, 0]
                        else:
                            self.line.point_labels = [1, 1]
                        self.setHiding()
                        self.drawingPolygon.emit(True)
                        self.update()
            elif self.editing():
                if self.selectedEdge() and ev.modifiers() == QtCore.Qt.AltModifier:
                    self.addPointToEdge()
                elif self.selectedVertex() and ev.modifiers() == (
                        QtCore.Qt.AltModifier | QtCore.Qt.ShiftModifier
                ):
                    self.removeSelectedPoint()
                else:
                    group_mode = int(ev.modifiers()) == QtCore.Qt.ControlModifier

                    # 🆕 优先使用多选处理器处理选择
                    if self.multi_selection_handler and self.multi_selection_handler.is_selection_enabled():
                        if self.multi_selection_handler.handle_selection_start(pos, ev):
                            # 被多选处理器拦截，不执行后续逻辑
                            self.prevPoint = pos
                            self.repaint()
                            return

                    # 继续原有的selectShapePoint逻辑
                    self.selectShapePoint(pos, multiple_selection_mode=group_mode)

                    self.prevPoint = pos
                    self.repaint()
        elif ev.button() == QtCore.Qt.RightButton and self.editing():  # type: ignore[attr-defined]
            group_mode = int(ev.modifiers()) == QtCore.Qt.ControlModifier  # type: ignore[attr-defined]
            if not self.selectedShapes or (
                    self.hShape is not None and self.hShape not in self.selectedShapes
            ):
                self.selectShapePoint(pos, multiple_selection_mode=group_mode)
                self.repaint()
            self.prevPoint = pos

    def _finish_grid_line_drag(self, ev):
        """完成网格线拖拽 - 集成命令系统"""
        if not self.dragging_grid_line:
            return

        pos = self.transformPos(ev.localPos())

        # 计算最终坐标
        if self.dragging_grid_line.direction == "vertical":
            final_coordinate = pos.x()
        else:
            final_coordinate = pos.y()

        # 计算移动距离
        move_distance = abs(final_coordinate - self.drag_start_coordinate)

        LOGGER.info(f"✅ 完成拖拽，最终坐标: {final_coordinate:.1f}，移动距离: {move_distance:.1f}")

        # 🔧 关键修复：区分不同拖拽模式，并创建相应命令
        if hasattr(self, 'dragging_grid_edge') and self.dragging_grid_edge:
            # 🎯 边拖拽模式：创建边拖拽命令
            self._create_and_execute_edge_drag_command()
        elif hasattr(self, 'rotation_pivot_point') and self.rotation_pivot_point:
            # 🎯 旋转模式：创建旋转命令
            self._create_and_execute_rotation_command()
        else:
            # 🎯 普通移动模式：创建平移命令
            if move_distance > 0.1:
                self._create_and_execute_translate_command(final_coordinate)
            else:
                LOGGER.debug("移动距离太小，忽略此次拖拽")

        # 🔧 清理拖拽状态
        self.dragging_grid_line = None
        self.drag_start_pos = None
        self.drag_start_coordinate = None
        self.original_grid_line = None

        # 🆕 清理边拖拽状态
        if hasattr(self, 'dragging_grid_edge'):
            self.dragging_grid_edge = False
        if hasattr(self, 'dragging_edge_index'):
            delattr(self, 'dragging_edge_index')
        if hasattr(self, 'grid_edge_info'):
            delattr(self, 'grid_edge_info')

        # 🆕 清理保存的原始位置
        if hasattr(self, 'original_cell_positions'):
            self.original_cell_positions = {}

        # 🆕 清理旋转模式状态
        self._clear_rotation_mode()

        # 🔧 重新检测网格线（因为单元格位置可能已经改变）
        self._detect_current_grid_lines()

        # 🔧 更新显示
        self.update()

    def _create_and_execute_translate_command(self, final_coordinate):
        """🆕 创建并执行网格线平移命令"""
        try:
            # 收集受影响的单元格和位置信息
            affected_cells = self.original_grid_line.affected_cells if self.original_grid_line else []
            if not affected_cells:
                LOGGER.warning("[GRID_CMD] 没有受影响的单元格，跳过命令创建")
                return

            # 收集原始位置
            old_positions = []
            for cell in affected_cells:
                cell_id = id(cell)
                if cell_id in self.original_cell_positions:
                    old_positions.append(self.original_cell_positions[cell_id])
                else:
                    # 如果没有保存的原始位置，使用当前位置作为原始位置
                    old_positions.append([QtCore.QPointF(p.x(), p.y()) for p in cell.points])

            # 应用移动并收集新位置
            self._apply_grid_line_movement(final_coordinate)
            new_positions = []
            for cell in affected_cells:
                new_positions.append([QtCore.QPointF(p.x(), p.y()) for p in cell.points])

            # 创建网格线信息
            grid_line_info = {
                'direction': self.original_grid_line.direction,
                'coordinate': final_coordinate,
                'old_coordinate': self.drag_start_coordinate
            }

            # 创建平移命令
            from labelme.commands.grid_commands import GridLineTranslateCommand
            translate_command = GridLineTranslateCommand(
                canvas=self,
                affected_cells=affected_cells,
                old_positions=old_positions,
                new_positions=new_positions,
                grid_line_info=grid_line_info
            )

            # 执行命令（通过历史管理器）
            if hasattr(self, 'history_manager') and self.history_manager:
                # 由于移动已经应用，我们需要先撤销，然后通过命令系统执行
                self._restore_cells_to_original_positions(affected_cells, old_positions)
                success = self.history_manager.execute_command(translate_command)
                if success:
                    LOGGER.info(f"✅ [GRID_CMD] 平移命令已执行并记录到历史")
                else:
                    LOGGER.error(f"❌ [GRID_CMD] 平移命令执行失败")
            else:
                LOGGER.warning("[GRID_CMD] 没有历史管理器，跳过命令记录")

        except Exception as e:
            LOGGER.error(f"[GRID_CMD] 创建平移命令失败: {e}")
            import traceback
            traceback.print_exc()

    def _create_and_execute_rotation_command(self):
        """🆕 创建并执行网格线旋转命令"""
        try:
            # 收集受影响的单元格
            affected_cells = []
            if hasattr(self, 'selected_rotation_lines') and self.selected_rotation_lines:
                # 多线旋转模式
                for line in self.selected_rotation_lines:
                    affected_cells.extend(line.affected_cells)
            elif self.original_grid_line:
                # 单线旋转模式
                affected_cells = self.original_grid_line.affected_cells

            # 去重
            affected_cells = list(set(affected_cells))

            if not affected_cells:
                LOGGER.warning("[GRID_CMD] 没有受影响的单元格，跳过旋转命令创建")
                return

            # 收集原始位置和当前位置
            old_positions = []
            new_positions = []
            for cell in affected_cells:
                cell_id = id(cell)
                if cell_id in self.original_cell_positions:
                    old_positions.append(self.original_cell_positions[cell_id])
                    new_positions.append([QtCore.QPointF(p.x(), p.y()) for p in cell.points])
                else:
                    LOGGER.warning(f"[GRID_CMD] 单元格 {cell.label} 没有原始位置记录")

            # 创建旋转信息
            rotation_info = {
                'pivot_point': self.rotation_pivot_point,
                'angle': 0,  # 实际角度已经应用，这里记录为0
                'grid_lines': self.selected_rotation_lines if hasattr(self, 'selected_rotation_lines') else [self.original_grid_line]
            }

            # 创建旋转命令
            from labelme.commands.grid_commands import GridLineRotateCommand
            rotate_command = GridLineRotateCommand(
                canvas=self,
                affected_cells=affected_cells,
                old_positions=old_positions,
                new_positions=new_positions,
                rotation_info=rotation_info
            )

            # 执行命令（通过历史管理器）
            if hasattr(self, 'history_manager') and self.history_manager:
                # 由于旋转已经应用，我们需要先撤销，然后通过命令系统执行
                self._restore_cells_to_original_positions(affected_cells, old_positions)
                success = self.history_manager.execute_command(rotate_command)
                if success:
                    LOGGER.info(f"✅ [GRID_CMD] 旋转命令已执行并记录到历史")
                else:
                    LOGGER.error(f"❌ [GRID_CMD] 旋转命令执行失败")
            else:
                LOGGER.warning("[GRID_CMD] 没有历史管理器，跳过命令记录")

        except Exception as e:
            LOGGER.error(f"[GRID_CMD] 创建旋转命令失败: {e}")
            import traceback
            traceback.print_exc()

    def _restore_cells_to_original_positions(self, affected_cells, old_positions):
        """🆕 恢复单元格到原始位置"""
        try:
            for i, cell in enumerate(affected_cells):
                if i < len(old_positions):
                    cell.points = [QtCore.QPointF(p.x(), p.y()) for p in old_positions[i]]
            self.update()
        except Exception as e:
            LOGGER.error(f"[GRID_CMD] 恢复原始位置失败: {e}")

    def _check_grid_edge_drag_start(self, ev):
        """🆕 检查是否开始网格线边拖拽"""
        pos = self.transformPos(ev.localPos())

        # 检查是否点击在网格线的边缘附近
        if not hasattr(self, 'current_grid_lines') or not self.current_grid_lines:
            return False

        for grid_line in self.current_grid_lines:
            # 检查是否点击在网格线的端点附近（边拖拽）
            endpoints = self._get_grid_line_endpoints(grid_line)
            for i, endpoint in enumerate(endpoints):
                distance = ((pos.x() - endpoint.x()) ** 2 +
                           (pos.y() - endpoint.y()) ** 2) ** 0.5

                if distance <= 15:  # 边拖拽检测容差
                    # 开始边拖拽
                    self._start_grid_edge_drag(ev, grid_line, i)
                    return True

        return False

    def _start_grid_edge_drag(self, ev, grid_line, edge_index):
        """🆕 开始网格线边拖拽"""
        pos = self.transformPos(ev.localPos())

        # 设置边拖拽状态
        self.dragging_grid_edge = True
        self.dragging_grid_line = grid_line
        self.dragging_edge_index = edge_index
        self.drag_start_pos = pos

        # 保存原始状态
        self.original_grid_line = grid_line
        self._save_original_cell_positions()

        # 创建边拖拽信息
        edge_info = {
            'direction': grid_line.direction,
            'coordinate': grid_line.coordinate,
            'edge_type': 'start' if edge_index == 0 else 'end'
        }
        self.grid_edge_info = edge_info

        LOGGER.info(f"🔄 [GRID_EDGE] 开始边拖拽: {edge_info}")
        LOGGER.info(f"   网格线: {grid_line.direction}, 坐标: {grid_line.coordinate}")
        LOGGER.info(f"   边索引: {edge_index}")

    def _create_and_execute_edge_drag_command(self):
        """🆕 创建并执行网格线边拖拽命令"""
        try:
            # 收集受影响的单元格
            affected_cells = self.original_grid_line.affected_cells if self.original_grid_line else []
            if not affected_cells:
                LOGGER.warning("[GRID_CMD] 没有受影响的单元格，跳过边拖拽命令创建")
                return

            # 收集原始位置和当前位置
            old_positions = []
            new_positions = []
            for cell in affected_cells:
                cell_id = id(cell)
                if cell_id in self.original_cell_positions:
                    old_positions.append(self.original_cell_positions[cell_id])
                    new_positions.append([QtCore.QPointF(p.x(), p.y()) for p in cell.points])
                else:
                    LOGGER.warning(f"[GRID_CMD] 单元格 {cell.label} 没有原始位置记录")

            # 创建边拖拽命令
            from labelme.commands.grid_commands import GridLineEdgeDragCommand
            edge_drag_command = GridLineEdgeDragCommand(
                canvas=self,
                affected_cells=affected_cells,
                old_positions=old_positions,
                new_positions=new_positions,
                edge_info=self.grid_edge_info
            )

            # 执行命令（通过历史管理器）
            if hasattr(self, 'history_manager') and self.history_manager:
                # 由于边拖拽已经应用，我们需要先撤销，然后通过命令系统执行
                self._restore_cells_to_original_positions(affected_cells, old_positions)
                success = self.history_manager.execute_command(edge_drag_command)
                if success:
                    LOGGER.info(f"✅ [GRID_CMD] 边拖拽命令已执行并记录到历史")
                else:
                    LOGGER.error(f"❌ [GRID_CMD] 边拖拽命令执行失败")
            else:
                LOGGER.warning("[GRID_CMD] 没有历史管理器，跳过命令记录")

        except Exception as e:
            LOGGER.error(f"[GRID_CMD] 创建边拖拽命令失败: {e}")
            import traceback
            traceback.print_exc()

    def _apply_grid_line_movement(self, new_coordinate):
        """应用网格线移动到单元格"""
        if not self.original_grid_line:  # 使用原始网格线的affected_cells
            return

        affected_cells = self.original_grid_line.affected_cells
        direction = self.original_grid_line.direction
        old_coordinate = self.drag_start_coordinate

        LOGGER.info(f"📝 应用移动: {len(affected_cells)}个单元格，{direction}方向，"
                    f"从{old_coordinate:.1f}移动到{new_coordinate:.1f}")

        # 🎯 复用现有的对齐逻辑，但是针对特定边缘
        if direction == "vertical":
            self._move_cells_vertical_edge(affected_cells, old_coordinate, new_coordinate)
        else:  # horizontal
            self._move_cells_horizontal_edge(affected_cells, old_coordinate, new_coordinate)

        # 触发Canvas重绘
        self.update()

    def _move_cells_horizontal_edge(self, cells, old_y, new_y):
        """移动单元格的水平边缘"""
        tolerance = 1.0  # 容差 - 与网格线检测器保持一致

        for cell in cells:
            for point in cell.points:
                # 🔧 只移动在old_y附近的点
                if abs(point.y() - old_y) <= tolerance:
                    point.setY(new_y)
                    LOGGER.debug(f"移动点: ({point.x():.1f}, {old_y:.1f}) -> ({point.x():.1f}, {new_y:.1f})")

    def _move_cells_vertical_edge(self, cells, old_x, new_x):
        """移动单元格的垂直边缘"""
        tolerance = 1.0  # 容差 - 与网格线检测器保持一致
        moved_points = 0

        for cell in cells:
            for point in cell.points:
                # 🔧 只移动在old_x附近的点
                if abs(point.x() - old_x) <= tolerance:
                    point.setX(new_x)
                    moved_points += 1
                    LOGGER.debug(f"移动点: ({old_x:.1f}, {point.y():.1f}) -> ({new_x:.1f}, {point.y():.1f})")

        LOGGER.info(f"✅ [MOVE_VERTICAL] 成功移动 {moved_points} 个顶点，容差={tolerance}px")
    def _draw_dragging_grid_line(self, painter):
        """绘制正在拖拽的网格线 - 如果需要的话"""

        if not hasattr(self, 'dragging_grid_line') or not self.dragging_grid_line:
            return

        line = self.dragging_grid_line

        painter.save()
        try:
            # 拖拽时用不同颜色和样式
            pen = QtGui.QPen(QtCore.Qt.blue, 3, QtCore.Qt.DashLine)
            painter.setPen(pen)

            # 同样直接使用图像坐标
            if line.direction == "vertical":
                painter.drawLine(
                    QtCore.QPointF(line.coordinate, line.start),
                    QtCore.QPointF(line.coordinate, line.end)
                )
            else:
                painter.drawLine(
                    QtCore.QPointF(line.start, line.coordinate),
                    QtCore.QPointF(line.end, line.coordinate)
                )
        finally:
            painter.restore()
    def _draw_grid_line_highlights(self, painter):
        """在图像坐标系中绘制网格线高亮 - 修复版"""

        if not hasattr(self, 'highlighted_grid_line') or not self.highlighted_grid_line:
            return

        line = self.highlighted_grid_line
        LOGGER.debug(f"🎯 绘制网格线高亮: {line.direction}, 坐标={line.coordinate:.1f}")

        # 保存painter状态
        painter.save()

        try:
            # 设置高亮样式
            pen = QtGui.QPen(QtCore.Qt.red, 3, QtCore.Qt.SolidLine)
            painter.setPen(pen)

            # 🔧 关键修复：直接使用图像坐标，不做额外变换
            # 因为此时painter已经在图像坐标系中了

            if line.direction == "vertical":
                # 绘制垂直高亮线
                painter.drawLine(
                    QtCore.QPointF(line.coordinate, line.start),
                    QtCore.QPointF(line.coordinate, line.end)
                )
                LOGGER.debug(f"绘制垂直线: x={line.coordinate:.1f}, y={line.start:.1f}-{line.end:.1f}")
            else:  # horizontal
                # 绘制水平高亮线
                painter.drawLine(
                    QtCore.QPointF(line.start, line.coordinate),
                    QtCore.QPointF(line.end, line.coordinate)
                )
                LOGGER.debug(f"绘制水平线: y={line.coordinate:.1f}, x={line.start:.1f}-{line.end:.1f}")

        except Exception as e:
            LOGGER.error(f"❌ 绘制网格线高亮失败: {e}")
        finally:
            # 恢复painter状态
            painter.restore()

    def _draw_rotation_mode_visuals(self, painter):
        """🆕 绘制旋转模式的视觉反馈"""
        painter.save()

        try:
            # 🆕 绘制多选的网格线
            if hasattr(self, 'selected_rotation_lines') and self.selected_rotation_lines:
                for line in self.selected_rotation_lines:
                    self._draw_selected_rotation_line(painter, line)
                    # 绘制端点候选
                    self._draw_rotation_endpoints(painter, line)

            # 兼容单选模式
            elif hasattr(self, 'rotation_mode_line') and self.rotation_mode_line:
                self._draw_selected_rotation_line(painter, self.rotation_mode_line)
                self._draw_rotation_endpoints(painter, self.rotation_mode_line)

            # 🆕 绘制多个旋转中心点
            if hasattr(self, 'rotation_pivot_points') and self.rotation_pivot_points:
                for pivot_point in self.rotation_pivot_points.values():
                    self._draw_rotation_pivot(painter, pivot_point)

                # 如果正在拖拽，绘制角度指示线（使用参考中心点）
                if hasattr(self, 'dragging_grid_line') and self.dragging_grid_line and hasattr(self, 'rotation_pivot_point'):
                    self._draw_rotation_angle_indicator(painter)

            # 兼容单选模式
            elif hasattr(self, 'rotation_pivot_point') and self.rotation_pivot_point:
                self._draw_rotation_pivot(painter, self.rotation_pivot_point)
                if hasattr(self, 'dragging_grid_line') and self.dragging_grid_line:
                    self._draw_rotation_angle_indicator(painter)

        except Exception as e:
            LOGGER.error(f"❌ 绘制旋转模式视觉反馈失败: {e}")
        finally:
            painter.restore()

    def _draw_selected_rotation_line(self, painter, grid_line):
        """🆕 绘制选中的待旋转网格线"""
        # 用橙色高亮显示选中的网格线
        pen = QtGui.QPen(QtCore.Qt.darkYellow, 4, QtCore.Qt.SolidLine)
        painter.setPen(pen)

        if grid_line.direction == "vertical":
            painter.drawLine(
                QtCore.QPointF(grid_line.coordinate, grid_line.start),
                QtCore.QPointF(grid_line.coordinate, grid_line.end)
            )
        else:  # horizontal
            painter.drawLine(
                QtCore.QPointF(grid_line.start, grid_line.coordinate),
                QtCore.QPointF(grid_line.end, grid_line.coordinate)
            )

    def _draw_rotation_endpoints(self, painter, grid_line):
        """🆕 绘制旋转中心候选点（端点）"""
        endpoints = self._get_grid_line_endpoints(grid_line)

        for endpoint in endpoints:
            # 🔧 检查是否为悬停端点，使用不同样式
            is_hovered = (hasattr(self, 'hovered_rotation_endpoint') and
                         self.hovered_rotation_endpoint and
                         abs(endpoint.x() - self.hovered_rotation_endpoint.x()) < 1 and
                         abs(endpoint.y() - self.hovered_rotation_endpoint.y()) < 1)

            if is_hovered:
                # 悬停状态：更大更亮的圆圈
                pen = QtGui.QPen(QtCore.Qt.yellow, 3, QtCore.Qt.SolidLine)
                brush = QtGui.QBrush(QtCore.Qt.yellow, QtCore.Qt.SolidPattern)
                radius = 12
            else:
                # 普通状态：蓝色圆圈
                pen = QtGui.QPen(QtCore.Qt.blue, 2, QtCore.Qt.SolidLine)
                brush = QtGui.QBrush(QtCore.Qt.cyan, QtCore.Qt.SolidPattern)
                radius = 8

            painter.setPen(pen)
            painter.setBrush(brush)
            painter.drawEllipse(endpoint, radius, radius)

    def _draw_rotation_pivot(self, painter, pivot_point):
        """🆕 绘制选中的旋转中心点"""
        # 用红色实心圆表示旋转中心
        pen = QtGui.QPen(QtCore.Qt.red, 3, QtCore.Qt.SolidLine)
        brush = QtGui.QBrush(QtCore.Qt.red, QtCore.Qt.SolidPattern)
        painter.setPen(pen)
        painter.setBrush(brush)

        # 绘制实心圆
        painter.drawEllipse(pivot_point, 10, 10)

    def _draw_rotation_angle_indicator(self, painter):
        """🆕 绘制旋转角度指示线（改进版）"""
        if not hasattr(self, 'prevMovePoint') or not self.prevMovePoint:
            return

        pivot = self.rotation_pivot_point
        current_pos = self.prevMovePoint

        # 计算角度信息
        current_angle = self._calculate_angle_from_pivot(current_pos, pivot)
        raw_rotation_angle = current_angle - self.rotation_start_angle
        controlled_rotation_angle = self._apply_rotation_sensitivity_control(
            raw_rotation_angle, current_pos, pivot
        )

        # 🎯 绘制旋转中心到起始位置的参考线（淡色）
        start_pos = self.drag_start_pos
        pen_ref = QtGui.QPen(QtCore.Qt.gray, 1, QtCore.Qt.DotLine)
        painter.setPen(pen_ref)
        painter.drawLine(pivot, start_pos)

        # 🆕 绘制另一端点的参考线（显示允许的移动方向）
        other_endpoint = self._get_other_endpoint_for_rotation()
        if other_endpoint:
            pen_boundary = QtGui.QPen(QtCore.Qt.darkGray, 1, QtCore.Qt.DashLine)
            painter.setPen(pen_boundary)
            painter.drawLine(pivot, other_endpoint)

        # 🎯 绘制旋转中心到当前位置的指示线（亮色）
        pen_current = QtGui.QPen(QtCore.Qt.magenta, 2, QtCore.Qt.SolidLine)
        painter.setPen(pen_current)
        painter.drawLine(pivot, current_pos)

        # 🔧 移除角度弧线，只保留参考线

        # 🎯 显示详细的角度信息
        painter.setPen(QtGui.QPen(QtCore.Qt.black, 1))

        # 创建信息文本
        info_text = f"旋转: {controlled_rotation_angle:.1f}°"
        if abs(controlled_rotation_angle) >= 14.5:  # 接近最大限制时提醒
            info_text += " (接近最大角度)"

        # 在鼠标附近显示角度文本，背景半透明
        text_pos = current_pos + QtCore.QPointF(15, -15)

        # 绘制文本背景
        font_metrics = painter.fontMetrics()
        text_rect = font_metrics.boundingRect(info_text)
        text_rect.moveTopLeft(text_pos.toPoint())
        text_rect.adjust(-3, -2, 3, 2)

        painter.fillRect(text_rect, QtGui.QColor(255, 255, 255, 200))
        painter.drawRect(text_rect)

        # 绘制文本
        painter.drawText(text_pos, info_text)

    def _apply_free_movement_to_grid_cells(self, movement_vector):
        """🆕 对网格线上的所有单元格应用自由移动（复用boundedMoveVertex思路）"""
        if not self.original_grid_line or not self.original_grid_line.affected_cells:
            LOGGER.warning("[FREE_DRAG] 没有受影响的单元格")
            return

        if not hasattr(self, 'original_cell_positions') or not self.original_cell_positions:
            LOGGER.warning("[FREE_DRAG] 没有保存的原始位置")
            return

        affected_cells = self.original_grid_line.affected_cells
        direction = self.original_grid_line.direction
        old_coordinate = self.drag_start_coordinate

        LOGGER.debug(f"🔄 [FREE_DRAG] 应用自由移动: {len(affected_cells)}个单元格")
        LOGGER.debug(f"   网格线方向: {direction}, 原坐标: {old_coordinate:.1f}")
        LOGGER.debug(f"   移动向量: ({movement_vector.x():.1f}, {movement_vector.y():.1f})")

        # 🎯 复用boundedMoveVertex的核心思路：基于原始位置应用移动向量
        for cell in affected_cells:
            cell_id = id(cell)
            if cell_id not in self.original_cell_positions:
                continue

            original_points = self.original_cell_positions[cell_id]
            if len(original_points) != len(cell.points):
                continue

            # 🔧 找到该单元格上位于网格线上的顶点（基于原始位置）
            affected_points = self._find_grid_line_points_from_original(
                original_points, direction, old_coordinate
            )

            # 🔧 基于原始位置应用移动向量
            for i, original_point in enumerate(original_points):
                if i in affected_points:
                    # 对网格线上的顶点应用移动向量
                    cell.points[i] = QtCore.QPointF(
                        original_point.x() + movement_vector.x(),
                        original_point.y() + movement_vector.y()
                    )
                else:
                    # 其他顶点保持原始位置
                    cell.points[i] = QtCore.QPointF(original_point.x(), original_point.y())

        LOGGER.debug(f"✅ [FREE_DRAG] 自由移动预览完成")

    def _handle_rotation_mode_click(self, ev):
        """🆕 处理旋转模式：支持多选同类型网格线"""
        current_line = self.highlighted_grid_line
        if not current_line:
            return True

        # 🆕 初始化多选列表
        if not hasattr(self, 'selected_rotation_lines'):
            self.selected_rotation_lines = []

        # 🆕 检查类型一致性
        if self.selected_rotation_lines:
            # 已有选中的线，检查类型是否一致
            first_line_type = self.selected_rotation_lines[0].direction
            if current_line.direction != first_line_type:
                # 类型不一致，自动清除之前的选择
                LOGGER.info(f"🔄 [MULTI_ROTATION] 类型不一致，清除之前选择: {first_line_type} -> {current_line.direction}")
                self.selected_rotation_lines = []

        # 🆕 添加到选择列表（避免重复）
        if current_line not in self.selected_rotation_lines:
            self.selected_rotation_lines.append(current_line)
            LOGGER.info(f"🔄 [MULTI_ROTATION] 添加网格线: {current_line.direction}, 总数: {len(self.selected_rotation_lines)}")
        else:
            LOGGER.info(f"🔄 [MULTI_ROTATION] 网格线已选中，跳过")

        # 清除普通高亮
        self.highlighted_grid_line = None

        # 更新提示
        line_count = len(self.selected_rotation_lines)
        line_type = "横线" if self.selected_rotation_lines[0].direction == "horizontal" else "竖线"
        self.setToolTip(f"已选中{line_count}条{line_type} - 点击端点选择旋转中心")

        # 触发重绘以显示选中状态
        self.update()
        return True

    def _handle_pivot_selection_click(self, ev):
        """🆕 处理多线旋转的端点选择"""
        pos = self.transformPos(ev.localPos())

        LOGGER.info(f"🔄 [MULTI_ROTATION] 处理端点选择点击，位置: ({pos.x():.1f}, {pos.y():.1f})")

        # 🆕 检查是否点击了某条选中线的端点
        clicked_line, pivot_point, pivot_type = self._find_clicked_line_pivot(pos)
        if clicked_line and pivot_point:
            LOGGER.info(f"🎯 [MULTI_ROTATION] 选择旋转基准: {pivot_type}, 坐标: ({pivot_point.x():.1f}, {pivot_point.y():.1f})")

            # 🆕 为所有选中的线计算对应的旋转中心点
            self.rotation_pivot_points = self._calculate_corresponding_pivot_points(clicked_line, pivot_type)

            # 开始多线旋转拖拽
            self._start_multi_rotation_drag(ev, clicked_line, pivot_point)
            return True
        else:
            # 点击了其他地方，取消旋转模式
            LOGGER.info("🔄 [MULTI_ROTATION] 未点击端点，取消旋转模式")
            self._clear_rotation_mode()
            self.setToolTip("旋转模式已取消")
            self.update()
            return True

    def _clear_rotation_mode(self):
        """🔧 清除旋转模式状态"""
        if hasattr(self, 'rotation_mode_line'):
            delattr(self, 'rotation_mode_line')
        if hasattr(self, 'rotation_pivot_point'):
            delattr(self, 'rotation_pivot_point')
        if hasattr(self, 'rotation_start_angle'):
            delattr(self, 'rotation_start_angle')
        # 🆕 清除多选状态
        if hasattr(self, 'selected_rotation_lines'):
            delattr(self, 'selected_rotation_lines')
        if hasattr(self, 'rotation_pivot_points'):
            delattr(self, 'rotation_pivot_points')
        # 🔧 清除悬停状态
        if hasattr(self, 'hovered_rotation_endpoint'):
            delattr(self, 'hovered_rotation_endpoint')
        if hasattr(self, 'hovered_rotation_line'):
            delattr(self, 'hovered_rotation_line')

    def _find_grid_line_points(self, cell, direction, coordinate):
        """🔧 找到单元格上位于指定网格线上的顶点索引"""
        affected_points = []
        tolerance = 1.0  # 容差

        for i, point in enumerate(cell.points):
            if direction == "vertical":
                # 垂直网格线：检查X坐标
                if abs(point.x() - coordinate) <= tolerance:
                    affected_points.append(i)
            else:  # horizontal
                # 水平网格线：检查Y坐标
                if abs(point.y() - coordinate) <= tolerance:
                    affected_points.append(i)

        LOGGER.debug(f"   单元格 {cell.label} 受影响顶点: {affected_points}")
        return affected_points

    def _find_grid_line_points_from_original(self, original_points, direction, coordinate):
        """🔧 基于原始位置找到位于指定网格线上的顶点索引"""
        affected_points = []
        tolerance = 1.0  # 容差

        for i, point in enumerate(original_points):
            if direction == "vertical":
                # 垂直网格线：检查X坐标
                if abs(point.x() - coordinate) <= tolerance:
                    affected_points.append(i)
            else:  # horizontal
                # 水平网格线：检查Y坐标
                if abs(point.y() - coordinate) <= tolerance:
                    affected_points.append(i)

        LOGGER.debug(f"   基于原始位置找到受影响顶点: {affected_points}")
        return affected_points

    def _get_grid_line_endpoints(self, grid_line):
        """🆕 获取网格线的两个端点坐标"""
        if grid_line.direction == "horizontal":
            # 水平线：两端点在X轴上
            return [
                QtCore.QPointF(grid_line.start, grid_line.coordinate),  # 左端点
                QtCore.QPointF(grid_line.end, grid_line.coordinate)     # 右端点
            ]
        else:  # vertical
            # 垂直线：两端点在Y轴上
            return [
                QtCore.QPointF(grid_line.coordinate, grid_line.start),  # 上端点
                QtCore.QPointF(grid_line.coordinate, grid_line.end)     # 下端点
            ]

    def _find_clicked_pivot_point(self, click_pos):
        """🆕 检查点击位置是否在端点附近"""
        if not hasattr(self, 'rotation_mode_line') or not self.rotation_mode_line:
            LOGGER.debug("🔍 [ROTATION] 没有rotation_mode_line")
            return None

        endpoints = self._get_grid_line_endpoints(self.rotation_mode_line)

        LOGGER.debug(f"🔍 [ROTATION] 检查端点选择:")
        LOGGER.debug(f"   点击位置: ({click_pos.x():.1f}, {click_pos.y():.1f})")
        LOGGER.debug(f"   网格线: {self.rotation_mode_line.direction}, 坐标: {self.rotation_mode_line.coordinate:.1f}")
        LOGGER.debug(f"   端点数量: {len(endpoints)}")

        for i, endpoint in enumerate(endpoints):
            distance = ((click_pos.x() - endpoint.x()) ** 2 +
                       (click_pos.y() - endpoint.y()) ** 2) ** 0.5
            LOGGER.debug(f"   端点{i}: ({endpoint.x():.1f}, {endpoint.y():.1f}), 距离: {distance:.1f}")

            if distance <= 25:  # 🔧 增加容差到25像素
                LOGGER.info(f"🎯 [ROTATION] 找到端点: ({endpoint.x():.1f}, {endpoint.y():.1f}), 距离: {distance:.1f}")

                # 🔧 关键修复：找到最接近的实际单元格顶点坐标
                actual_pivot = self._find_actual_cell_vertex_near_point(endpoint)
                if actual_pivot:
                    LOGGER.info(f"🎯 [ROTATION] 使用实际单元格顶点: ({actual_pivot.x():.1f}, {actual_pivot.y():.1f})")
                    return actual_pivot
                else:
                    return endpoint

        LOGGER.debug(f"🔍 [ROTATION] 未找到端点，最大容差: 25像素")
        return None

    def _find_actual_cell_vertex_near_point(self, target_point):
        """🔧 找到最接近目标点的实际单元格顶点坐标"""
        if not hasattr(self, 'rotation_mode_line') or not self.rotation_mode_line:
            return None

        closest_vertex = None
        min_distance = float('inf')
        tolerance = 5.0  # 5像素容差

        # 遍历所有受影响的单元格
        for cell in self.rotation_mode_line.affected_cells:
            if not hasattr(cell, 'points'):
                continue

            # 检查每个顶点
            for point in cell.points:
                distance = ((point.x() - target_point.x()) ** 2 +
                           (point.y() - target_point.y()) ** 2) ** 0.5

                if distance <= tolerance and distance < min_distance:
                    min_distance = distance
                    closest_vertex = QtCore.QPointF(point.x(), point.y())

        if closest_vertex:
            LOGGER.debug(f"🎯 [ROTATION] 找到最接近的实际顶点: ({closest_vertex.x():.1f}, {closest_vertex.y():.1f}), 距离: {min_distance:.2f}")
        else:
            LOGGER.debug(f"🔍 [ROTATION] 未找到接近的实际顶点")

        return closest_vertex

    def _find_clicked_line_pivot(self, click_pos):
        """🆕 检查点击位置是否在某条选中线的端点附近"""
        if not hasattr(self, 'selected_rotation_lines') or not self.selected_rotation_lines:
            return None, None, None

        tolerance = 25.0

        for line in self.selected_rotation_lines:
            endpoints = self._get_grid_line_endpoints(line)

            for i, endpoint in enumerate(endpoints):
                distance = ((click_pos.x() - endpoint.x()) ** 2 +
                           (click_pos.y() - endpoint.y()) ** 2) ** 0.5

                if distance <= tolerance:
                    # 确定端点类型
                    if line.direction == "horizontal":
                        pivot_type = "left" if i == 0 else "right"
                    else:  # vertical
                        pivot_type = "top" if i == 0 else "bottom"

                    # 找到实际的单元格顶点
                    actual_pivot = self._find_actual_cell_vertex_near_point_for_line(endpoint, line)
                    if actual_pivot:
                        LOGGER.info(f"🎯 [MULTI_ROTATION] 找到端点: 线{line.direction}, {pivot_type}, 坐标({actual_pivot.x():.1f}, {actual_pivot.y():.1f})")
                        return line, actual_pivot, pivot_type

        return None, None, None

    def _find_actual_cell_vertex_near_point_for_line(self, target_point, grid_line):
        """🆕 为特定网格线找到最接近目标点的实际单元格顶点"""
        closest_vertex = None
        min_distance = float('inf')
        tolerance = 5.0

        for cell in grid_line.affected_cells:
            if not hasattr(cell, 'points'):
                continue

            for point in cell.points:
                distance = ((point.x() - target_point.x()) ** 2 +
                           (point.y() - target_point.y()) ** 2) ** 0.5

                if distance <= tolerance and distance < min_distance:
                    min_distance = distance
                    closest_vertex = QtCore.QPointF(point.x(), point.y())

        return closest_vertex

    def _calculate_corresponding_pivot_points(self, reference_line, pivot_type):
        """🆕 为所有选中线计算对应的旋转中心点"""
        pivot_points = {}

        for line in self.selected_rotation_lines:
            endpoints = self._get_grid_line_endpoints(line)

            # 根据pivot_type选择对应的端点
            if pivot_type in ["left", "top"]:
                target_endpoint = endpoints[0]  # 第一个端点
            else:  # "right" or "bottom"
                target_endpoint = endpoints[1]  # 第二个端点

            # 找到实际的单元格顶点
            actual_pivot = self._find_actual_cell_vertex_near_point_for_line(target_endpoint, line)
            if actual_pivot:
                pivot_points[id(line)] = actual_pivot
                LOGGER.debug(f"   线{line.direction}的{pivot_type}点: ({actual_pivot.x():.1f}, {actual_pivot.y():.1f})")

        LOGGER.info(f"🎯 [MULTI_ROTATION] 计算了{len(pivot_points)}个旋转中心点")
        return pivot_points

    def _start_rotation_drag(self, ev):
        """🆕 开始旋转拖拽（简化版）"""
        pos = self.transformPos(ev.localPos())

        # 设置旋转拖拽状态
        self.dragging_grid_line = self.rotation_mode_line
        self.drag_start_pos = pos  # 🎯 关键：记录拖拽起始位置，用于计算相对变化
        self.drag_start_coordinate = self.rotation_mode_line.coordinate

        # 记录原始网格线和单元格位置
        self.original_grid_line = self.rotation_mode_line
        self._save_original_cell_positions()

        LOGGER.info(f"🔄 [ROTATION] 开始旋转拖拽")
        LOGGER.info(f"   中心点: ({self.rotation_pivot_point.x():.1f}, {self.rotation_pivot_point.y():.1f})")
        LOGGER.info(f"   起始位置: ({pos.x():.1f}, {pos.y():.1f})")

    def _start_multi_rotation_drag(self, ev, reference_line, reference_pivot):
        """🆕 开始多线旋转拖拽（简化版）"""
        pos = self.transformPos(ev.localPos())

        # 设置多线旋转拖拽状态
        self.dragging_grid_line = reference_line  # 使用参考线作为主拖拽线
        self.rotation_pivot_point = reference_pivot  # 参考旋转中心
        self.drag_start_pos = pos  # 🎯 关键：记录拖拽起始位置
        self.drag_start_coordinate = reference_line.coordinate

        # 保存所有选中线的原始状态
        self.original_grid_line = reference_line
        self._save_multi_line_original_positions()

        line_count = len(self.selected_rotation_lines)
        LOGGER.info(f"🔄 [MULTI_ROTATION] 开始多线旋转: {line_count}条线")
        LOGGER.info(f"   参考中心点: ({reference_pivot.x():.1f}, {reference_pivot.y():.1f})")
        LOGGER.info(f"   起始位置: ({pos.x():.1f}, {pos.y():.1f})")

    def _save_multi_line_original_positions(self):
        """🆕 保存所有选中线的原始单元格位置"""
        self.original_cell_positions = {}

        for line in self.selected_rotation_lines:
            for cell in line.affected_cells:
                if hasattr(cell, 'points'):
                    cell_id = id(cell)
                    if cell_id not in self.original_cell_positions:
                        # 避免重复保存（多条线可能影响同一个单元格）
                        self.original_cell_positions[cell_id] = [
                            QtCore.QPointF(p.x(), p.y()) for p in cell.points
                        ]

        LOGGER.debug(f"💾 [MULTI_ROTATION] 保存了{len(self.original_cell_positions)}个单元格的原始位置")

    def _move_cells_y_coordinate(self, cells, old_y, new_y):
        """移动单元格的Y坐标"""
        dy = new_y - old_y
        for cell in cells:
            for point in cell.points:
                if abs(point.y() - old_y) <= 1.0:  # 容差范围内的点 - 统一为1.0
                    point.setY(new_y)
    def _move_cells_x_coordinate(self, cells, old_x, new_x):
        """移动单元格的X坐标"""
        dx = new_x - old_x
        for cell in cells:
            for point in cell.points:
                if abs(point.x() - old_x) <= 1.0:  # 容差范围内的点 - 保持1.0
                    point.setX(new_x)

    def mouseReleaseEvent(self, ev):

        # 🆕 表格旋转结束处理（最高优先级）
        if (self.table_rotation_mode and ev.button() == QtCore.Qt.LeftButton):
            self._finish_table_rotation(ev)
            return

        # 🆕 网格线框选结束处理
        if (self.is_grid_drag_mode() and
            self.grid_line_multi_selector and
            self.grid_line_multi_selector.is_box_selecting() and
            ev.button() == QtCore.Qt.LeftButton):
            if self.grid_line_multi_selector.complete_box_selection():
                # 清除选择框显示
                self._selection_box = None
                self.update()
                return

        # 🆕 网格拖拽结束处理
        if (self.is_grid_drag_mode() and
                hasattr(self, 'dragging_grid_line') and
                self.dragging_grid_line and
                ev.button() == QtCore.Qt.LeftButton):
            self._finish_grid_line_drag(ev)
            return

        # 🆕 图像拖动结束处理
        if self._image_dragging:
            self._image_dragging = False
            self._image_drag_start_pos = None
            if hasattr(self, '_image_last_pos'):
                delattr(self, '_image_last_pos')
            self.restoreCursor()
            LOGGER.debug("[CANVAS] 图像拖动结束")
            return
        # 🆕 处理顶点拖拽结束
        if self._vertex_drag_info is not None:
            self._handle_vertex_drag_finish()
            return
        if self._edge_drag_info is not None:
            self._handle_edge_drag_finish()
            return
        # 🎯 最小化拖拽状态清理
        if hasattr(self, 'press_start_pos'):
            self.press_start_pos = None
        if hasattr(self, 'drag_started'):
            if self.drag_started:
                LOGGER.debug("[CANVAS] 拖拽结束")
                # 🔧 检测TableCellShape移动，使用命令系统处理
                from labelme.table_shape import TableCellShape
                if (self.selectedShapes and 
                    any(isinstance(s, TableCellShape) for s in self.selectedShapes)):
                    LOGGER.debug("[CANVAS] 检测到TableCellShape移动，使用命令系统处理")
                    
                    # 🔧 简化处理：暂时跳过命令系统，直接更新界面
                    # TODO: 需要记录移动前后的位置才能使用MoveCellCommand
                    LOGGER.debug("[CANVAS] TableCellShape移动完成（暂时跳过命令系统）")
                    # from labelme.commands.cell_commands import MoveCellCommand
                    # if hasattr(self, 'main_window') and self.main_window:
                    #     # 需要old_positions和new_positions参数
                    #     move_cmd = MoveCellCommand(self, shapes, old_positions, new_positions)
                    #     success = self.main_window.history_manager.execute_command(move_cmd)
            self.drag_started = False
            
        # 🆕 多选处理器：选择框完成处理
        if self.multi_selection_handler and self.multi_selection_handler.handle_selection_end(ev):
            return  # 被多选处理器拦截
        if ev.button() == QtCore.Qt.RightButton:  # type: ignore[attr-defined]
            menu = self.menus[len(self.selectedShapesCopy) > 0]
            self.restoreCursor()
            if not menu.exec_(self.mapToGlobal(ev.pos())) and self.selectedShapesCopy:
                # Cancel the move by deleting the shadow copy.
                self.selectedShapesCopy = []
                self.repaint()
        elif ev.button() == QtCore.Qt.LeftButton:  # type: ignore[attr-defined]
            if self.editing():
                if (
                        self.hShape is not None
                        and self.hShapeIsSelected
                        and not self.movingShape
                ):
                    self.selectionChanged.emit(
                        [x for x in self.selectedShapes if x != self.hShape]
                    )

        # 🔧 修复：处理TableCellShape移动的命令系统集成
        if self.movingShape and self.hShape:
            index = self.shapes.index(self.hShape)
            if self.shapesBackups[-1][index].points != self.shapes[index].points:
                # 检查是否是TableCellShape移动
                from labelme.table_shape import TableCellShape
                
                if isinstance(self.hShape, TableCellShape) and hasattr(self, 'history_manager') and self.history_manager:
                    # 🎯 使用命令系统处理TableCellShape移动
                    LOGGER.debug("[CANVAS] 检测到TableCellShape移动，使用命令系统处理")
                    
                    # 获取移动的shapes
                    moved_shapes = [shape for shape in self.selectedShapes if isinstance(shape, TableCellShape)]
                    if not moved_shapes:
                        moved_shapes = [self.hShape]
                    
                    # 获取旧位置（从备份中）
                    old_positions = []
                    new_positions = []
                    
                    for shape in moved_shapes:
                        try:
                            shape_index = self.shapes.index(shape)
                            old_pos = [QtCore.QPointF(p.x(), p.y()) for p in self.shapesBackups[-1][shape_index].points]
                            new_pos = [QtCore.QPointF(p.x(), p.y()) for p in shape.points]
                            old_positions.append(old_pos)
                            new_positions.append(new_pos)
                        except (IndexError, ValueError) as e:
                            LOGGER.warning(f"获取移动位置失败: {e}")
                            continue

                    has_movement = False
                    for old_pos, new_pos in zip(old_positions, new_positions):
                        if len(old_pos) == len(new_pos):
                            # 检查这个单元格的所有顶点是否都移动了
                            if len(old_pos) == 4 and len(new_pos) == 4:
                                # 计算第一个点的移动向量
                                dx = new_pos[0].x() - old_pos[0].x()
                                dy = new_pos[0].y() - old_pos[0].y()
                                # 检查其他点是否有相同的移动向量
                                all_points_moved = all(
                                    abs((new_pos[i].x() - old_pos[i].x()) - dx) < 0.1 and
                                    abs((new_pos[i].y() - old_pos[i].y()) - dy) < 0.1
                                    for i in range(1, 4)
                                ) and (abs(dx) > 1.0 or abs(dy) > 1.0)
                            else:
                                all_points_moved = False
                            if all_points_moved:
                                has_movement = True
                                break

                    # 创建移动命令（移到for循环外面）
                    if has_movement:
                        from labelme.commands.cell_commands import MoveCellCommand
                        move_command = MoveCellCommand(
                            canvas=self,
                            shapes=moved_shapes,
                            old_positions=old_positions,
                            new_positions=new_positions,
                            description=f"移动单元格: {len(moved_shapes)}个"
                        )
                        
                        # 先恢复到旧位置，然后通过命令系统执行移动
                        for i, shape in enumerate(moved_shapes):
                            if i < len(old_positions):
                                shape.points = old_positions[i].copy()
                        
                        # 执行命令
                        self.history_manager.execute_command(move_command)
                        LOGGER.debug("[CANVAS] TableCellShape移动已通过命令系统处理")
                    else:
                        LOGGER.warning("[CANVAS] 无法获取有效的移动位置数据，回退到传统处理")
                        self.storeShapes()
                        self.shapeMoved.emit()
                else:
                    # 🔄 传统形状使用原有的备份系统
                    self.storeShapes()
                    self.shapeMoved.emit()

            self.movingShape = False
    def _handle_edge_drag_finish(self):
        """处理边拖拽完成，执行命令并清理状态"""
        if self._edge_drag_info is None:
            return
            
        LOGGER.debug(f"🔧 [EDGE_DRAG_FINISH] 开始处理边拖动完成")
        
        try:
            # 🔧 获取拖动信息
            affected_shapes = self._edge_drag_info['shapes']
            edge_index = self._edge_drag_info['edge_index']
            old_positions = self._edge_drag_info['old_positions']
            
            # 🔧 获取当前位置（新位置）
            new_positions = []
            for shape in affected_shapes:
                new_pos = [QtCore.QPointF(p.x(), p.y()) for p in shape.points]
                new_positions.append(new_pos)
            
            # 🔧 检查是否有历史管理器
            if hasattr(self, 'main_window') and hasattr(self.main_window, 'history_manager'):
                history_manager = self.main_window.history_manager
                
                try:
                    from labelme.commands.cell_commands import ResizeEdgeCommand
                    
                    # 🔧 创建边拖动命令
                    resize_command = ResizeEdgeCommand(
                        canvas=self,
                        shapes=affected_shapes,
                        edge_name=f"edge_{edge_index}",  # 简化的边名称
                        old_positions=old_positions,
                        new_positions=new_positions,
                        description=f"拖动边: {len(affected_shapes)}个单元格"
                    )
                    
                    # 🔧 执行命令
                    success = history_manager.execute_command(resize_command)
                    
                    if success:
                        LOGGER.debug(f"✅ [EDGE_DRAG_FINISH] 边拖动命令执行成功")
                    else:
                        LOGGER.error(f"[EDGE_DRAG_FINISH] 边拖动命令执行失败")
                        
                except Exception as e:
                    LOGGER.error(f"[EDGE_DRAG_FINISH] 命令执行异常: {e}")
                    import traceback
                    traceback.print_exc()
            else:
                LOGGER.warning(f"[EDGE_DRAG_FINISH] 未找到命令系统，跳过命令执行")
                
        except Exception as e:
            LOGGER.error(f"[EDGE_DRAG_FINISH] 处理边拖动完成时出错: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 🔧 清理拖动状态
            self._edge_drag_info = None
            LOGGER.debug(f"🔧 [EDGE_DRAG_FINISH] 边拖动状态已清理")
            
            # 🔧 清理所有相关状态，避免干扰后续逻辑
            self.movingShape = False
            self.hEdge = None
            self.hShape = None
            if hasattr(self, 'drag_started'):
                self.drag_started = False
            # 🔧 清理前一个状态，避免状态污染
            self.prevhEdge = None
            self.prevhShape = None
            # 🔧 关键：清理选中状态，避免被误认为在移动形状
            self.prevPoint = None
            for shape in self.selectedShapes:
                shape.selected = False
            self.selectedShapes = []           # 清空选中列表
            self.selectedShapesCopy = []       # 清空选中备份
            # 🔧 取消高亮和光标状态
            self.unHighlight()
            self.restoreCursor()
            
            # 🔧 更新界面
            self.update()
    def endMove(self, copy):
        assert self.selectedShapes and self.selectedShapesCopy
        assert len(self.selectedShapesCopy) == len(self.selectedShapes)
        if copy:
            for i, shape in enumerate(self.selectedShapesCopy):
                self.shapes.append(shape)
                self.selectedShapes[i].selected = False
                self.selectedShapes[i] = shape
        else:
            for i, shape in enumerate(self.selectedShapesCopy):
                self.selectedShapes[i].points = shape.points
        self.selectedShapesCopy = []
        self.repaint()
        self.storeShapes()
        return True

    def hideBackroundShapes(self, value):
        self.hideBackround = value
        if self.selectedShapes:
            # Only hide other shapes if there is a current selection.
            # Otherwise the user will not be able to select a shape.
            self.setHiding(True)
            self.update()

    def setHiding(self, enable=True):
        self._hideBackround = self.hideBackround if enable else False

    def canCloseShape(self):
        return self.drawing() and (
                (self.current and len(self.current) > 2)
                or self.createMode in ["ai_polygon", "ai_mask"]
        )

    def mouseDoubleClickEvent(self, ev):
        """处理鼠标双击事件 - 用于快速文本编辑"""
        try:
            LOGGER.debug("[CANVAS] 双击事件开始")
            pos = self.transformPos(ev.localPos())
            LOGGER.debug(f"[CANVAS] 双击位置: ({pos.x():.1f}, {pos.y():.1f})")
        except AttributeError as e:
            LOGGER.error(f"[CANVAS] 双击事件位置转换失败: {e}")
            return
        except Exception as e:
            LOGGER.error(f"[CANVAS] 双击事件处理异常: {e}")
            return
        
        # 检查是否启用了双击编辑功能
        if not getattr(self, '_double_click_edit_enabled', True):
            LOGGER.debug("[CANVAS] 双击编辑功能已禁用，使用原有逻辑")
            # 如果禁用了双击编辑，使用原有的双击逻辑
            if self.double_click == "close":
                if (self.createMode == "polygon" and self.canCloseShape()) or self.createMode in ["ai_polygon", "ai_mask"]:
                    self.finalise()
            return
            
        try:
            # 检查是否双击了表格单元格
            LOGGER.debug(f"[CANVAS] 检查{len(self.shapes)}个形状")
            for shape in reversed(self.shapes):
                if (self.isVisible(shape) and 
                    hasattr(shape, 'shape_type') and 
                    shape.shape_type == 'table_cell' and
                    shape.containsPoint(pos)):
                    
                    LOGGER.info(f"[CANVAS] 双击了表格单元格: {shape.label}")
                    # 触发表格单元格文本编辑
                    self._start_cell_text_editing(shape, pos)
                    return
                    
        except Exception as e:
            LOGGER.error(f"[CANVAS] 双击单元格检测异常: {e}")
            return
                
        # 如果没有点击单元格，使用原有的双击逻辑
        LOGGER.debug("[CANVAS] 没有点击单元格，使用原有双击逻辑")
        if self.double_click == "close":
            if (self.createMode == "polygon" and self.canCloseShape()) or self.createMode in ["ai_polygon", "ai_mask"]:
                self.finalise()

    def _start_cell_text_editing(self, cell_shape, click_pos):
        """开始单元格文本编辑"""
        try:
            LOGGER.debug(f"[CANVAS] 开始单元格文本编辑: {cell_shape.label}")
            
            # 如果已经有编辑器在运行，先关闭它
            if hasattr(self, '_cell_text_editor') and self._cell_text_editor:
                LOGGER.debug("[CANVAS] 关闭现有的文本编辑器")
                self._cell_text_editor.close()
                
            # 检查是否有多表格控制器
            if hasattr(self, 'multi_table_controller') and self.multi_table_controller:
                active_controller = self.multi_table_controller.get_active_controller()
                if active_controller and not active_controller.handle_cell_text_editing(cell_shape):
                    LOGGER.warning("表格控制器拒绝了文本编辑请求")
                    return
            
            # 导入并创建文本编辑器
            from labelme.widgets.cell_text_editor import CellTextEditor
            LOGGER.debug("[CANVAS] 创建文本编辑器")
            self._cell_text_editor = CellTextEditor(self, cell_shape)
            
            # 连接信号 - 当文本编辑完成时
            self._cell_text_editor.text_edited.connect(self._on_cell_text_edited)
            LOGGER.debug("[CANVAS] 文本编辑器信号已连接")
            
            # 计算编辑器位置（在单元格中心）
            cell_rect = cell_shape.boundingRect()
            if cell_rect:
                # 转换到屏幕坐标
                center_x = cell_rect.center().x() * self.scale + self.offsetToCenter().x()
                center_y = cell_rect.center().y() * self.scale + self.offsetToCenter().y()
                
                # 转换到全局坐标
                global_pos = self.mapToGlobal(QtCore.QPoint(int(center_x), int(center_y)))
                LOGGER.debug(f"[CANVAS] 编辑器位置(单元格中心): ({global_pos.x()}, {global_pos.y()})")
                
                # 计算单元格在Canvas上的矩形区域（用于智能定位）
                canvas_cell_rect = QtCore.QRect(
                    int(cell_rect.x() * self.scale + self.offsetToCenter().x()),
                    int(cell_rect.y() * self.scale + self.offsetToCenter().y()),
                    int(cell_rect.width() * self.scale),
                    int(cell_rect.height() * self.scale)
                )
                
                LOGGER.debug(f"[CANVAS] Canvas上的单元格矩形: {canvas_cell_rect}")
                
                # 显示编辑器，传递单元格矩形信息
                self._cell_text_editor.show_at_position(global_pos, canvas_cell_rect)
            else:
                # 如果无法获取边界框，在点击位置显示
                global_pos = self.mapToGlobal(QtCore.QPoint(int(click_pos.x()), int(click_pos.y())))
                LOGGER.debug(f"[CANVAS] 编辑器位置(点击位置): ({global_pos.x()}, {global_pos.y()})")
                self._cell_text_editor.show_at_position(global_pos)
                
            LOGGER.info("[CANVAS] 文本编辑器启动成功")
            
        except Exception as e:
            LOGGER.error(f"[CANVAS] 启动文本编辑器失败: {e}")
            import traceback
            LOGGER.error(f"[CANVAS] 错误堆栈: {traceback.format_exc()}")
    
    def _on_cell_text_edited(self, cell_shape, new_text):
        """处理单元格文本编辑完成"""
        # 获取原始文本（从编辑器中获取）
        old_text = ""
        if hasattr(self, '_cell_text_editor') and self._cell_text_editor:
            old_text = self._cell_text_editor.original_text
        
        LOGGER.info(f"单元格文本编辑完成: '{old_text}' -> '{new_text}'")
        
        # 通知表格控制器处理文本变更
        if hasattr(self, 'multi_table_controller') and self.multi_table_controller:
            active_controller = self.multi_table_controller.get_active_controller()
            if active_controller:
                active_controller.handle_cell_text_changed(cell_shape, old_text, new_text)
        
        # 更新显示
        self.update()

    def selectShapes(self, shapes):
        LOGGER.debug(f"[CANVAS] selectShapes被调用，形状数量: {len(shapes)}")

        self.setHiding()

        # 🔍 检查选中状态设置
        for shape in shapes:
            LOGGER.debug(f"[CANVAS] 设置形状选中: {type(shape).__name__}")
            shape.selected = True
            # 🆕 如果是TableCellShape被选中，清除其悬停状态
            if (hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell' and 
                hasattr(shape, '_is_hovered')):
                shape._is_hovered = False
                if self._hovered_table_cell == shape:
                    self._hovered_table_cell = None

        self.selectionChanged.emit(shapes)

        # 🔗 通知表格控制器选择变更
        if self.multi_table_controller:
            self.multi_table_controller.handle_canvas_event('selection_changed', shapes)

        self.update()

    def selectShapePoint(self, point, multiple_selection_mode):
        """🎯 最简化的选择逻辑 - 专注于TableCellShape选中"""
        
        LOGGER.debug(f"[CANVAS] selectShapePoint开始，点击位置: ({point.x():.1f}, {point.y():.1f})")
        
        # 🎯 第一优先级：TableCellShape立即选中
        for shape in reversed(self.shapes):
            if (self.isVisible(shape) and 
                hasattr(shape, 'shape_type') and 
                shape.shape_type == 'table_cell' and
                shape.containsPoint(point)):
                
                LOGGER.debug(f"[CANVAS] 找到TableCellShape: {shape.label}")
                # 🎯 使用最简单的选中逻辑
                self._simple_select_shape(shape, multiple_selection_mode)
                return
        
        # 🔧 如果没有找到TableCellShape，清除选择
        LOGGER.debug("[CANVAS] 没有找到TableCellShape，清除选择")
        self.deSelectShape()

    def _simple_select_shape(self, shape, multiple_selection_mode):
        """🎯 最简单的选中逻辑，屏蔽所有复杂功能"""
        
        LOGGER.debug(f"[CANVAS] _simple_select_shape开始: {type(shape).__name__}")
        
        # 🔧 最简单的选中逻辑
        if not multiple_selection_mode:
            # 清除所有旧选择
            for old_shape in self.selectedShapes:
                old_shape.selected = False
            self.selectedShapes = []
        
        # 选中新形状
        if shape not in self.selectedShapes:
            self.selectedShapes.append(shape)
        
        shape.selected = True

        # 🆕 添加置顶逻辑：将选中的TableCellShape移到最前面（绘制顺序最靠后）
        if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell':
            self._bring_table_cell_to_front(shape)
        self.storeShapes()
        LOGGER.debug(f"[CANVAS] 形状选中状态设置: {shape.selected}")
        LOGGER.debug(f"[CANVAS] selectedShapes数量: {len(self.selectedShapes)}")
        
        # 发射信号
        self.selectionChanged.emit(self.selectedShapes.copy())
        
        # 强制更新显示
        self.update()
        
        LOGGER.debug("[CANVAS] _simple_select_shape完成")

    def _bring_table_cell_to_front(self, shape):
        """🆕 将TableCellShape置顶到绘制顺序最前面"""
        if shape in self.shapes:
            # 从当前位置移除
            self.shapes.remove(shape)
            # 添加到列表末尾（绘制时最后渲染，显示在最上层）
            self.shapes.append(shape)
            LOGGER.debug(f"[CANVAS] TableCellShape已置顶: {shape.label}")
    def _select_shape_immediately(self, shape, multiple_selection_mode, point):
        """🎯 立即选中形状，不等待拖动"""
        
        # 为移动操作准备备份
        self.storeShapes()
        
        self.setHiding()
        
        # 🔧 关键修复：立即更新selectedShapes列表和shape.selected状态
        if shape not in self.selectedShapes:
            # 清除旧选择状态（仅在非多选模式下）
            if not multiple_selection_mode:
                for old_shape in self.selectedShapes:
                    old_shape.selected = False
                self.selectedShapes = []
            
            # 添加新选择
            self.selectedShapes.append(shape)
            shape.selected = True
            
            # 发射信号通知外部
            self.selectionChanged.emit(self.selectedShapes.copy())
            self.hShapeIsSelected = False
            
            LOGGER.debug(f"[CANVAS] 立即选中形状: {type(shape).__name__}, 选中状态: {shape.selected}")
        else:
            # 形状已经被选中
            self.hShapeIsSelected = True
            LOGGER.debug(f"[CANVAS] 形状已选中: {type(shape).__name__}")
        
        # 🔧 关键：设置当前形状，确保后续操作能正确处理
        self.hShape = shape
        
        # 立即更新显示
        self.update()
        
        self.calculateOffsets(point)

    def calculateOffsets(self, point):
        left = self.pixmap.width() - 1
        right = 0
        top = self.pixmap.height() - 1
        bottom = 0
        for s in self.selectedShapes:
            rect = s.boundingRect()
            if rect.left() < left:
                left = rect.left()
            if rect.right() > right:
                right = rect.right()
            if rect.top() < top:
                top = rect.top()
            if rect.bottom() > bottom:
                bottom = rect.bottom()

        x1 = left - point.x()
        y1 = top - point.y()
        x2 = right - point.x()
        y2 = bottom - point.y()
        # 🔧 修复：确保坐标值为整数类型，避免QPoint构造函数类型错误
        self.offsets = QtCore.QPoint(int(x1), int(y1)), QtCore.QPoint(int(x2), int(y2))

    def boundedMoveVertex(self, pos):
        index, shape = self.hVertex, self.hShape
        point = shape[index]  # type: ignore[index]
        if self.outOfPixmap(pos):
            pos = self.intersectionPoint(point, pos)

        # 🆕 TableCellShape顶点拖拽：记录状态信息
        if (hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell'
                and self._vertex_drag_info is None):
            # 🔧 初始化拖拽状态（只在第一次调用时）
            self._vertex_drag_info = {
                'shapes': [shape],
                'vertex_index': index,
                'old_positions': [[QtCore.QPointF(p.x(), p.y()) for p in shape.points]],
                'is_rectangular': False  # 自由拖拽模式
            }

        # 🔧 执行原有的移动逻辑（用于预览）
        shape.moveVertexBy(index, pos - point)  # type: ignore[union-attr]

    def _handle_vertex_drag_finish(self):
        """完成顶点拖拽 - 创建ResizeVertexCommand"""

        try:
            # 1. 记录当前位置作为新位置
            new_positions = []
            for shape in self._vertex_drag_info['shapes']:
                new_pos = [QtCore.QPointF(p.x(), p.y()) for p in shape.points]
                new_positions.append(new_pos)

            # 2. 检查是否有实际移动
            has_movement = False
            for old_pos, new_pos in zip(self._vertex_drag_info['old_positions'], new_positions):
                if len(old_pos) == len(new_pos):
                    for old_p, new_p in zip(old_pos, new_pos):
                        if abs(old_p.x() - new_p.x()) > 1 or abs(old_p.y() - new_p.y()) > 1:
                            has_movement = True
                            break
                    if has_movement:
                        break

            if not has_movement:
                LOGGER.debug("[VERTEX_CMD] 没有检测到顶点移动")
                return

            # 3. 🔧 关键：先恢复到old_positions，然后让命令系统执行
            for i, shape in enumerate(self._vertex_drag_info['shapes']):
                if i < len(self._vertex_drag_info['old_positions']):
                    old_points = [QtCore.QPointF(p.x(), p.y()) for p in self._vertex_drag_info['old_positions'][i]]
                    shape.points = old_points  # 恢复到初始状态

            # 4. 使用命令系统执行从old → new的变化
            if hasattr(self, 'main_window') and hasattr(self.main_window, 'history_manager'):
                from labelme.commands.cell_commands import ResizeVertexCommand

                resize_vertex_cmd = ResizeVertexCommand(
                    canvas=self,
                    shapes=self._vertex_drag_info['shapes'],
                    vertex_index=self._vertex_drag_info['vertex_index'],
                    old_positions=self._vertex_drag_info['old_positions'],
                    new_positions=new_positions,
                    is_rectangular=self._vertex_drag_info['is_rectangular']
                )

                success = self.main_window.history_manager.execute_command(resize_vertex_cmd)
                if success:
                    LOGGER.debug("✅ [VERTEX_CMD] 顶点拖拽命令执行成功")
                else:
                    LOGGER.error("[VERTEX_CMD] 顶点拖拽命令执行失败")
            else:
                LOGGER.warning("[VERTEX_CMD] 未找到命令系统，保持当前修改")

        finally:
            # 5. 清理拖拽状态
            # 5. 清理拖拽状态

            self._vertex_drag_info = None
            # 🔧 清理所有相关状态，避免干扰后续逻辑
            self.movingShape = False
            self.hVertex = None
            self.hShape = None
            if hasattr(self, 'drag_started'):
                self.drag_started = False
            # 🔧 清理前一个状态，避免状态污染
            self.prevhVertex = None
            self.prevhShape = None
            # 🔧 关键：清理选中状态，避免被误认为在移动形状
            self.prevPoint = None
            # 🔧 修复：清理单元格的selected属性，确保视觉上取消选中状态
            for shape in self.selectedShapes:
                shape.selected = False
            self.selectedShapes = []           # 清空选中列表
            self.selectedShapesCopy = []       # 清空选中备份
            # 🔧 取消高亮和光标状态
            self.unHighlight()
            self.restoreCursor()
            # 🔧 触发重绘，确保状态更新
            self.update()
    def boundedMoveShapes(self, shapes, pos):
        if self.outOfPixmap(pos):
            return False  # No need to move
        o1 = pos + self.offsets[0]
        if self.outOfPixmap(o1):
            pos -= QtCore.QPointF(min(0, o1.x()), min(0, o1.y()))
        o2 = pos + self.offsets[1]
        if self.outOfPixmap(o2):
            pos += QtCore.QPointF(
                min(0, self.pixmap.width() - o2.x()),
                min(0, self.pixmap.height() - o2.y()),
            )
        if self.prevPoint is None:
            self.prevPoint = pos
            return False  # 第一次调用，不移动
        # XXX: The next line tracks the new position of the cursor
        # relative to the shape, but also results in making it
        # a bit "shaky" when nearing the border and allows it to
        # go outside of the shape's area for some reason.
        # self.calculateOffsets(self.selectedShapes, pos)
        dp = pos - self.prevPoint
        if dp:
            for shape in shapes:
                shape.moveBy(dp)
            self.prevPoint = pos
            return True
        return False

    def deSelectShape(self):
        if self.selectedShapes:
            # 🆕 清除TableCellShape的选中状态
            for shape in self.selectedShapes:
                shape.selected = False
            self.selectedShapes = []
            
            self.setHiding(False)
            self.selectionChanged.emit([])
            self.hShapeIsSelected = False
            self.update()

    def deleteSelected(self):
        deleted_shapes = []
        if self.selectedShapes:
            for shape in self.selectedShapes:
                self.shapes.remove(shape)
                deleted_shapes.append(shape)
            self.storeShapes()
            self.selectedShapes = []
            self.update()
        return deleted_shapes

    def deleteShape(self, shape):
        if shape in self.selectedShapes:
            self.selectedShapes.remove(shape)
        if shape in self.shapes:
            self.shapes.remove(shape)
        self.storeShapes()
        self.update()

    def paintEvent(self, event: QtGui.QPaintEvent) -> None:
        if self.unified_grid_drag_mode:
            LOGGER.info("🔍 paintEvent被调用，网格拖拽模式激活")
        if not self.pixmap:
            return super(Canvas, self).paintEvent(event)
        p = self._painter
        p.begin(self)
        p.setRenderHint(QtGui.QPainter.Antialiasing)
        p.setRenderHint(QtGui.QPainter.HighQualityAntialiasing)
        p.setRenderHint(QtGui.QPainter.SmoothPixmapTransform)

        # 进入图像坐标系
        p.scale(self.scale, self.scale)
        p.translate(self.offsetToCenter())

        # 绘制pixmap
        p.drawPixmap(0, 0, self.pixmap)

        # 🆕 在pixmap绘制后立即绘制选择框（图像坐标系）
        if self._selection_box:
            self._draw_selection_box(p)

        # 退出图像坐标系
        p.scale(1 / self.scale, 1 / self.scale)

        # 🔧 在widget坐标系中绘制crosshair
        if (
                self._crosshair[self._createMode]
                and self.drawing()
                and self.prevMovePoint
                and not self.outOfPixmap(self.prevMovePoint)
        ):
            p.setPen(QtGui.QColor(0, 0, 0))
            p.drawLine(
                0,
                int(self.prevMovePoint.y() * self.scale),
                self.width() - 1,
                int(self.prevMovePoint.y() * self.scale),
            )
            p.drawLine(
                int(self.prevMovePoint.x() * self.scale),
                0,
                int(self.prevMovePoint.x() * self.scale),
                self.height() - 1,
            )

        # 🔧 在原来的位置绘制形状（混合坐标系）
        Shape.scale = self.scale
        for shape in self.shapes:
            if (shape.selected or not self._hideBackround) and self.isVisible(shape):
                shape.fill = shape.selected or shape == self.hShape
                shape.paint(p)
        if self.current:
            self.current.paint(p)
            assert len(self.line.points) == len(self.line.point_labels)
            self.line.paint(p)
        if self.selectedShapesCopy:
            for s in self.selectedShapesCopy:
                s.paint(p)

        # 🆕 形状绘制完成后，重新进入图像坐标系绘制网格线
        if self.unified_grid_drag_mode:
            # 重置变换矩阵，重新进入图像坐标系
            p.resetTransform()
            p.setRenderHint(QtGui.QPainter.Antialiasing)
            p.setRenderHint(QtGui.QPainter.HighQualityAntialiasing)
            p.setRenderHint(QtGui.QPainter.SmoothPixmapTransform)

            p.scale(self.scale, self.scale)
            p.translate(self.offsetToCenter())

            # 1. 先绘制悬停高亮（如果不在拖拽状态）
            if not hasattr(self, 'dragging_grid_line') or not self.dragging_grid_line:
                self._draw_grid_line_highlights(p)

            # 2. 绘制正在拖拽的线（如果在拖拽状态）
            if hasattr(self, 'dragging_grid_line') and self.dragging_grid_line:
                self._draw_dragging_grid_line(p)

            # 🆕 3. 绘制旋转模式的视觉反馈
            self._draw_rotation_mode_visuals(p)

            # 再次退出图像坐标系
            p.resetTransform()

        # 绘制表格控制器内容
        # if self.table_controller:
        #    self.table_controller.paint(p)

        # 🔧 后续绘制逻辑保持不变
        if not self.current:
            p.end()
            return

        if (
                self.createMode == "polygon"
                and self.fillDrawing()
                and len(self.current.points) >= 2
        ):
            drawing_shape = self.current.copy()
            if drawing_shape.fill_color.getRgb()[3] == 0:
                logger.warning(
                    "fill_drawing=true, but fill_color is transparent,"
                    " so forcing to be opaque."
                )
                drawing_shape.fill_color.setAlpha(64)
            drawing_shape.addPoint(self.line[1])

        if self.createMode not in ["ai_polygon", "ai_mask"]:
            p.end()
            return

        drawing_shape = self.current.copy()
        drawing_shape.addPoint(
            point=self.line.points[1],
            label=self.line.point_labels[1],
        )
        _update_shape_with_sam(
            sam=_get_ai_model(model_name=self._ai_model_name),
            pixmap=self.pixmap,
            shape=drawing_shape,
            createMode=self.createMode,
        )
        drawing_shape.fill = self.fillDrawing()
        drawing_shape.selected = True
        drawing_shape.paint(p)

        p.end()
    def transformPos(self, point):
        """Convert from widget-logical coordinates to painter-logical ones."""
        return point / self.scale - self.offsetToCenter()

    def offsetToCenter(self):
        s = self.scale
        area = super(Canvas, self).size()
        w, h = self.pixmap.width() * s, self.pixmap.height() * s
        aw, ah = area.width(), area.height()
        x = (aw - w) / (2 * s) if aw > w else 0
        y = (ah - h) / (2 * s) if ah > h else 0
        return QtCore.QPointF(x, y)

    def outOfPixmap(self, p):
        w, h = self.pixmap.width(), self.pixmap.height()
        return not (0 <= p.x() <= w - 1 and 0 <= p.y() <= h - 1)

    # 添加选择框绘制方法：
    def _draw_selection_box(self, painter):
        """绘制选择框（现在在图像坐标系中）"""
        if not self._selection_box:
            return

        start = self._selection_box["start"]
        current = self._selection_box["current"]

        # 🔧 直接使用图像坐标（不需要复杂转换）
        x = min(start.x(), current.x())
        y = min(start.y(), current.y())
        width = abs(current.x() - start.x())
        height = abs(current.y() - start.y())

        rect = QtCore.QRectF(x, y, width, height)

        painter.save()
        fill_color = QtGui.QColor(0, 120, 215, 50)
        painter.fillRect(rect, fill_color)

        pen = QtGui.QPen(QtGui.QColor(0, 120, 215, 200))
        pen.setStyle(QtCore.Qt.DashLine)
        pen.setWidth(2)
        painter.setPen(pen)
        painter.drawRect(rect)
        painter.restore()

    def finalise(self):
        assert self.current
        LOGGER.debug(f"Canvas finalise开始，形状类型: {self.current.shape_type}")

        # 🆕 P1阶段：拦截快速表格选区模式
        if self.createMode == "quick_table_region":
            self._handle_quick_table_region_finalise()
            return

        # 🔗 检查multi_table_controller处理事件
        table_intercepted = False
        table_cell_from_controller = None
        
        if hasattr(self, 'multi_table_controller') and self.multi_table_controller:
            LOGGER.debug("找到MultiTableController，准备处理事件")
            handled = self.multi_table_controller.handle_canvas_event('shape_finalize', self.current)
            LOGGER.debug(f"事件处理结果: {handled}")

            if handled and handled.get('intercepted', False):
                LOGGER.debug("事件被拦截，将使用命令系统处理表格单元格")
                table_intercepted = True
                # 获取MultiTableController处理后的单元格
                if handled.get('action') == 'create_table_cell':
                    table_cell_from_controller = handled['result_data']['table_cell_shape']
            else:
                LOGGER.debug("事件未被拦截，执行标准流程")
        else:
            LOGGER.error("没有找到MultiTableController！")

        # 处理AI增强形状
        if self.createMode in ["ai_polygon", "ai_mask"]:
            _update_shape_with_sam(
                sam=_get_ai_model(model_name=self._ai_model_name),
                pixmap=self.pixmap,
                shape=self.current,
                createMode=self.createMode,
            )
        self.current.close()

        # ===== 🆕 集成命令系统处理形状创建 =====

        # 🔧 修复：处理表格拦截的情况
        # 在第2122行 if table_intercepted and table_cell_from_controller: 之后添加：
        LOGGER.info(f"🔍 拦截状态检查:")
        LOGGER.info(f"  - table_intercepted: {table_intercepted}")
        LOGGER.info(f"  - table_cell_from_controller: {table_cell_from_controller}")
        LOGGER.info(
            f"  - table_cell_from_controller类型: {type(table_cell_from_controller).__name__ if table_cell_from_controller else 'None'}")
        LOGGER.info(
            f"  - table_cell_from_controller ID: {id(table_cell_from_controller) if table_cell_from_controller else 'None'}")
        # 🔧 修复：处理表格拦截的情况
        if table_intercepted and table_cell_from_controller:
            LOGGER.debug("使用MultiTableController处理的表格单元格")

            # 🔧 新增：检查对象是否已经在shapes中，避免重复添加
            if table_cell_from_controller not in self.shapes:
                self.shapes.append(table_cell_from_controller)
                LOGGER.debug("添加拦截的TableCellShape到shapes")
            else:
                LOGGER.debug("TableCellShape已在shapes中，跳过重复添加")

            self.storeShapes()

            # 🔧 关键：添加必要的清理步骤
            self.current = None
            self.setHiding(False)

            # 🔗 finalise后通知MultiTableController
            if hasattr(self, 'multi_table_controller') and self.multi_table_controller:
                self.multi_table_controller.handle_canvas_event('shape_finalized', None)

            self.newShape.emit()
            self.update()
            LOGGER.debug("拦截处理完成，Canvas状态已清理")
            return  # 🔧 关键：直接返回，避免重复处理
        else:
            # 使用原始的current形状
            shape_to_add = self.current
            LOGGER.debug("使用原始current形状")
        
        # 检查是否为TableCellShape，使用命令系统
        if (hasattr(shape_to_add, 'shape_type') and 
            shape_to_add.shape_type == 'table_cell' and 
            hasattr(self, 'history_manager') and 
            self.history_manager is not None):
            
            try:
                from ..commands import CreateCellCommand
                
                # 创建命令对象
                create_cmd = CreateCellCommand(
                    canvas=self,
                    points=shape_to_add.points[:],  # 拷贝点列表
                    label=getattr(shape_to_add, 'label', ''),
                    table_properties=getattr(shape_to_add, 'table_properties', {})
                )
                
                # 通过历史管理器执行命令
                success = self.history_manager.execute_command(create_cmd)
                
                if success:
                    LOGGER.debug("使用命令系统成功创建TableCellShape")
                else:
                    LOGGER.error("命令系统创建失败，回退到传统方式")
                    # 回退到传统方式
                    self.shapes.append(shape_to_add)
                    self.storeShapes()
                    
            except Exception as e:
                LOGGER.error(f"命令系统处理异常: {e}")
                # 回退到传统方式
                self.shapes.append(shape_to_add)
                self.storeShapes()
        else:
            # 非TableCellShape或无历史管理器，使用传统方式
            self.shapes.append(shape_to_add)
            self.storeShapes()
            LOGGER.debug("使用传统方式添加形状")

        self.current = None
        self.setHiding(False)

        # 🔗 finalise后通知MultiTableController
        if hasattr(self, 'multi_table_controller') and self.multi_table_controller:
            self.multi_table_controller.handle_canvas_event('shape_finalized', None)

        self.newShape.emit()
        self.update()

        LOGGER.info(f"🔍 Canvas finalise完成后全面状态检查:")
        LOGGER.info(f"  - shapes总数: {len(self.shapes)}")
        LOGGER.info(f"  - shapes类型: {[type(s).__name__ for s in self.shapes]}")
        LOGGER.info(f"  - shapes ID: {[id(s) for s in self.shapes]}")

        LOGGER.debug("Canvas更新完成")

    def boundedMoveVertexRectangular(self, pos):
        """🆕 矩形约束的顶点移动方法 - 保持矩形形状（优化版本）"""
        index, shape = self.hVertex, self.hShape
        point = shape[index]  # type: ignore[index]
        
        if self.outOfPixmap(pos):
            pos = self.intersectionPoint(point, pos)

            
        # 🔧 矩形约束逻辑：确保形状保持矩形
        if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell' and len(shape.points) == 4:
            # 🆕 记录顶点拖拽状态信息
            if self._vertex_drag_info is None:
                self._vertex_drag_info = {
                    'shapes': [shape],
                    'vertex_index': index,
                    'old_positions': [[QtCore.QPointF(p.x(), p.y()) for p in shape.points]],
                    'is_rectangular': True  # 矩形约束模式
                }
            # 🆕 优化：获取原始矩形的4个顶点，确保正确的顺序
            points = [QtCore.QPointF(p.x(), p.y()) for p in shape.points]  # 深拷贝避免引用问题
            
            # 🔧 优化：根据拖拽的顶点索引，使用更直观的矩形约束逻辑
            # 矩形的4个顶点通常按顺序：左上(0)、右上(1)、右下(2)、左下(3)
            if index == 0:  # 左上角
                # 移动左上角，右下角(2)固定不动
                points[0] = pos  # 左上：直接设置为新位置
                points[1] = QtCore.QPointF(points[2].x(), pos.y())  # 右上：X保持右下角的X，Y跟随左上角
                points[3] = QtCore.QPointF(pos.x(), points[2].y())  # 左下：X跟随左上角，Y保持右下角的Y
                # points[2] 右下角保持不变
                
            elif index == 1:  # 右上角
                # 移动右上角，左下角(3)固定不动
                points[1] = pos  # 右上：直接设置为新位置
                points[0] = QtCore.QPointF(points[3].x(), pos.y())  # 左上：X保持左下角的X，Y跟随右上角
                points[2] = QtCore.QPointF(pos.x(), points[3].y())  # 右下：X跟随右上角，Y保持左下角的Y
                # points[3] 左下角保持不变
                
            elif index == 2:  # 右下角
                # 移动右下角，左上角(0)固定不动
                points[2] = pos  # 右下：直接设置为新位置
                points[1] = QtCore.QPointF(pos.x(), points[0].y())  # 右上：X跟随右下角，Y保持左上角的Y
                points[3] = QtCore.QPointF(points[0].x(), pos.y())  # 左下：X保持左上角的X，Y跟随右下角
                # points[0] 左上角保持不变
                
            elif index == 3:  # 左下角
                # 移动左下角，右上角(1)固定不动
                points[3] = pos  # 左下：直接设置为新位置
                points[0] = QtCore.QPointF(pos.x(), points[1].y())  # 左上：X跟随左下角，Y保持右上角的Y
                points[2] = QtCore.QPointF(points[1].x(), pos.y())  # 右下：X保持右上角的X，Y跟随左下角
                # points[1] 右上角保持不变
            
            # 🔧 优化：确保矩形的合理性（最小尺寸限制）
            min_size = 15.0  # 提高最小尺寸到15像素，提供更好的操作体验
            x_coords = [p.x() for p in points]
            y_coords = [p.y() for p in points]
            
            width = max(x_coords) - min(x_coords)
            height = max(y_coords) - min(y_coords)
            
            if width >= min_size and height >= min_size:
                # 🔧 优化：批量更新所有顶点，确保原子性操作
                shape.points = points
                
                # 🆕 提供视觉反馈：更新工具提示显示当前尺寸
                self.setToolTip(f"矩形调整模式 - 尺寸: {width:.0f} × {height:.0f} 像素")
            else:
                # 🔧 优化：尺寸太小时提供反馈，但不阻止操作
                self.setToolTip(f"尺寸过小 (最小 {min_size:.0f} 像素) - 当前: {width:.0f} × {height:.0f}")
                return
        else:
            # 非表格单元格或非4点形状，使用原有逻辑
            shape.moveVertexBy(index, pos - point)  # type: ignore[union-attr]

    def closeEnough(self, p1, p2):
        # d = distance(p1 - p2)
        # m = (p1-p2).manhattanLength()
        # print "d %.2f, m %d, %.2f" % (d, m, d - m)
        # divide by scale to allow more precision when zoomed in
        return labelme.utils.distance(p1 - p2) < (self.epsilon / self.scale)

    def intersectionPoint(self, p1, p2):
        # Cycle through each image edge in clockwise fashion,
        # and find the one intersecting the current line segment.
        # http://paulbourke.net/geometry/lineline2d/
        size = self.pixmap.size()
        points = [
            (0, 0),
            (size.width() - 1, 0),
            (size.width() - 1, size.height() - 1),
            (0, size.height() - 1),
        ]
        # x1, y1 should be in the pixmap, x2, y2 should be out of the pixmap
        x1 = min(max(p1.x(), 0), size.width() - 1)
        y1 = min(max(p1.y(), 0), size.height() - 1)
        x2, y2 = p2.x(), p2.y()
        d, i, (x, y) = min(self.intersectingEdges((x1, y1), (x2, y2), points))
        x3, y3 = points[i]
        x4, y4 = points[(i + 1) % 4]
        if (x, y) == (x1, y1):
            # Handle cases where previous point is on one of the edges.
            if x3 == x4:
                return QtCore.QPointF(x3, min(max(0, y2), max(y3, y4)))
            else:  # y3 == y4
                return QtCore.QPointF(min(max(0, x2), max(x3, x4)), y3)
        return QtCore.QPointF(x, y)

    def intersectingEdges(self, point1, point2, points):
        """Find intersecting edges.

        For each edge formed by `points', yield the intersection
        with the line segment `(x1,y1) - (x2,y2)`, if it exists.
        Also return the distance of `(x2,y2)' to the middle of the
        edge along with its index, so that the one closest can be chosen.
        """
        (x1, y1) = point1
        (x2, y2) = point2
        for i in range(4):
            x3, y3 = points[i]
            x4, y4 = points[(i + 1) % 4]
            denom = (y4 - y3) * (x2 - x1) - (x4 - x3) * (y2 - y1)
            nua = (x4 - x3) * (y1 - y3) - (y4 - y3) * (x1 - x3)
            nub = (x2 - x1) * (y1 - y3) - (y2 - y1) * (x1 - x3)
            if denom == 0:
                # This covers two cases:
                #   nua == nub == 0: Coincident
                #   otherwise: Parallel
                continue
            ua, ub = nua / denom, nub / denom
            if 0 <= ua <= 1 and 0 <= ub <= 1:
                x = x1 + ua * (x2 - x1)
                y = y1 + ua * (y2 - y1)
                m = QtCore.QPointF((x3 + x4) / 2, (y3 + y4) / 2)
                d = labelme.utils.distance(m - QtCore.QPointF(x2, y2))
                yield d, i, (x, y)

    # These two, along with a call to adjustSize are required for the
    # scroll area.
    def sizeHint(self):
        return self.minimumSizeHint()

    def minimumSizeHint(self):
        if self.pixmap:
            return self.scale * self.pixmap.size()
        return super(Canvas, self).minimumSizeHint()

    def wheelEvent(self, ev):
        delta = ev.angleDelta()
        modifiers = ev.modifiers()

        # 检查是否按下了Ctrl键
        if modifiers & QtCore.Qt.ControlModifier:  # type: ignore[attr-defined]
            # Ctrl+滚轮：缩放功能
            if delta.y() != 0:
                # 发送缩放请求信号，delta.y() > 0 表示向上滚动（放大），< 0 表示向下滚动（缩小）
                self.zoomRequest.emit(delta.y(), ev.pos())
            ev.accept()
        else:
            # 普通滚轮：滚动图像（修正方向）
            # 🔧 修正滚轮方向：取反delta值，让滚轮方向更自然
            self.scrollRequest.emit(-delta.y(), QtCore.Qt.Vertical)  # type: ignore[attr-defined]
            if delta.x() != 0:
                self.scrollRequest.emit(-delta.x(), QtCore.Qt.Horizontal)  # type: ignore[attr-defined]
            ev.accept()

    def moveByKeyboard(self, offset):
        if self.selectedShapes:
            self.boundedMoveShapes(self.selectedShapes, self.prevPoint + offset)
            self.repaint()
            self.movingShape = True

    def keyPressEvent(self, ev):
        modifiers = ev.modifiers()
        key = ev.key()  # 🔧 确保这行存在
        if self.drawing():
            if key == QtCore.Qt.Key_Escape and self.current:  # type: ignore[attr-defined]
                self.current = None
                self.drawingPolygon.emit(False)
                self.update()
            elif key == QtCore.Qt.Key_Return and self.canCloseShape():  # type: ignore[attr-defined]
                self.finalise()
            elif modifiers == QtCore.Qt.AltModifier:  # type: ignore[attr-defined]
                self.snapping = False
        elif self.editing():
            if key == QtCore.Qt.Key_Up:  # type: ignore[attr-defined]
                self.moveByKeyboard(QtCore.QPointF(0.0, -MOVE_SPEED))
            elif key == QtCore.Qt.Key_Down:  # type: ignore[attr-defined]
                self.moveByKeyboard(QtCore.QPointF(0.0, MOVE_SPEED))
            elif key == QtCore.Qt.Key_Left:  # type: ignore[attr-defined]
                self.moveByKeyboard(QtCore.QPointF(-MOVE_SPEED, 0.0))
            elif key == QtCore.Qt.Key_Right:  # type: ignore[attr-defined]
                self.moveByKeyboard(QtCore.QPointF(MOVE_SPEED, 0.0))

    def is_grid_drag_mode(self):
        """检查是否在网格拖拽模式"""
        return getattr(self, 'unified_grid_drag_mode', False)

    def _exit_unified_grid_drag_mode(self):
        """停用统一网格线拖动模式 - 修复版"""
        if not self.is_grid_drag_mode():
            return  # 已经退出

        LOGGER.info("🔧 停用统一网格线拖动模式")
        self.unified_grid_drag_mode = False

        # 清理网格线检测器
        if self.grid_line_detector:
            self.grid_line_detector.clear_cache()
            self.grid_line_detector = None

        # 清理网格线多选器
        if self.grid_line_multi_selector:
            self.grid_line_multi_selector.cancel_box_selection()
            self.grid_line_multi_selector.clear_selection()

        # 🆕 清理表格旋转状态
        self._clear_table_rotation_state()

        # 🔧 修复：使用修复版的恢复方法
        self._restore_other_interactions()

        # 恢复默认光标和提示
        self.setCursor(QtCore.Qt.ArrowCursor)  # 先设置默认光标
        self.restoreCursor()
        self.setToolTip("")

        # 更新显示
        self.update()

    def _clear_table_rotation_state(self):
        """🆕 清理表格旋转状态"""
        self.table_rotation_mode = False
        self.table_rotation_center = None
        self.table_rotation_start_angle = 0
        self.rotating_table_cells.clear()
        self.table_rotation_original_positions.clear()
        self.table_rotation_hover_mode = False
        LOGGER.debug("🔧 表格旋转状态已清理")

    def _handle_table_rotation_hover(self, pos):
        """🆕 处理表格旋转悬停模式"""
        # 检测鼠标位置附近的表格单元格
        table_cells = self._detect_table_cells_at_position(pos)

        if table_cells:
            # 进入表格旋转悬停模式
            if not self.table_rotation_hover_mode:
                self.table_rotation_hover_mode = True
                self.overrideCursor(CURSOR_TABLE_ROTATE)
                LOGGER.debug("🔄 [TABLE_ROTATE] 进入表格旋转悬停模式")
            self.setToolTip("按住拖拽旋转整个表格 (Ctrl+Alt)")
        else:
            # 退出表格旋转悬停模式
            if self.table_rotation_hover_mode:
                self.table_rotation_hover_mode = False
                self.restoreCursor()
                LOGGER.debug("🔄 [TABLE_ROTATE] 退出表格旋转悬停模式")
            self.setToolTip("网格拖拽模式 - 按住Ctrl+Alt在表格上旋转")

    def _detect_table_cells_at_position(self, pos):
        """🆕 检测指定位置的表格单元格"""
        table_cells = []

        # 遍历所有可见的表格单元格形状
        for shape in reversed([s for s in self.shapes if self.isVisible(s)]):
            if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell':
                if shape.containsPoint(pos):
                    # 🆕 使用智能检测模式：基于空间连接关系检测相邻单元格
                    table_cells = self._get_connected_table_cells(shape)

                    # 🔧 回退机制：如果智能检测只找到一个单元格，尝试基于table_id检测
                    if len(table_cells) <= 1:
                        table_id = shape.table_properties.get('table_id', 0)
                        fallback_cells = self._get_all_cells_in_table(table_id)
                        if len(fallback_cells) > len(table_cells):
                            table_cells = fallback_cells
                            LOGGER.debug(f"🔍 [TABLE_DETECT] 回退到ID检测，找到 {len(table_cells)} 个单元格")
                        else:
                            LOGGER.debug(f"🔍 [TABLE_DETECT] 智能检测到 {len(table_cells)} 个连接的单元格")
                    else:
                        LOGGER.debug(f"🔍 [TABLE_DETECT] 智能检测到 {len(table_cells)} 个连接的单元格")
                    break

        return table_cells

    def _get_all_cells_in_table(self, table_id):
        """🆕 获取指定表格ID的所有单元格"""
        table_cells = []

        for shape in self.shapes:
            if (hasattr(shape, 'shape_type') and
                shape.shape_type == 'table_cell' and
                shape.table_properties.get('table_id', 0) == table_id):
                table_cells.append(shape)

        LOGGER.debug(f"🔍 [TABLE_DETECT] 表格ID {table_id} 包含 {len(table_cells)} 个单元格")
        return table_cells

    def _get_connected_table_cells(self, start_cell):
        """🆕 获取与起始单元格空间连接的所有单元格"""
        if not start_cell:
            return []

        # 获取所有表格单元格
        all_table_cells = []
        for shape in self.shapes:
            if (hasattr(shape, 'shape_type') and
                shape.shape_type == 'table_cell' and
                self.isVisible(shape)):
                all_table_cells.append(shape)

        if not all_table_cells:
            return []

        # 使用广度优先搜索找到所有连接的单元格
        connected_cells = set()
        queue = [start_cell]
        connected_cells.add(start_cell)

        # 连接容差（像素）- 适应不同的对齐精度
        connection_tolerance = 10.0  # 增加容差以适应对齐后的单元格

        while queue:
            current_cell = queue.pop(0)

            # 检查与当前单元格相邻的所有单元格
            for other_cell in all_table_cells:
                if other_cell in connected_cells:
                    continue

                # 检查两个单元格是否相邻（共享边界或接近）
                if self._are_cells_adjacent(current_cell, other_cell, connection_tolerance):
                    connected_cells.add(other_cell)
                    queue.append(other_cell)

        result = list(connected_cells)
        LOGGER.debug(f"🔍 [CONNECTED_CELLS] 从起始单元格找到 {len(result)} 个连接的单元格")
        return result

    def _are_cells_adjacent(self, cell1, cell2, tolerance):
        """🆕 检查两个单元格是否相邻"""
        if not cell1.points or not cell2.points or len(cell1.points) < 4 or len(cell2.points) < 4:
            return False

        # 获取单元格的边界框
        bbox1 = self._get_cell_bounding_box(cell1)
        bbox2 = self._get_cell_bounding_box(cell2)

        if not bbox1 or not bbox2:
            return False

        # 检查边界框是否相邻（在容差范围内）
        # 水平相邻：一个的右边接近另一个的左边
        horizontal_adjacent = (
            abs(bbox1['right'] - bbox2['left']) <= tolerance or
            abs(bbox2['right'] - bbox1['left']) <= tolerance
        )

        # 垂直相邻：一个的下边接近另一个的上边
        vertical_adjacent = (
            abs(bbox1['bottom'] - bbox2['top']) <= tolerance or
            abs(bbox2['bottom'] - bbox1['top']) <= tolerance
        )

        # 检查是否有重叠的区域（用于判断是否真正相邻）
        horizontal_overlap = not (bbox1['right'] < bbox2['left'] - tolerance or
                                bbox2['right'] < bbox1['left'] - tolerance)
        vertical_overlap = not (bbox1['bottom'] < bbox2['top'] - tolerance or
                              bbox2['bottom'] < bbox1['top'] - tolerance)

        # 相邻条件：在一个方向上相邻，在另一个方向上有重叠
        return ((horizontal_adjacent and vertical_overlap) or
                (vertical_adjacent and horizontal_overlap))

    def _get_cell_bounding_box(self, cell):
        """🆕 获取单元格的边界框"""
        if not cell.points or len(cell.points) < 4:
            return None

        x_coords = [p.x() for p in cell.points]
        y_coords = [p.y() for p in cell.points]

        return {
            'left': min(x_coords),
            'right': max(x_coords),
            'top': min(y_coords),
            'bottom': max(y_coords)
        }

    def _update_table_rotation(self, pos):
        """🆕 更新表格旋转"""
        if not self.table_rotation_mode or not self.table_rotation_center:
            return

        # 计算旋转角度（极低灵敏度）
        rotation_angle = self._calculate_table_rotation_angle(pos)

        # 应用旋转到所有表格单元格
        self._apply_table_rotation(rotation_angle)

        # 更新工具提示
        self.setToolTip(f"表格旋转中 - 角度: {rotation_angle:.1f}° (松开鼠标完成旋转)")

        LOGGER.debug(f"🔄 [TABLE_ROTATE] 旋转角度: {rotation_angle:.1f}°")

    def _calculate_table_rotation_angle(self, current_pos):
        """🆕 计算表格旋转角度（极低灵敏度）"""
        if not self.table_rotation_center or not self.drag_start_pos:
            return 0

        import math

        # 计算当前鼠标相对于旋转中心的向量
        current_dx = current_pos.x() - self.table_rotation_center.x()
        current_dy = current_pos.y() - self.table_rotation_center.y()

        # 计算起始位置相对于旋转中心的向量
        start_dx = self.drag_start_pos.x() - self.table_rotation_center.x()
        start_dy = self.drag_start_pos.y() - self.table_rotation_center.y()

        # 使用向量叉积和点积计算角度差
        cross_product = start_dx * current_dy - start_dy * current_dx
        dot_product = start_dx * current_dx + start_dy * current_dy

        # 计算角度差（弧度转度数）
        angle_diff_radians = math.atan2(cross_product, dot_product)
        angle_diff_degrees = math.degrees(angle_diff_radians)

        # 🎯 极低灵敏度控制（比网格线旋转更低）
        sensitivity = 0.15  # 15%的灵敏度
        controlled_angle = angle_diff_degrees * sensitivity

        # 🎯 角度限制（表格旋转通常需要更大的角度范围）
        max_rotation = 45.0  # 最大±45度
        controlled_angle = max(-max_rotation, min(max_rotation, controlled_angle))

        return controlled_angle

    def _apply_table_rotation(self, rotation_angle):
        """🆕 对整个表格应用旋转变换"""
        if not self.rotating_table_cells or not self.table_rotation_center:
            return

        # 对每个表格单元格的所有顶点应用旋转
        for cell in self.rotating_table_cells:
            cell_id = id(cell)
            if cell_id not in self.table_rotation_original_positions:
                continue

            original_points = self.table_rotation_original_positions[cell_id]
            if len(original_points) != len(cell.points):
                continue

            # 对该单元格的所有顶点应用旋转
            for i, original_point in enumerate(original_points):
                rotated_point = self._rotate_point(
                    original_point,
                    self.table_rotation_center,
                    rotation_angle
                )
                cell.points[i] = rotated_point

        LOGGER.debug(f"✅ [TABLE_ROTATE] 已对{len(self.rotating_table_cells)}个单元格应用旋转")

    def _calculate_table_center(self, table_cells):
        """🆕 计算表格几何中心"""
        if not table_cells:
            return None

        # 收集所有单元格的所有顶点
        all_points = []
        for cell in table_cells:
            all_points.extend(cell.points)

        if not all_points:
            return None

        # 计算所有顶点的几何中心
        total_x = sum(point.x() for point in all_points)
        total_y = sum(point.y() for point in all_points)
        center_x = total_x / len(all_points)
        center_y = total_y / len(all_points)

        center = QtCore.QPointF(center_x, center_y)
        LOGGER.debug(f"🎯 [TABLE_CENTER] 表格中心: ({center_x:.1f}, {center_y:.1f})")
        return center

    def _start_table_rotation(self, pos):
        """🆕 开始表格旋转"""
        # 检测鼠标位置的表格单元格
        table_cells = self._detect_table_cells_at_position(pos)

        if not table_cells:
            LOGGER.debug("🔄 [TABLE_ROTATE] 未检测到表格单元格，无法开始旋转")
            return False

        # 计算表格中心点
        table_center = self._calculate_table_center(table_cells)
        if not table_center:
            LOGGER.debug("🔄 [TABLE_ROTATE] 无法计算表格中心，无法开始旋转")
            return False

        # 设置表格旋转状态
        self.table_rotation_mode = True
        self.table_rotation_center = table_center
        self.rotating_table_cells = table_cells
        self.drag_start_pos = pos
        self.table_rotation_start_angle = 0

        # 保存所有表格单元格的原始位置
        self.table_rotation_original_positions.clear()
        for cell in table_cells:
            cell_id = id(cell)
            # 深拷贝原始位置
            original_points = [QtCore.QPointF(p.x(), p.y()) for p in cell.points]
            self.table_rotation_original_positions[cell_id] = original_points

        # 设置旋转光标
        self.overrideCursor(CURSOR_TABLE_ROTATE)
        self.setToolTip("表格旋转中 - 松开鼠标完成旋转")

        LOGGER.info(f"✅ [TABLE_ROTATE] 开始表格旋转，{len(table_cells)}个单元格，中心: ({table_center.x():.1f}, {table_center.y():.1f})")
        return True

    def _finish_table_rotation(self, ev):
        """🆕 完成表格旋转"""
        if not self.table_rotation_mode:
            return

        pos = self.transformPos(ev.localPos())

        # 计算最终旋转角度
        final_rotation_angle = self._calculate_table_rotation_angle(pos)

        # 🔧 移除旋转阈值限制 - 保留所有用户旋转操作
        # 创建并执行旋转命令
        self._create_table_rotation_command(final_rotation_angle)
        LOGGER.info(f"✅ [TABLE_ROTATE] 表格旋转完成，角度: {final_rotation_angle:.1f}°")

        # 清理旋转状态
        self._clear_table_rotation_state()

        # 恢复光标
        self.setCursor(QtCore.Qt.ArrowCursor)
        self.restoreCursor()
        self.setToolTip("")

        # 更新显示
        self.update()

    def _restore_table_original_positions(self):
        """🆕 恢复表格原始位置"""
        for cell in self.rotating_table_cells:
            cell_id = id(cell)
            if cell_id in self.table_rotation_original_positions:
                original_points = self.table_rotation_original_positions[cell_id]
                # 恢复原始位置
                for i, original_point in enumerate(original_points):
                    if i < len(cell.points):
                        cell.points[i] = QtCore.QPointF(original_point.x(), original_point.y())

    def _create_table_rotation_command(self, rotation_angle):
        """🆕 创建表格旋转命令"""
        try:
            # 收集受影响的单元格
            affected_cells = list(self.rotating_table_cells)

            # 收集原始位置
            old_positions = []
            for cell in affected_cells:
                cell_id = id(cell)
                if cell_id in self.table_rotation_original_positions:
                    old_positions.append(self.table_rotation_original_positions[cell_id])
                else:
                    # 备用方案：使用当前位置作为原始位置
                    old_positions.append([QtCore.QPointF(p.x(), p.y()) for p in cell.points])

            # 收集新位置（当前位置）
            new_positions = []
            for cell in affected_cells:
                new_positions.append([QtCore.QPointF(p.x(), p.y()) for p in cell.points])

            # 构建旋转信息
            table_id = affected_cells[0].table_properties.get('table_id', 0) if affected_cells else 0
            rotation_info = {
                'center': self.table_rotation_center,
                'angle': rotation_angle,
                'table_id': table_id
            }

            # 创建表格旋转命令
            from labelme.commands.grid_commands import TableRotateCommand
            rotate_command = TableRotateCommand(
                canvas=self,
                affected_cells=affected_cells,
                old_positions=old_positions,
                new_positions=new_positions,
                rotation_info=rotation_info
            )

            # 执行命令（通过历史管理器）
            if hasattr(self, 'history_manager') and self.history_manager:
                # 由于旋转已经应用，我们需要先撤销，然后通过命令系统执行
                self._restore_table_original_positions()
                success = self.history_manager.execute_command(rotate_command)
                if success:
                    LOGGER.info(f"✅ [TABLE_ROTATE_CMD] 表格旋转命令已执行并记录到历史")
                else:
                    LOGGER.error(f"❌ [TABLE_ROTATE_CMD] 表格旋转命令执行失败")
            else:
                LOGGER.warning("[TABLE_ROTATE_CMD] 没有历史管理器，跳过命令记录")

        except Exception as e:
            LOGGER.error(f"[TABLE_ROTATE_CMD] 创建表格旋转命令失败: {e}")
            import traceback
            traceback.print_exc()

    def _handle_grid_mode_key_release(self, ev, modifiers):
        """🆕 处理网格拖拽模式的键盘释放事件"""
        # 检测Ctrl+Alt键释放
        is_ctrl_pressed = modifiers & QtCore.Qt.ControlModifier
        is_alt_pressed = modifiers & QtCore.Qt.AltModifier
        is_ctrl_alt_pressed = is_ctrl_pressed and is_alt_pressed

        # 如果Ctrl+Alt组合键被释放，退出表格旋转悬停模式
        # 注意：不影响正在进行的表格旋转操作（table_rotation_mode=True时）
        if not is_ctrl_alt_pressed:
            self._exit_table_rotation_hover_mode()

    def _exit_table_rotation_hover_mode(self):
        """🆕 退出表格旋转悬停模式"""
        # 只有在悬停模式且不在实际旋转状态时才退出
        if self.table_rotation_hover_mode and not self.table_rotation_mode:
            self.table_rotation_hover_mode = False
            self.restoreCursor()

            # 恢复默认工具提示
            if self.is_grid_drag_mode():
                self.setToolTip("网格拖拽模式 - 悬停在网格线上拖拽调整")
            else:
                self.setToolTip("")

            LOGGER.debug("🔄 [TABLE_ROTATE] 已退出表格旋转悬停模式")

    def keyReleaseEvent(self, ev):
        modifiers = ev.modifiers()

        # 🆕 处理网格拖拽模式的键盘释放事件
        if self.is_grid_drag_mode():
            self._handle_grid_mode_key_release(ev, modifiers)

        if self.drawing():
            if int(modifiers) == 0:
                self.snapping = True
        elif self.editing():
            if self.movingShape and self.selectedShapes:
                index = self.shapes.index(self.selectedShapes[0])
                if self.shapesBackups[-1][index].points != self.shapes[index].points:
                    self.storeShapes()
                    self.shapeMoved.emit()

                self.movingShape = False

    def setLastLabel(self, text, flags):
        assert text
        self.shapes[-1].label = text
        self.shapes[-1].flags = flags
        self.shapesBackups.pop()
        self.storeShapes()
        return self.shapes[-1]

    def undoLastLine(self):
        assert self.shapes
        self.current = self.shapes.pop()
        self.current.setOpen()
        self.current.restoreShapeRaw()
        if self.createMode in ["polygon", "linestrip"]:
            self.line.points = [self.current[-1], self.current[0]]
        elif self.createMode in ["rectangle", "line", "circle"]:
            self.current.points = self.current.points[0:1]
        elif self.createMode == "point":
            self.current = None
        self.drawingPolygon.emit(True)

    def undoLastPoint(self):
        if not self.current or self.current.isClosed():
            return
        self.current.popPoint()
        if len(self.current) > 0:
            self.line[0] = self.current[-1]
        else:
            self.current = None
            self.drawingPolygon.emit(False)
        self.update()

    def loadPixmap(self, pixmap, clear_shapes=True):
        self.pixmap = pixmap
        if clear_shapes:
            self.shapes = []
        self.update()

    def loadShapes(self, shapes, replace=True):
        if replace:
            self.shapes = list(shapes)
        else:
            self.shapes.extend(shapes)
        self.storeShapes()
        self.current = None
        self.hShape = None
        self.hVertex = None
        self.hEdge = None
        self.update()

    def setShapeVisible(self, shape, value):
        self.visible[shape] = value
        self.update()

    def overrideCursor(self, cursor):
        self.restoreCursor()
        self._cursor = cursor
        QtWidgets.QApplication.setOverrideCursor(cursor)

    def restoreCursor(self):
        QtWidgets.QApplication.restoreOverrideCursor()

    def setBatchSelectCursor(self):
        """设置批量选中十字光标"""
        self.overrideCursor(CURSOR_SELECT)

    def resetState(self):
        self.restoreCursor()
        self.pixmap = None  # type: ignore[assignment]
        self.shapesBackups = []
        self.update()

    def _handle_quick_table_region_finalise(self):
        """🆕 处理快速表格选区完成事件"""
        # 获取选区坐标
        if not self.current or len(self.current.points) < 2:
            LOGGER.error("快速表格选区无效")
            self.current = None
            self.setEditing(True)
            return
        
        # 计算选区边界
        points = self.current.points
        x1, y1 = points[0].x(), points[0].y()
        x2, y2 = points[1].x(), points[1].y()
        
        # 确保坐标顺序正确
        region_rect = (
            min(x1, x2), min(y1, y2),
            max(x1, x2), max(y1, y2)
        )
        
        LOGGER.debug(f"快速表格选区: {region_rect}")
        
        # 清理当前状态
        self.current = None
        self.setEditing(True)
        
        # 🔗 触发主窗口的表格生成对话框
        if hasattr(self, 'main_window') and self.main_window:
            self.main_window._show_quick_table_dialog(region_rect)
        else:
            LOGGER.error("无法访问主窗口，无法显示对话框")

    def _handle_table_finalise(self, intercept_result):
        """处理表格特殊的finalise逻辑"""
        action = intercept_result['action']
        result_data = intercept_result['result_data']
        LOGGER.debug(f"动作: {action}")

        if action == 'create_table_cell':
            # 🔧 修复：这里不再处理单元格创建，交由finalise方法统一处理
            # 避免重复调用execute_command
            table_cell = result_data['table_cell_shape']
            
            LOGGER.debug("_handle_table_finalise检测到单元格创建")
            LOGGER.debug("单元格将由finalise方法的命令系统统一处理")
            LOGGER.debug(f"表格单元格创建准备完成，总计: {result_data['total_cells']}")
            
            # 注意：不在这里添加到shapes，让finalise方法处理

        elif action == 'create_table_boundary':
            # 🔧 处理表格边界框创建
            boundary_shape = result_data['table_boundary_shape']
            LOGGER.debug("添加表格边界框到Canvas.shapes")
            LOGGER.debug(f"添加前shapes数量: {len(self.shapes)}")
            self.shapes.insert(0, boundary_shape)  # 插入到开头
            self.visible[boundary_shape] = True
            # 🔧 设置边界框为不可选择（可选方案）
            boundary_shape.selectable = False  # 防止边界框被选中
            LOGGER.debug(f"添加后shapes数量: {len(self.shapes)}")
            LOGGER.debug(f"成功创建表格边界框: {result_data['message']}")

        elif action == 'analyze_table_area':
            # 🔧 分析时也要保留边界框
            boundary_shape = result_data['table_boundary_shape']
            LOGGER.debug("保留表格边界框")

            self.shapes.append(boundary_shape)
            self.visible[boundary_shape] = True

            analysis_result = result_data['analysis_result']
            selected_cells = result_data['selected_cells']
            grid_size = result_data['grid_size']
            LOGGER.debug(f"表格分析完成: {grid_size[0]}行 x {grid_size[1]}列")

        elif action == 'no_cells_found':
            # 保持原有处理
            LOGGER.warning("选择区域内没有找到单元格")

        elif action == 'analysis_failed':
            error = result_data['error']
            LOGGER.error(f"表格分析失败: {error}")

        # 通用清理和更新
        self.current = None
        self.storeShapes()
        self.setHiding(False)

        if hasattr(self, 'multi_table_controller') and self.multi_table_controller:
            self.multi_table_controller.handle_canvas_event('shape_finalized', None)

    def enter_unified_grid_drag_mode(self):
        """激活统一网格线拖动模式"""
        if self.is_grid_drag_mode():
            return  # 已经在模式中

        LOGGER.debug("🔧 激活统一网格线拖动模式")
        self.unified_grid_drag_mode = True

        # 初始化网格线检测器
        self._initialize_grid_line_detector()

        # 禁用其他交互（根据PRD FR-1.3要求）
        self._save_current_interaction_state()
        self._disable_other_interactions()

        # 🆕 修改：设置状态提示 Alt+1
        self.setToolTip("统一网格线拖动模式已激活 (按住Z键) - 将鼠标悬停在网格线上开始拖动")

        # 立即检测当前的网格线（如果有表格单元格）
        self._detect_current_grid_lines()

        # 更新显示
        self.update()

    def exit_grid_drag_mode(self):
        """退出网格拖拽模式（由主应用调用）"""
        self._exit_unified_grid_drag_mode()
        # 🆕 确保光标恢复
        self.setCursor(QtCore.Qt.ArrowCursor)

        self.restoreCursor()
    def enter_grid_drag_mode(self):
        """进入网格拖拽模式（由主应用调用）"""
        self._enter_unified_grid_drag_mode()
    def _enter_unified_grid_drag_mode(self):
        """激活统一网格线拖动模式 - 修复版"""
        if self.unified_grid_drag_mode:
            return  # 已经在模式中

        LOGGER.info("🔧 激活统一网格线拖动模式（修复版）")
        self.unified_grid_drag_mode = True

        # 初始化网格线检测器
        self._initialize_grid_line_detector()

        # 🔧 修复：正确的状态保存和设置
        self._save_current_interaction_state()
        self._disable_other_interactions()  # 使用修复版本

        # 设置状态提示
        self.setToolTip("统一网格线拖动模式已激活 (Ctrl+G) - 将鼠标悬停在网格线上开始拖动")

        # 立即检测当前的网格线（如果有表格单元格）
        self._detect_current_grid_lines()

        # 更新显示
        self.update()
    def _initialize_grid_line_detector(self):
        """初始化网格线检测器"""
        if not self.grid_line_detector:
            # 延迟导入避免循环依赖
            from labelme.utils.grid_line_detector import GridLineDetector
            self.grid_line_detector = GridLineDetector(tolerance=1.0)
            LOGGER.info("✅ 创建网格线检测器，容差=1.0px")

    def _detect_current_grid_lines(self):
        """检测当前画布上的网格线"""
        if not self.grid_line_detector:
            return

        # 收集所有TableCellShape
        table_cells = []
        for shape in self.shapes:
            if (hasattr(shape, 'shape_type') and
                    shape.shape_type == 'table_cell' and
                    self.isVisible(shape)):
                table_cells.append(shape)

        if table_cells:
            # 执行网格线检测
            grid_lines = self.grid_line_detector.detect_continuous_lines(table_cells)
            LOGGER.debug(f"✅ 检测到 {len(grid_lines)} 条网格线")

            # 打印检测结果用于调试
            from labelme.utils.grid_line_detector import print_grid_lines_info
            print_grid_lines_info(grid_lines)
        else:
            LOGGER.debug("⚠️ 画布上没有TableCellShape，无法检测网格线")

    def _save_current_interaction_state(self):
        """保存当前交互状态"""
        self._saved_create_mode = getattr(self, 'createMode', None)
        self._saved_editing_mode = self.editing() if hasattr(self, 'editing') else False
        self._saved_selected_shapes = self.selectedShapes.copy() if hasattr(self, 'selectedShapes') else []

    def _disable_other_interactions(self):
        """禁用其他交互功能 - 修复版（不改变Canvas模式）"""
        # ✅ 修复：保持EDIT模式，不调用setEditing(False)
        # 网格拖拽模式应该在EDIT模式下工作，只是临时禁用某些交互

        # 保存当前选择状态
        self._saved_selected_shapes = self.selectedShapes.copy() if hasattr(self, 'selectedShapes') else []

        # 清除当前选择（防止干扰网格线操作）
        if hasattr(self, 'deSelectShape'):
            self.deSelectShape()

        # 设置特殊标志，告诉其他事件处理器我们在网格拖拽模式
        self._grid_drag_mode_active = True

        LOGGER.info("🔒 已禁用其他交互功能（保持EDIT模式）")

    def _restore_other_interactions(self):
        """恢复其他交互功能 - 修复版"""
        # 清除网格拖拽模式标志
        self._grid_drag_mode_active = False

        # 恢复之前的选择状态（如果需要的话）
        # 注意：通常不需要恢复选择，让用户重新选择即可

        # 清理保存的状态
        for attr in ['_saved_create_mode', '_saved_editing_mode', '_saved_selected_shapes']:
            if hasattr(self, attr):
                delattr(self, attr)

        LOGGER.info("🔓 已恢复其他交互功能")

def _update_shape_with_sam(
        sam: osam.types.Model,
        pixmap: QtGui.QPixmap,
        shape: Shape,
        createMode: Literal["ai_polygon", "ai_mask"],
) -> None:
    if createMode not in ["ai_polygon", "ai_mask"]:
        raise ValueError(
            f"createMode must be 'ai_polygon' or 'ai_mask', not {createMode}"
        )

    image_embedding: osam.types.ImageEmbedding = _compute_image_embedding(
        sam=sam, pixmap=pixmap
    )

    response: osam.types.GenerateResponse = osam.apis.generate(
        osam.types.GenerateRequest(
            model=sam.name,
            image_embedding=image_embedding,
            prompt=osam.types.Prompt(
                points=[[point.x(), point.y()] for point in shape.points],
                point_labels=shape.point_labels,
            ),
        )
    )
    if not response.annotations:
        logger.warning("No annotations returned by model {!r}", sam)
        return

    if createMode == "ai_mask":
        y1: int
        x1: int
        y2: int
        x2: int
        if response.annotations[0].bounding_box is None:
            y1, x1, y2, x2 = imgviz.instances.mask_to_bbox(
                [response.annotations[0].mask]
            )[0].astype(int)
        else:
            y1 = response.annotations[0].bounding_box.ymin
            x1 = response.annotations[0].bounding_box.xmin
            y2 = response.annotations[0].bounding_box.ymax
            x2 = response.annotations[0].bounding_box.xmax
        shape.setShapeRefined(
            shape_type="mask",
            points=[QtCore.QPointF(x1, y1), QtCore.QPointF(x2, y2)],
            point_labels=[1, 1],
            mask=response.annotations[0].mask[y1: y2 + 1, x1: x2 + 1],
        )
    elif createMode == "ai_polygon":
        points = polygon_from_mask.compute_polygon_from_mask(
            mask=response.annotations[0].mask
        )
        if len(points) < 2:
            return
        shape.setShapeRefined(
            shape_type="polygon",
            points=[QtCore.QPointF(point[0], point[1]) for point in points],
            point_labels=[1] * len(points),
        )


@functools.lru_cache(maxsize=1)
def _get_ai_model(model_name: str) -> osam.types.Model:
    return osam.apis.get_model_type_by_name(name=model_name)()


def _compute_image_embedding(
        sam: osam.types.Model, pixmap: QtGui.QPixmap
) -> osam.types.ImageEmbedding:
    return __compute_image_embedding(sam=sam, pixmap=_QPixmapForLruCache(pixmap))


class _QPixmapForLruCache(QtGui.QPixmap):
    def __hash__(self) -> int:
        qimage: QtGui.QImage = self.toImage()
        bits = qimage.constBits()
        if bits is None:
            return hash(None)
        return hash(bits.asstring(qimage.sizeInBytes()))

    def __eq__(self, other) -> bool:
        if not isinstance(other, _QPixmapForLruCache):
            return False
        return self.__hash__() == other.__hash__()


@functools.lru_cache(maxsize=3)
def __compute_image_embedding(
        sam: osam.types.Model, pixmap: _QPixmapForLruCache
) -> osam.types.ImageEmbedding:
    logger.debug("Computing image embeddings for model {!r}", sam.name)
    image: np.ndarray = labelme.utils.img_qt_to_arr(pixmap.toImage())
    return sam.encode_image(image=imgviz.asrgb(image))