#!/usr/bin/python3
# -*- coding: utf-8 -*-
# Time: 2025-01-03 18:00
# Author: <EMAIL>
# FileName: ai_prompt_widget.py

"""
AI提示组件 - 第三方API辅助打标界面

提供TextIn API调用和状态显示的用户界面组件。
"""

from PyQt5 import QtWidgets, QtCore, QtGui

from ..utils.log import get_logger

LOGGER = get_logger()


class AIPromptWidget(QtWidgets.QWidget):
    """
    AI提示组件
    
    提供TextIn API辅助打标的用户界面，包括：
    - 一键调用按钮
    - 状态显示和错误提示
    """
    
    # 信号定义
    api_labeling_requested = QtCore.pyqtSignal(str)  # 参数为API类型
    
    def __init__(self, parent=None):
        """
        初始化AI提示组件
        
        Args:
            parent: 父控件
        """
        super(AIPromptWidget, self).__init__(parent)
        
        self._is_calling_api = False
        
        self._setup_ui()
        self._connect_signals()
        
        LOGGER.debug("AIPromptWidget初始化完成")
    
    def _setup_ui(self):
        """设置用户界面布局"""
        # 主布局
        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)
        
        # 执行按钮
        self.execute_btn = QtWidgets.QPushButton("🚀 执行TextIn表格识别", self)
        self.execute_btn.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
                min-height: 25px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #95a5a6;
                color: #ecf0f1;
            }
        """)
        self.execute_btn.setToolTip("调用TextIn API进行表格自动识别")
        
        layout.addWidget(self.execute_btn)
        
        # 状态显示区域
        self.status_label = QtWidgets.QLabel("")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 10px;
                padding: 3px 5px;
                border-radius: 3px;
                background-color: #ecf0f1;
            }
        """)
        self.status_label.setWordWrap(True)
        self.status_label.hide()  # 默认隐藏
        
        layout.addWidget(self.status_label)
        
        self.setMaximumHeight(100)  # 限制组件高度，保持紧凑
    
    def _connect_signals(self):
        """连接信号槽"""
        # 执行按钮点击
        self.execute_btn.clicked.connect(self._on_execute_clicked)
    
    def _on_execute_clicked(self):
        """处理执行按钮点击"""
        if self._is_calling_api:
            LOGGER.warning("API调用正在进行中，忽略重复点击")
            return
        
        LOGGER.debug("用户请求执行TextIn API辅助打标")
        
        # 显示调用状态
        self.show_loading("正在调用TextIn API，请稍候...")
        
        # 发送信号
        self.api_labeling_requested.emit("textin")
    
    def show_loading(self, message: str = "正在处理..."):
        """显示加载状态"""
        self._is_calling_api = True
        self.execute_btn.setEnabled(False)
        
        self.status_label.setText(f"⏳ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #f39c12;
                font-size: 10px;
                padding: 3px 5px;
                border-radius: 3px;
                background-color: #fef9e7;
                border: 1px solid #f39c12;
            }
        """)
        self.status_label.show()
        
        LOGGER.debug(f"显示加载状态: {message}")
    
    def show_success(self, message: str = "API调用成功"):
        """显示成功状态"""
        self._is_calling_api = False
        self.execute_btn.setEnabled(True)
        
        self.status_label.setText(f"✅ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #27ae60;
                font-size: 10px;
                padding: 3px 5px;
                border-radius: 3px;
                background-color: #eafaf1;
                border: 1px solid #27ae60;
            }
        """)
        self.status_label.show()
        
        # 3秒后自动隐藏成功消息
        QtCore.QTimer.singleShot(3000, self.clear_status)
        
        LOGGER.debug(f"显示成功状态: {message}")
    
    def show_error(self, message: str = "API调用失败"):
        """显示错误状态"""
        self._is_calling_api = False
        self.execute_btn.setEnabled(True)
        
        self.status_label.setText(f"❌ {message}")
        self.status_label.setStyleSheet("""
            QLabel {
                color: #e74c3c;
                font-size: 10px;
                padding: 3px 5px;
                border-radius: 3px;
                background-color: #fdedec;
                border: 1px solid #e74c3c;
            }
        """)
        self.status_label.show()
        
        LOGGER.debug(f"显示错误状态: {message}")
    
    def clear_status(self):
        """清除状态显示"""
        self.status_label.hide()
        self.status_label.setText("")
        
        # 确保控件状态正确
        if not self._is_calling_api:
            self.execute_btn.setEnabled(True)
    
    def set_enabled(self, enabled: bool):
        """设置组件启用状态"""
        if not enabled or not self._is_calling_api:
            self.execute_btn.setEnabled(enabled)
        
        # 如果禁用组件，清除状态
        if not enabled:
            self.clear_status()
    
    def get_selected_api_type(self) -> str:
        """获取当前选中的API类型"""
        return "textin"  # 始终返回TextIn
    
    def get_api_info(self) -> dict:
        """获取当前API信息"""
        return {
            "api_type": "textin",
            "api_display_name": "TextIn 通用表格识别",
            "is_calling": self._is_calling_api
        }


class AiPromptWidget(QtWidgets.QWidget):
    def __init__(self, on_submit, parent=None):
        super().__init__(parent=parent)

        self.setLayout(QtWidgets.QVBoxLayout())
        self.layout().setSpacing(0)  # type: ignore[union-attr]

        self._text_prompt_widget = _TextPromptWidget(on_submit=on_submit, parent=self)
        self._text_prompt_widget.setMaximumWidth(400)
        self.layout().addWidget(self._text_prompt_widget)  # type: ignore[union-attr]

        self._nms_params_widget = _NmsParamsWidget(parent=self)
        self._nms_params_widget.setMaximumWidth(400)
        self.layout().addWidget(self._nms_params_widget)  # type: ignore[union-attr]

    def get_text_prompt(self) -> str:
        return self._text_prompt_widget.get_text_prompt()

    def get_iou_threshold(self) -> float:
        return self._nms_params_widget.get_iou_threshold()

    def get_score_threshold(self) -> float:
        return self._nms_params_widget.get_score_threshold()


class _TextPromptWidget(QtWidgets.QWidget):
    def __init__(self, on_submit, parent=None):
        super().__init__(parent=parent)

        self.setLayout(QtWidgets.QHBoxLayout())
        self.layout().setContentsMargins(0, 0, 0, 0)  # type: ignore[union-attr]

        label = QtWidgets.QLabel(self.tr("AI Prompt"))
        self.layout().addWidget(label)

        self._texts_widget = QtWidgets.QLineEdit()
        self._texts_widget.setPlaceholderText(self.tr("e.g., dog,cat,bird"))
        self.layout().addWidget(self._texts_widget)  # type: ignore[union-attr]

        submit_button = QtWidgets.QPushButton(text="Submit", parent=self)
        submit_button.clicked.connect(slot=on_submit)
        self.layout().addWidget(submit_button)  # type: ignore[union-attr]

    def get_text_prompt(self) -> str:
        return self._texts_widget.text()


class _NmsParamsWidget(QtWidgets.QWidget):
    def __init__(self, parent=None):
        super().__init__(parent=parent)

        self.setLayout(QtWidgets.QHBoxLayout())
        self.layout().setContentsMargins(0, 0, 0, 0)  # type: ignore[union-attr]

        self._score_threshold_widget: _ScoreThresholdWidget = _ScoreThresholdWidget(
            parent=parent
        )
        self.layout().addWidget(self._score_threshold_widget)

        self._iou_threshold_widget: _IouThresholdWidget = _IouThresholdWidget(
            parent=parent
        )
        self.layout().addWidget(self._iou_threshold_widget)

    def get_score_threshold(self) -> float:
        return self._score_threshold_widget.get_value()

    def get_iou_threshold(self) -> float:
        return self._iou_threshold_widget.get_value()


class _ScoreThresholdWidget(QtWidgets.QWidget):
    default_score_threshold: float = 0.1

    def __init__(self, parent=None):
        super().__init__(parent=parent)

        self.setLayout(QtWidgets.QHBoxLayout())
        self.layout().setContentsMargins(0, 0, 0, 0)  # type: ignore[union-attr]

        label = QtWidgets.QLabel(self.tr("Score Threshold"))
        self.layout().addWidget(label)  # type: ignore[union-attr]

        self._threshold_widget: QtWidgets.QDoubleSpinBox = QtWidgets.QDoubleSpinBox()
        self._threshold_widget.setRange(0, 1)
        self._threshold_widget.setSingleStep(0.05)
        self._threshold_widget.setValue(self.default_score_threshold)
        self.layout().addWidget(self._threshold_widget)  # type: ignore[union-attr]

    def get_value(self) -> float:
        return self._threshold_widget.value()


class _IouThresholdWidget(QtWidgets.QWidget):
    default_iou_threshold: float = 0.5

    def __init__(self, parent=None):
        super().__init__(parent=parent)

        self.setLayout(QtWidgets.QHBoxLayout())
        self.layout().setContentsMargins(0, 0, 0, 0)  # type: ignore[union-attr]

        label = QtWidgets.QLabel(self.tr("IoU Threshold"))
        self.layout().addWidget(label)  # type: ignore[union-attr]

        self._threshold_widget: QtWidgets.QDoubleSpinBox = QtWidgets.QDoubleSpinBox()
        self._threshold_widget.setRange(0, 1)
        self._threshold_widget.setSingleStep(0.05)
        self._threshold_widget.setValue(self.default_iou_threshold)
        self.layout().addWidget(self._threshold_widget)  # type: ignore[union-attr]

    def get_value(self) -> float:
        return self._threshold_widget.value()


# 测试用的主函数
def main():
    """独立测试AI提示组件"""
    import sys
    
    app = QtWidgets.QApplication(sys.argv)
    
    window = QtWidgets.QMainWindow()
    window.setWindowTitle("AI提示组件测试")
    window.setGeometry(100, 100, 300, 250)
    
    widget = AIPromptWidget()
    
    # 测试信号连接
    def on_api_labeling_requested(api_type):
        print(f"🔔 收到API打标请求: {api_type}")
        
        # 模拟API调用过程
        QtCore.QTimer.singleShot(2000, lambda: widget.show_success("API调用成功！"))
    
    widget.api_labeling_requested.connect(on_api_labeling_requested)
    
    window.setCentralWidget(widget)
    window.show()
    
    print("🎉 AI提示组件测试启动")
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
