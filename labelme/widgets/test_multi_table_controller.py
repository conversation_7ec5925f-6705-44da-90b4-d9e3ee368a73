#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/3 17:30
# <AUTHOR> <EMAIL>
# @FileName: test_multi_table_controller.py

"""MultiTableController单元测试"""

import unittest
import sys
import os
from unittest.mock import Mock, MagicMock
from PyQt5 import QtCore, QtWidgets

# 正确添加labelme路径 - 修复导入问题
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 从widgets目录上升到labelme目录
sys.path.insert(0, parent_dir)

# 使用正确的导入路径
from labelme.widgets.multi_table_controller import (
    MultiTableController,
    TableInstanceManager,
    TableSwitchManager,
    create_multi_table_controller_for_canvas
)
# 修复导入路径 - 使用相对导入
from .table_controller import TableController

class TestTableInstanceManager(unittest.TestCase):
    """TableInstanceManager单元测试"""
    
    def test_register_table_region(self):
        """测试表格区域注册"""
        # Table-Driven Test cases
        test_cases = [
            # (区域坐标, 期望成功, 描述)
            ((0, 0, 100, 100), True, "正常区域"),
            ((150, 150, 250, 250), True, "不重叠区域"),
            ((300, 300, 400, 400), True, "远距离区域"),
        ]
        
        manager = TableInstanceManager()
        registered_ids = []
        
        for region, should_succeed, description in test_cases:
            with self.subTest(region=region, desc=description):
                if should_succeed:
                    table_id = manager.register_table_region(region)
                    self.assertIsInstance(table_id, int)
                    self.assertGreater(table_id, 0)
                    registered_ids.append(table_id)
                    
                    # 验证区域已注册
                    stored_region = manager.get_table_region(table_id)
                    self.assertEqual(stored_region, region)
        
        # 验证ID是递增的
        for i in range(1, len(registered_ids)):
            self.assertGreater(registered_ids[i], registered_ids[i-1])
    
    def test_region_overlap_detection(self):
        """测试区域重叠检测"""
        # Table-Driven Test cases
        test_cases = [
            # (第一个区域, 第二个区域, 是否应该重叠)
            ((0, 0, 100, 100), (50, 50, 150, 150), True),   # 重叠
            ((0, 0, 100, 100), (110, 110, 200, 200), False), # 不重叠（有间隙）
            ((0, 0, 100, 100), (100, 100, 200, 200), True),  # 边界接触（算重叠）
            ((0, 0, 50, 50), (60, 60, 100, 100), False),     # 不重叠（有间隙）
        ]
        
        for region1, region2, should_overlap in test_cases:
            with self.subTest(region1=region1, region2=region2):
                manager = TableInstanceManager()
                
                # 注册第一个区域
                id1 = manager.register_table_region(region1)
                self.assertIsInstance(id1, int)
                
                # 尝试注册第二个区域
                if should_overlap:
                    with self.assertRaises(ValueError):
                        manager.register_table_region(region2)
                else:
                    id2 = manager.register_table_region(region2)
                    self.assertIsInstance(id2, int)
                    self.assertNotEqual(id1, id2)
    
    def test_invalid_regions(self):
        """测试无效区域处理"""
        # Table-Driven Test cases
        invalid_regions = [
            (100, 0, 0, 100),    # x1 > x2
            (0, 100, 100, 0),    # y1 > y2
            (50, 50, 50, 100),   # x1 == x2
            (50, 50, 100, 50),   # y1 == y2
        ]
        
        manager = TableInstanceManager()
        
        for invalid_region in invalid_regions:
            with self.subTest(region=invalid_region):
                with self.assertRaises(ValueError):
                    manager.register_table_region(invalid_region)


class TestTableSwitchManager(unittest.TestCase):
    """TableSwitchManager单元测试"""
    
    def setUp(self):
        """测试准备"""
        if not QtWidgets.QApplication.instance():
            self.app = QtWidgets.QApplication([])
        
        # 创建Mock MultiTableController
        self.mock_multi_controller = Mock()
        self.mock_multi_controller.table_controllers = {}
        self.mock_multi_controller.active_table_id = None
        self.mock_multi_controller.instance_manager = Mock()
        
        self.switch_manager = TableSwitchManager(self.mock_multi_controller)
    
    def test_auto_detect_active_table(self):
        """测试自动检测活动表格"""
        # 设置表格区域
        regions = {
            1: (0, 0, 100, 100),
            2: (150, 150, 250, 250),
            3: (300, 0, 400, 100),
        }
        self.mock_multi_controller.instance_manager.get_all_regions.return_value = regions
        
        # Table-Driven Test cases
        test_cases = [
            # (光标位置, 期望检测到的表格ID)
            ((50, 50), 1),      # 在表格1内
            ((200, 200), 2),    # 在表格2内
            ((350, 50), 3),     # 在表格3内
            ((500, 500), None), # 在所有表格外
            ((125, 125), None), # 在表格间隙
        ]
        
        for cursor_pos, expected_table_id in test_cases:
            with self.subTest(pos=cursor_pos):
                result = self.switch_manager.auto_detect_active_table(cursor_pos)
                self.assertEqual(result, expected_table_id)
    
    def test_switch_to_table(self):
        """测试表格切换"""
        # 创建Mock TableController
        mock_controller1 = Mock()
        mock_controller2 = Mock()
        
        self.mock_multi_controller.table_controllers = {
            1: mock_controller1,
            2: mock_controller2
        }
        
        # Table-Driven Test cases
        test_cases = [
            # (目标表格ID, 期望成功)
            (1, True),   # 切换到存在的表格
            (2, True),   # 切换到另一个表格
            (999, False), # 切换到不存在的表格
        ]
        
        for table_id, should_succeed in test_cases:
            with self.subTest(table_id=table_id):
                result = self.switch_manager.switch_to_table(table_id)
                self.assertEqual(result, should_succeed)
                
                if should_succeed:
                    self.assertEqual(self.mock_multi_controller.active_table_id, table_id)


class TestMultiTableController(unittest.TestCase):
    """MultiTableController单元测试"""
    
    def setUp(self):
        """测试准备"""
        if not QtWidgets.QApplication.instance():
            self.app = QtWidgets.QApplication([])
        
        # 创建Mock Canvas
        self.mock_canvas = Mock()
        self.mock_canvas.shapes = []
        self.mock_canvas.update = Mock()
        
        # 创建MultiTableController
        self.controller = MultiTableController(self.mock_canvas)
    
    def test_create_table_instance(self):
        """测试创建表格实例"""
        # Table-Driven Test cases
        test_cases = [
            # (表格区域, 期望成功)
            ((0, 0, 100, 100), True),
            ((150, 150, 250, 250), True),
            ((300, 0, 400, 100), True),
        ]
        
        created_table_ids = []
        
        for region, should_succeed in test_cases:
            with self.subTest(region=region):
                if should_succeed:
                    table_id = self.controller.create_table_instance(region)
                    self.assertIsInstance(table_id, int)
                    self.assertGreater(table_id, 0)
                    created_table_ids.append(table_id)
                    
                    # 验证表格控制器已创建
                    self.assertIn(table_id, self.controller.table_controllers)
                    table_controller = self.controller.table_controllers[table_id]
                    self.assertIsInstance(table_controller, TableController)
                    self.assertEqual(table_controller.table_region, region)
                    self.assertEqual(table_controller.table_id, table_id)
        
        # 验证第一个表格成为活动表格
        if created_table_ids:
            self.assertEqual(self.controller.active_table_id, created_table_ids[0])
        
        # 验证表格数量
        self.assertEqual(self.controller.get_table_count(), len(created_table_ids))
    
    def test_create_overlapping_tables(self):
        """测试创建重叠表格"""
        # 创建第一个表格
        table_id1 = self.controller.create_table_instance((0, 0, 100, 100))
        self.assertIsInstance(table_id1, int)
        
        # 尝试创建重叠的表格
        overlapping_regions = [
            (50, 50, 150, 150),  # 部分重叠
            (0, 0, 100, 100),    # 完全重叠
            (90, 90, 200, 200),  # 边界重叠
        ]
        
        for region in overlapping_regions:
            with self.subTest(region=region):
                with self.assertRaises(ValueError):
                    self.controller.create_table_instance(region)
    
    def test_switch_active_table(self):
        """测试切换活动表格"""
        # 创建多个表格
        table_id1 = self.controller.create_table_instance((0, 0, 100, 100))
        table_id2 = self.controller.create_table_instance((150, 150, 250, 250))
        table_id3 = self.controller.create_table_instance((300, 0, 400, 100))
        
        # Table-Driven Test cases
        test_cases = [
            # (目标表格ID, 期望成功)
            (table_id2, True),
            (table_id3, True),
            (table_id1, True),
            (999, False),  # 不存在的表格
        ]
        
        for target_id, should_succeed in test_cases:
            with self.subTest(target_id=target_id):
                result = self.controller.switch_active_table(target_id)
                self.assertEqual(result, should_succeed)
                
                if should_succeed:
                    self.assertEqual(self.controller.active_table_id, target_id)
                    active_controller = self.controller.get_active_controller()
                    self.assertIsNotNone(active_controller)
                    self.assertEqual(active_controller.table_id, target_id)
    
    def test_remove_table_instance(self):
        """测试移除表格实例"""
        # 创建多个表格
        table_id1 = self.controller.create_table_instance((0, 0, 100, 100))
        table_id2 = self.controller.create_table_instance((150, 150, 250, 250))
        table_id3 = self.controller.create_table_instance((300, 0, 400, 100))
        
        initial_count = self.controller.get_table_count()
        
        # Table-Driven Test cases
        test_cases = [
            # (要移除的表格ID, 期望成功)
            (table_id2, True),   # 移除中间的表格
            (999, False),        # 移除不存在的表格
            (table_id3, True),   # 移除最后的表格
        ]
        
        expected_count = initial_count
        
        for table_id, should_succeed in test_cases:
            with self.subTest(table_id=table_id):
                result = self.controller.remove_table_instance(table_id)
                self.assertEqual(result, should_succeed)
                
                if should_succeed:
                    expected_count -= 1
                    self.assertNotIn(table_id, self.controller.table_controllers)
                    self.assertEqual(self.controller.get_table_count(), expected_count)
                    
                    # 如果移除的是活动表格，应该切换到其他表格
                    if self.controller.get_table_count() > 0:
                        self.assertIsNotNone(self.controller.active_table_id)
                        self.assertIn(self.controller.active_table_id, self.controller.table_controllers)
    
    def test_export_all_tables(self):
        """测试导出所有表格数据"""
        # 创建表格
        table_id1 = self.controller.create_table_instance((0, 0, 100, 100))
        table_id2 = self.controller.create_table_instance((150, 150, 250, 250))
        
        # 导出数据
        export_data = self.controller.export_all_tables()
        
        # 验证导出数据结构
        self.assertIn('image_info', export_data)
        self.assertIn('tables', export_data)
        
        # 验证图像信息
        image_info = export_data['image_info']
        self.assertEqual(image_info['total_tables'], 2)
        self.assertEqual(image_info['active_table_id'], self.controller.active_table_id)
        
        # 验证表格数据
        tables = export_data['tables']
        self.assertIn(table_id1, tables)
        self.assertIn(table_id2, tables)
        
        # 验证表格数据结构
        for table_id in [table_id1, table_id2]:
            table_data = tables[table_id]
            self.assertIn('table_id', table_data)
            self.assertIn('region', table_data)
            self.assertIn('cells', table_data)
            self.assertIn('stats', table_data)
            self.assertEqual(table_data['table_id'], table_id)
    
    def test_get_status_info(self):
        """测试获取状态信息"""
        # 空状态
        status = self.controller.get_status_info()
        self.assertEqual(status['table_count'], 0)
        self.assertEqual(status['total_cells'], 0)
        self.assertIsNone(status['active_table_id'])
        self.assertEqual(status['regions'], {})
        
        # 创建表格后的状态
        table_id1 = self.controller.create_table_instance((0, 0, 100, 100))
        table_id2 = self.controller.create_table_instance((150, 150, 250, 250))
        
        status = self.controller.get_status_info()
        self.assertEqual(status['table_count'], 2)
        self.assertEqual(status['active_table_id'], table_id1)  # 第一个表格是活动的
        self.assertEqual(len(status['regions']), 2)
        self.assertIn(table_id1, status['regions'])
        self.assertIn(table_id2, status['regions'])
    
    def test_clear_all_tables(self):
        """测试清除所有表格"""
        # 创建多个表格
        self.controller.create_table_instance((0, 0, 100, 100))
        self.controller.create_table_instance((150, 150, 250, 250))
        self.controller.create_table_instance((300, 0, 400, 100))
        
        # 验证表格已创建
        self.assertEqual(self.controller.get_table_count(), 3)
        
        # 清除所有表格
        self.controller.clear_all_tables()
        
        # 验证所有表格已清除
        self.assertEqual(self.controller.get_table_count(), 0)
        self.assertIsNone(self.controller.active_table_id)
        self.assertEqual(len(self.controller.table_controllers), 0)


class TestMultiTableControllerIntegration(unittest.TestCase):
    """MultiTableController集成测试"""
    
    def setUp(self):
        """测试准备"""
        if not QtWidgets.QApplication.instance():
            self.app = QtWidgets.QApplication([])
        
        self.mock_canvas = Mock()
        self.mock_canvas.shapes = []
        self.mock_canvas.update = Mock()
    
    def test_create_multi_table_controller_for_canvas(self):
        """测试便捷创建函数"""
        controller = create_multi_table_controller_for_canvas(self.mock_canvas)
        
        # 验证控制器类型
        self.assertIsInstance(controller, MultiTableController)
        
        # 验证Canvas引用设置
        self.assertEqual(controller.canvas, self.mock_canvas)
        self.assertEqual(self.mock_canvas.multi_table_controller, controller)
    
    def test_table_lifecycle(self):
        """测试表格完整生命周期"""
        controller = MultiTableController(self.mock_canvas)
        
        # 1. 创建表格
        table_id = controller.create_table_instance((0, 0, 200, 100))
        self.assertIsInstance(table_id, int)
        self.assertEqual(controller.get_table_count(), 1)
        
        # 2. 添加表格到活动状态
        self.assertEqual(controller.active_table_id, table_id)
        active_controller = controller.get_active_controller()
        self.assertIsNotNone(active_controller)
        
        # 3. 创建第二个表格并切换
        table_id2 = controller.create_table_instance((250, 250, 450, 350))
        self.assertEqual(controller.get_table_count(), 2)
        
        success = controller.switch_active_table(table_id2)
        self.assertTrue(success)
        self.assertEqual(controller.active_table_id, table_id2)
        
        # 4. 导出数据
        export_data = controller.export_all_tables()
        self.assertEqual(len(export_data['tables']), 2)
        
        # 5. 移除表格
        success = controller.remove_table_instance(table_id)
        self.assertTrue(success)
        self.assertEqual(controller.get_table_count(), 1)
        self.assertNotIn(table_id, controller.table_controllers)
        
        # 6. 清除所有表格
        controller.clear_all_tables()
        self.assertEqual(controller.get_table_count(), 0)


if __name__ == "__main__":
    unittest.main() 