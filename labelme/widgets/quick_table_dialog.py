#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/3 16:45
# <AUTHOR> <EMAIL>
# @FileName: quick_table_dialog.py

"""
快速生成表格对话框 - P1阶段新增功能
支持用户快速创建MxN规格的表格，简化表格创建流程
"""

from PyQt5 import QtCore, QtGui, QtWidgets
from typing import Tuple, Optional

from ..utils.log import get_logger

LOGGER = get_logger()


class QuickTableDialog(QtWidgets.QDialog):
    """快速生成表格对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("快速生成表格")
        self.setModal(True)
        self.setFixedSize(350, 250)
        
        # 初始化UI
        self._init_ui()
        
        # 设置默认值
        self.rows_spinbox.setValue(3)
        self.cols_spinbox.setValue(4)
        
        LOGGER.debug("QuickTableDialog初始化完成")

    def _init_ui(self):
        """初始化用户界面"""
        layout = QtWidgets.QVBoxLayout(self)
        
        # 标题和说明
        title_label = QtWidgets.QLabel("快速生成表格")
        title_label.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        desc_label = QtWidgets.QLabel("选择要创建的表格规格，系统将自动生成均匀分布的表格单元格")
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #666; margin-bottom: 15px;")
        layout.addWidget(desc_label)
        
        # 表格规格设置区域
        spec_group = QtWidgets.QGroupBox("表格规格")
        spec_layout = QtWidgets.QFormLayout(spec_group)
        
        # 行数设置
        self.rows_spinbox = QtWidgets.QSpinBox()
        self.rows_spinbox.setRange(1, 20)
        self.rows_spinbox.setSuffix(" 行")
        self.rows_spinbox.valueChanged.connect(self._on_spec_changed)
        spec_layout.addRow("行数:", self.rows_spinbox)
        
        # 列数设置
        self.cols_spinbox = QtWidgets.QSpinBox()
        self.cols_spinbox.setRange(1, 20)
        self.cols_spinbox.setSuffix(" 列")
        self.cols_spinbox.valueChanged.connect(self._on_spec_changed)
        spec_layout.addRow("列数:", self.cols_spinbox)
        
        layout.addWidget(spec_group)
        
        # 表格预览区域
        preview_group = QtWidgets.QGroupBox("预览")
        preview_layout = QtWidgets.QVBoxLayout(preview_group)
        
        self.preview_label = QtWidgets.QLabel()
        self.preview_label.setAlignment(QtCore.Qt.AlignCenter)
        self.preview_label.setStyleSheet("""
            QLabel {
                border: 1px solid #ccc;
                background-color: #f9f9f9;
                min-height: 60px;
                font-size: 12px;
                color: #333;
            }
        """)
        preview_layout.addWidget(self.preview_label)
        
        layout.addWidget(preview_group)
        
        # 按钮区域
        button_layout = QtWidgets.QHBoxLayout()
        
        # 预设按钮
        preset_layout = QtWidgets.QHBoxLayout()
        preset_2x3_btn = QtWidgets.QPushButton("2×3")
        preset_2x3_btn.clicked.connect(lambda: self._set_preset(2, 3))
        preset_3x4_btn = QtWidgets.QPushButton("3×4")
        preset_3x4_btn.clicked.connect(lambda: self._set_preset(3, 4))
        preset_4x5_btn = QtWidgets.QPushButton("4×5")
        preset_4x5_btn.clicked.connect(lambda: self._set_preset(4, 5))
        
        preset_layout.addWidget(QtWidgets.QLabel("快速选择:"))
        preset_layout.addWidget(preset_2x3_btn)
        preset_layout.addWidget(preset_3x4_btn)
        preset_layout.addWidget(preset_4x5_btn)
        preset_layout.addStretch()
        
        button_layout.addLayout(preset_layout)
        
        # 对话框按钮
        button_box = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        
        # 设置按钮文本
        button_box.button(QtWidgets.QDialogButtonBox.Ok).setText("生成表格")
        button_box.button(QtWidgets.QDialogButtonBox.Cancel).setText("取消")
        
        button_layout.addWidget(button_box)
        
        layout.addLayout(button_layout)
        
        # 触发预览更新
        self._on_spec_changed()

    def _set_preset(self, rows: int, cols: int):
        """设置预设规格"""
        self.rows_spinbox.setValue(rows)
        self.cols_spinbox.setValue(cols)

    def _on_spec_changed(self):
        """规格变化时更新预览"""
        rows = self.rows_spinbox.value()
        cols = self.cols_spinbox.value()
        total_cells = rows * cols
        
        preview_text = f"""
表格规格: {rows} 行 × {cols} 列
总单元格数: {total_cells} 个
        """.strip()
        
        self.preview_label.setText(preview_text)

    def get_table_spec(self) -> Tuple[int, int]:
        """获取用户选择的表格规格
        
        Returns:
            (rows, cols): 行数和列数的元组
        """
        return (self.rows_spinbox.value(), self.cols_spinbox.value())

    @classmethod
    def get_table_specification(cls, parent=None) -> Optional[Tuple[int, int]]:
        """静态方法：显示对话框并获取表格规格
        
        Args:
            parent: 父窗口
            
        Returns:
            (rows, cols) 如果用户确认，否则返回 None
        """
        dialog = cls(parent)
        
        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            LOGGER.debug(f"用户选择生成 {dialog.get_table_spec()[0]}×{dialog.get_table_spec()[1]} 表格")
            return dialog.get_table_spec()
        else:
            LOGGER.debug("用户取消了操作")
            return None

    def keyPressEvent(self, event):
        """处理键盘事件"""
        if event.key() == QtCore.Qt.Key_Return or event.key() == QtCore.Qt.Key_Enter:
            self.accept()
        elif event.key() == QtCore.Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)


def test_quick_table_dialog():
    """测试函数"""
    import sys
    app = QtWidgets.QApplication(sys.argv)
    
    rows, cols, accepted = QuickTableDialog.get_table_size_from_user()
    if accepted:
        LOGGER.debug(f"用户选择生成 {rows}×{cols} 表格")
    else:
        LOGGER.debug("用户取消了操作")
    
    sys.exit()


if __name__ == "__main__":
    test_quick_table_dialog() 