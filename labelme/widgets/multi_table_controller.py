#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/3 17:20  
# <AUTHOR> <EMAIL>
# @FileName: multi_table_controller.py

"""
多表格协调器 - 管理一个图像中的多个表格实例
核心职责：表格实例创建、切换、事件分发、数据统一管理
"""


import sys
from typing import Dict, Any, Optional, List, Tuple
from PyQt5 import QtCore, QtWidgets

from labelme.widgets.table_controller import TableController
from labelme.table_shape import TableCellShape, is_table_cell
from labelme.utils.log import get_logger

LOGGER = get_logger()

class TableInstanceManager:
    """表格实例管理器"""
    
    def __init__(self):
        self.next_table_id = 1
        self.table_regions: Dict[int, Tuple[float, float, float, float]] = {}
        self.region_overlap_tolerance = 5.0  # 区域重叠容差
    
    def register_table_region(self, region: Tuple[float, float, float, float]) -> int:
        """注册新的表格区域
        
        Args:
            region: 表格区域坐标(x1, y1, x2, y2)
            
        Returns:
            新分配的表格ID
            
        Raises:
            ValueError: 如果区域重叠或无效
        """
        # 验证区域有效性
        x1, y1, x2, y2 = region
        if x1 >= x2 or y1 >= y2:
            raise ValueError(f"无效的表格区域: {region}")
        
        # 检查区域重叠
        if self._check_region_overlap(region):
            raise ValueError(f"表格区域与现有区域重叠: {region}")
        
        # 分配新的表格ID
        table_id = self.next_table_id
        self.next_table_id += 1
        
        # 注册区域
        self.table_regions[table_id] = region
        
        LOGGER.debug(f"注册表格区域 ID={table_id}: {region}")
        return table_id
    
    def _check_region_overlap(self, new_region: Tuple[float, float, float, float]) -> bool:
        """检查新区域是否与现有区域重叠"""
        x1, y1, x2, y2 = new_region
        
        for existing_region in self.table_regions.values():
            ex1, ey1, ex2, ey2 = existing_region
            
            # 检查两个矩形是否重叠（考虑容差）
            if not (x2 <= ex1 - self.region_overlap_tolerance or 
                   x1 >= ex2 + self.region_overlap_tolerance or
                   y2 <= ey1 - self.region_overlap_tolerance or 
                   y1 >= ey2 + self.region_overlap_tolerance):
                return True
        
        return False
    
    def get_table_region(self, table_id: int) -> Optional[Tuple[float, float, float, float]]:
        """获取表格区域"""
        return self.table_regions.get(table_id)
    
    def update_table_region(self, table_id: int, region: Tuple[float, float, float, float]) -> bool:
        """更新表格区域"""
        if table_id not in self.table_regions:
            return False
        
        # 临时移除当前区域进行重叠检查
        old_region = self.table_regions.pop(table_id)
        
        try:
            if self._check_region_overlap(region):
                # 恢复旧区域
                self.table_regions[table_id] = old_region
                return False
            
            # 更新区域
            self.table_regions[table_id] = region
            return True
            
        except Exception:
            # 恢复旧区域
            self.table_regions[table_id] = old_region
            return False
    
    def remove_table_region(self, table_id: int) -> bool:
        """移除表格区域"""
        if table_id in self.table_regions:
            del self.table_regions[table_id]
            return True
        return False
    
    def get_all_regions(self) -> Dict[int, Tuple[float, float, float, float]]:
        """获取所有表格区域"""
        return self.table_regions.copy()


class TableSwitchManager:
    """表格切换管理器"""
    
    def __init__(self, controller_ref):
        self.controller_ref = controller_ref  # MultiTableController引用
        self.auto_switch_enabled = True
    
    def switch_to_table(self, table_id: int) -> bool:
        """切换到指定表格
        
        Args:
            table_id: 目标表格ID
            
        Returns:
            切换是否成功
        """
        if table_id not in self.controller_ref.table_controllers:
            LOGGER.error(f"表格 ID={table_id} 不存在")
            return False
        
        if table_id == self.controller_ref.active_table_id:
            LOGGER.debug(f"表格 ID={table_id} 已经是活动表格")
            return True
        
        # 通知当前活动表格失去焦点
        if self.controller_ref.active_table_id is not None:
            current_controller = self.controller_ref.table_controllers.get(
                self.controller_ref.active_table_id
            )
            if current_controller:
                current_controller.exit_all_modes()
        
        # 切换活动表格
        old_id = self.controller_ref.active_table_id
        self.controller_ref.active_table_id = table_id
        
        # 通知新活动表格获得焦点
        new_controller = self.controller_ref.table_controllers[table_id]
        
        # 高亮活动表格区域
        self.highlight_active_table(table_id)
        
        LOGGER.debug(f"表格切换: {old_id} -> {table_id}")
        return True
    
    def auto_detect_active_table(self, cursor_pos: Tuple[float, float]) -> Optional[int]:
        """根据光标位置自动检测活动表格
        
        Args:
            cursor_pos: 光标位置(x, y)
            
        Returns:
            检测到的表格ID，如果没有则返回None
        """
        if not self.auto_switch_enabled:
            return None
        
        x, y = cursor_pos
        
        # 遍历所有表格区域，找到包含光标的区域
        for table_id, region in self.controller_ref.instance_manager.get_all_regions().items():
            x1, y1, x2, y2 = region
            if x1 <= x <= x2 and y1 <= y <= y2:
                return table_id
        
        return None
    
    def highlight_active_table(self, table_id: int):
        """高亮活动表格"""
        # 清除所有表格的高亮
        for controller in self.controller_ref.table_controllers.values():
            controller._clear_highlights()
        
        # 高亮指定表格的所有单元格
        if table_id in self.controller_ref.table_controllers:
            controller = self.controller_ref.table_controllers[table_id]
            cells = controller.table_cells
            controller._highlight_selected_cells(cells)


class MultiTableController(QtCore.QObject):
    """多表格协调器主类"""
    
    # 信号定义
    table_created = QtCore.pyqtSignal(int)  # 表格创建信号
    table_switched = QtCore.pyqtSignal(int, int)  # 表格切换信号(old_id, new_id)
    table_removed = QtCore.pyqtSignal(int)  # 表格移除信号
    
    def __init__(self, canvas):
        super().__init__()
        self.canvas = canvas
        self.table_controllers: Dict[int, TableController] = {}
        self.active_table_id: Optional[int] = None
        
        # 子管理器
        self.instance_manager = TableInstanceManager()
        self.switch_manager = TableSwitchManager(self)
        
        # 设置Canvas的多表格控制器引用
        self.canvas.multi_table_controller = self
        
        LOGGER.debug("MultiTableController 初始化完成")
    
    def get_or_create_controller(self, table_id: int) -> TableController:
        """获取或创建表格控制器
        
        如果指定ID的控制器不存在，会创建一个新的控制器。
        
        Args:
            table_id: 表格ID
            
        Returns:
            TableController: 表格控制器实例
        """
        if table_id in self.table_controllers:
            LOGGER.debug(f"复用已有表格控制器 ID={table_id}")
            return self.table_controllers[table_id]
        
        # 创建新的表格区域（默认大小，后续会根据单元格更新）
        table_region = (0, 0, 100, 100)
        
        try:
            # 注册表格区域
            registered_id = self.instance_manager.register_table_region(table_region)
            if registered_id != table_id:
                LOGGER.warning(f"请求的表格ID {table_id} 与注册ID {registered_id} 不一致")
            
            # 创建TableController实例
            controller = TableController(self.canvas)
            controller.table_region = table_region
            controller.table_id = registered_id
            
            # 存储控制器
            self.table_controllers[registered_id] = controller
            
            # 如果是第一个表格，设为活动表格
            if self.active_table_id is None:
                self.active_table_id = registered_id
                self.switch_manager.highlight_active_table(registered_id)
            
            # 发送表格创建信号
            self.table_created.emit(registered_id)
            
            LOGGER.debug(f"创建新表格控制器 ID={registered_id}")
            return controller
            
        except ValueError as e:
            LOGGER.error(f"创建表格控制器失败: {e}")
            raise
    
    def create_table_instance(self, table_region: Tuple[float, float, float, float]) -> int:
        """创建新的表格实例
        
        Args:
            table_region: 表格区域坐标(x1, y1, x2, y2)
            
        Returns:
            新创建的表格ID
            
        Raises:
            ValueError: 如果区域无效或重叠
        """
        try:
            # 注册表格区域
            table_id = self.instance_manager.register_table_region(table_region)
            
            # 创建TableController实例
            controller = TableController(self.canvas)
            controller.table_region = table_region
            controller.table_id = table_id
            
            # 存储控制器
            self.table_controllers[table_id] = controller
            
            # 如果是第一个表格，设为活动表格
            if self.active_table_id is None:
                self.active_table_id = table_id
                self.switch_manager.highlight_active_table(table_id)
            
            # 发送表格创建信号
            self.table_created.emit(table_id)
            
            LOGGER.debug(f"创建表格实例 ID={table_id}, 区域={table_region}")
            return table_id
            
        except ValueError as e:
            LOGGER.error(f"创建表格实例失败: {e}")
            raise
    
    def switch_active_table(self, table_id: int) -> bool:
        """切换活动表格
        
        Args:
            table_id: 目标表格ID
            
        Returns:
            切换是否成功
        """
        old_id = self.active_table_id
        success = self.switch_manager.switch_to_table(table_id)
        
        if success and old_id != table_id:
            self.table_switched.emit(old_id or -1, table_id)
        
        return success
    
    def get_active_controller(self) -> Optional[TableController]:
        """获取当前活动的表格控制器"""
        if self.active_table_id is None:
            return None
        return self.table_controllers.get(self.active_table_id)
    
    def get_all_controllers(self) -> Dict[int, TableController]:
        """获取所有表格控制器"""
        return self.table_controllers.copy()

    def remove_table_instance(self, table_id: int) -> bool:
        """移除表格实例及其所有数据

        Args:
            table_id: 表格ID

        Returns:
            bool: 移除是否成功
        """
        if table_id not in self.table_controllers:
            LOGGER.error(f"表格 ID={table_id} 不存在")
            return False

        LOGGER.debug(f"开始移除表格实例 ID={table_id}")

        # 获取控制器
        controller = self.table_controllers[table_id]

        # 🔧 修复：先获取要移除的单元格，再清理数据
        cells_to_remove = controller.get_table_cells()
        LOGGER.debug(f"找到 {len(cells_to_remove)} 个单元格需要删除")

        # 从Canvas中移除该表格的所有单元格
        removed_count = 0
        for cell in cells_to_remove:
            if cell in self.canvas.shapes:
                self.canvas.shapes.remove(cell)
                removed_count += 1
                LOGGER.debug(f"从Canvas移除单元格: {getattr(cell, 'label', 'unknown')}")

        LOGGER.debug(f"从Canvas移除了 {removed_count} 个单元格")

        # 🔧 修复：移除表格边界框（如果有的话）
        boundary_shapes = [s for s in self.canvas.shapes
                           if hasattr(s, 'label') and s.label == "表格区域"]
        for boundary in boundary_shapes:
            self.canvas.shapes.remove(boundary)
            LOGGER.debug("移除表格边界框")

        # 清理控制器数据
        controller.clear_table_data()

        # 移除控制器和区域
        del self.table_controllers[table_id]
        self.instance_manager.remove_table_region(table_id)

        # 如果移除的是活动表格，切换到其他表格
        if self.active_table_id == table_id:
            if self.table_controllers:
                # 切换到第一个可用的表格
                next_id = next(iter(self.table_controllers.keys()))
                self.switch_active_table(next_id)
                LOGGER.debug(f"切换到表格 ID={next_id}")
            else:
                self.active_table_id = None
                LOGGER.debug("没有其他表格，设置活动表格为None")

        # 清除Canvas的选中状态
        self.canvas.selectedShapes = []

        # 更新Canvas显示
        self.canvas.update()

        # 发送表格移除信号
        self.table_removed.emit(table_id)

        LOGGER.debug(f"移除表格实例 ID={table_id}，移除了 {len(cells_to_remove)} 个单元格")
        return True

    def import_table_data(self, shapes: List['TableCellShape']) -> Dict[int, int]:
        """导入TableCellShape列表到控制器

        Args:
            shapes: TableCellShape对象列表

        Returns:
            字典：{table_id: cell_count} 导入统计信息

        Raises:
            ValueError: 当数据无效时
        """
        # 🔧 修复：允许空shapes列表，支持空表格场景
        if not shapes:
            LOGGER.debug("导入空shapes列表，返回空统计信息")
            return {}
        
        LOGGER.info(f"🚀 开始批量导入 {len(shapes)} 个表格单元格（性能优化模式）...")

        # 按table_id分组shapes
        table_groups = {}
        for shape in shapes:
            table_id = shape.table_properties.get("table_id", 0)
            if table_id not in table_groups:
                table_groups[table_id] = []
            table_groups[table_id].append(shape)
        
        import_stats = {}
        
        # 为每个table_id创建或获取TableController
        for table_id, cell_shapes in table_groups.items():
            if table_id not in self.table_controllers:
                # 计算表格区域（基于所有cell的边界）
                table_region = self._calculate_table_region(cell_shapes)
                
                # 创建新的表格实例
                try:
                    created_id = self.create_table_instance(table_region)
                    # 如果创建的ID与预期不同，更新shapes中的table_id
                    if created_id != table_id:
                        for shape in cell_shapes:
                            shape.table_properties["table_id"] = created_id
                        table_id = created_id
                except ValueError as e:
                    LOGGER.warning(f"创建表格 {table_id} 失败: {e}")
                    continue
            
            # 获取TableController并批量添加单元格
            controller = self.table_controllers[table_id]
            LOGGER.info(f"🔍 批量导入表格数据到table_id={table_id}: {len(cell_shapes)}个单元格")

            # 🚀 性能优化：批量添加单元格，避免频繁调用storeShapes
            # 先批量添加到控制器
            controller.table_cells.extend(cell_shapes)

            # 🚀 进一步优化：使用集合快速检查重复，避免O(n²)复杂度
            existing_shapes_set = set(self.canvas.shapes)
            shapes_to_add = [shape for shape in cell_shapes if shape not in existing_shapes_set]

            # 批量添加到Canvas
            if shapes_to_add:
                self.canvas.shapes.extend(shapes_to_add)
                LOGGER.debug(f"  - 批量添加 {len(shapes_to_add)} 个新单元格到canvas.shapes")
            else:
                LOGGER.debug(f"  - 所有 {len(cell_shapes)} 个单元格已存在于canvas.shapes中")

            # 🔧 修复：同步设置控制器的表格类型
            if cell_shapes:
                # 从第一个单元格获取表格类型并设置到控制器
                first_cell_type = cell_shapes[0].get_table_type()
                if hasattr(controller, 'set_table_type'):
                    controller.set_table_type(first_cell_type)
                    LOGGER.debug(f"同步设置控制器表格类型: ID={table_id}, type={first_cell_type}")

            import_stats[table_id] = len(cell_shapes)
            LOGGER.debug(f"表格 {table_id} 导入完成: {len(cell_shapes)} 个单元格")
        
        # 🚀 性能优化：批量导入完成后统一备份shapes状态
        if import_stats:  # 只有在实际导入了数据时才备份
            self.canvas.storeShapes()
            LOGGER.debug(f"批量导入完成，统一备份shapes状态")

        # 更新Canvas显示
        self.canvas.update()

        # 设置第一个表格为活动表格
        if table_groups and self.active_table_id is None:
            first_table_id = list(table_groups.keys())[0]
            self.active_table_id = first_table_id
            self.switch_manager.highlight_active_table(first_table_id)

        total_cells = sum(import_stats.values())
        LOGGER.info(f"🚀 批量导入优化完成! 共导入 {len(table_groups)} 个表格, {total_cells} 个单元格")
        return import_stats

    def import_table_metadata(self, table_metadata: List[Dict]) -> Dict[int, int]:
        """导入表格元数据，创建空表格控制器

        Args:
            table_metadata: 表格元数据列表，每个元素包含table_id, table_type等信息

        Returns:
            字典：{table_id: cell_count} 导入统计信息
        """
        if not table_metadata:
            LOGGER.warning("表格元数据为空")
            return {}

        import_stats = {}

        for metadata in table_metadata:
            original_table_id = metadata.get("table_id", 0)
            table_type = metadata.get("table_type", 1)

            # 创建表格实例（会自动分配新的ID）
            default_region = (50, 50, 950, 750)
            actual_table_id = self.create_table_instance(default_region)
            controller = self.table_controllers[actual_table_id]

            # 设置表格类型
            if hasattr(controller, 'set_table_type'):
                controller.set_table_type(table_type)
                LOGGER.debug(f"为空表格 ID={actual_table_id} (原ID={original_table_id}) 设置类型: {table_type}")

            import_stats[actual_table_id] = 0  # 空表格，0个单元格

        # 设置第一个表格为活动表格（使用实际分配的ID）
        if import_stats and self.active_table_id is None:
            first_actual_table_id = list(import_stats.keys())[0]
            self.active_table_id = first_actual_table_id
            self.switch_manager.highlight_active_table(first_actual_table_id)

        LOGGER.debug(f"表格元数据导入完成! 共创建 {len(import_stats)} 个空表格")
        return import_stats
    
    def reset_all_controllers(self):
        """重置所有控制器和数据"""
        # 清空所有表格控制器
        self.table_controllers.clear()
        
        # 重置活动表格ID
        self.active_table_id = None
        
        # 重置表格ID计数器
        self.instance_manager.next_table_id = 1
        
        # 清空表格区域注册信息
        self.instance_manager.table_regions.clear()
        
        LOGGER.debug("已重置所有表格控制器和区域信息")
    
    def _calculate_table_region(self, shapes: List['TableCellShape']) -> Tuple[float, float, float, float]:
        """计算表格区域边界
        
        Args:
            shapes: 表格中的所有单元格
            
        Returns:
            表格区域坐标(x1, y1, x2, y2)
        """
        if not shapes:
            return (0.0, 0.0, 100.0, 100.0)  # 默认区域
        
        min_x = min_y = float('inf')
        max_x = max_y = float('-inf')
        
        for shape in shapes:
            if shape.points:
                for point in shape.points:
                    min_x = min(min_x, point.x())
                    min_y = min(min_y, point.y())
                    max_x = max(max_x, point.x())
                    max_y = max(max_y, point.y())
        
        # 添加一些边距
        margin = 10.0
        return (min_x - margin, min_y - margin, max_x + margin, max_y + margin)

    def handle_canvas_event(self, event_type: str, event_data: Any) -> Dict[str, Any]:
        """处理Canvas事件（按设计文档实现）

        Args:
            event_type: 事件类型('shape_finalize', 'selection_changed'等)
            event_data: 事件数据

        Returns:
            dict: 事件处理结果，包含 'intercepted' 标志
        """
        # 获取活动的表格控制器
        active_controller = self.get_active_controller()
        if not active_controller:
            LOGGER.warning("没有活动的表格控制器")
            return {'intercepted': False}

        # 转发事件给活动的TableController
        if event_type == 'shape_finalize':
            result = active_controller.handle_before_finalise(event_data)
            LOGGER.debug(f"事件转发结果: {result}")
            return result
        elif event_type == 'selection_changed':
            active_controller.handle_selection_changed(event_data)
            return {'intercepted': True}
        elif event_type == 'shape_finalized':
            # finalise后的通知
            if hasattr(active_controller, 'handle_after_finalise'):
                active_controller.handle_after_finalise()
            return {'intercepted': True}

        return {'intercepted': False}
    
    def export_all_tables(self) -> List[Dict[str, Any]]:
        """
        导出所有表格数据，并严格按照指定的目标格式进行序列化。

        该函数会遍历所有的 TableController，并从每个 Controller 管理的
        TableCellShape 对象中提取信息，以构建最终的 JSON 结构。

        Returns:
            一个列表，其中每个元素都是一个符合目标格式的表格数据字典。
        """
        exported_data = []

        if not self.table_controllers:
            LOGGER.warning("没有发现任何表格控制器 (table_controllers)，返回文本类型数据。")
            # 🆕 当没有表格时，返回文本类型的数据结构
            text_data = {
                'table_ind': 0,
                'image_path': "",
                'type': 0,  # 文本类型
                'cells': []
            }
            return text_data

        # 使用 enumerate 来自动生成 `table_ind`
        for i, (table_id, controller) in enumerate(self.table_controllers.items()):

            table_cells: List['TableCellShape'] = controller.get_table_cells()

            # --- 🆕 根据用户设置确定表格类型 (type) ---
            # 检查是否启用自动推导
            if (hasattr(controller, 'get_auto_determine_enabled') and
                controller.get_auto_determine_enabled() and
                hasattr(controller, 'auto_determine_table_type')):
                # 启用自动推导：根据单元格边框状态自动判断表格类型
                table_type = controller.auto_determine_table_type()
                LOGGER.debug(f"表格{i}自动推导类型: {table_type}")
            else:
                # 使用用户设置的类型
                if hasattr(controller, 'get_table_type'):
                    table_type = controller.get_table_type()
                elif table_cells:
                    table_type = table_cells[0].get_table_type()
                else:
                    table_type = 1
                LOGGER.debug(f"表格{i}使用用户设置类型: {table_type}")

            # 1. 构建单个表格的基础结构
            table_data = {
                'table_ind': i,
                'image_path': "",
                'type': table_type,
                'cells': []
            }

            # 2. 遍历并序列化单元格数据
            for j, cell in enumerate(table_cells):
                # --- 转换 bbox 格式 ---
                # 从 cell.points (QPointF列表) 转换为 {"p1": (x, y), ...}
                formatted_bbox = {}
                if cell.points and len(cell.points) >= 4:
                    # 假设 cell.points 是按顺时针顺序存储的四个点
                    formatted_bbox = {
                        "p1": (cell.points[0].x(), cell.points[0].y()),
                        "p2": (cell.points[1].x(), cell.points[1].y()),
                        "p3": (cell.points[2].x(), cell.points[2].y()),
                        "p4": (cell.points[3].x(), cell.points[3].y()),
                    }

                # --- 处理 content 字段 ---
                # 目标格式要求 content 是详细的 OCR 结果列表。
                # TableCellShape 中只有 get_cell_text() 方法返回一个字符串。
                # 这里我们做一个兼容性处理：如果 cell 对象上存在一个叫 `ocr_results` 的
                # 详细列表属性，就使用它；否则，我们将简单文本包装成目标格式。
                if hasattr(cell, 'ocr_results') and isinstance(cell.ocr_results, list):
                    content = cell.ocr_results
                else:
                    # 将简单文本包装成目标格式，其他字段留空
                    content = [{
                        "bbox": None,
                        "direction": None,
                        "text": cell.get_cell_text(),
                        "score": None
                    }]

                # --- 组装单个单元格数据 ---
                cell_data = {
                    'cell_ind': j,
                    'header': cell.get_header(),  # 直接调用您提供的方法
                    'content': content,
                    'bbox': formatted_bbox,
                    'lloc': cell.get_logical_location(),  # 直接调用您提供的方法
                    'border': {
                        'style': cell.get_border_style(),  # 直接调用您提供的方法
                        'width': None,  # 遵从要求 "暂无规划"
                        'color': None,  # 遵从要求 "暂无规划"
                    }
                }
                table_data['cells'].append(cell_data)

            exported_data.append(table_data)

        if len(exported_data) == 1:
            LOGGER.debug("成功导出单个表格数据")
            return exported_data[0]  # 返回单个dict
        else:
            LOGGER.debug(f"成功导出 {len(exported_data)} 个表格的数据")
            return exported_data  # 返回数组
    
    def get_table_count(self) -> int:
        """获取表格数量"""
        return len(self.table_controllers)
    
    def get_total_cell_count(self) -> int:
        """获取所有表格的单元格总数"""
        total_count = 0
        for controller in self.table_controllers.values():
            total_count += len(controller.get_table_cells())
        return total_count
    
    def clear_all_tables(self):
        """清除所有表格"""
        table_ids = list(self.table_controllers.keys())
        for table_id in table_ids:
            self.remove_table_instance(table_id)
        
        LOGGER.debug("清除所有表格")
    
    def get_status_info(self) -> Dict[str, Any]:
        """获取状态信息"""
        total_cells = sum(
            len(controller.get_table_cells()) 
            for controller in self.table_controllers.values()
        )
        
        return {
            "total_tables": len(self.table_controllers),
            "active_table_id": self.active_table_id,
            "total_cells": total_cells,
            "table_regions": self.instance_manager.get_all_regions()
        }

    def _sync_from_canvas_shapes(self):
        """🆕 从Canvas的shapes同步重建表格控制器状态

        当执行撤销操作后，Canvas的shapes会被恢复，但MultiTableController
        的状态可能不同步，此方法用于重新同步
        """
        try:
            # 🔍 调试：打印调用栈
            import traceback
            LOGGER.warning(f"⚠️ _sync_from_canvas_shapes被调用！")
            LOGGER.warning(f"📚 调用栈:")
            for line in traceback.format_stack()[-5:]:  # 显示最近5层调用栈
                LOGGER.warning(f"    {line.strip()}")
            
            LOGGER.info(f"🔍 开始从Canvas同步状态:")

            # 获取Canvas中的所有表格单元格
            table_cells = [
                shape for shape in self.canvas.shapes
                if is_table_cell(shape)
            ]
            LOGGER.info(f"  - Canvas中表格单元格数量: {len(table_cells)}")
            LOGGER.info(f"  - Canvas单元格对象ID: {[id(cell) for cell in table_cells[:3]]}")

            if not table_cells:
                # 如果没有表格单元格，清空所有控制器
                self.reset_all_controllers()
                LOGGER.info("撤销后无表格单元格，已清空控制器")
                return
            LOGGER.info(f"  - 重置所有控制器")

            # 清空现有控制器
            self.reset_all_controllers()

            # 重新导入表格数据
            import_stats = self.import_table_data(table_cells)

            LOGGER.info(f"撤销后重新同步: {len(import_stats)}个表格, {sum(import_stats.values())}个单元格")

            # 刷新结构视图
            if hasattr(self, '_refresh_all_structure_views'):
                self._refresh_all_structure_views()

        except Exception as e:
            LOGGER.error(f"撤销后同步失败: {e}")
            # 失败时至少清空控制器避免状态不一致
            self.reset_all_controllers()


# ===== 工具函数 =====

def create_multi_table_controller_for_canvas(canvas) -> MultiTableController:
    """为Canvas创建多表格控制器的便捷函数"""
    controller = MultiTableController(canvas)
    canvas.multi_table_controller = controller
    return controller 