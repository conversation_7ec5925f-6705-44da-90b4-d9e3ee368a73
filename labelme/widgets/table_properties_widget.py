#!/usr/bin/env python
# -*- coding: utf-8 -*-

from PyQt5 import QtCore
from PyQt5 import QtWidgets
from labelme.utils.log import get_logger

LOGGER = get_logger()


class BorderStyleWidget(QtWidgets.QWidget):
    """边框样式编辑子组件"""

    borderChanged = QtCore.pyqtSignal()  # 边框变更信号（保持兼容性）
    borderToggled = QtCore.pyqtSignal(str, int)  # 🆕 具体边框切换信号 (side, value)

    def __init__(self, parent=None):
        super(BorderStyleWidget, self).__init__(parent)
        self.border_buttons = {}
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置边框样式界面"""
        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(0, 0, 0, 0)
        
        # 边框样式标题
        border_label = QtWidgets.QLabel("边框线条控制:")
        layout.addWidget(border_label)
        
        # 四个边框复选框，水平一行排列
        border_layout = QtWidgets.QHBoxLayout()
        self.border_buttons["top"] = QtWidgets.QCheckBox("上")
        self.border_buttons["right"] = QtWidgets.QCheckBox("右")
        self.border_buttons["bottom"] = QtWidgets.QCheckBox("下")
        self.border_buttons["left"] = QtWidgets.QCheckBox("左")
        
        # 水平排列：上-右-下-左
        border_layout.addWidget(self.border_buttons["top"])
        border_layout.addWidget(self.border_buttons["left"])
        border_layout.addWidget(self.border_buttons["bottom"])
        border_layout.addWidget(self.border_buttons["right"])

        border_layout.addStretch()  # 添加弹性空间
        
        layout.addLayout(border_layout)
        self.setLayout(layout)
    
    def _connect_signals(self):
        """连接信号"""
        # 🔧 新的信号连接方式：发送具体的边框操作
        for side, checkbox in self.border_buttons.items():
            # 使用lambda捕获side变量
            checkbox.toggled.connect(lambda checked, s=side: self._on_border_toggled(s, checked))

        # 保持旧的信号以维持兼容性
        for checkbox in self.border_buttons.values():
            checkbox.toggled.connect(self.borderChanged.emit)

    def _on_border_toggled(self, side, checked):
        """处理单个边框切换"""
        value = 1 if checked else 0
        LOGGER.debug(f"🔧 边框切换: {side} = {value}")
        self.borderToggled.emit(side, value)
    
    def set_border_style(self, border_style):
        """设置边框样式"""
        sides = ["top", "right", "bottom", "left"]
        for side in sides:
            if side in border_style:
                self.border_buttons[side].setChecked(border_style[side] == 1)

        # 🆕 更新上次状态，避免在程序设置时触发变化检测
        self._last_border_state = self.get_border_style()
    
    def get_border_style(self):
        """获取边框样式"""
        return {
            "top": 1 if self.border_buttons["top"].isChecked() else 0,
            "right": 1 if self.border_buttons["right"].isChecked() else 0,
            "bottom": 1 if self.border_buttons["bottom"].isChecked() else 0,
            "left": 1 if self.border_buttons["left"].isChecked() else 0
        }

    def get_changed_border_style(self):
        """获取变化的边框样式（只返回与上次状态不同的边框）"""
        current_style = self.get_border_style()
        changed_style = {}

        for side, value in current_style.items():
            if side not in self._last_border_state or self._last_border_state[side] != value:
                changed_style[side] = value

        # 更新上次状态
        self._last_border_state = current_style.copy()

        return changed_style
    
    def toggle_border(self, side, enabled):
        """切换边框状态"""
        if side in self.border_buttons:
            self.border_buttons[side].setChecked(enabled)
    
    def set_mixed_state(self, side, is_mixed):
        """设置混合状态（批量编辑时使用）"""
        if side in self.border_buttons:
            if is_mixed:
                self.border_buttons[side].setCheckState(QtCore.Qt.PartiallyChecked)


class LogicalLocationWidget(QtWidgets.QWidget):
    """逻辑位置编辑子组件 - 紧凑一行设计"""
    
    locationChanged = QtCore.pyqtSignal()  # 位置变更信号
    
    def __init__(self, parent=None):
        super(LogicalLocationWidget, self).__init__(parent)
        self.spin_boxes = {}
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置逻辑位置界面 - 紧凑一行设计"""
        main_layout = QtWidgets.QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(5)
        
        # 🆕 紧凑的一行设计 - 行列信息
        location_layout = QtWidgets.QHBoxLayout()
        location_layout.setSpacing(5)
        
        # 行范围输入
        location_layout.addWidget(QtWidgets.QLabel("行:"))
        self.spin_boxes["start_row"] = QtWidgets.QSpinBox()
        self.spin_boxes["start_row"].setMinimum(0)
        self.spin_boxes["start_row"].setMaximum(999)
        self.spin_boxes["start_row"].setFixedWidth(50)
        location_layout.addWidget(self.spin_boxes["start_row"])
        
        location_layout.addWidget(QtWidgets.QLabel("-"))
        self.spin_boxes["end_row"] = QtWidgets.QSpinBox()
        self.spin_boxes["end_row"].setMinimum(0)
        self.spin_boxes["end_row"].setMaximum(999)
        self.spin_boxes["end_row"].setFixedWidth(50)
        location_layout.addWidget(self.spin_boxes["end_row"])
        
        # 分隔符
        location_layout.addWidget(QtWidgets.QLabel(" | "))
        
        # 列范围输入
        location_layout.addWidget(QtWidgets.QLabel("列:"))
        self.spin_boxes["start_col"] = QtWidgets.QSpinBox()
        self.spin_boxes["start_col"].setMinimum(0)
        self.spin_boxes["start_col"].setMaximum(999)
        self.spin_boxes["start_col"].setFixedWidth(50)
        location_layout.addWidget(self.spin_boxes["start_col"])
        
        location_layout.addWidget(QtWidgets.QLabel("-"))
        self.spin_boxes["end_col"] = QtWidgets.QSpinBox()
        self.spin_boxes["end_col"].setMinimum(0)
        self.spin_boxes["end_col"].setMaximum(999)
        self.spin_boxes["end_col"].setFixedWidth(50)
        location_layout.addWidget(self.spin_boxes["end_col"])
        
        # 左对齐，右侧留空
        location_layout.addStretch()
        
        main_layout.addLayout(location_layout)
        
        self.setLayout(main_layout)
    
    def _connect_signals(self):
        """连接信号"""
        for spinbox in self.spin_boxes.values():
            spinbox.valueChanged.connect(self._on_value_changed)

    def _on_value_changed(self):
        """数值变更处理"""
        if self.validate_location():
            self.locationChanged.emit()
    
    def set_location(self, location):
        """设置逻辑位置"""
        self.spin_boxes["start_row"].setValue(location.get("start_row", 0))
        self.spin_boxes["end_row"].setValue(location.get("end_row", 0))
        self.spin_boxes["start_col"].setValue(location.get("start_col", 0))
        self.spin_boxes["end_col"].setValue(location.get("end_col", 0))
    
    def get_location(self):
        """获取逻辑位置"""
        return {
            "start_row": self.spin_boxes["start_row"].value(),
            "end_row": self.spin_boxes["end_row"].value(),
            "start_col": self.spin_boxes["start_col"].value(),
            "end_col": self.spin_boxes["end_col"].value()
        }
    
    def validate_location(self):
        """验证位置合法性"""
        location = self.get_location()
        
        # 检查行范围合法性
        if location["start_row"] > location["end_row"]:
            return False
        
        # 检查列范围合法性  
        if location["start_col"] > location["end_col"]:
            return False
        
        return True


class LogicalStructureEditWidget(QtWidgets.QWidget):
    """🆕 逻辑结构编辑组件 - 包含位置输入和合并/拆分操作"""
    
    # 信号定义
    locationChanged = QtCore.pyqtSignal()
    merge_cells_requested = QtCore.pyqtSignal()
    split_cells_requested = QtCore.pyqtSignal()
    
    def __init__(self, parent=None):
        super(LogicalStructureEditWidget, self).__init__(parent)
        self.selected_cells = []  # 当前选中的单元格
        self._setup_ui()
        self._connect_signals()
    
    def _setup_ui(self):
        """设置UI布局"""
        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(8)
        
        # 逻辑位置组件
        self.logical_location_widget = LogicalLocationWidget()
        layout.addWidget(self.logical_location_widget)
        
        # 🆕 合并/拆分操作按钮 - 横向布局
        button_layout = QtWidgets.QHBoxLayout()
        button_layout.setSpacing(5)
        
        self.btn_merge_selected = QtWidgets.QPushButton("合并选中")
        self.btn_split_selected = QtWidgets.QPushButton("拆分选中")
        
        # 设置按钮样式和工具提示
        self.btn_merge_selected.setToolTip("合并选中的相邻单元格为一个合并单元格")
        self.btn_split_selected.setToolTip("将选中的合并单元格拆分为普通单元格")
        self.btn_merge_selected.setEnabled(False)  # 默认禁用
        self.btn_split_selected.setEnabled(False)  # 默认禁用
        
        button_layout.addWidget(self.btn_merge_selected)
        button_layout.addWidget(self.btn_split_selected)
        
        layout.addLayout(button_layout)
        
        self.setLayout(layout)
    
    def _connect_signals(self):
        """连接信号槽"""
        # 位置变更信号
        self.logical_location_widget.locationChanged.connect(self.locationChanged.emit)
        
        # 按钮点击信号
        self.btn_merge_selected.clicked.connect(self._on_merge_clicked)
        self.btn_split_selected.clicked.connect(self._on_split_clicked)
    
    def _on_merge_clicked(self):
        """处理合并按钮点击"""
        if len(self.selected_cells) < 2:
            QtWidgets.QMessageBox.warning(self, "合并单元格", "请选择至少2个单元格进行合并")
            return
        
        # 发送合并信号
        self.merge_cells_requested.emit()
        LOGGER.debug(f"属性面板发送合并请求：{len(self.selected_cells)}个单元格")
    
    def _on_split_clicked(self):
        """处理拆分按钮点击"""
        if len(self.selected_cells) != 1:
            QtWidgets.QMessageBox.warning(self, "拆分单元格", "请选择一个合并单元格进行拆分")
            return
        
        # 发送拆分信号
        self.split_cells_requested.emit()
        LOGGER.debug("属性面板发送拆分请求")
    
    def update_from_selection(self, selected_cells):
        """根据选中的单元格更新按钮状态"""
        self.selected_cells = selected_cells
        
        if not selected_cells:
            # 没有选中
            self.btn_merge_selected.setEnabled(False)
            self.btn_merge_selected.setText("合并选中")
            self.btn_split_selected.setEnabled(False)
            self.btn_split_selected.setText("拆分选中")
        elif len(selected_cells) == 1:
            # 单选：检查是否是合并单元格
            cell = selected_cells[0]
            is_merged = self._is_merged_cell(cell)
            
            self.btn_merge_selected.setEnabled(False)
            self.btn_merge_selected.setText("合并选中")
            self.btn_split_selected.setEnabled(is_merged)
            
            if is_merged:
                self.btn_split_selected.setText("拆分合并单元格")
            else:
                self.btn_split_selected.setText("非合并单元格")
        else:
            # 多选：检查是否可以合并
            can_merge = self._can_merge_cells(selected_cells)
            
            self.btn_merge_selected.setEnabled(can_merge)
            self.btn_split_selected.setEnabled(False)
            self.btn_split_selected.setText("拆分选中")
            
            if can_merge:
                self.btn_merge_selected.setText(f"合并{len(selected_cells)}个单元格")
            else:
                self.btn_merge_selected.setText("无法合并")
    
    def _is_merged_cell(self, cell):
        """检查是否是合并单元格"""
        if hasattr(cell, 'get_logical_location'):
            lloc = cell.get_logical_location()
            return (lloc.get('start_row') != lloc.get('end_row') or 
                   lloc.get('start_col') != lloc.get('end_col'))
        return False
    
    def _can_merge_cells(self, cells):
        """检查单元格是否可以合并（简化版检查）"""
        if len(cells) < 2:
            return False
        
        # 简化检查：只要有多个单元格就允许合并
        # 具体的相邻性检查由TableController负责
        return True
    
    # 位置相关方法代理
    def set_location(self, location):
        """设置逻辑位置"""
        self.logical_location_widget.set_location(location)
    
    def get_location(self):
        """获取逻辑位置"""
        return self.logical_location_widget.get_location()
    
    def validate_location(self):
        """验证位置合法性"""
        return self.logical_location_widget.validate_location()


class TablePropertiesWidget(QtWidgets.QWidget):
    """表格属性编辑界面 - 简化版本
    
    核心功能：
    1. 表格类型设置
    2. 快速样式设置（全边框、无边框、仅外边框、设置表头、清除表头）
    3. 边框样式编辑
    4. 逻辑位置设置
    5. 单元格内容编辑
    """
    
    # 🔗 TableController期望的信号：property_changed(property_name: str, property_value: Any)
    property_changed = QtCore.pyqtSignal(str, object)

    def __init__(self, parent=None):
        super(TablePropertiesWidget, self).__init__(parent)
        
        # 初始化控制器和选中单元格
        self.controller = None
        self.selected_cells = []
        
        # 信号阻塞标记（防止循环更新）
        self._updating_from_selection = False

        # 🆕 用户偏好设置（在图片切换时保持）
        self._user_auto_determine_preference = True  # 默认启用自动推导

        self._setup_ui()
        self._connect_signals()

        # 初始化表格类型按钮状态
        self._update_table_type_button_state(1)  # 默认选择有线表格

    def _setup_ui(self):
        """设置界面布局"""
        layout = QtWidgets.QVBoxLayout(self)
        
        # 🆕 批量编辑状态提示
        self.batch_edit_label = QtWidgets.QLabel()
        self.batch_edit_label.setStyleSheet("color: #2E86AB; font-weight: bold; padding: 5px;")
        self.batch_edit_label.hide()  # 默认隐藏
        layout.addWidget(self.batch_edit_label)

        # 🔧 原有的表格类型已删除，新的表格类型选择在下方

        # 🆕 快速样式设置（合并表头设置和批量操作）
        quick_style_group = QtWidgets.QGroupBox("快速样式设置")
        quick_style_layout = QtWidgets.QVBoxLayout(quick_style_group)
        
        # 第一行：表头样式快捷设置
        header_quick_layout = QtWidgets.QHBoxLayout()
        self.btn_set_header = QtWidgets.QPushButton("设置表头")
        self.btn_clear_header = QtWidgets.QPushButton("清除表头")
        
        self.btn_set_header.setToolTip("将选中单元格标记为表头，在逻辑视图中以浅蓝色背景显示")
        self.btn_clear_header.setToolTip("清除选中单元格的表头标记")
        
        header_quick_layout.addWidget(self.btn_set_header)
        header_quick_layout.addWidget(self.btn_clear_header)
        
        quick_style_layout.addLayout(header_quick_layout)
        
        # 第二行：边框样式快捷设置
        border_quick_layout = QtWidgets.QHBoxLayout()
        self.btn_full_borders = QtWidgets.QPushButton("全边框")
        self.btn_no_borders = QtWidgets.QPushButton("无边框")
        self.btn_outer_borders = QtWidgets.QPushButton("仅外边框")
        
        self.btn_full_borders.setToolTip("为选中单元格设置全边框")
        self.btn_no_borders.setToolTip("清除选中单元格的所有边框")
        self.btn_outer_borders.setToolTip("仅保留选中区域的外边框")
        
        border_quick_layout.addWidget(self.btn_full_borders)
        border_quick_layout.addWidget(self.btn_no_borders)
        border_quick_layout.addWidget(self.btn_outer_borders)
        
        quick_style_layout.addLayout(border_quick_layout)
        
        layout.addWidget(quick_style_group)

        # 边框样式
        border_group = QtWidgets.QGroupBox("边框样式")
        border_layout = QtWidgets.QVBoxLayout(border_group)
        
        self.border_style_widget = BorderStyleWidget()
        border_layout.addWidget(self.border_style_widget)
        
        layout.addWidget(border_group)

        # 🆕 逻辑结构编辑（包含位置和合并/拆分操作）
        location_group = QtWidgets.QGroupBox("逻辑结构编辑")
        location_layout = QtWidgets.QVBoxLayout(location_group)
        
        self.logical_structure_edit_widget = LogicalStructureEditWidget()
        location_layout.addWidget(self.logical_structure_edit_widget)
        
        layout.addWidget(location_group)

        # 单元格内容
        content_group = QtWidgets.QGroupBox("单元格内容")
        content_layout = QtWidgets.QVBoxLayout(content_group)

        self.content_text = QtWidgets.QTextEdit()
        self.content_text.setMaximumHeight(80)
        self.content_text.setPlaceholderText("输入单元格内容...")
        content_layout.addWidget(self.content_text)

        layout.addWidget(content_group)

        # ===== 表格类型选择组 =====
        table_type_group = QtWidgets.QGroupBox("表格类型选择")
        table_type_layout = QtWidgets.QVBoxLayout(table_type_group)  # 改为垂直布局以容纳复选框

        # 🆕 自动推导复选框（放在类型选择上方）
        self.auto_determine_checkbox = QtWidgets.QCheckBox("保存时自动推导表格类型")
        self.auto_determine_checkbox.setChecked(True)  # 默认启用
        self.auto_determine_checkbox.setToolTip("启用后，保存时会根据单元格边框状态自动判断表格类型")
        table_type_layout.addWidget(self.auto_determine_checkbox)

        # 类型选择行
        type_row_layout = QtWidgets.QHBoxLayout()
        type_label = QtWidgets.QLabel("类型:")
        type_row_layout.addWidget(type_label)

        # 创建三个按钮代替下拉框，使用与快速样本标注相同的样式
        self.btn_type_text = QtWidgets.QPushButton("文本")
        self.btn_type_lined = QtWidgets.QPushButton("有线表")
        self.btn_type_unlined = QtWidgets.QPushButton("无线表")

        # 设置按钮固定宽度和样式
        fixed_width = 80
        for btn in [self.btn_type_text, self.btn_type_lined, self.btn_type_unlined]:
            btn.setFixedWidth(fixed_width)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 5px;
                    border-radius: 4px;
                    font-weight: bold;
                    border: none;
                }
            """)

        # 设置按钮颜色
        self.btn_type_text.setStyleSheet(self.btn_type_text.styleSheet() +
            "QPushButton { background-color: #e3f2fd; color: #1976d2; }")
        self.btn_type_lined.setStyleSheet(self.btn_type_lined.styleSheet() +
            "QPushButton { background-color: #e8f5e8; color: #2e7d32; }")
        self.btn_type_unlined.setStyleSheet(self.btn_type_unlined.styleSheet() +
            "QPushButton { background-color: #fff3e0; color: #f57c00; }")

        # 添加按钮到类型选择行
        type_row_layout.addWidget(self.btn_type_text)
        type_row_layout.addWidget(self.btn_type_lined)
        type_row_layout.addWidget(self.btn_type_unlined)
        type_row_layout.addStretch()  # 添加弹性空间

        # 将类型选择行添加到主布局
        table_type_layout.addLayout(type_row_layout)

        layout.addWidget(table_type_group)

        # 快速样本标注
        quality_group = QtWidgets.QGroupBox("快速样本标注")
        quality_layout = QtWidgets.QHBoxLayout(quality_group)

        quality_label = QtWidgets.QLabel("样本质量:")
        quality_layout.addWidget(quality_label)

        # 创建四个按钮代替下拉框
        self.btn_quality_good = QtWidgets.QPushButton("合格")
        self.btn_quality_quasi = QtWidgets.QPushButton("准合格")
        self.btn_quality_bad = QtWidgets.QPushButton("不合格")
        self.btn_quality_pending = QtWidgets.QPushButton("待校准")

        # 设置按钮固定宽度和样式
        for btn in [self.btn_quality_good, self.btn_quality_quasi, self.btn_quality_bad, self.btn_quality_pending]:
            btn.setFixedWidth(fixed_width)
            btn.setStyleSheet("""
                QPushButton {
                    padding: 5px;
                    border-radius: 4px;
                    font-weight: bold;
                    border: none;
                }
            """)

        # 设置各按钮的基础颜色
        self.btn_quality_good.setStyleSheet(self.btn_quality_good.styleSheet() + """
            QPushButton {
                background-color: #c8e6c9;
                color: #2e7d32;
            }
        """)
        self.btn_quality_quasi.setStyleSheet(self.btn_quality_quasi.styleSheet() + """
            QPushButton {
                background-color: #e1f5fe;
                color: #0277bd;
            }
        """)
        self.btn_quality_bad.setStyleSheet(self.btn_quality_bad.styleSheet() + """
            QPushButton {
                background-color: #ffcdd2;
                color: #c62828;
            }
        """)
        self.btn_quality_pending.setStyleSheet(self.btn_quality_pending.styleSheet() + """
            QPushButton {
                background-color: #fff9c4;
                color: #f57f17;
            }
        """)

        # 添加按钮到布局
        quality_layout.addWidget(self.btn_quality_good)
        quality_layout.addWidget(self.btn_quality_quasi)
        quality_layout.addWidget(self.btn_quality_bad)
        quality_layout.addWidget(self.btn_quality_pending)
        quality_layout.addStretch()  # 添加弹性空间

        layout.addWidget(quality_group)

        # 保留隐藏的combobox以兼容现有代码
        self.quality_combo = QtWidgets.QComboBox()
        self.quality_combo.addItems(["合格", "准合格", "不合格", "待校准"])
        self.quality_combo.setCurrentIndex(3)  # 默认为"待校准"
        self.quality_combo.hide()  # 隐藏下拉框
        
        # 🆕 导入AI提示组件 - 移至底部
        from .ai_prompt_widget import AIPromptWidget
        self.ai_prompt_widget = AIPromptWidget()
        layout.addWidget(self.ai_prompt_widget)

    def _connect_signals(self):
        """连接所有控件信号 - 发送给TableController"""
        # 表格类型变更信号
        self.btn_type_text.clicked.connect(lambda: self._on_type_changed(0))
        self.btn_type_lined.clicked.connect(lambda: self._on_type_changed(1))
        self.btn_type_unlined.clicked.connect(lambda: self._on_type_changed(2))

        # 🆕 自动推导复选框变更信号
        self.auto_determine_checkbox.toggled.connect(self._on_auto_determine_changed)

        # 样本质量变更信号
        self.btn_quality_good.clicked.connect(lambda: self._on_quality_changed("合格"))
        self.btn_quality_quasi.clicked.connect(lambda: self._on_quality_changed("准合格"))
        self.btn_quality_bad.clicked.connect(lambda: self._on_quality_changed("不合格"))
        self.btn_quality_pending.clicked.connect(lambda: self._on_quality_changed("待校准"))
        
        # 边框样式变更
        self.border_style_widget.borderChanged.connect(self._on_border_changed)
        # 🆕 具体边框切换信号
        self.border_style_widget.borderToggled.connect(self._on_border_toggled)
        
        # 逻辑位置变更
        self.logical_structure_edit_widget.locationChanged.connect(self._on_position_changed)
        
        # 🆕 合并/拆分信号连接
        self.logical_structure_edit_widget.merge_cells_requested.connect(self._on_merge_requested)
        self.logical_structure_edit_widget.split_cells_requested.connect(self._on_split_requested)
        
        # 内容变更
        self.content_text.textChanged.connect(self._on_content_changed)
        
        # 🆕 快速样式设置按钮
        self.btn_full_borders.clicked.connect(self._on_set_full_borders)
        self.btn_no_borders.clicked.connect(self._on_clear_all_borders)
        self.btn_outer_borders.clicked.connect(self._on_set_outer_borders)
        self.btn_set_header.clicked.connect(self._on_set_headers)
        self.btn_clear_header.clicked.connect(self._on_clear_headers)
        
        # 🆕 API辅助打标信号连接
        self.ai_prompt_widget.api_labeling_requested.connect(self._on_api_labeling_requested)

    def bind_controller(self, controller):
        """绑定TableController，实现双向数据同步"""
        self.controller = controller
        controller.bind_properties_widget(self)

        # 🆕 双向同步自动推导复选框状态
        if hasattr(controller, 'get_auto_determine_enabled') and hasattr(controller, 'set_auto_determine_enabled'):
            self._updating_from_selection = True
            try:
                # 🔧 修复：优先使用用户偏好设置，而不是控制器默认状态
                user_preference = self._user_auto_determine_preference
                controller_state = controller.get_auto_determine_enabled()

                # 将用户偏好应用到新控制器
                if controller_state != user_preference:
                    controller.set_auto_determine_enabled(user_preference)
                    LOGGER.debug(f"应用用户偏好到新控制器: {user_preference}")

                # 同步UI状态
                self.auto_determine_checkbox.setChecked(user_preference)
                LOGGER.debug(f"自动推导状态已同步: {user_preference}")
            finally:
                self._updating_from_selection = False

        LOGGER.debug("属性面板已绑定到TableController")

    def update_from_selection(self, selected_cells):
        """根据选中的单元格更新界面（从TableController调用）"""
        # 🔗 阻塞信号，防止循环更新
        self._updating_from_selection = True
        
        try:
            self.selected_cells = selected_cells
            
            if not selected_cells:
                self._clear_display()
                return
            
            if len(selected_cells) == 1:
                # 单选模式
                self._update_single_selection(selected_cells[0])
                self._set_batch_mode(False)
            else:
                # 多选模式
                self._update_multiple_selection(selected_cells)
                self._set_batch_mode(True, len(selected_cells))
                
        finally:
            # 🔗 恢复信号
            self._updating_from_selection = False

    def _clear_display(self):
        """清空显示"""
        # 🔧 修复：保持当前控制器的表格类型，不强制重置为有线表格
        if self.controller and hasattr(self.controller, 'get_table_type'):
            # 如果有控制器，显示控制器的表格类型
            current_table_type = self.controller.get_table_type()
            self._update_table_type_button_state(current_table_type)
        else:
            # 如果没有控制器，使用默认显示（有线表格）
            self._update_table_type_button_state(1)

        self.border_style_widget.set_border_style({"top": 0, "right": 0, "bottom": 0, "left": 0})
        self.logical_structure_edit_widget.set_location({"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0})
        # 🆕 清空时也更新按钮状态
        self.logical_structure_edit_widget.update_from_selection([])
        self.content_text.setPlainText("")
        self._set_batch_mode(False)

        # 🔧 修复：保持用户偏好设置，不强制重置
        if self.controller and hasattr(self.controller, 'get_auto_determine_enabled'):
            # 如果有控制器，同步控制器的状态
            self.auto_determine_checkbox.setChecked(self.controller.get_auto_determine_enabled())
        else:
            # 🆕 如果没有控制器，使用用户偏好（而不是强制设为True）
            self.auto_determine_checkbox.setChecked(self._user_auto_determine_preference)

    def _update_single_selection(self, cell):
        """更新单选模式显示"""
        # 表格类型
        table_type = cell.get_table_type()
        self._update_table_type_button_state(table_type)

        # 边框样式
        border = cell.get_border_style()
        self.border_style_widget.set_border_style(border)

        # 逻辑位置
        lloc = cell.get_logical_location()
        self.logical_structure_edit_widget.set_location(lloc)

        # 🆕 更新合并/拆分按钮状态
        self.logical_structure_edit_widget.update_from_selection([cell])

        # 内容
        self.content_text.setPlainText(cell.get_cell_text())

    def _update_multiple_selection(self, cells):
        """更新多选模式显示"""
        # 分析共同属性
        common_props = self._analyze_common_properties(cells)
        
        # 显示共同属性，差异项显示为"混合"
        self._display_common_properties(common_props)
        
        # 🆕 更新合并/拆分按钮状态
        self.logical_structure_edit_widget.update_from_selection(cells)

    def _analyze_common_properties(self, cells):
        """分析多个单元格的共同属性"""
        if not cells:
            return {}
        
        # 分析表格类型
        types = set(cell.get_table_type() for cell in cells)
        common_type = list(types)[0] if len(types) == 1 else "mixed"
        
        # 🆕 分析表头状态
        headers = set(cell.get_header() for cell in cells)
        common_header = list(headers)[0] if len(headers) == 1 else "mixed"
        
        # 分析边框样式
        borders = [cell.get_border_style() for cell in cells]
        common_border = {}
        for side in ["top", "right", "bottom", "left"]:
            values = set(border[side] for border in borders)
            common_border[side] = list(values)[0] if len(values) == 1 else "mixed"
        
        # 分析文本内容
        texts = set(cell.get_cell_text() for cell in cells)
        common_text = list(texts)[0] if len(texts) == 1 else "mixed"
        
        return {
            "table_type": common_type,
            "header": common_header,
            "border": common_border,
            "cell_text": common_text
        }

    def _display_common_properties(self, common_props):
        """显示共同属性"""
        # 表格类型
        if common_props["table_type"] != "mixed":
            self._update_table_type_button_state(common_props["table_type"])
        else:
            # 多选混合时显示第一个选中项的类型
            self._update_table_type_button_state(0)
        
        # 🆕 表头状态 - 通过按钮提示显示，不需要控件
        # 表头功能现在通过快捷按钮实现，无需在此处处理
        
        # 边框样式
        border = common_props["border"]
        for side in ["top", "right", "bottom", "left"]:
            if border[side] != "mixed":
                self.border_style_widget.toggle_border(side, border[side] == 1)
            else:
                self.border_style_widget.set_mixed_state(side, True)
        
        # 内容 - 多选时显示为提示文本
        if common_props["cell_text"] != "mixed":
            self.content_text.setPlainText(common_props["cell_text"])
        else:
            self.content_text.setPlainText(f"[已选中 {len(self.selected_cells)} 个单元格]")

    def _set_batch_mode(self, is_batch, cell_count=0):
        """设置批量编辑模式提示"""
        if is_batch:
            self.batch_edit_label.setText(f"📋 批量编辑模式 - 已选中 {cell_count} 个单元格")
            self.batch_edit_label.show()
        else:
            self.batch_edit_label.hide()

    # ===== 事件处理方法 - 发送TableController期望的信号 =====

    def _on_quality_changed(self, quality):
        """样本质量变更处理 - 改为按钮点击触发，自动保存并切换到下一张"""
        if not self._updating_from_selection:
            # 更新隐藏的combobox值，保持一致性
            self.quality_combo.setCurrentText(quality)
            
            # 发送属性变更信号
            self.property_changed.emit("sample_quality", quality)
            LOGGER.debug(f"样本质量变更: {quality}")
            
            # 发送自动保存请求信号
            self.property_changed.emit("auto_save_and_next", quality)
            
            # 显示反馈消息
            self._show_feedback_message(f"✅ 已设置样本质量为【{quality}】并自动保存")

    def _on_type_changed(self, table_type):
        """表格类型变更处理"""
        if not self._updating_from_selection:
            # 更新按钮状态
            self._update_table_type_button_state(table_type)

            # 发送属性变更信号
            self.property_changed.emit("table_type", table_type)

            # 显示反馈消息
            type_names = ["纯文本", "有线表格", "无线表格"]
            type_name = type_names[table_type] if 0 <= table_type < len(type_names) else f"未知({table_type})"
            self._show_feedback_message(f"✅ 已设置表格类型为【{type_name}】")

    def _on_auto_determine_changed(self, enabled):
        """🆕 自动推导复选框变更处理"""
        if not self._updating_from_selection:
            # 🆕 保存用户偏好（在图片切换时保持）
            self._user_auto_determine_preference = enabled

            # 发送属性变更信号
            self.property_changed.emit("auto_determine_enabled", enabled)

            # 显示反馈消息
            status = "启用" if enabled else "禁用"
            self._show_feedback_message(f"✅ 已{status}保存时自动推导表格类型")

    def _update_table_type_button_state(self, table_type):
        """更新表格类型按钮状态"""
        # 🔧 优化：未选中的按钮显示为灰色，选中的按钮显示原色+光环

        # 灰色样式（未选中）
        gray_style = """
            QPushButton {
                background-color: #f5f5f5;
                color: #9e9e9e;
                padding: 5px;
                border-radius: 4px;
                font-weight: bold;
                border: none;
            }
        """

        # 重置所有按钮为灰色
        self.btn_type_text.setStyleSheet(gray_style)
        self.btn_type_lined.setStyleSheet(gray_style)
        self.btn_type_unlined.setStyleSheet(gray_style)

        # 选中按钮的样式和光环效果
        selected_style_with_glow = """
            QPushButton {
                background-color: %s;
                color: %s;
                padding: 5px;
                border-radius: 4px;
                font-weight: bold;
                border: 2px solid %s;
                box-shadow: 0 0 8px %s;
            }
        """

        # 根据选中的类型设置对应按钮的高亮样式
        if table_type == 0:  # 纯文本
            self.btn_type_text.setStyleSheet(
                selected_style_with_glow % ('#e3f2fd', '#1976d2', '#1976d2', '#1976d2')
            )
        elif table_type == 1:  # 有线表格
            self.btn_type_lined.setStyleSheet(
                selected_style_with_glow % ('#e8f5e8', '#2e7d32', '#2e7d32', '#2e7d32')
            )
        elif table_type == 2:  # 无线表格
            self.btn_type_unlined.setStyleSheet(
                selected_style_with_glow % ('#fff3e0', '#f57c00', '#f57c00', '#f57c00')
            )

    def _on_border_changed(self):
        """边框样式变更处理（保持兼容性，但优先使用新的切换信号）"""
        # 这个方法现在主要用于兼容性，实际的边框操作通过 _on_border_toggled 处理
        pass

    def _on_border_toggled(self, side, value):
        """处理具体的边框切换操作"""
        if not self._updating_from_selection:
            LOGGER.debug(f"🔧 边框切换操作: {side} = {value}")
            # 🆕 发送具体的边框操作信号
            self.property_changed.emit("border_toggle", {"side": side, "value": value})

    def _on_position_changed(self):
        """逻辑位置变更处理"""
        if not self._updating_from_selection:
            logical_location = self.logical_structure_edit_widget.get_location()
            self.property_changed.emit("logical_location", logical_location)

    def _on_content_changed(self):
        """内容变更处理"""
        if not self._updating_from_selection:
            cell_text = self.content_text.toPlainText()
            self.property_changed.emit("cell_text", cell_text)
    
    def _on_merge_requested(self):
        """🆕 处理合并请求信号"""
        LOGGER.debug("属性面板收到合并请求")
        self.property_changed.emit("merge_cells_requested", self.selected_cells)
    
    def _on_split_requested(self):
        """🆕 处理拆分请求信号"""  
        LOGGER.debug("属性面板收到拆分请求")
        self.property_changed.emit("split_cells_requested", self.selected_cells)
    
    def _on_api_labeling_requested(self, api_type):
        """🆕 处理API辅助打标请求信号"""
        LOGGER.debug(f"属性面板收到API辅助打标请求: {api_type}")
        self.property_changed.emit("api_labeling_requested", api_type)

    # ===== 🆕 批量操作事件处理 =====

    def _on_set_full_borders(self):
        """设置全边框"""
        if not self.controller:
            self._show_feedback_message("❌ 控制器未绑定", is_error=True)
            return
            
        if not self.selected_cells:
            self._show_feedback_message("❌ 没有选中的单元格", is_error=True)
            return
            
        try:
            success = self.controller.set_selected_cells_border_style(top=1, right=1, bottom=1, left=1)
            if success:
                # 更新界面显示
                self.border_style_widget.set_border_style({"top": 1, "right": 1, "bottom": 1, "left": 1})
                # 🆕 可视化反馈
                self._show_feedback_message(f"✅ 已为 {len(self.selected_cells)} 个单元格设置全边框")
            else:
                self._show_feedback_message("❌ 设置全边框失败", is_error=True)
        except Exception as e:
            LOGGER.error(f"设置全边框错误: {e}")
            self._show_feedback_message(f"❌ 设置全边框失败: {str(e)}", is_error=True)

    def _on_clear_all_borders(self):
        """清除所有边框"""
        if not self.controller:
            self._show_feedback_message("❌ 控制器未绑定", is_error=True)
            return
            
        if not self.selected_cells:
            self._show_feedback_message("❌ 没有选中的单元格", is_error=True)
            return
            
        try:
            success = self.controller.set_selected_cells_border_style(top=0, right=0, bottom=0, left=0)
            if success:
                # 更新界面显示
                self.border_style_widget.set_border_style({"top": 0, "right": 0, "bottom": 0, "left": 0})
                # 🆕 可视化反馈
                self._show_feedback_message(f"✅ 已清除 {len(self.selected_cells)} 个单元格的所有边框")
            else:
                self._show_feedback_message("❌ 清除边框失败", is_error=True)
        except Exception as e:
            LOGGER.error(f"清除边框错误: {e}")
            self._show_feedback_message(f"❌ 清除边框失败: {str(e)}", is_error=True)

    def _on_set_outer_borders(self):
        """设置仅外边框"""
        if not self.selected_cells:
            self._show_feedback_message("❌ 没有选中的单元格", is_error=True)
            return
            
        # TODO: 需要在TableController中实现智能外边框设置
        self._show_feedback_message("🚧 外边框功能开发中，敬请期待", is_error=False)

    def _on_set_headers(self):
        """设置表头"""
        if not self.selected_cells:
            self._show_feedback_message("❌ 没有选中的单元格", is_error=True)
            return
            
        try:
            # 统计操作结果
            success_count = 0
            for cell in self.selected_cells:
                cell.set_header(True)
                success_count += 1
            
            # 🆕 可视化反馈
            self._show_feedback_message(f"✅ 已将 {success_count} 个单元格设置为表头")
            
            # 更新Canvas显示
            if self.controller and hasattr(self.controller, 'canvas'):
                self.controller.canvas.update()
            
            # 🆕 刷新结构视图以显示表头效果
            self._refresh_structure_view()
                
        except Exception as e:
            LOGGER.error(f"设置表头错误: {e}")
            self._show_feedback_message(f"❌ 设置表头失败: {str(e)}", is_error=True)

    def _on_clear_headers(self):
        """清除表头"""
        if not self.selected_cells:
            self._show_feedback_message("❌ 没有选中的单元格", is_error=True)
            return
            
        try:
            # 统计操作结果
            success_count = 0
            for cell in self.selected_cells:
                cell.set_header(False)
                success_count += 1
            
            # 🆕 可视化反馈
            self._show_feedback_message(f"✅ 已清除 {success_count} 个单元格的表头标记")
            
            # 更新Canvas显示
            if self.controller and hasattr(self.controller, 'canvas'):
                self.controller.canvas.update()
            
            # 🆕 刷新结构视图以显示清除效果
            self._refresh_structure_view()
                
        except Exception as e:
            LOGGER.error(f"清除表头错误: {e}")
            self._show_feedback_message(f"❌ 清除表头失败: {str(e)}", is_error=True)

    # ===== 🆕 可视化反馈辅助方法 =====
    
    def _show_feedback_message(self, message, is_error=False):
        """显示操作反馈消息"""
        # 方法1：通过主窗口状态栏显示（优先）
        if self._try_show_statusbar_message(message):
            return
        
        # 方法2：通过临时弹框显示（备选）
        self._show_temporary_tooltip(message, is_error)
    
    def _try_show_statusbar_message(self, message):
        """尝试通过主窗口状态栏显示消息"""
        try:
            # 向上查找主窗口
            parent = self.parent()
            while parent:
                if hasattr(parent, 'statusBar') and callable(parent.statusBar):
                    status_bar = parent.statusBar()
                    if status_bar:
                        status_bar.showMessage(message, 3000)  # 显示3秒
                        LOGGER.debug(f"状态栏消息: {message}")
                        return True
                parent = parent.parent()
            return False
        except Exception as e:
            LOGGER.warning(f"状态栏显示失败: {e}")
            return False
    
    def _show_temporary_tooltip(self, message, is_error=False):
        """显示临时工具提示"""
        try:
            from PyQt5 import QtWidgets, QtCore
            
            # 创建临时标签
            tooltip = QtWidgets.QLabel(message)
            tooltip.setStyleSheet(
                f"background-color: {'#ffebee' if is_error else '#e8f5e8'}; "
                f"color: {'#d32f2f' if is_error else '#2e7d32'}; "
                f"border: 1px solid {'#f44336' if is_error else '#4caf50'}; "
                f"border-radius: 4px; padding: 8px; font-weight: bold;"
            )
            tooltip.setAlignment(QtCore.Qt.AlignCenter)
            tooltip.setWindowFlags(QtCore.Qt.ToolTip)
            
            # 显示在快速样式按钮区域
            if hasattr(self, 'btn_set_header'):
                pos = self.btn_set_header.mapToGlobal(self.btn_set_header.rect().center())
                tooltip.move(pos.x() - 100, pos.y() - 30)
                tooltip.show()
                
                # 3秒后自动隐藏
                QtCore.QTimer.singleShot(3000, tooltip.hide)
                
            LOGGER.debug(f"临时提示: {message}")
            
        except Exception as e:
            LOGGER.warning(f"临时提示显示失败: {e}")
            # 最后备选：只打印消息  
            LOGGER.debug(f"反馈消息: {message}")
    
    def _refresh_structure_view(self):
        """刷新逻辑结构视图以显示表头变化"""
        try:
            if self.controller and hasattr(self.controller, '_refresh_structure_widget'):
                self.controller._refresh_structure_widget()
                LOGGER.debug("🔄 已刷新逻辑结构视图")
        except Exception as e:
            LOGGER.debug(f"⚠️ 刷新逻辑结构视图失败: {e}")

    # ===== 🆕 API组件状态控制方法 =====
    
    def show_api_loading(self, message: str = "正在调用API，请稍候..."):
        """显示API调用加载状态"""
        if hasattr(self, 'ai_prompt_widget'):
            self.ai_prompt_widget.show_loading(message)
    
    def show_api_success(self, message: str = "API调用成功"):
        """显示API调用成功状态"""
        if hasattr(self, 'ai_prompt_widget'):
            self.ai_prompt_widget.show_success(message)
    
    def show_api_error(self, message: str = "API调用失败"):
        """显示API调用错误状态"""
        if hasattr(self, 'ai_prompt_widget'):
            self.ai_prompt_widget.show_error(message)
    
    def clear_api_status(self):
        """清除API状态显示"""
        if hasattr(self, 'ai_prompt_widget'):
            self.ai_prompt_widget.clear_status()

    # ===== 向后兼容的数据接口 =====

    def set_data(self, data):
        """设置数据到界面（向后兼容）"""
        self._updating_from_selection = True
        
        try:
            if "table_type" in data:
                self.table_type_combo.setCurrentIndex(data["table_type"])

            if "border" in data:
                border = data["border"]
                self.border_style_widget.set_border_style(border)

            if "logical_position" in data:
                pos = data["logical_position"]
                self.logical_structure_edit_widget.set_location(pos)

            if "cell_text" in data:
                self.content_text.setPlainText(data["cell_text"])
                
        finally:
            self._updating_from_selection = False

    def get_data(self):
        """从界面获取数据（向后兼容）"""
        return {
            "table_type": self.table_type_combo.currentIndex(),
            "border": self.border_style_widget.get_border_style(),
            "logical_position": self.logical_structure_edit_widget.get_location(),
            "cell_text": self.content_text.toPlainText(),
        }


def main():
    """独立测试 - 简化版"""
    import sys

    app = QtWidgets.QApplication(sys.argv)

    window = QtWidgets.QMainWindow()
    window.setWindowTitle("表格属性编辑器 - 简化版")
    window.setGeometry(100, 100, 400, 600)

    widget = TablePropertiesWidget()
    
    # 🔗 测试信号连接
    def on_property_changed(prop_name, prop_value):
        LOGGER.debug(f"🔄 属性变更信号: {prop_name} = {prop_value}")
    
    widget.property_changed.connect(on_property_changed)
    
    # 创建测试用的mock controller
    class MockController:
        def __init__(self):
            self.canvas = None
            
        def set_selected_cells_border_style(self, **kwargs):
            LOGGER.debug(f"🔧 Mock: set_selected_cells_border_style({kwargs})")
            return True
            
        def bind_properties_widget(self, widget):
            LOGGER.debug("🔧 Mock: bind_properties_widget()")
    
    # 创建mock单元格
    class MockTableCell:
        def __init__(self, name):
            self.name = name
            self._table_type = 1
            self._header = False
            self._border = {"top": 1, "right": 1, "bottom": 1, "left": 1}
            self._lloc = {"start_row": 0, "end_row": 0, "start_col": 0, "end_col": 0}
            self._text = f"Mock单元格{name}"
            
        def get_table_type(self):
            return self._table_type
            
        def set_table_type(self, value):
            self._table_type = value
            
        def get_header(self):
            return self._header
            
        def set_header(self, value):
            self._header = value
            LOGGER.debug(f"🔧 {self.name}.set_header({value})")
            
        def get_border_style(self):
            return self._border
            
        def set_border_style(self, **kwargs):
            self._border.update(kwargs)
            LOGGER.debug(f"🔧 {self.name}.set_border_style({kwargs})")
            
        def get_logical_location(self):
            return self._lloc
            
        def set_logical_location(self, start_row, end_row, start_col, end_col):
            self._lloc = {"start_row": start_row, "end_row": end_row, "start_col": start_col, "end_col": end_col}
            
        def get_cell_text(self):
            return self._text
            
        def set_cell_text(self, text):
            self._text = text
    
    # 绑定mock controller
    mock_controller = MockController()
    widget.bind_controller(mock_controller)
    
    # 创建测试数据：模拟选中多个单元格
    test_cells = [MockTableCell("A"), MockTableCell("B"), MockTableCell("C")]
    widget.update_from_selection(test_cells)
    
    window.setCentralWidget(widget)
    window.show()
    
    LOGGER.debug("=" * 50)
    LOGGER.debug("🎉 表格属性编辑器 - 简化版启动!")
    LOGGER.debug("=" * 50)
    LOGGER.debug("✅ 保留功能:")
    LOGGER.debug("  📊 表格类型设置")
    LOGGER.debug("  🏷️ 表头设置")
    LOGGER.debug("  🔲 边框样式")
    LOGGER.debug("  📍 逻辑位置")
    LOGGER.debug("  📝 单元格内容")
    LOGGER.debug("  🎛️ 批量操作")
    LOGGER.debug("=" * 50)

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()