#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/1/3 17:05
# <AUTHOR> <EMAIL>
# @FileName: test_table_controller.py

"""TableController单元测试"""

import unittest
import sys
from unittest.mock import Mock, MagicMock
from PyQt5 import QtCore, QtWidgets

# 添加labelme路径
sys.path.insert(0, '..')

import os
# 正确添加labelme路径 - 修复导入问题
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)  # 从widgets目录上升到labelme目录
sys.path.insert(0, parent_dir)

# 使用正确的导入路径
from labelme.widgets.table_controller import TableController
from labelme.table_shape import TableCellShape, create_table_cell_from_rect
class TestTableController(unittest.TestCase):
    """TableController单元测试类"""
    
    def setUp(self):
        """每个测试前的准备工作"""
        # 创建QApplication（Qt测试必需）
        if not QtWidgets.QApplication.instance():
            self.app = QtWidgets.QApplication([])
        
        # 创建Mock Canvas
        self.mock_canvas = Mock()
        self.mock_canvas.shapes = []
        self.mock_canvas.selectedShapes = []
        self.mock_canvas.storeShapes = Mock()
        self.mock_canvas.update = Mock()
        
        # 创建TableController实例
        self.controller = TableController(self.mock_canvas)
    
    def test_align_selected_cells_top(self):
        """测试顶端对齐算法"""
        # Table-Driven Test cases
        test_cases = [
            # (单元格坐标列表, 期望的顶端Y坐标)
            ([(10, 20, 50, 60), (15, 30, 55, 70)], 20),  # 最小Y=20
            ([(0, 100, 40, 140), (20, 50, 60, 90), (10, 80, 50, 120)], 50),  # 最小Y=50
            ([(100, 200, 150, 250), (80, 180, 120, 220)], 180),  # 最小Y=180
        ]
        
        for cell_coords, expected_top_y in test_cases:
            with self.subTest(cell_coords=cell_coords):
                # 创建测试单元格
                cells = []
                for x1, y1, x2, y2 in cell_coords:
                    cell = create_table_cell_from_rect(x1, y1, x2, y2)
                    cell.selected = True
                    cells.append(cell)
                
                # 设置选中的单元格
                self.controller.highlighted_cells = cells
                self.mock_canvas.selectedShapes = cells
                
                # 执行对齐
                result = self.controller.align_selected_cells_top()
                
                # 验证结果
                self.assertTrue(result, "对齐操作应该成功")
                
                # 验证所有单元格的顶端Y坐标都对齐到期望值
                for cell in cells:
                    top_y = min(p.y() for p in cell.points)
                    self.assertAlmostEqual(top_y, expected_top_y, places=1,
                                         msg=f"单元格顶端应该对齐到Y={expected_top_y}")
    
    def test_align_selected_cells_left(self):
        """测试左对齐算法"""
        # Table-Driven Test cases
        test_cases = [
            # (单元格坐标列表, 期望的左边X坐标)
            ([(20, 10, 60, 50), (30, 15, 70, 55)], 20),  # 最小X=20
            ([(100, 0, 140, 40), (50, 20, 90, 60), (80, 10, 120, 50)], 50),  # 最小X=50
        ]
        
        for cell_coords, expected_left_x in test_cases:
            with self.subTest(cell_coords=cell_coords):
                # 创建测试单元格
                cells = []
                for x1, y1, x2, y2 in cell_coords:
                    cell = create_table_cell_from_rect(x1, y1, x2, y2)
                    cell.selected = True
                    cells.append(cell)
                
                # 设置选中的单元格
                self.controller.highlighted_cells = cells
                self.mock_canvas.selectedShapes = cells
                
                # 执行对齐
                result = self.controller.align_selected_cells_left()
                
                # 验证结果
                self.assertTrue(result)
                
                # 验证所有单元格的左边X坐标都对齐到期望值
                for cell in cells:
                    left_x = min(p.x() for p in cell.points)
                    self.assertAlmostEqual(left_x, expected_left_x, places=1,
                                         msg=f"单元格左边应该对齐到X={expected_left_x}")
    
    def test_auto_decompose_region_to_grid(self):
        """测试自动网格分解算法"""
        # Table-Driven Test cases
        test_cases = [
            # (表格区域, 行数, 列数, 期望单元格数量)
            ((0, 0, 300, 200), 2, 3, 6),    # 2x3网格
            ((50, 50, 250, 150), 3, 2, 6),  # 3x2网格
            ((10, 10, 110, 60), 1, 4, 4),   # 1x4网格
        ]
        
        for region, rows, cols, expected_count in test_cases:
            with self.subTest(region=region, rows=rows, cols=cols):
                # 设置表格区域
                self.controller.table_region = region
                
                # 执行网格分解
                created_cells = self.controller.auto_decompose_region_to_grid(rows, cols)
                
                # 验证结果
                self.assertEqual(len(created_cells), expected_count,
                               f"应该创建{expected_count}个单元格")
                
                # 验证单元格都在表格区域内
                x1, y1, x2, y2 = region
                for cell in created_cells:
                    for point in cell.points:
                        self.assertGreaterEqual(point.x(), x1, "单元格X坐标应该在区域内")
                        self.assertLessEqual(point.x(), x2, "单元格X坐标应该在区域内")
                        self.assertGreaterEqual(point.y(), y1, "单元格Y坐标应该在区域内")
                        self.assertLessEqual(point.y(), y2, "单元格Y坐标应该在区域内")
                
                # 验证逻辑坐标正确分配
                for i, cell in enumerate(created_cells):
                    row = i // cols
                    col = i % cols
                    lloc = cell.get_logical_location()
                    self.assertEqual(lloc["start_row"], row)
                    self.assertEqual(lloc["start_col"], col)
    
    def test_auto_decompose_region_to_grid_with_estimation(self):
        """测试带智能估算的网格分解"""
        # Table-Driven Test cases - 不指定行列数，测试智能估算
        test_cases = [
            # (表格区域, 期望最小单元格数)
            ((0, 0, 400, 300), 4),   # 大区域，至少4个单元格
            ((0, 0, 200, 100), 4),   # 中等区域，至少4个单元格
            ((0, 0, 100, 50), 4),    # 小区域，至少4个单元格
        ]
        
        for region, min_expected_count in test_cases:
            with self.subTest(region=region):
                # 设置表格区域
                self.controller.table_region = region
                
                # 执行智能网格分解（不指定行列数）
                created_cells = self.controller.auto_decompose_region_to_grid()
                
                # 验证结果
                self.assertGreaterEqual(len(created_cells), min_expected_count,
                                      f"智能估算应该至少创建{min_expected_count}个单元格")
                
                # 验证单元格有效性
                for cell in created_cells:
                    self.assertIsInstance(cell, TableCellShape)
                    self.assertTrue(cell.is_confirmed())
                    lloc = cell.get_logical_location()
                    self.assertGreaterEqual(lloc["start_row"], 0)
                    self.assertGreaterEqual(lloc["start_col"], 0)


class TestTableControllerLineInference(unittest.TestCase):
    """TableController划线推理功能测试"""
    
    def setUp(self):
        """测试准备"""
        if not QtWidgets.QApplication.instance():
            self.app = QtWidgets.QApplication([])
        
        self.mock_canvas = Mock()
        self.mock_canvas.shapes = []
        self.mock_canvas.selectedShapes = []
        self.mock_canvas.storeShapes = Mock()
        self.mock_canvas.update = Mock()
        
        self.controller = TableController(self.mock_canvas)
    
    def test_analyze_line_direction(self):
        """测试线条方向分析"""
        # Table-Driven Test cases
        test_cases = [
            # (起始点, 结束点, 期望方向)
            ((10, 50), (100, 55), "horizontal"),   # 水平线（Y变化小）
            ((50, 10), (55, 100), "vertical"),     # 垂直线（X变化小）
            ((0, 0), (100, 10), "horizontal"),     # 近似水平线
            ((0, 0), (10, 100), "vertical"),       # 近似垂直线
            ((20, 30), (80, 30), "horizontal"),    # 完全水平线
            ((40, 20), (40, 80), "vertical"),      # 完全垂直线
        ]
        
        for start_point, end_point, expected_direction in test_cases:
            with self.subTest(start=start_point, end=end_point):
                # 创建模拟线条
                mock_line = Mock()
                mock_line.points = [
                    QtCore.QPointF(start_point[0], start_point[1]),
                    QtCore.QPointF(end_point[0], end_point[1])
                ]
                
                # 执行方向分析
                result = self.controller._analyze_line_direction(mock_line)
                
                # 验证结果
                self.assertEqual(result, expected_direction,
                               f"线条从{start_point}到{end_point}应该是{expected_direction}方向")
    
    def test_find_cells_affected_by_line(self):
        """测试查找受线条影响的单元格"""
        # 创建测试单元格
        test_cells = [
            create_table_cell_from_rect(10, 10, 50, 50),   # 中心(30, 30)
            create_table_cell_from_rect(60, 10, 100, 50),  # 中心(80, 30)
            create_table_cell_from_rect(10, 60, 50, 100),  # 中心(30, 80)
            create_table_cell_from_rect(60, 60, 100, 100), # 中心(80, 80)
        ]
        self.controller.table_cells = test_cells
        
        # Table-Driven Test cases
        test_cases = [
            # (线条位置, 方向, 容差, 期望受影响的单元格索引)
            (30, "horizontal", 20, [0, 1]),  # 水平线Y=30，影响上排单元格
            (80, "horizontal", 20, [2, 3]),  # 水平线Y=80，影响下排单元格
            (30, "vertical", 20, [0, 2]),    # 垂直线X=30，影响左排单元格
            (80, "vertical", 20, [1, 3]),    # 垂直线X=80，影响右排单元格
        ]
        
        for line_position, direction, tolerance, expected_indices in test_cases:
            with self.subTest(pos=line_position, dir=direction):
                # 执行查找
                affected_cells = self.controller._find_cells_affected_by_line(
                    line_position, direction, tolerance
                )
                
                # 验证结果
                expected_cells = [test_cells[i] for i in expected_indices]
                self.assertEqual(len(affected_cells), len(expected_cells),
                               f"应该找到{len(expected_cells)}个受影响的单元格")
                
                for expected_cell in expected_cells:
                    self.assertIn(expected_cell, affected_cells,
                                "期望的单元格应该在受影响列表中")


if __name__ == "__main__":
    unittest.main() 