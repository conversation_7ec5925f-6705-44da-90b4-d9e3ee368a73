#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
单元格文本编辑器 - 提供直接点击编辑功能
"""

from qtpy import QtCore, QtGui, QtWidgets
from labelme.utils.log import get_logger

LOGGER = get_logger()


class CellTextEditor(QtWidgets.QWidget):
    """单元格文本编辑器 - 浮动编辑窗口"""
    
    # 编辑完成信号
    text_edited = QtCore.Signal(object, str)  # (cell_shape, new_text)
    
    def __init__(self, parent_canvas, cell_shape):
        try:
            super().__init__(parent=None)  # 无父窗口，作为独立窗口
            
            self.parent_canvas = parent_canvas
            self.cell_shape = cell_shape
            
            # 安全地获取原始文本
            try:
                self.original_text = cell_shape.get_cell_text()
                LOGGER.debug(f"获取到原始文本: '{self.original_text}'")
            except Exception as e:
                LOGGER.error(f"获取单元格文本失败: {e}")
                self.original_text = ""
            
            self._setup_ui()
            self._setup_style()
            self._connect_signals()
            
            # 设置窗口属性
            self.setWindowFlags(
                QtCore.Qt.FramelessWindowHint |  # 无边框
                QtCore.Qt.WindowStaysOnTopHint |  # 保持在顶层
                QtCore.Qt.Tool  # 工具窗口
            )
            
            LOGGER.debug(f"创建单元格文本编辑器成功，原始文本: '{self.original_text}'")
            
        except Exception as e:
            LOGGER.error(f"创建单元格文本编辑器失败: {e}")
            import traceback
            LOGGER.error(f"错误堆栈: {traceback.format_exc()}")
            raise
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QtWidgets.QVBoxLayout(self)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 文本编辑框
        self.text_edit = QtWidgets.QTextEdit()
        self.text_edit.setPlainText(self.original_text)
        self.text_edit.setMaximumHeight(80)   # 限制高度，更紧凑
        self.text_edit.setMinimumWidth(200)   # 增加最小宽度，提高可读性
        self.text_edit.setMaximumWidth(300)   # 限制最大宽度
        
        # 设置字体
        font = QtGui.QFont()
        font.setPointSize(10)
        self.text_edit.setFont(font)
        
        layout.addWidget(self.text_edit)
        
        # 按钮区域
        button_layout = QtWidgets.QHBoxLayout()
        
        self.btn_save = QtWidgets.QPushButton("保存")
        self.btn_save.setMaximumWidth(60)
        self.btn_cancel = QtWidgets.QPushButton("取消")
        self.btn_cancel.setMaximumWidth(60)
        
        button_layout.addWidget(self.btn_save)
        button_layout.addWidget(self.btn_cancel)
        button_layout.addStretch()
        
        layout.addLayout(button_layout)
        
        # 提示标签
        self.hint_label = QtWidgets.QLabel("Enter保存 | Shift+Enter换行 | Esc取消")
        self.hint_label.setStyleSheet("color: gray; font-size: 9px;")
        self.hint_label.setAlignment(QtCore.Qt.AlignCenter)
        layout.addWidget(self.hint_label)
    
    def _setup_style(self):
        """设置样式"""
        self.setStyleSheet("""
            CellTextEditor {
                background-color: white;
                border: 2px solid #4A90E2;
                border-radius: 8px;
                padding: 5px;
            }
            QTextEdit {
                background-color: white;
                color: black;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px;
                font-size: 12px;
                selection-background-color: #4A90E2;
                selection-color: white;
            }
            QTextEdit:focus {
                border: 2px solid #4A90E2;
            }
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 6px 12px;
                font-weight: bold;
                font-size: 11px;
                min-width: 50px;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
            QPushButton:pressed {
                background-color: #2E6DA4;
            }
            QLabel {
                color: #666;
                font-size: 10px;
                padding: 2px;
            }
        """)
    
    def _connect_signals(self):
        """连接信号"""
        self.btn_save.clicked.connect(self._save_text)
        self.btn_cancel.clicked.connect(self._cancel_edit)
        
        # 文本编辑框快捷键
        self.text_edit.installEventFilter(self)
    
    def eventFilter(self, obj, event):
        """事件过滤器 - 处理快捷键"""
        if obj == self.text_edit and event.type() == QtCore.QEvent.KeyPress:
            key = event.key()
            modifiers = event.modifiers()
            
            if key == QtCore.Qt.Key_Return or key == QtCore.Qt.Key_Enter:
                if modifiers == QtCore.Qt.ShiftModifier:
                    # Shift+Enter 插入换行
                    return False  # 让文本编辑框处理
                else:
                    # 普通Enter 保存
                    self._save_text()
                    return True
            elif key == QtCore.Qt.Key_Escape:
                # Esc 取消
                self._cancel_edit()
                return True
                
        return super().eventFilter(obj, event)
    
    def show_at_position(self, global_pos, cell_rect=None):
        """在指定位置显示编辑器，确保不与单元格相交"""
        try:
            LOGGER.debug(f"准备在位置显示编辑器: ({global_pos.x()}, {global_pos.y()})")
            
            # 调整大小
            self.adjustSize()
            
            # 获取屏幕几何信息
            screen = QtWidgets.QApplication.primaryScreen()
            if not screen:
                LOGGER.error("无法获取主屏幕信息")
                return
                
            screen_rect = screen.availableGeometry()
            LOGGER.debug(f"屏幕可用区域: {screen_rect}")
            
            # 计算编辑器尺寸
            editor_width = self.width()
            editor_height = self.height()
            
            # 默认位置（如果没有单元格信息）
            x = global_pos.x() + 20
            y = global_pos.y() - editor_height // 2
            
            # 如果有单元格矩形信息，计算最佳不相交位置
            if cell_rect:
                # 将单元格矩形转换为全局坐标
                cell_global_rect = QtCore.QRect(
                    self.parent_canvas.mapToGlobal(cell_rect.topLeft()),
                    cell_rect.size()
                )
                
                LOGGER.debug(f"单元格全局矩形: {cell_global_rect}")
                
                # 设置安全间距
                margin = 10
                
                # 尝试不同位置，优先级：右侧 > 左侧 > 下方 > 上方
                positions = [
                    # 右侧
                    (cell_global_rect.right() + margin, 
                     cell_global_rect.center().y() - editor_height // 2),
                    
                    # 左侧
                    (cell_global_rect.left() - editor_width - margin,
                     cell_global_rect.center().y() - editor_height // 2),
                    
                    # 下方
                    (cell_global_rect.center().x() - editor_width // 2,
                     cell_global_rect.bottom() + margin),
                    
                    # 上方
                    (cell_global_rect.center().x() - editor_width // 2,
                     cell_global_rect.top() - editor_height - margin),
                ]
                
                # 选择第一个在屏幕范围内且不相交的位置
                best_pos = None
                for pos_x, pos_y in positions:
                    # 检查是否在屏幕范围内
                    if (pos_x >= screen_rect.left() and 
                        pos_x + editor_width <= screen_rect.right() and
                        pos_y >= screen_rect.top() and 
                        pos_y + editor_height <= screen_rect.bottom()):
                        
                        # 创建编辑器矩形
                        editor_rect = QtCore.QRect(pos_x, pos_y, editor_width, editor_height)
                        
                        # 检查是否与单元格相交
                        if not editor_rect.intersects(cell_global_rect):
                            best_pos = (pos_x, pos_y)
                            LOGGER.debug(f"找到最佳位置: ({pos_x}, {pos_y})")
                            break
                
                if best_pos:
                    x, y = best_pos
                else:
                    # 如果所有位置都不合适，选择右侧位置并调整到屏幕边界
                    x = cell_global_rect.right() + margin
                    y = cell_global_rect.center().y() - editor_height // 2
                    LOGGER.debug("使用备选位置（右侧）")
            
            # 最终边界检查和调整
            if x < screen_rect.left():
                x = screen_rect.left() + 10
            elif x + editor_width > screen_rect.right():
                x = screen_rect.right() - editor_width - 10
                
            if y < screen_rect.top():
                y = screen_rect.top() + 10
            elif y + editor_height > screen_rect.bottom():
                y = screen_rect.bottom() - editor_height - 10
            
            # 最终检查：如果调整后的位置仍然与单元格相交，强制偏移
            if cell_rect:
                cell_global_rect = QtCore.QRect(
                    self.parent_canvas.mapToGlobal(cell_rect.topLeft()),
                    cell_rect.size()
                )
                final_editor_rect = QtCore.QRect(x, y, editor_width, editor_height)
                
                if final_editor_rect.intersects(cell_global_rect):
                    # 强制移动到右下角
                    x = cell_global_rect.right() + 15
                    y = cell_global_rect.bottom() + 15
                    
                    # 再次边界检查
                    if x + editor_width > screen_rect.right():
                        x = screen_rect.right() - editor_width - 10
                    if y + editor_height > screen_rect.bottom():
                        y = screen_rect.bottom() - editor_height - 10
                    
                    LOGGER.debug(f"强制调整位置避免相交: ({x}, {y})")
            
            # 设置位置并显示
            self.move(x, y)
            self.show()
            self.raise_()
            self.activateWindow()
            
            # 聚焦到文本编辑框并全选
            self.text_edit.setFocus()
            self.text_edit.selectAll()
            
            LOGGER.info(f"文本编辑器成功显示在位置: ({x}, {y})")
            
        except Exception as e:
            LOGGER.error(f"显示文本编辑器失败: {e}")
            import traceback
            LOGGER.error(f"错误堆栈: {traceback.format_exc()}")
    
    def _save_text(self):
        """保存文本"""
        new_text = self.text_edit.toPlainText().strip()
        
        if new_text != self.original_text:
            # 更新单元格文本
            self.cell_shape.set_cell_text(new_text)
            
            # 发送信号通知变更
            self.text_edited.emit(self.cell_shape, new_text)
            
            # 更新Canvas显示
            if self.parent_canvas:
                self.parent_canvas.update()
                
            LOGGER.info(f"单元格文本已更新: '{self.original_text}' -> '{new_text}'")
        else:
            LOGGER.debug("文本无变化，跳过保存")
        
        self._close_editor()
    
    def _cancel_edit(self):
        """取消编辑"""
        LOGGER.debug("取消文本编辑")
        self._close_editor()
    
    def _close_editor(self):
        """关闭编辑器"""
        self.hide()
        
        # 清理Canvas中的引用
        if (self.parent_canvas and 
            hasattr(self.parent_canvas, '_cell_text_editor') and 
            self.parent_canvas._cell_text_editor == self):
            self.parent_canvas._cell_text_editor = None
        
        # 延迟删除
        QtCore.QTimer.singleShot(100, self.deleteLater)
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        self._close_editor()
        event.accept()


def create_cell_text_editor(parent_canvas, cell_shape):
    """创建单元格文本编辑器的工厂函数"""
    return CellTextEditor(parent_canvas, cell_shape)


if __name__ == "__main__":
    """测试代码"""
    import sys
    from labelme.table_shape import TableCellShape
    from qtpy.QtCore import QPointF
    
    app = QtWidgets.QApplication(sys.argv)
    
    # 创建测试单元格
    cell = TableCellShape(label="测试单元格")
    cell.points = [QPointF(0, 0), QPointF(100, 0), QPointF(100, 50), QPointF(0, 50)]
    cell.set_cell_text("原始文本内容")
    
    # 创建编辑器
    editor = CellTextEditor(None, cell)
    
    def on_text_edited(cell_shape, new_text):
        print(f"文本已更新: {new_text}")
    
    editor.text_edited.connect(on_text_edited)
    
    # 显示编辑器
    editor.show_at_position(QtCore.QPoint(300, 200))
    
    LOGGER.debug("单元格文本编辑器测试启动")
    sys.exit(app.exec_()) 