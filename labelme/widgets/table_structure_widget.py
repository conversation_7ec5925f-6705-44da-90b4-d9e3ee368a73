#!/usr/bin/env python
# -*- coding: utf-8 -*-

from qtpy import QtCore
from qtpy import QtWidgets
from qtpy import QtGui
from labelme.utils.log import get_logger

LOGGER = get_logger()


class TableCellDelegate(QtWidgets.QStyledItemDelegate):
    """自定义单元格渲染器 - 支持边框样式显示"""

    def paint(self, painter, option, index):
        """自定义绘制单元格边框"""
        super().paint(painter, option, index)

        # 获取单元格数据
        cell_data = index.data(QtCore.Qt.UserRole)
        if not cell_data or "border" not in cell_data:
            return

        # 绘制边框
        self._draw_cell_borders(painter, option.rect, cell_data["border"])

    def _draw_cell_borders(self, painter, rect, border):
        """绘制单元格边框

        Args:
            painter: QPainter对象
            rect: 单元格矩形区域
            border: 边框样式字典 {'top': 0/1, 'right': 0/1, 'bottom': 0/1, 'left': 0/1}
        """
        # 保存画笔状态
        painter.save()

        # 设置边框样式
        solid_pen = QtGui.QPen(QtGui.QColor(0, 0, 0), 2, QtCore.Qt.SolidLine)
        
        # 🆕 改进无边框的虚线样式 - 调大间隔，颜色更淡
        dash_color = QtGui.QColor(80, 120, 80)  # 淡绿色
        dash_color.setAlpha(100)  # 更淡的颜色
        
        # 使用自定义虚线模式：更大的间隔
        dash_pen = QtGui.QPen(dash_color, 1, QtCore.Qt.CustomDashLine)
        
        # 设置虚线模式：[线段长度, 间隔长度]
        # 更大的间隔使虚线更明显但不突兀
        dash_pattern = [6, 8]  # 6像素线段 + 8像素间隔（比默认间隔大）
        dash_pen.setDashPattern(dash_pattern)

        # 绘制四条边
        if border.get('top', 0) == 1:
            painter.setPen(solid_pen)
            painter.drawLine(rect.topLeft(), rect.topRight())
        else:
            painter.setPen(dash_pen)
            painter.drawLine(rect.topLeft(), rect.topRight())

        if border.get('right', 0) == 1:
            painter.setPen(solid_pen)
            painter.drawLine(rect.topRight(), rect.bottomRight())
        else:
            painter.setPen(dash_pen)
            painter.drawLine(rect.topRight(), rect.bottomRight())

        if border.get('bottom', 0) == 1:
            painter.setPen(solid_pen)
            painter.drawLine(rect.bottomLeft(), rect.bottomRight())
        else:
            painter.setPen(dash_pen)
            painter.drawLine(rect.bottomLeft(), rect.bottomRight())

        if border.get('left', 0) == 1:
            painter.setPen(solid_pen)
            painter.drawLine(rect.topLeft(), rect.bottomLeft())
        else:
            painter.setPen(dash_pen)
            painter.drawLine(rect.topLeft(), rect.bottomLeft())

        # 恢复画笔状态
        painter.restore()


class TableStructureWidget(QtWidgets.QWidget):
    """表格逻辑结构显示界面"""
    # 添加合并相关信号
    merge_cells_requested = QtCore.Signal(list)  # 合并请求信号
    split_cells_requested = QtCore.Signal(list)  # 拆分请求信号
    # 信号：用户点击了某个单元格（保留兼容性）
    cellClicked = QtCore.Signal(int, int)  # row, col
    # 新信号：用户选择变化，支持批量选择
    selectionChanged = QtCore.Signal(list)  # 选中单元格数据列表

    def __init__(self, parent=None):
        super(TableStructureWidget, self).__init__(parent)
        self.canvas = None  # Canvas引用
        self._grid_data = {}  # 网格数据
        self._processing_logical_selection = False  # 🆕 防止信号循环的标志
        self.show_text_content = True  # 🆕 文本显示状态
        self._setup_ui()
        self._setup_table_view()
        self._connect_signals()  # 🆕 连接信号

    def _setup_ui(self):
        """设置界面布局"""
        layout = QtWidgets.QVBoxLayout(self)

        # 🆕 标题和文本显示控制区域
        title_layout = QtWidgets.QHBoxLayout()

        # 标题和统计信息
        self.info_label = QtWidgets.QLabel("逻辑表格结构")
        self.info_label.setStyleSheet("font-weight: bold; color: #2c3e50;")
        title_layout.addWidget(self.info_label)

        # 🆕 文本显示开关
        self.show_text_checkbox = QtWidgets.QCheckBox("显示文本")
        self.show_text_checkbox.setChecked(True)  # 默认显示文本
        self.show_text_checkbox.setToolTip("控制是否在单元格中显示文本内容")
        title_layout.addWidget(self.show_text_checkbox)

        # 推到左侧
        title_layout.addStretch()

        layout.addLayout(title_layout)
        # 🆕 注释：合并/拆分工具栏已移动到属性面板的"逻辑结构编辑"中
        # 这里保留原有的表格视图功能，不再包含合并/拆分按钮
        # 表格视图
        self.table_view = QtWidgets.QTableWidget()
        self.table_view.setSelectionBehavior(QtWidgets.QAbstractItemView.SelectItems)
        # 变更：将选择模式从单选改为扩展选择（支持Ctrl和Shift多选）
        self.table_view.setSelectionMode(QtWidgets.QAbstractItemView.ExtendedSelection)
        self.table_view.horizontalHeader().hide()
        self.table_view.verticalHeader().hide()
        self.table_view.setShowGrid(True)
        self.table_view.setGridStyle(QtCore.Qt.SolidLine)

        # 变更：连接新的选择变化信号，替换原来的cellClicked
        self.table_view.itemSelectionChanged.connect(self._on_selection_changed)
        # 🆕 添加右键菜单支持
        self.table_view.setContextMenuPolicy(QtCore.Qt.CustomContextMenu)
        self.table_view.customContextMenuRequested.connect(self._show_context_menu)
        layout.addWidget(self.table_view)

        # 提示信息
        self.hint_label = QtWidgets.QLabel("提示：单击选择单元格，Ctrl+单击多选，Shift+单击范围选择")
        self.hint_label.setStyleSheet("color: gray; font-size: 10px;")
        layout.addWidget(self.hint_label)

    def _connect_signals(self):
        """连接信号"""
        # 🆕 连接文本显示开关信号
        self.show_text_checkbox.toggled.connect(self._on_show_text_toggled)

    def _on_show_text_toggled(self, checked):
        """文本显示开关切换事件"""
        self.show_text_content = checked
        # 重新渲染表格以应用新的显示设置
        self._update_table_view()
        LOGGER.debug(f"文本显示开关: {'开启' if checked else '关闭'}")

    def _setup_table_view(self):
        """设置表格视图的渲染"""
        # 使用自定义委托渲染器
        delegate = TableCellDelegate()
        self.table_view.setItemDelegate(delegate)
        # 🆕 注释：合并/拆分信号连接已移动到属性面板

    def _can_merge_cells(self, cell_data_list):
        """检查单元格是否可以合并"""
        if len(cell_data_list) < 2:
            return False

        # 收集所有行列位置
        positions = set()
        for cell_data in cell_data_list:
            # 检查是否已经是合并单元格
            if self._is_merged_cell(cell_data):
                return False  # 不能合并已合并的单元格
            positions.add((cell_data['row'], cell_data['col']))

        # 检查是否形成矩形区域
        rows = set(pos[0] for pos in positions)
        cols = set(pos[1] for pos in positions)

        if not rows or not cols:
            return False

        min_row, max_row = min(rows), max(rows)
        min_col, max_col = min(cols), max(cols)

        # 生成期望的所有位置
        expected_positions = set()
        for r in range(min_row, max_row + 1):
            for c in range(min_col, max_col + 1):
                expected_positions.add((r, c))

        # 检查选中的位置是否完全覆盖期望的矩形区域
        return positions == expected_positions

    def _is_merged_cell(self, cell_data):
        """检查是否是合并单元格 - 使用逻辑位置信息"""
        logical_location = cell_data.get('logical_location')
        if logical_location:
            return (logical_location['start_row'] != logical_location['end_row'] or
                    logical_location['start_col'] != logical_location['end_col'])

        # 降级方案：检查 cell_id
        cell_id = str(cell_data.get('cell_id', ''))
        return cell_id.startswith('merged_')

    # 🆕 注释：以下合并/拆分相关方法已移动到属性面板的LogicalStructureEditWidget
    # def _on_merge_button_clicked(self): -> 已移动到属性面板
    # def _on_split_button_clicked(self): -> 已移动到属性面板

    def _show_context_menu(self, position):
        """显示右键菜单"""
        item = self.table_view.itemAt(position)
        if not item:
            return

        menu = QtWidgets.QMenu(self)

        selected_items = self.table_view.selectedItems()

        # 🆕 注释：合并/拆分选项已移动到属性面板，这里只保留信息查看功能
        # 原有的合并/拆分菜单项已移动到属性面板的"逻辑结构编辑"按钮中

        # 其他选项
        if not menu.isEmpty():
            menu.addSeparator()

        info_action = menu.addAction("单元格信息")
        info_action.triggered.connect(lambda: self._show_cell_info(item))

        if not menu.isEmpty():
            menu.exec_(self.table_view.mapToGlobal(position))

    def _show_cell_info(self, item):
        """显示单元格详细信息"""
        cell_data = item.data(QtCore.Qt.UserRole)
        if not cell_data:
            return

        info_text = f"位置: ({cell_data['row']}, {cell_data['col']})\n"
        info_text += f"内容: {cell_data.get('text', '无')}\n"
        info_text += f"类型: {'空单元格' if cell_data.get('is_empty') else '数据单元格'}\n"
        info_text += f"ID: {cell_data.get('cell_id', '无')}\n"

        border = cell_data.get('border', {})
        border_info = []
        for side in ['top', 'right', 'bottom', 'left']:
            style = "实线" if border.get(side, 0) == 1 else "无"
            border_info.append(f"{side}: {style}")
        info_text += f"边框: {', '.join(border_info)}"

        QtWidgets.QMessageBox.information(self, "单元格信息", info_text)

    def bind_canvas(self, canvas):
        """绑定Canvas实现双向联动

        Args:
            canvas: Canvas对象
        """
        self.canvas = canvas
        # 连接Canvas选择变更信号
        if hasattr(canvas, 'selectionChanged'):
            canvas.selectionChanged.connect(self._on_canvas_selection_changed)
            LOGGER.debug("Canvas双向联动已建立")
        else:
            LOGGER.warning("Canvas不支持selectionChanged信号")

    def _on_canvas_selection_changed(self, shapes):
        """响应Canvas选择变更

        Args:
            shapes: 选中的形状列表
        """
        # 🔧 关键修复：如果正在处理逻辑选择，跳过Canvas反向更新
        if self._processing_logical_selection:
            LOGGER.debug("跳过Canvas反向更新（正在处理逻辑选择）")
            return
        # 找到选中的表格单元格
        from labelme.table_shape import TableCellShape
        selected_cells = [s for s in shapes if isinstance(s, TableCellShape)]

        if selected_cells:
            # 收集所有需要高亮的逻辑位置
            positions_to_highlight = []
            for cell in selected_cells:
                lloc = cell.get_logical_location()
                if lloc:
                    row = lloc.get("start_row", 0)
                    col = lloc.get("start_col", 0)
                    positions_to_highlight.append((row, col))

            # 批量高亮对应的逻辑视图单元格
            if positions_to_highlight:
                self.highlight_cells(positions_to_highlight)
                LOGGER.debug(f"Canvas选择变更 -> 逻辑视图批量高亮: {len(positions_to_highlight)}个单元格")
        else:
            # 清除所有选择
            self.highlight_cells([])
            LOGGER.debug("Canvas选择清除 -> 逻辑视图清除选择")

    def _on_selection_changed(self):
        """处理表格视图选择变化"""
        # 🆕 设置标志，表示正在处理逻辑选择
        self._processing_logical_selection = True

        try:
            selected_items = self.table_view.selectedItems()
            # 🔧 屏蔽过多的选择调试输出
            debug_structure_selection = False  # 设置为 True 可开启选择调试
            if debug_structure_selection:
                LOGGER.debug(f"当前选中: {len(selected_items)}个单元格")

            # 🆕 注释：按钮状态更新已移动到属性面板

            if not selected_items:
                # 没有选中任何项，发出空列表信号
                self.selectionChanged.emit([])
                return

            # 提取所有选中单元格的数据
            selected_cells_data = []
            for item in selected_items:
                cell_data = item.data(QtCore.Qt.UserRole)
                if cell_data:
                    selected_cells_data.append(cell_data)

            # 发出包含所有选中单元格数据的信号
            if selected_cells_data:
                LOGGER.debug(f"选择变更: {len(selected_cells_data)}个单元格被选中")
                self.selectionChanged.emit(selected_cells_data)
        finally:
            # 🆕 确保标志被清除
            self._processing_logical_selection = False

    # 🆕 注释：_update_merge_button_states 方法已移动到属性面板的LogicalStructureEditWidget.update_from_selection
    # def _update_merge_button_states(self, selected_items): -> 已移动到属性面板

    def _notify_canvas_highlight_multiple(self, cells_data):
        """通知Canvas高亮多个对应的物理单元格

        Args:
            cells_data: 选中单元格的数据列表
        """
        if self.canvas and hasattr(self.canvas, 'table_controller'):
            controller = self.canvas.table_controller

            # 优先使用批量高亮方法
            if hasattr(controller, 'highlight_cells_by_data'):
                controller.highlight_cells_by_data(cells_data)
                LOGGER.debug(f"逻辑视图点击 -> Canvas批量高亮: {len(cells_data)}个单元格")
            # 备用：使用逻辑位置批量高亮
            elif hasattr(controller, 'highlight_cells_by_logical_positions'):
                positions = []
                for cell in cells_data:
                    positions.append((cell['row'], cell['col']))
                controller.highlight_cells_by_logical_positions(positions)
                LOGGER.debug(f"逻辑视图点击 -> Canvas批量高亮(位置): {len(positions)}个单元格")
            # 备用：只高亮第一个单元格
            elif hasattr(controller, 'highlight_cells_by_logical_position') and cells_data:
                first_cell = cells_data[0]
                controller.highlight_cells_by_logical_position(first_cell['row'], first_cell['col'])
                LOGGER.debug(f"逻辑视图点击 -> Canvas高亮(仅第一个): ({first_cell['row']}, {first_cell['col']})")

    def highlight_cell(self, row, col):
        """高亮指定单元格（保留用于单选高亮）"""
        self.highlight_cells([(row, col)])

    def highlight_cells(self, positions):
        """高亮指定位置的多个单元格

        Args:
            positions: 一个包含 (row, col) 元组的列表
        """
        # 🔧 如果正在处理逻辑选择，跳过
        if self._processing_logical_selection:
            LOGGER.debug("跳过Canvas反向高亮（正在处理逻辑选择）")
            return

        # 暂时断开信号连接，防止循环触发
        self.table_view.itemSelectionChanged.disconnect()

        try:
            # 清除之前的选择
            self.table_view.clearSelection()

            # 选择指定的单元格
            for row, col in positions:
                if 0 <= row < self.table_view.rowCount() and 0 <= col < self.table_view.columnCount():
                    item = self.table_view.item(row, col)
                    if item:
                        item.setSelected(True)
        finally:
            # 重新连接信号
            self.table_view.itemSelectionChanged.connect(self._on_selection_changed)

    def _update_table_view(self):
        """更新表格视图"""
        if not self._grid_data:
            self.table_view.clear()
            self.table_view.setRowCount(0)
            self.table_view.setColumnCount(0)
            self.info_label.setText("逻辑表格结构")
            return

        rows = self._grid_data['rows']
        cols = self._grid_data['cols']
        cells = self._grid_data['cells']

        # 设置表格尺寸
        self.table_view.setRowCount(rows)
        self.table_view.setColumnCount(cols)

        # 创建单元格映射
        cell_map = {}
        for cell in cells:
            key = (cell['row'], cell['col'])
            cell_map[key] = cell

        # 填充表格
        for row in range(rows):
            for col in range(cols):
                item = QtWidgets.QTableWidgetItem()

                if (row, col) in cell_map:
                    # 有数据的单元格
                    cell_data = cell_map[(row, col)]
                    self._setup_cell_item(item, cell_data)
                else:
                    # 空单元格：显示位置信息
                    item.setText(f"({row},{col})")
                    item.setTextAlignment(QtCore.Qt.AlignCenter)
                    item.setBackground(QtGui.QColor(240, 240, 240))
                    item.setFlags(QtCore.Qt.ItemIsEnabled | QtCore.Qt.ItemIsSelectable)
                    # 🔧 新增：为空单元格也设置数据，便于合并操作
                    empty_cell_data = {
                        'row': row,
                        'col': col,
                        'text': '',
                        'border': {'top': 0, 'right': 0, 'bottom': 0, 'left': 0},
                        'cell_id': f'empty_{row}_{col}',
                        'is_empty': True  # 标记为空单元格
                    }
                    item.setData(QtCore.Qt.UserRole, empty_cell_data)
                self.table_view.setItem(row, col, item)

        # 调整列宽
        self.table_view.resizeColumnsToContents()
        self.table_view.resizeRowsToContents()

        # 更新信息标签
        cell_count = len(cells)
        self.info_label.setText(f"逻辑表格结构 ({rows}行 × {cols}列，共{cell_count}个单元格)")

    def get_selected_cells_for_merge(self):
        """获取选中的单元格用于合并（包括空单元格）"""
        selected_items = self.table_view.selectedItems()
        selected_data = []

        for item in selected_items:
            cell_data = item.data(QtCore.Qt.UserRole)
            if cell_data:
                selected_data.append(cell_data)

        return selected_data

    # 🔧 重命名现有方法，使其更通用
    def request_merge_selected_cells(self):
        """请求合并选中的单元格（包括空单元格和数据单元格）"""
        selected_cells = self.get_selected_cells_for_merge()

        if len(selected_cells) < 2:
            LOGGER.warning("请选择至少2个单元格进行合并")
            return False

        # 检查选中的单元格是否可以合并
        if self._can_merge_cells(selected_cells):
            # 发送合并信号给controller
            self.merge_cells_requested.emit(selected_cells)
            return True
        else:
            LOGGER.error("选中的单元格无法合并")
            return False

    # 新增合并请求信号
    merge_cells_requested = QtCore.Signal(list)  # 添加到类的信号定义中

    def _setup_cell_item(self, item, cell_data):
        """设置单元格项目的显示内容和样式 - 使用内置逻辑位置信息"""
        # 显示文本
        text = cell_data.get('text', '')

        # 🎯 优雅解决方案：直接从逻辑位置信息计算跨度和位置显示
        logical_location = cell_data.get('logical_location')
        if logical_location:
            start_row = logical_location['start_row']
            end_row = logical_location['end_row']
            start_col = logical_location['start_col']
            end_col = logical_location['end_col']

            # 检查是否是合并单元格（跨度大于1x1）
            if start_row != end_row or start_col != end_col:
                # 合并单元格：显示为（start-end,start-end）格式
                row_display = f"{start_row}-{end_row}" if start_row != end_row else str(start_row)
                col_display = f"{start_col}-{end_col}" if start_col != end_col else str(start_col)
                position_text = f"({row_display},{col_display})"
                LOGGER.debug(f"✅ 合并单元格显示: {position_text}")
            else:
                # 普通单元格：显示为（x,y）格式
                position_text = f"({start_row},{start_col})"
        else:
            # 降级方案：使用当前位置
            position_text = f"({cell_data['row']},{cell_data['col']})"

        # 🔧 修改：根据文本显示开关决定显示内容
        if self.show_text_content and text:
            # 显示文本：格式化显示内容为 (row,col)[text]
            if len(text) > 15:
                truncated_text = text[:12] + "..."
            else:
                truncated_text = text
            display_text = f"{position_text}[{truncated_text}]"
        else:
            # 不显示文本或没有文本时只显示位置
            display_text = position_text

        # ✨ 新增：检查表头状态并添加文本标记
        is_header = self._check_if_header(cell_data)
        if is_header:
            display_text = f"Head: {display_text}"

        item.setText(display_text)
        item.setTextAlignment(QtCore.Qt.AlignCenter)

        # 设置工具提示
        logical_location = cell_data.get('logical_location')
        if logical_location:
            start_row = logical_location['start_row']
            end_row = logical_location['end_row']
            start_col = logical_location['start_col']
            end_col = logical_location['end_col']

            if start_row != end_row or start_col != end_col:
                # 合并单元格的工具提示
                row_span = end_row - start_row + 1
                col_span = end_col - start_col + 1
                tooltip = f"合并单元格\n"
                tooltip += f"范围: ({start_row}-{end_row}, {start_col}-{end_col})\n"
                tooltip += f"跨度: {row_span}行 × {col_span}列\n"
            else:
                # 普通单元格的工具提示
                tooltip = f"位置: ({start_row}, {start_col})\n"
        else:
            # 降级方案
            tooltip = f"位置: ({cell_data['row']}, {cell_data['col']})\n"

        tooltip += f"内容: {text}\n"
        
        # ✨ 新增：在工具提示中显示表头状态
        if is_header:
            tooltip += f"表头: 是\n"
        else:
            tooltip += f"表头: 否\n"

        # 显示边框信息
        border = cell_data.get('border', {})
        border_info = []
        for side in ['top', 'right', 'bottom', 'left']:
            style = "实线" if border.get(side, 0) == 1 else "无边框"
            border_info.append(f"{side}: {style}")
        tooltip += f"边框: {', '.join(border_info)}"

        item.setToolTip(tooltip)

        # ✨ 优化：设置背景样式 - 只区分合并单元格和普通单元格
        logical_location = cell_data.get('logical_location')
        is_merged = logical_location and (logical_location['start_row'] != logical_location['end_row'] or
                                         logical_location['start_col'] != logical_location['end_col'])
        
        if is_merged:
            # 🟡 合并单元格：浅黄色背景，深棕色文字
            item.setBackground(QtGui.QColor(255, 248, 220))  # 浅黄色背景
            item.setForeground(QtGui.QColor(139, 69, 19))  # 深棕色文字
        else:
            # 🔵 普通单元格（包括表头）：更蓝的背景，黑色文字
            item.setBackground(QtGui.QColor(230, 240, 255))  # 更明显的浅蓝色背景
            item.setForeground(QtGui.QColor(0, 0, 0))  # 黑色文字

        # 存储原始数据
        item.setData(QtCore.Qt.UserRole, cell_data)

    def _check_if_header(self, cell_data):
        """检查单元格是否为表头
        
        Args:
            cell_data: 单元格数据字典
            
        Returns:
            bool: 是否为表头
        """
        # 方法1：检查table_properties中的header字段
        table_properties = cell_data.get('table_properties')
        if table_properties and table_properties.get('header', False):
            return True
            
        # 方法2：检查cell_id对应的原始Shape对象（如果可以访问）
        if self.canvas and hasattr(self.canvas, 'shapes'):
            cell_id = cell_data.get('cell_id')
            if cell_id:
                for shape in self.canvas.shapes:
                    if (hasattr(shape, 'table_properties') and 
                        hasattr(shape, 'get_table_id') and 
                        shape.get_table_id() == cell_id):
                        return shape.get_header()
                        
        # 方法3：通过TableController查询
        if (self.canvas and hasattr(self.canvas, 'table_controller') and
            hasattr(self.canvas.table_controller, 'is_cell_header')):
            row = cell_data.get('row')
            col = cell_data.get('col')
            if row is not None and col is not None:
                return self.canvas.table_controller.is_cell_header(row, col)
                
        return False

    def _apply_cell_spans(self, grid_data):
        """应用合并单元格的正确跨度显示"""
        if not grid_data:
            return

        cells = grid_data.get('cells', [])

        # 清除现有的合并
        self.table_view.clearSpans()

        # 🔧 修复：根据实际的逻辑位置设置正确的跨度
        processed_positions = set()  # 记录已处理的位置，避免重复

        for cell in cells:
            row = cell.get('row', 0)
            col = cell.get('col', 0)

            # 跳过已处理的位置
            if (row, col) in processed_positions:
                continue

            # 🔧 关键修复：检查是否是合并单元格
            if self._is_merged_cell(cell):
                # 从cell_id或其他方式获取跨度信息
                row_span, col_span = self._calculate_cell_span(cell)
                LOGGER.debug(f"设置合并单元格跨度: ({row},{col}) span=({row_span},{col_span})")

                # 设置跨度
                self.table_view.setSpan(row, col, row_span, col_span)

                # 标记所有被这个合并单元格占用的位置
                for r in range(row, row + row_span):
                    for c in range(col, col + col_span):
                        processed_positions.add((r, c))
            else:
                # 🔧 修复：普通单元格不需要设置1x1跨度，避免Qt警告
                # 普通单元格默认就是1x1，无需显式设置
                processed_positions.add((row, col))

    def _calculate_cell_span(self, cell_data):
        """计算单元格跨度 - 使用逻辑位置信息"""
        logical_location = cell_data.get('logical_location')
        if logical_location:
            row_span = logical_location['end_row'] - logical_location['start_row'] + 1
            col_span = logical_location['end_col'] - logical_location['start_col'] + 1
            return row_span, col_span

        # 降级方案：返回默认值
        return 1, 1
    def set_grid_data(self, grid_data):
        """设置网格数据并应用合并单元格

        Args:
            grid_data: {
                'rows': int,
                'cols': int,
                'cells': [
                    {
                        'row': int,
                        'col': int,
                        'text': str,
                        'border': dict,  # 边框样式 {'top': 0/1, 'right': 0/1, 'bottom': 0/1, 'left': 0/1}
                        'cell_id': any  # 用于标识原始单元格
                    },
                    ...
                ]
            }
        """
        self._grid_data = grid_data
        self._update_table_view()
        # 应用合并单元格
        if grid_data:
            self._apply_cell_spans(grid_data)

    def update_structure_view(self, grid_data):
        """更新表格结构显示（桥接方法）

        这是一个桥接方法，用于兼容TableController的调用
        实际调用set_grid_data方法

        Args:
            grid_data: 表格网格数据，格式与set_grid_data相同
        """
        if grid_data:
            rows = grid_data.get('rows', 0)
            cols = grid_data.get('cols', 0) 
            cells_count = len(grid_data.get('cells', []))
            LOGGER.debug(f"update_structure_view调用: rows={rows}, cols={cols}, cells={cells_count}")
        else:
            LOGGER.debug("update_structure_view调用: 清空数据")
        self.set_grid_data(grid_data)

    def get_selected_cell_data(self):
        """获取当前选中单元格的数据"""
        current_item = self.table_view.currentItem()
        if current_item:
            return current_item.data(QtCore.Qt.UserRole)
        return None

    def update_cell_border_styles(self, cells_with_borders):
        """局部更新单元格边框样式 - 避免全量刷新

        Args:
            cells_with_borders: List[Tuple[TableCellShape, dict]]
                               [(单元格对象, 新边框样式), ...]
        """
        if not self._grid_data or not cells_with_borders:
            return

        LOGGER.debug(f"[STRUCTURE] 局部更新{len(cells_with_borders)}个单元格的边框样式")

        # 创建单元格对象到边框样式的映射
        cell_border_map = {}
        for cell, border_style in cells_with_borders:
            # 使用单元格对象本身作为键，更可靠
            cell_border_map[cell] = border_style

        # 只更新受影响的单元格项目
        for row in range(self.table_view.rowCount()):
            for col in range(self.table_view.columnCount()):
                item = self.table_view.item(row, col)
                if not item:
                    continue

                cell_data = item.data(QtCore.Qt.UserRole)
                if not cell_data:
                    continue

                # 检查是否需要更新这个单元格
                # 通过逻辑位置匹配单元格
                cell_row = cell_data.get('row')
                cell_col = cell_data.get('col')

                # 查找匹配的单元格对象
                matching_cell = None
                for cell_obj in cell_border_map.keys():
                    cell_lloc = cell_obj.get_logical_location()
                    if (cell_lloc and
                        cell_lloc.get('start_row') == cell_row and
                        cell_lloc.get('start_col') == cell_col):
                        matching_cell = cell_obj
                        break

                if matching_cell:
                    # 更新单元格数据中的边框信息
                    cell_data['border'] = cell_border_map[matching_cell].copy()
                    item.setData(QtCore.Qt.UserRole, cell_data)

                    # 触发重绘该单元格（委托会自动使用新的边框数据）
                    self.table_view.update(self.table_view.indexFromItem(item))

                    LOGGER.debug(f"[STRUCTURE] 更新单元格({row},{col})边框: {cell_border_map[matching_cell]}")

        LOGGER.debug(f"[STRUCTURE] 边框样式局部更新完成")

    def clear_grid(self):
        """清空网格"""
        self.table_view.clear()
        self.table_view.setRowCount(0)
        self.table_view.setColumnCount(0)
        self.info_label.setText("逻辑表格结构")

    def get_grid_info(self):
        """获取网格信息"""
        if not self._grid_data:
            return {"rows": 0, "cols": 0, "cells": 0}

        return {
            "rows": self._grid_data['rows'],
            "cols": self._grid_data['cols'],
            "cells": len(self._grid_data['cells'])
        }


def main():
    """独立测试"""
    import sys

    app = QtWidgets.QApplication(sys.argv)

    window = QtWidgets.QMainWindow()
    window.setWindowTitle("表格逻辑结构显示器")
    window.setGeometry(100, 100, 500, 400)

    widget = TableStructureWidget()
    window.setCentralWidget(widget)

    # 设置测试数据
    test_data = {
        'rows': 3,
        'cols': 4,
        'cells': [
            {
                'row': 0, 'col': 0,
                'text': '单元格1',
                'border': {'top': 1, 'right': 1, 'bottom': 1, 'left': 1},
                'cell_id': 'cell_1'
            },
            {
                'row': 0, 'col': 1,
                'text': '单元格2',
                'border': {'top': 1, 'right': 0, 'bottom': 1, 'left': 0},
                'cell_id': 'cell_2'
            },
            {
                'row': 0, 'col': 2,
                'text': '单元格3',
                'border': {'top': 1, 'right': 1, 'bottom': 0, 'left': 1},
                'cell_id': 'cell_3'
            },
            {
                'row': 1, 'col': 0,
                'text': '数据1',
                'border': {'top': 0, 'right': 1, 'bottom': 1, 'left': 1},
                'cell_id': 'cell_4'
            },
            {
                'row': 1, 'col': 1,
                'text': '数据2',
                'border': {'top': 0, 'right': 0, 'bottom': 0, 'left': 0},
                'cell_id': 'cell_5'
            },
            {
                'row': 1, 'col': 2,
                'text': '这是一个很长的单元格内容，会会会会会会会被截断显示',
                'border': {'top': 1, 'right': 1, 'bottom': 1, 'left': 1},
                'cell_id': 'cell_6'
            },
            {
                'row': 2, 'col': 1,
                'text': '合并单元格示例',
                'border': {'top': 1, 'right': 0, 'bottom': 1, 'left': 0},
                'cell_id': 'cell_7'
            }
        ]
    }

    widget.set_grid_data(test_data)

    # 连接点击信号测试
    def on_cell_clicked(row, col):
        cell_data = widget.get_selected_cell_data()
        LOGGER.debug(f"选中单元格 ({row}, {col}): {cell_data}")

    widget.cellClicked.connect(on_cell_clicked)

    # 连接选择变化信号测试
    def on_selection_changed(selected_cells):
        LOGGER.debug(f"选中单元格变化：{selected_cells}")

    widget.selectionChanged.connect(on_selection_changed)

    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()