#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/6/13 11:45
# <AUTHOR> <EMAIL>
# @FileName: app_tableme

"""
表格标注专用应用 - 完整版本
基于LabelMe架构，专注于表格标注功能
集成所有已开发的表格组件，提供完整的标注工作流程
"""

import os
import sys
import json
import datetime
import functools
import os.path as osp
import tempfile
from typing import Optional, Tuple, List
from PyQt5.QtCore import Qt, QTimer, QEvent
from PyQt5 import QtCore, QtGui, QtWidgets
from dotenv import load_dotenv

# 导入LabelMe基础组件
from labelme import utils
from labelme.shape import Shape
from labelme.config import get_config
from labelme.widgets.canvas import Canvas
from labelme.widgets.zoom_widget import ZoomWidget

# 导入表格专用组件
from labelme.table_shape import TableCellShape
from labelme.utils.table_analyzer import TableAnalyzer
from labelme.widgets.table_structure_widget import TableStructureWidget
from labelme.widgets.multi_table_controller import MultiTableController
from labelme.widgets.table_properties_widget import TablePropertiesWidget
from labelme.utils.table_alignment_engine import TableAlignmentEngine

# 导入命令系统相关组件
from labelme.commands import CreateCellCommand, DeleteCellCommand, BatchCreateCellCommand
from labelme.managers import GlobalHistoryManager, ModeManager, OperationMode

# 导入日志系统
from labelme.utils.log import get_logger

# 🆕 导入API模块
from labelme.api import TextInAPIClient, TextInResponseConverter, APIError

# 🆕 导入文件索引管理器
from labelme.utils.file_index_manager import FileIndexManager
from labelme.utils.directory_aggregator import DirectoryAggregator

LOGGER = get_logger()

# 🆕 表格优化器可用性标志
print("🔍 设置优化器为可用...")
OPTIMIZER_AVAILABLE = True
print(f"🔍 OPTIMIZER_AVAILABLE = {OPTIMIZER_AVAILABLE}")

# ===== 🆕 文件状态持久化相关常量 =====
VIEWED_FILES_STATE_FILE = "viewed_files_state.json"  # 状态文件名
FILE_INDEX_VERSION = "2.0"  # 索引文件版本


class AlignmentToolWidget(QtWidgets.QWidget):
    """对齐工具复合控件：包含容差设置"""

    def __init__(self, parent, apply_action=None):
        super().__init__(parent)
        self.main_window = parent  # 保存主窗口引用
        self.init_ui(apply_action)
        self.setup_connections()

        # 初始化时设置一次分析模式
        self.update_analysis_mode()

    def init_ui(self, apply_action):
        """初始化UI"""
        # 使用水平布局，更紧凑
        layout = QtWidgets.QHBoxLayout(self)
        layout.setContentsMargins(2, 2, 2, 2)
        layout.setSpacing(2)

        # 容差滑块
        self.tolerance_slider = QtWidgets.QSlider(Qt.Horizontal)
        self.tolerance_slider.setRange(1, 35)
        self.tolerance_slider.setValue(15)
        self.tolerance_slider.setMaximumWidth(80)
        self.tolerance_slider.setMinimumWidth(70)
        self.tolerance_slider.setStyleSheet("""
            QSlider::groove:horizontal {
                border: 1px solid #bbb;
                background: white;
                height: 6px;
                border-radius: 3px;
            }
            QSlider::handle:horizontal {
                background: #4CAF50;
                border: 1px solid #388E3C;
                width: 12px;
                height: 12px;
                border-radius: 6px;
                margin: -3px 0;
            }
        """)
        layout.addWidget(self.tolerance_slider)

        # 设置整体样式
        self.setStyleSheet("""
            AlignmentToolWidget {
                background-color: transparent;
                border: none;
                padding: 0px;
            }
        """)
        
        # 设置最大高度，确保在工具栏中不占用过多空间
        self.setMaximumHeight(32)

    def setup_connections(self):
        """设置信号连接"""
        # 滑块拖动完成时更新分析模式
        self.tolerance_slider.sliderReleased.connect(self.update_analysis_mode)

    def update_analysis_mode(self):
        """更新表格分析模式的容差设置"""
        try:
            current_tolerance = self.tolerance_slider.value()

            # 调用table_analyzer的全局函数
            from labelme.utils.table_analyzer import set_analysis_mode
            set_analysis_mode(smart=True, tolerance=float(current_tolerance), debug=True)

            # 在状态栏显示更新信息
            if hasattr(self.main_window, 'statusBar'):
                self.main_window.statusBar().showMessage(
                    f"✅ 对齐容差已更新: {current_tolerance}px", 3000
                )

            LOGGER.debug(f"表格分析容差已更新: {current_tolerance}px")

        except Exception as e:
            LOGGER.error(f"更新分析模式失败: {e}")
            if hasattr(self.main_window, 'statusBar'):
                self.main_window.statusBar().showMessage(f"❌ 容差更新失败: {e}", 5000)

    def get_tolerance_value(self):
        """获取当前容差值"""
        return self.tolerance_slider.value()

    def set_tolerance_value(self, value):
        """设置容差值"""
        self.tolerance_slider.setValue(value)

class TableLabelMainWindow(QtWidgets.QMainWindow):
    """表格标注专用主窗口 - 完整功能版本"""

    def __init__(self):
        super().__init__()

        # 基础配置
        self._config = get_config()
        self.setWindowTitle("TableLabelMe - 表格标注工具")
        self.setGeometry(100, 100, 1400, 900)
        
        # 🆕 设置焦点策略，确保主窗口能接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)

        # 设置Shape默认颜色
        self._setup_shape_colors()

        # 状态变量
        self.filename = None
        self.image = QtGui.QImage()
        self.zoom_level = 100
        self.fit_window_mode = False

        # ===== 🆕 文件管理系统变量 =====
        self.lastOpenDir = None
        self.imageList = []
        self.recentFiles = []
        self.maxRecent = 7
        
        # 🆕 浏览历史追踪
        self.viewed_files = set()  # 记录已浏览过的文件

        # 🆕 记录每个目录最后打开的文件
        self.last_files_by_dir = {}

        # 🆕 文件筛选状态
        self.content_type_filters = {
            "文本": True,
            "有线表": True,
            "无线表": True,
            "待确认": True
        }
        self.quality_filters = {
            "合格": True,
            "准合格": True,
            "不合格": True,
            "待校准": True
        }

        # 🚀 文件元数据缓存（性能优化）
        self.metadata_cache = {}  # filename -> {content_type, quality}

        # 🚀 文件索引管理器和索引数据
        self.file_index_manager = None  # 将在打开目录时初始化
        self.file_index = None  # 当前目录的文件索引数据

        # 从设置中加载上次目录的文件记录
        settings = QtCore.QSettings("labelme", "tableme")
        self.last_files_by_dir = settings.value("last_files_by_dir", {}) or {}

        # 文件管理UI组件（将在_setup_dock_widgets中初始化）
        self.file_dock = None
        self.fileListWidget = None
        self.fileSearch = None

        # 核心组件
        self.canvas = None
        self.multi_table_controller = None
        self.properties_widget = None
        self.structure_widget = None

        # ===== 🆕 初始化历史管理器 =====
        self.history_manager = GlobalHistoryManager(max_history_size=50)
        LOGGER.debug("GlobalHistoryManager初始化完成")

        # ===== 🆕 初始化模式管理器 =====
        self.mode_manager = ModeManager()
        LOGGER.debug("ModeManager初始化完成")

        # 初始化界面
        self._setup_ui()
        self._setup_canvas()
        self._setup_table_components()
        self._create_actions()
        self._create_menus()
        self._create_toolbars()
        self._setup_dock_widgets()
        self._connect_signals()
        
        # 🆕 启动时自动加载测试数据目录
        self._auto_load_test_directory()

        # 🆕 初始化撤销/重做状态
        self._update_undo_redo_actions()

        # 🆕 初始化模式状态
        self._initialize_mode_state()

        LOGGER.debug("TableLabelMe 启动完成")

    def _setup_shape_colors(self):
        """设置Shape默认颜色"""
        Shape.line_color = QtGui.QColor(0, 255, 0)
        Shape.fill_color = QtGui.QColor(255, 0, 0, 128)
        Shape.select_line_color = QtGui.QColor(255, 255, 255)
        Shape.select_fill_color = QtGui.QColor(0, 128, 255, 155)
        Shape.vertex_fill_color = QtGui.QColor(0, 255, 0)
        Shape.hvertex_fill_color = QtGui.QColor(255, 255, 255)

    def _setup_ui(self):
        """设置基础UI布局"""
        # 创建中央区域的垂直布局组件
        central_widget = QtWidgets.QWidget()
        central_layout = QtWidgets.QVBoxLayout(central_widget)
        central_layout.setContentsMargins(0, 0, 0, 0)
        central_layout.setSpacing(0)

        # 画布滚动区域
        self.scroll_area = QtWidgets.QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        
        # ===== 🆕 添加滚动条支持 =====
        self.scrollBars = {
            Qt.Vertical: self.scroll_area.verticalScrollBar(),
            Qt.Horizontal: self.scroll_area.horizontalScrollBar(),
        }
        # 初始化滚动值记录
        self.scroll_values = {
            Qt.Vertical: {},
            Qt.Horizontal: {},
        }

        # 将画布和逻辑结构放在垂直布局中
        central_layout.addWidget(self.scroll_area, 2)  # 画布占2/3空间

        self.setCentralWidget(central_widget)
        self.central_layout = central_layout  # 保存引用供后续使用

        # 🔧 补充：创建缩放控件（原代码中遗漏了）
        self.zoom_widget = ZoomWidget()
        self.zoom_widget.setEnabled(False)

        # 状态栏
        self.statusBar().showMessage("TableLabelMe 已启动 - 自动加载测试数据...")

    def _setup_canvas(self):
        """设置Canvas画布"""
        self.canvas = Canvas(
            epsilon=self._config["epsilon"],
            double_click="close",
        )

        # ===== 🆕 设置历史管理器到Canvas =====
        self.canvas.history_manager = self.history_manager
        LOGGER.debug("Canvas与历史管理器关联完成")

        # ===== 🆕 设置主窗口引用到Canvas =====
        self.canvas.main_window = self
        LOGGER.debug("Canvas与主窗口关联完成")

        self.scroll_area.setWidget(self.canvas)

        # Canvas事件连接
        self.canvas.mouseMoved.connect(
            lambda pos: self.statusBar().showMessage(f"鼠标位置: x={pos.x():.0f}, y={pos.y():.0f}")
        )

        LOGGER.debug("Canvas 设置完成")

    # ===== 🆕 添加滚动处理方法 =====
    def scrollRequest(self, delta, orientation):
        """处理Canvas的滚动请求"""
        try:
            bar = self.scrollBars[orientation]
            old_value = bar.value()
            
            # 🔧 调试：输出滚动条的基本信息
            LOGGER.debug(f"滚动条信息: min={bar.minimum()}, max={bar.maximum()}, step={bar.singleStep()}, page={bar.pageStep()}")
            
            # 🔧 重新设计：直接使用像素级增量，不依赖singleStep
            # delta已经是Canvas中经过speed_factor调整的像素增量
            pixel_increment = delta  # 直接使用像素增量
            
            # 🔧 限制单次移动的像素范围
            max_pixel_move = 150  # 提高最大移动限制
            if abs(pixel_increment) > max_pixel_move:
                pixel_increment = max_pixel_move if pixel_increment > 0 else -max_pixel_move
                LOGGER.debug(f"像素增量限制: 原始={delta:.1f}, 限制后={pixel_increment}")
            
            new_value = old_value + pixel_increment
            
            # 🔧 边界检查：确保在滚动条范围内
            min_val = bar.minimum()
            max_val = bar.maximum()
            safe_value = max(min_val, min(max_val, new_value))
            
            self.setScroll(orientation, safe_value)
            LOGGER.debug(f"滚动请求: delta={delta:.1f}, 像素增量={pixel_increment:.1f}, 旧值={old_value}, 新值={safe_value:.1f}")
        except Exception as e:
            LOGGER.error(f"滚动请求处理失败: {e}")

    def setScroll(self, orientation, value):
        """设置滚动条值（带安全检查）"""
        try:
            bar = self.scrollBars[orientation]
            
            # 🔧 确保值在有效范围内
            min_val = bar.minimum()
            max_val = bar.maximum()
            safe_value = max(min_val, min(max_val, value))
            
            # 🔧 确保值在32位整数范围内
            INT_MAX = 2147483647
            INT_MIN = -2147483648
            safe_value = max(INT_MIN, min(INT_MAX, safe_value))
            
            bar.setValue(int(safe_value))
            
            # 如果有当前文件，记录滚动值
            if hasattr(self, 'filename') and self.filename:
                self.scroll_values[orientation][self.filename] = safe_value
            LOGGER.debug(f"设置滚动: orientation={orientation}, value={safe_value}")
        except Exception as e:
            LOGGER.error(f"设置滚动值失败: {e}")

    def _setup_table_components(self):
        """设置表格组件"""
        # 1. 创建多表格控制器
        self.multi_table_controller = MultiTableController(self.canvas)

        # 2. 创建属性面板
        self.properties_widget = TablePropertiesWidget()

        # 3. 创建结构视图
        self.structure_widget = TableStructureWidget()

        # 4. 将逻辑结构添加到中央布局（而不是dock）
        self.central_layout.addWidget(self.structure_widget, 1)  # 占1/3空间

        # 5. 建立绑定关系
        self._bind_table_components()

        LOGGER.debug("表格组件设置完成")

    def _bind_table_components(self):
        """建立表格组件间的绑定关系（修复选中状态设置）"""
        # 🔗 关键：Canvas绑定MultiTableController
        self.canvas.multi_table_controller = self.multi_table_controller

        # 绑定Canvas和结构视图
        self.structure_widget.bind_canvas(self.canvas)

        # 连接多表格控制器信号
        self.multi_table_controller.table_created.connect(self._on_table_created)
        self.multi_table_controller.table_switched.connect(self._on_table_switched)

        # 🔧 修复：先设置选中状态，再通知multi_table_controller
        self.canvas.selectionChanged.connect(self.shapeSelectionChanged)

        # 连接结构视图信号
        self.structure_widget.selectionChanged.connect(self._on_structure_selection_changed)

        # 🔧 修复绑定逻辑：使用正确的属性名
        self._bind_structure_to_active_controller()

        LOGGER.debug("表格组件绑定完成（修复选中状态设置）")

    def _bind_structure_to_active_controller(self):
        """将结构视图绑定到活动的TableController"""
        active_controller = self.multi_table_controller.get_active_controller()
        if active_controller and hasattr(self, 'structure_widget'):
            active_controller.bind_structure_widget(self.structure_widget)  # 🔧 使用正确的属性名
            LOGGER.debug("已绑定结构视图到活动TableController")
        else:
            LOGGER.warning("暂无活动TableController，将在创建表格时绑定")

    def _create_actions(self):
        """创建动作"""
        action = functools.partial(utils.newAction, self)

        # 文件操作
        self.action_open = action(
            "打开图片(&O)", self.open_file, "Ctrl+O", "open", "打开图片文件"
        )
        # 🆕 文件管理操作
        self.action_open_dir = action(
            "打开目录(&D)", self.open_dir, "Ctrl+Shift+O", "open", "批量导入图片目录"
        )

        self.action_save = action(
            "保存标注(&S)", self.save_file, "Ctrl+S", "save", "保存标注数据", enabled=False
        )

        self.action_quit = action(
            "退出(&Q)", self.close, "Ctrl+Q", "quit", "退出应用"
        )

        # 🆕 撤销/重做操作
        self.action_undo = action(
            "撤销(&U)", self.undo_last_operation, "Ctrl+Z", "undo", "撤销上一步操作", enabled=False
        )
        
        self.action_redo = action(
            "重做(&R)", self.redo_last_operation, "Ctrl+Shift+Z", "redo", "重做上一步操作", enabled=False
        )

        # 🆕 简化的2种模式切换
        self.action_select_mode = action(
            "选择模式", self.set_select_mode, "1", "@选择模式.png", "选择、移动、调整已有单元格，支持多选"
        )
        self.action_select_mode.setCheckable(True)

        self.action_create_cell_mode = action(
            "单元格模式", self.set_create_cell_mode, "2", "@单元格模式.png", "拖拽绘制新的表格单元格"
        )
        self.action_create_cell_mode.setCheckable(True)

        # 🆕 添加网格拖拽模式
        self.action_grid_drag_mode = action(
            "网格拖拽", self.set_grid_drag_mode, "C", "@网格拖拽.png", "按C键或点击按钮拖拽网格线调整表格布局"
        )
        self.action_grid_drag_mode.setCheckable(True)

        # 🆕 图片导航功能
        self.action_prev_image = action(
            "上一张(&P)", self.prev_image, "Left", "prev", "上一张图片"
        )
        
        self.action_next_image = action(
            "下一张(&N)", self.next_image, "Right", "next", "下一张图片"
        )
        # 对齐工具
        self.action_align_top = action(
            "顶端对齐", self.align_top, "Ctrl+T", "@顶端对齐.png", "将选中单元格顶端对齐"
        )

        self.action_align_bottom = action(
            "底端对齐", self.align_bottom, "Ctrl+B", "@底端对齐.png", "将选中单元格底端对齐"
        )

        self.action_align_left = action(
            "左对齐", self.align_left, "Ctrl+L", "@左对齐.png", "将选中单元格左对齐"
        )

        self.action_align_right = action(
            "右对齐", self.align_right, "Ctrl+R", "@右对齐.png", "将选中单元格右对齐"
        )

        # 表格工具
        self.action_analyze_table = action(
            "分析表格结构", self.analyze_table_structure, "A", "@分析表格结构.png", "分析选中区域的表格结构"
        )
        self.action_apply_alignment = action(
            "应用表格对齐", self.apply_table_alignment, "Ctrl+Shift+A", "@应用表格对齐.png", "将所有单元格对齐到分析出的理想网格"
        )
        # 在 _create_actions 方法中
        self.action_split_row = action(
            "按行拆分", self._split_cells_by_row, "Ctrl+H", "@按行拆分.png", "将选中单元格按行拆分（水平分割线）"
        )

        self.action_split_column = action(
            "按列拆分", self._split_cells_by_column, "Ctrl+V", "@按列拆分.png", "将选中单元格按列拆分（垂直分割线）"
        )
        # 🆕 P1阶段：快速生成表格功能
        self.action_quick_table = action(
            "快速生成表格", self.quick_generate_table, "Q", "@快速生成表格.png", "在选中区域快速生成M×N表格（Q）"
        )
        self.action_quick_table.setCheckable(True)  # 🆕 设置为可切换状态

        # 缩放操作
        self.action_zoom_in = action(
            "放大", self.zoom_in, "Ctrl+=", "zoom-in", "放大图像", enabled=False
        )

        self.action_zoom_out = action(
            "缩小", self.zoom_out, "Ctrl+-", "zoom-out", "缩小图像", enabled=False
        )

        self.action_fit_window = action(
            "适应窗口", self.fit_window, "Ctrl+F", "fit-window", "适应窗口大小", enabled=False
        )

        self.action_delete = action(
            "删除选中(&D)", self.delete_selected_shape, "Delete", "cancel",
            "删除选中的单元格或表格 (Delete/Backspace)", enabled=False
        )

        self.action_delete_table = action(
            "删除整个表格", self.delete_current_table, "Ctrl+Shift+Delete", "cancel",
            "删除当前活动表格及其所有单元格", enabled=False
        )

        # 🆕 可视化对比功能（重新实现）
        self.action_compare_visualization = action(
            "对比可视化", self.toggle_visualization_comparison, "Space", "@对比可视化.png",
            "按住空格键对比表格可视化效果 - 隐藏表格显示原图", enabled=False
        )
        # 设置为可切换的动作
        self.action_compare_visualization.setCheckable(True)

        # 🆕 逻辑可视化功能
        self.action_logical_visualization = action(
            "逻辑可视化", self.toggle_logical_visualization, "M", "@逻辑可视化.png",
            "按M键切换逻辑可视化 - 在物理单元格上显示逻辑坐标", enabled=False
        )
        # 设置为可切换的动作
        self.action_logical_visualization.setCheckable(True)

        # 🆕 批量API打标功能
        self.action_batch_api_labeling = action(
            "批量API辅助打标", self.batch_api_labeling, None, "api",
            "批量调用TextIn API对当前目录所有图片进行表格识别", enabled=True
        )

        # 🆕 图像旋转功能
        self.action_rotate_left = action(
            "向左旋转", self.rotate_image_left, "Ctrl+Left", "@向左旋转.png",
            "将图像向左旋转90度（会清空当前标注）", enabled=False
        )

        self.action_rotate_right = action(
            "向右旋转", self.rotate_image_right, "Ctrl+Right", "@向右旋转.png",
            "将图像向右旋转90度（会清空当前标注）", enabled=False
        )

        # 🆕 表格优化功能
        self.action_optimize_table = action(
            "优化表格", self.optimize_current_table, "Ctrl+Shift+O", "@优化表格.png",
            "优化当前显示的表格标注，减少角点分叉", enabled=False
        )

    def _create_menus(self):
        """创建菜单栏"""
        # 文件菜单
        file_menu = self.menuBar().addMenu("文件(&F)")
        utils.addActions(file_menu, (
            self.action_open,
            self.action_open_dir,  # 🆕 添加打开目录
            None,
            self.action_save,
            None,
            self.action_prev_image,  # 🆕 添加导航
            self.action_next_image,
            None,
            self.action_quit,
        ))

        # 🆕 编辑菜单
        edit_menu = self.menuBar().addMenu("编辑(&E)")
        utils.addActions(edit_menu, (
            self.action_undo,
            self.action_redo,
            None,
            self.action_delete,
            self.action_delete_table,
        ))

        # 🆕 简化的表格菜单
        table_menu = self.menuBar().addMenu("表格(&T)")
        utils.addActions(table_menu, (
            self.action_select_mode,
            self.action_create_cell_mode,
            self.action_grid_drag_mode,      # 🆕 添加到菜单
            None,
            self.action_quick_table,      # 🆕 快速生成表格
            self.action_analyze_table,
            self.action_apply_alignment, # 🆕 应用表格对齐
            None,
            self.action_batch_api_labeling,  # 🆕 批量API打标
            None,
            self.action_align_top,
            self.action_align_bottom,
            self.action_align_left,
            self.action_align_right,
        ))

        # 视图菜单
        view_menu = self.menuBar().addMenu("视图(&V)")
        utils.addActions(view_menu, (
            self.action_zoom_in,
            self.action_zoom_out,
            self.action_fit_window,
            None,
            self.action_compare_visualization,  # 🆕 对比可视化
            self.action_logical_visualization,  # 🆕 逻辑可视化
            None,
            self.action_rotate_left,   # 🆕 向左旋转
            self.action_rotate_right,  # 🆕 向右旋转
        ))

    def _create_toolbars(self):
        """创建工具栏"""
        # 🔧 设置统一的图标尺寸（更大更清晰）
        icon_size = QtCore.QSize(32, 32)  # 32x32像素图标
        
        # 🆕 第一组：文件操作和导航
        file_toolbar = self.addToolBar("文件操作")
        file_toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        file_toolbar.setIconSize(icon_size)

        utils.addActions(file_toolbar, (
            self.action_open,
            self.action_open_dir,
            self.action_save,
            None,
            self.action_prev_image,
            self.action_next_image,
        ))

        # 🆕 第二组：模式和对比工具
        mode_toolbar = self.addToolBar("模式工具")
        mode_toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        mode_toolbar.setIconSize(icon_size)

        utils.addActions(mode_toolbar, (
            self.action_select_mode,              # 选择模式
            self.action_create_cell_mode,         # 单元格模式
            self.action_grid_drag_mode,           # 🆕 网格拖拽模式
            self.action_compare_visualization,    # 对比可视化
            self.action_logical_visualization,    # 🆕 逻辑可视化
        ))

        # 🆕 第三组：表格工具
        table_toolbar = self.addToolBar("表格工具")
        table_toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        table_toolbar.setIconSize(icon_size)

        utils.addActions(table_toolbar, (
            self.action_quick_table,      # 快速生成表格
            self.action_analyze_table,    # 分析表格结构
        ))

        # 🆕 添加分隔符
        table_toolbar.addSeparator()

        # 🆕 创建表格优化组件
        self._create_table_optimization_widget(table_toolbar)

        # 🆕 第四组：对齐工具
        align_toolbar = self.addToolBar("对齐工具")
        align_toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        align_toolbar.setIconSize(icon_size)
        
        # 添加应用对齐按钮
        align_toolbar.addAction(self.action_apply_alignment)
        
        # 创建一个垂直布局的小部件来容纳拆分按钮和容差控件
        split_container = QtWidgets.QWidget()
        split_layout = QtWidgets.QVBoxLayout(split_container)
        split_layout.setContentsMargins(2, 2, 2, 2)
        split_layout.setSpacing(2)
        
        # 创建一个水平布局来放置拆分按钮
        split_buttons_layout = QtWidgets.QHBoxLayout()
        split_buttons_layout.setContentsMargins(0, 0, 0, 0)
        split_buttons_layout.setSpacing(2)
        
        # 创建按钮并添加到布局
        split_row_btn = QtWidgets.QToolButton()
        split_row_btn.setDefaultAction(self.action_split_row)
        split_row_btn.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        split_col_btn = QtWidgets.QToolButton()
        split_col_btn.setDefaultAction(self.action_split_column)
        split_col_btn.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        
        split_buttons_layout.addWidget(split_row_btn)
        split_buttons_layout.addWidget(split_col_btn)
        
        # 添加按钮布局到容器
        split_layout.addLayout(split_buttons_layout)
        
        # 添加容差控件到容器，位于按钮下方
        self.alignment_tool_widget = AlignmentToolWidget(self)
        split_layout.addWidget(self.alignment_tool_widget)
        
        # 将整个容器添加到工具栏
        align_toolbar.addWidget(split_container)
        
        # 添加分隔符
        align_toolbar.addSeparator()
        
        utils.addActions(align_toolbar, (
            self.action_align_top,        # 顶端对齐
            self.action_align_bottom,     # 底端对齐
            self.action_align_left,       # 左对齐
            self.action_align_right,      # 右对齐
        ))

        # 🔧 缩放工具栏（移到最后，显示在最右侧）
        zoom_toolbar = self.addToolBar("缩放工具")
        zoom_toolbar.setIconSize(icon_size)  # 🔧 设置图标大小
        zoom_toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)  # 🆕 添加统一样式
        
        # 🆕 添加弹性空间，将缩放控件推到右侧
        spacer = QtWidgets.QWidget()
        spacer.setSizePolicy(QtWidgets.QSizePolicy.Expanding, QtWidgets.QSizePolicy.Preferred)
        zoom_toolbar.addWidget(spacer)
        
        zoom_toolbar.addWidget(self.zoom_widget)

        utils.addActions(zoom_toolbar, (
            self.action_zoom_in,
            self.action_zoom_out,
            self.action_fit_window,
        ))

        # 🆕 第五组：图像旋转工具
        rotate_toolbar = self.addToolBar("图像旋转")
        rotate_toolbar.setToolButtonStyle(Qt.ToolButtonTextUnderIcon)
        rotate_toolbar.setIconSize(icon_size)

        utils.addActions(rotate_toolbar, (
            self.action_rotate_left,   # 向左旋转
            self.action_rotate_right,  # 向右旋转
        ))

        # 🔧 确保缩放工具栏在顶部工具栏区域的最右侧
        self.addToolBarBreak(Qt.TopToolBarArea)

    def _create_table_optimization_widget(self, toolbar):
        """创建表格优化组件"""
        # 创建容器组件
        optimization_container = QtWidgets.QWidget()
        optimization_layout = QtWidgets.QVBoxLayout(optimization_container)
        optimization_layout.setContentsMargins(4, 2, 4, 2)
        optimization_layout.setSpacing(2)

        # 创建优化强度进度条
        strength_label = QtWidgets.QLabel("优化强度:")
        strength_label.setStyleSheet("font-size: 10px; color: #666;")
        optimization_layout.addWidget(strength_label)

        self.optimization_strength_slider = QtWidgets.QSlider(Qt.Horizontal)
        self.optimization_strength_slider.setMinimum(0)
        self.optimization_strength_slider.setMaximum(3)
        self.optimization_strength_slider.setValue(2)  # 默认常用角点对齐
        self.optimization_strength_slider.setTickPosition(QtWidgets.QSlider.TicksBelow)
        self.optimization_strength_slider.setTickInterval(1)
        self.optimization_strength_slider.setMinimumWidth(120)
        self.optimization_strength_slider.setToolTip("调整角点对齐的强度级别")

        # 连接滑块变化事件
        self.optimization_strength_slider.valueChanged.connect(self._update_optimization_mode_display)

        optimization_layout.addWidget(self.optimization_strength_slider)

        # 创建优化按钮
        self.optimize_button = QtWidgets.QPushButton()
        self.optimize_button.setMinimumWidth(120)
        self.optimize_button.setMinimumHeight(40)
        self.optimize_button.clicked.connect(self.optimize_current_table)

        # 初始化按钮文本
        self._update_optimization_mode_display()

        optimization_layout.addWidget(self.optimize_button)

        # 将容器添加到工具栏
        toolbar.addWidget(optimization_container)

    def _setup_dock_widgets(self):
        """设置停靠窗口"""
        # ===== 🆕 文件列表停靠窗口（左侧） =====
        # 🆕 创建筛选控件
        self._setup_filter_widgets()

        self.fileSearch = QtWidgets.QLineEdit()
        self.fileSearch.setPlaceholderText("搜索文件名...")
        # 🔧 延迟连接搜索信号，避免初始化时触发

        self.fileListWidget = QtWidgets.QListWidget()
        self.fileListWidget.itemSelectionChanged.connect(self.fileSelectionChanged)

        # 🆕 添加右键菜单支持
        self.fileListWidget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.fileListWidget.customContextMenuRequested.connect(self._show_file_list_context_menu)

        # 创建文件列表布局
        fileListLayout = QtWidgets.QVBoxLayout()
        fileListLayout.setContentsMargins(0, 0, 0, 0)
        fileListLayout.setSpacing(0)
        # 🆕 添加筛选控件到布局
        fileListLayout.addWidget(self.content_type_filter_widget)
        fileListLayout.addWidget(self.quality_filter_widget)
        fileListLayout.addWidget(self.fileSearch)
        fileListLayout.addWidget(self.fileListWidget)

        # 创建文件列表停靠窗口
        self.file_dock = QtWidgets.QDockWidget("文件列表", self)
        self.file_dock.setObjectName("Files")
        fileListWidget = QtWidgets.QWidget()
        fileListWidget.setLayout(fileListLayout)
        self.file_dock.setWidget(fileListWidget)

        # 将文件列表放在左侧
        self.addDockWidget(Qt.LeftDockWidgetArea, self.file_dock)

        # 属性面板作为dock放在右侧
        self.properties_dock = QtWidgets.QDockWidget("表格属性", self)
        self.properties_dock.setObjectName("TableProperties")
        self.properties_dock.setWidget(self.properties_widget)
        self.addDockWidget(Qt.RightDockWidgetArea, self.properties_dock)

        # 设置停靠窗口功能
        dock_features = (
                QtWidgets.QDockWidget.DockWidgetClosable |
                QtWidgets.QDockWidget.DockWidgetMovable |
                QtWidgets.QDockWidget.DockWidgetFloatable
        )

        self.properties_dock.setFeatures(dock_features)

        # 注意：逻辑结构不再是dock，而是中央widget的一部分

    def _setup_filter_widgets(self):
        """设置筛选控件"""
        # 内容类型筛选
        self.content_type_filter_widget = QtWidgets.QWidget()
        content_layout = QtWidgets.QHBoxLayout(self.content_type_filter_widget)
        content_layout.setContentsMargins(5, 2, 5, 2)
        content_layout.setSpacing(5)

        # 添加"类型："标签
        content_label = QtWidgets.QLabel("类型：")
        content_label.setStyleSheet("font-weight: bold;")
        content_layout.addWidget(content_label)

        # 添加"全选"按钮
        self.content_type_select_all_btn = QtWidgets.QPushButton("全选")
        self.content_type_select_all_btn.setMaximumWidth(50)
        self.content_type_select_all_btn.clicked.connect(self._on_content_type_select_all)
        content_layout.addWidget(self.content_type_select_all_btn)

        # 内容类型复选框
        self.content_type_checkboxes = {}
        for filter_name in ["文本", "有线表", "无线表", "待确认"]:
            checkbox = QtWidgets.QCheckBox(filter_name)
            # 🔧 先设置状态，再连接信号，避免初始化时触发
            checkbox.setChecked(self.content_type_filters[filter_name])
            self.content_type_checkboxes[filter_name] = checkbox
            content_layout.addWidget(checkbox)
        content_layout.addStretch()  # 添加弹性空间

        # 质量状态筛选
        self.quality_filter_widget = QtWidgets.QWidget()
        quality_layout = QtWidgets.QHBoxLayout(self.quality_filter_widget)
        quality_layout.setContentsMargins(5, 2, 5, 2)
        quality_layout.setSpacing(5)

        # 添加"质量："标签
        quality_label = QtWidgets.QLabel("质量：")
        quality_label.setStyleSheet("font-weight: bold;")
        quality_layout.addWidget(quality_label)

        # 添加"全选"按钮
        self.quality_select_all_btn = QtWidgets.QPushButton("全选")
        self.quality_select_all_btn.setMaximumWidth(50)
        self.quality_select_all_btn.clicked.connect(self._on_quality_select_all)
        quality_layout.addWidget(self.quality_select_all_btn)

        # 质量状态复选框
        self.quality_checkboxes = {}
        for filter_name in ["合格", "准合格", "不合格", "待校准"]:
            checkbox = QtWidgets.QCheckBox(filter_name)
            # 🔧 先设置状态，再连接信号，避免初始化时触发
            checkbox.setChecked(self.quality_filters[filter_name])
            self.quality_checkboxes[filter_name] = checkbox
            quality_layout.addWidget(checkbox)
        quality_layout.addStretch()  # 添加弹性空间

    # 🔧 表格类型选择和快速样本标注已迁移到属性面板中

    def _connect_signals(self):
        """连接信号槽"""
        # 缩放控件信号
        self.zoom_widget.valueChanged.connect(self.paint_canvas)

        # Canvas缩放信号
        self.canvas.zoomRequest.connect(self.zoom_request)
        
        # ===== 🆕 添加滚动信号连接 =====
        self.canvas.scrollRequest.connect(self.scrollRequest)

        # ===== 🆕 连接历史管理器信号 =====
        self.history_manager.history_changed.connect(self._update_undo_redo_actions)
        self.history_manager.command_executed.connect(self._on_command_executed)
        self.history_manager.command_undone.connect(self._on_command_undone)
        self.history_manager.command_redone.connect(self._on_command_redone)
        LOGGER.debug("历史管理器信号连接完成")

        # ===== 🆕 连接模式管理器信号 =====
        self.mode_manager.mode_changed.connect(self._on_mode_changed)
        LOGGER.debug("模式管理器信号连接完成")

        # ===== 🔧 连接筛选控件信号 =====
        self._connect_filter_signals()
        LOGGER.debug("筛选控件信号连接完成")

    def _connect_filter_signals(self):
        """连接筛选控件信号，确保在UI完全初始化后调用"""
        # 🔧 连接搜索框信号
        if hasattr(self, 'fileSearch') and self.fileSearch:
            self.fileSearch.textChanged.connect(self.fileSearchChanged)
            LOGGER.debug("搜索框信号连接完成")

        # 连接内容类型筛选信号
        for filter_name, checkbox in self.content_type_checkboxes.items():
            # 🔧 使用functools.partial避免lambda闭包问题
            from functools import partial
            checkbox.stateChanged.connect(partial(self._on_content_type_filter_changed, filter_name))

        # 连接质量状态筛选信号
        for filter_name, checkbox in self.quality_checkboxes.items():
            # 🔧 使用functools.partial避免lambda闭包问题
            from functools import partial
            checkbox.stateChanged.connect(partial(self._on_quality_filter_changed, filter_name))

        LOGGER.debug("筛选控件信号连接完成")

        # ===== 🆕 连接属性面板信号 =====
        self.properties_widget.property_changed.connect(self._on_property_changed)
        LOGGER.debug("属性面板信号连接完成")

        # ===== 🆕 安装键盘事件过滤器 =====
        self.canvas.installEventFilter(self)
        self.canvas.setFocusPolicy(Qt.StrongFocus)
        LOGGER.debug("键盘事件过滤器安装完成，Canvas焦点策略已设置")

    def _split_cells_by_row(self):
        """按行拆分选中单元格"""
        controller = self.multi_table_controller.get_active_controller()
        if controller:
            controller.split_selected_cells_by_row()
            self.analyze_table_structure()  # 刷新逻辑结构

    def _split_cells_by_column(self):
        """按列拆分选中单元格"""
        controller = self.multi_table_controller.get_active_controller()
        if controller:
            controller.split_selected_cells_by_column()
            self.analyze_table_structure()  # 刷新逻辑结构

    def _on_property_changed(self, property_name, property_value):
        """处理属性面板变更信号"""
        if property_name == "sample_quality":
            LOGGER.debug(f"样本质量已更新: {property_value}")
            # 保存质量状态到文件
            if self.filename:
                self._save_sample_quality_to_file(self.filename, property_value)
            # 更新文件列表显示
            self._refresh_current_file_status()
        elif property_name == "auto_save_and_next":
            # 处理自动保存并切换到下一张的请求
            LOGGER.debug(f"收到自动保存并切换请求，样本质量: {property_value}")
            # 先保存当前文件
            self.save_file()
            # 注意：save_file方法内部已经包含了自动切换到下一张的逻辑
        elif property_name == "api_labeling_requested":
            # 🆕 处理API辅助打标请求
            self._handle_api_labeling_request(property_value)
        else:
            # 其他属性变更传递给活动的表格控制器
            active_controller = self.multi_table_controller.get_active_controller()

            # 🔧 修复：如果没有活动控制器但是设置表格类型，自动创建一个表格控制器
            if not active_controller and property_name == "table_type":
                LOGGER.debug(f"没有活动控制器，为表格类型设置创建默认表格控制器")
                # 创建默认表格区域
                default_region = (50, 50, 950, 750)
                table_id = self.multi_table_controller.create_table_instance(default_region)
                active_controller = self.multi_table_controller.get_active_controller()
                LOGGER.debug(f"已创建默认表格控制器 ID={table_id}")

            if active_controller:
                # 🔧 修复：检查方法是否存在再调用
                if hasattr(active_controller, 'handle_property_change'):
                    active_controller.handle_property_change(property_name, property_value)
                else:
                    LOGGER.warning(f"TableController不支持属性变更: {property_name} = {property_value}")
                    # 对于不支持的属性变更，暂时忽略或记录日志
            else:
                LOGGER.warning(f"无法处理属性变更，没有活动的表格控制器: {property_name} = {property_value}")

    def _get_annotation_json_path(self, image_filename):
        """获取标注JSON文件路径，支持_table_annotation和同名JSON文件

        Args:
            image_filename: 图片文件路径

        Returns:
            str: 找到的JSON文件路径，优先返回_table_annotation格式
        """
        base_name = osp.splitext(image_filename)[0]

        # 优先使用_table_annotation格式
        table_annotation_json = f"{base_name}_table_annotation.json"
        if osp.exists(table_annotation_json):
            return table_annotation_json

        # 回退到同名JSON文件
        same_name_json = f"{base_name}.json"
        if osp.exists(same_name_json):
            return same_name_json

        # 都不存在时，返回默认的_table_annotation格式（用于新建）
        return table_annotation_json

    def _save_sample_quality_to_file(self, image_filename, quality):
        """保存样本质量状态到对应的标注文件"""
        try:
            json_filename = self._get_annotation_json_path(image_filename)
            
            # 读取现有数据或创建新数据
            data = {}
            if osp.exists(json_filename):
                import json
                with open(json_filename, 'r', encoding='utf-8') as f:
                    loaded_data = json.load(f)
                
                # 🔧 修复：处理不同的数据格式
                if isinstance(loaded_data, list):
                    # 旧格式（单表格列表）-> 转换为新格式
                    data = {
                        'quality': quality,
                        'tables': loaded_data if loaded_data else []
                    }
                    LOGGER.debug(f"旧格式(列表)转换为新格式，表格数量: {len(loaded_data)}")
                elif isinstance(loaded_data, dict):
                    if 'tables' in loaded_data:
                        # 新格式（MultiTableController）-> 直接更新quality
                        data = loaded_data.copy()
                        data['quality'] = quality
                        LOGGER.debug(f"新格式更新quality: {quality}")
                    elif 'cells' in loaded_data or 'table_ind' in loaded_data:
                        # 旧格式（单表格字典）-> 转换为新格式
                        # 这是最重要的修复！将单表格字典包装到tables数组中
                        data = {
                            'quality': quality,
                            'tables': [loaded_data]  # 将单表格字典包装为数组
                        }
                        LOGGER.debug(f"旧格式(单表格字典)转换为新格式，单元格数量: {len(loaded_data.get('cells', []))}")
                    else:
                        # 其他字典格式，保持原有数据并添加quality
                        data = loaded_data.copy()
                        data['quality'] = quality
                        LOGGER.debug(f"其他字典格式添加quality: {quality}")
                else:
                    # 未知格式，创建新数据
                    data = {'quality': quality, 'tables': []}
                    LOGGER.warning(f"未知格式，创建新数据结构")
            else:
                # 文件不存在，创建新数据
                data = {'quality': quality, 'tables': []}
                LOGGER.debug(f"创建新文件，质量: {quality}")
            
            # 保存回文件
            import json
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

            LOGGER.debug(f"样本质量已保存到文件: {quality}")

            # 🚀 同步更新索引中的质量状态
            self._update_quality_in_index(image_filename, quality)

        except Exception as e:
            LOGGER.error(f"保存样本质量失败: {e}")
            import traceback
            traceback.print_exc()

    def _update_quality_in_index(self, image_filename, new_quality):
        """🚀 更新索引中的质量信息"""
        if not self.file_index or "files" not in self.file_index:
            LOGGER.debug("索引不存在，跳过质量状态更新")
            return

        # 查找文件在索引中的key
        index_key = self._find_file_in_index(image_filename)

        if index_key:
            # 获取旧质量用于统计更新
            old_quality = self.file_index["files"][index_key]["quality"]

            # 准备新的状态数据
            new_status = {
                "quality": new_quality,
                "has_annotation": True
            }

            # 判断当前是父目录还是子目录
            if hasattr(self, 'directory_aggregator') and self.directory_aggregator:
                # 父目录：通过聚合器更新
                success = self.directory_aggregator.update_file_status(index_key, new_status)
                if success:
                    # 🔧 优化：直接更新内存中的聚合索引，避免重新聚合
                    if self.file_index and "files" in self.file_index and index_key in self.file_index["files"]:
                        # 更新内存中的聚合索引
                        self.file_index["files"][index_key]["quality"] = new_quality
                        self.file_index["files"][index_key]["has_annotation"] = True

                        # 更新统计计数
                        if "stats" in self.file_index and "quality_counts" in self.file_index["stats"]:
                            self.file_index["stats"]["quality_counts"][old_quality] -= 1
                            self.file_index["stats"]["quality_counts"][new_quality] += 1

                        # 重新填充缓存
                        self._populate_metadata_cache_from_index()
                        LOGGER.debug(f"通过聚合器更新质量状态（内存优化）: {osp.basename(image_filename)} {old_quality} -> {new_quality}")
                    else:
                        # 兜底：如果内存索引不可用，才重新聚合
                        LOGGER.warning(f"内存索引不可用，重新聚合: {osp.basename(image_filename)}")
                        try:
                            updated_index = self.directory_aggregator.aggregate_subdirectories()
                            if updated_index:
                                self.file_index = updated_index
                                self._populate_metadata_cache_from_index()
                                LOGGER.debug(f"重新聚合索引完成: {osp.basename(image_filename)}")
                        except Exception as e:
                            LOGGER.error(f"重新聚合索引时出错: {e}")
                else:
                    LOGGER.warning(f"聚合器更新失败: {osp.basename(image_filename)}")
            else:
                # 子目录：直接更新本地索引
                self.file_index["files"][index_key]["quality"] = new_quality
                self.file_index["files"][index_key]["has_annotation"] = True

                # 更新统计计数
                if "stats" in self.file_index and "quality_counts" in self.file_index["stats"]:
                    self.file_index["stats"]["quality_counts"][old_quality] -= 1
                    self.file_index["stats"]["quality_counts"][new_quality] += 1

                # 保存索引文件
                if self.file_index_manager:
                    self.file_index_manager.save_index(self.file_index)

                LOGGER.debug(f"直接更新质量状态: {osp.basename(image_filename)} {old_quality} -> {new_quality}")
        else:
            LOGGER.warning(f"在索引中未找到文件，无法更新质量状态: {osp.basename(image_filename)}")

    def _update_content_type_in_index_after_save(self, image_filename, saved_data):
        """🚀 保存文件后同步更新索引中的内容类型"""
        if not self.file_index or "files" not in self.file_index:
            LOGGER.debug("文件索引未初始化，跳过内容类型更新")
            return

        # 从保存的数据中提取内容类型
        new_content_type = self._extract_content_type_from_saved_data(saved_data)
        if new_content_type is None:
            LOGGER.debug("无法从保存数据中提取内容类型")
            return

        # 🔧 修复：将content_type_map定义移到方法开头，避免变量作用域问题
        content_type_map = {0: "文本", 1: "有线表", 2: "无线表", -1: "待确认"}

        # 查找文件在索引中的键
        index_key = self._find_file_in_index(image_filename)
        if index_key:
            old_content_type = self.file_index["files"][index_key]["content_type"]

            # 只有当内容类型发生变化时才更新
            if old_content_type != new_content_type:
                # 更新文件数据
                self.file_index["files"][index_key]["content_type"] = new_content_type
                self.file_index["files"][index_key]["has_annotation"] = True
                self.file_index["files"][index_key]["json_last_modified"] = datetime.datetime.now().isoformat()

                # 更新统计计数
                old_type_name = content_type_map.get(old_content_type, "待确认")
                new_type_name = content_type_map.get(new_content_type, "待确认")

                if old_type_name in self.file_index["stats"]["content_type_counts"]:
                    self.file_index["stats"]["content_type_counts"][old_type_name] -= 1
                if new_type_name in self.file_index["stats"]["content_type_counts"]:
                    self.file_index["stats"]["content_type_counts"][new_type_name] += 1

                # 准备新的状态数据
                new_status = {
                    "content_type": new_content_type,
                    "has_annotation": True
                }

                # 判断当前是父目录还是子目录
                if hasattr(self, 'directory_aggregator') and self.directory_aggregator:
                    # 父目录：通过聚合器更新
                    success = self.directory_aggregator.update_file_status(index_key, new_status)
                    if success:
                        # 🔧 优化：直接更新内存中的聚合索引，避免重新聚合
                        if self.file_index and "files" in self.file_index and index_key in self.file_index["files"]:
                            # 更新内存中的聚合索引
                            self.file_index["files"][index_key]["content_type"] = new_content_type
                            self.file_index["files"][index_key]["has_annotation"] = True
                            self.file_index["files"][index_key]["json_last_modified"] = datetime.datetime.now().isoformat()

                            # 更新统计计数
                            if "stats" in self.file_index and "content_type_counts" in self.file_index["stats"]:
                                self.file_index["stats"]["content_type_counts"][old_type_name] -= 1
                                self.file_index["stats"]["content_type_counts"][new_type_name] += 1

                            # 重新填充缓存
                            self._populate_metadata_cache_from_index()
                            LOGGER.debug(f"通过聚合器更新内容类型（内存优化）: {osp.basename(image_filename)} {old_type_name} -> {new_type_name}")
                        else:
                            LOGGER.warning(f"内存索引不可用，跳过内容类型更新: {osp.basename(image_filename)}")
                    else:
                        LOGGER.warning(f"聚合器更新内容类型失败: {osp.basename(image_filename)}")
                else:
                    # 子目录：直接保存索引文件
                    if self.file_index_manager:
                        self.file_index_manager.save_index(self.file_index)

                LOGGER.debug(f"索引中内容类型已更新: {osp.basename(image_filename)} {old_type_name} -> {new_type_name}")
            else:
                LOGGER.debug(f"内容类型未变化，跳过更新: {osp.basename(image_filename)} = {content_type_map.get(new_content_type, '待确认')}")
        else:
            LOGGER.warning(f"在索引中未找到文件，无法更新内容类型: {osp.basename(image_filename)}")

    def _extract_content_type_from_saved_data(self, saved_data):
        """从保存的数据中提取内容类型"""
        try:
            if isinstance(saved_data, dict):
                # 检查是否有type字段（主要格式）
                if 'type' in saved_data:
                    return saved_data['type']
                # 检查是否有table_type字段（备用格式）
                elif 'table_type' in saved_data:
                    return saved_data['table_type']
                # 检查是否有tables字段（多表格格式）
                elif 'tables' in saved_data and saved_data['tables']:
                    first_table = saved_data['tables'][0]
                    if isinstance(first_table, dict):
                        if 'type' in first_table:
                            return first_table['type']
                        elif 'table_type' in first_table:
                            return first_table['table_type']
            elif isinstance(saved_data, list) and saved_data:
                # 列表格式，取第一个表格的类型
                first_table = saved_data[0]
                if isinstance(first_table, dict):
                    if 'type' in first_table:
                        return first_table['type']
                    elif 'table_type' in first_table:
                        return first_table['table_type']

            return None
        except Exception as e:
            LOGGER.warning(f"提取内容类型失败: {e}")
            return None

    def _find_file_in_index(self, image_filename):
        """在索引中查找文件对应的键"""
        if not self.file_index or "files" not in self.file_index:
            return None

        # 直接匹配
        if image_filename in self.file_index["files"]:
            return image_filename

        # 🚀 优先使用basename_map进行O(1)查找
        if "basename_map" in self.file_index:
            base_name = osp.basename(image_filename)
            if base_name in self.file_index["basename_map"]:
                mapped_path = self.file_index["basename_map"][base_name]
                if mapped_path in self.file_index["files"]:
                    return mapped_path

        # 🔧 兜底：通过basename线性搜索（向后兼容）
        base_name = osp.basename(image_filename)
        for indexed_filename in self.file_index["files"]:
            if osp.basename(indexed_filename) == base_name:
                return indexed_filename

        return None

    def _load_sample_quality_from_file(self, image_filename):
        """🚀 优先从索引加载样本质量状态，兜底从文件读取"""
        quality = "待校准"  # 默认值

        try:
            # 🚀 优先从索引获取质量状态
            quality = self._get_quality_from_index_or_file(image_filename)

            # 🔧 修复：如果索引中没有找到或返回None，才从文件读取
            if quality is None:
                json_filename = self._get_annotation_json_path(image_filename)

                if osp.exists(json_filename):
                    import json
                    with open(json_filename, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # 🔧 修复：处理不同的数据格式
                    if isinstance(data, dict) and 'quality' in data:
                        # 新格式（有quality字段）
                        quality = data.get('quality', '待校准')
                    elif isinstance(data, (list, dict)):
                        # 旧格式（没有quality字段），使用默认值
                        quality = "待校准"
                else:
                    # 文件不存在，使用默认值
                    quality = "待校准"

        except Exception as e:
            LOGGER.error(f"加载样本质量失败: {e}")
            import traceback
            traceback.print_exc()
            quality = "待校准"

        # 🔧 修复：无论质量状态是什么，都要更新UI显示
        if hasattr(self, 'properties_widget') and self.properties_widget:
            # 更新隐藏的ComboBox
            self.properties_widget.quality_combo.setCurrentText(quality)
            # 高亮当前选中的按钮
            self._update_quality_button_state(quality)

        LOGGER.debug(f"已加载样本质量: {quality}")
        return quality
            
    def _update_quality_button_state(self, quality):
        """更新样本质量按钮状态（属性面板中的按钮）"""
        if not hasattr(self, 'properties_widget') or not self.properties_widget:
            return

        if not hasattr(self.properties_widget, 'btn_quality_good'):
            return

        # 重置所有按钮样式为基础样式
        self.properties_widget.btn_quality_good.setStyleSheet("""
            QPushButton {
                background-color: #c8e6c9;
                color: #2e7d32;
                padding: 5px;
                border-radius: 4px;
                font-weight: bold;
                border: none;
            }
        """)
        self.properties_widget.btn_quality_quasi.setStyleSheet("""
            QPushButton {
                background-color: #e1f5fe;
                color: #0277bd;
                padding: 5px;
                border-radius: 4px;
                font-weight: bold;
                border: none;
            }
        """)
        self.properties_widget.btn_quality_bad.setStyleSheet("""
            QPushButton {
                background-color: #ffcdd2;
                color: #c62828;
                padding: 5px;
                border-radius: 4px;
                font-weight: bold;
                border: none;
            }
        """)
        self.properties_widget.btn_quality_pending.setStyleSheet("""
            QPushButton {
                background-color: #fff9c4;
                color: #f57f17;
                padding: 5px;
                border-radius: 4px;
                font-weight: bold;
                border: none;
            }
        """)

        # 为选中的按钮添加外层光环效果
        glow_style = """
            QPushButton {
                border: 2px solid %s;
                box-shadow: 0 0 5px %s;
            }
        """

        # 根据不同的质量状态设置不同的光环颜色
        if quality == "合格":
            self.properties_widget.btn_quality_good.setStyleSheet(
                self.properties_widget.btn_quality_good.styleSheet() +
                glow_style % ('#2e7d32', '#2e7d32')
            )
        elif quality == "准合格":
            self.properties_widget.btn_quality_quasi.setStyleSheet(
                self.properties_widget.btn_quality_quasi.styleSheet() +
                glow_style % ('#0277bd', '#0277bd')
            )
        elif quality == "不合格":
            self.properties_widget.btn_quality_bad.setStyleSheet(
                self.properties_widget.btn_quality_bad.styleSheet() +
                glow_style % ('#c62828', '#c62828')
            )
        elif quality == "待校准":
            self.properties_widget.btn_quality_pending.setStyleSheet(
                self.properties_widget.btn_quality_pending.styleSheet() +
                glow_style % ('#f57f17', '#f57f17')
            )

    # ===== 🆕 API辅助打标功能 =====

    def _handle_api_labeling_request(self, api_type):
        """处理API辅助打标请求"""
        if not self.filename:
            self._show_api_error("请先打开图片文件")
            return
            
        LOGGER.debug(f"开始处理API辅助打标请求: {api_type}")
        
        # 显示加载状态
        self._show_api_loading(f"正在调用{api_type.upper()}API识别表格...")
        
        # 异步调用API（避免阻塞UI）
        import threading
        api_thread = threading.Thread(
            target=self._execute_api_call,
            args=(api_type, self.filename)
        )
        api_thread.daemon = True
        api_thread.start()
    
    def _execute_api_call(self, api_type, image_path):
        """在后台线程执行API调用"""
        try:
            LOGGER.debug(f"后台线程开始执行API调用: {api_type}")

            # 🆕 首先检查是否存在API缓存文件
            base_name = osp.splitext(image_path)[0]
            cache_file = f"{base_name}-api-resp.json"

            if osp.exists(cache_file):
                LOGGER.debug(f"发现API缓存文件，直接使用: {cache_file}")
                try:
                    import json
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        api_response = json.load(f)
                    LOGGER.debug("成功加载API缓存数据")
                except Exception as e:
                    LOGGER.error(f"读取API缓存文件失败: {e}")
                    # 缓存文件损坏，继续进行网络请求
                    api_response = None
            else:
                api_response = None

            # 如果没有缓存或缓存读取失败，则进行网络API调用
            if api_response is None:
                # 创建API客户端
                if api_type == "textin":
                    # 从环境变量获取API密钥
                    try:
                        # 使用TextInAPIClient的from_env方法从环境变量获取密钥
                        client = TextInAPIClient.from_env()
                        LOGGER.debug("成功从环境变量获取TextIn API密钥")
                    except APIError as e:
                        LOGGER.error(f"获取API密钥失败: {e}")
                        raise
                else:
                    raise APIError(f"不支持的API类型: {api_type}")

                # 调用API
                LOGGER.debug("开始调用API...")
                api_response = client.recognize_table(image_path)

                # 保存原始响应
                self._save_api_response(image_path, api_response)
            else:
                LOGGER.debug("使用缓存的API响应，跳过网络请求")
            
            # 转换响应数据
            LOGGER.debug("开始转换API响应...")
            if api_type == "textin":
                converter = TextInResponseConverter()
            else:
                raise APIError(f"不支持的转换器类型: {api_type}")
            
            # 获取图像尺寸
            from PIL import Image
            with Image.open(image_path) as img:
                image_size = img.size  # (width, height)
            
            # 转换数据为标准格式
            annotation_data = converter.convert(api_response, image_size)
            
            # 注意：现在annotation_data是MultiTableController格式的列表，不需要设置imagePath
            
            # 在主线程中更新UI
            QtCore.QMetaObject.invokeMethod(
                self,
                "_apply_api_results",
                QtCore.Qt.QueuedConnection,
                QtCore.Q_ARG(list, annotation_data),  # 改为list类型
                QtCore.Q_ARG(str, api_type)
            )
            
        except APIError as e:
            LOGGER.error(f"API调用失败: {e}")
            QtCore.QMetaObject.invokeMethod(
                self,
                "_show_api_error",
                QtCore.Qt.QueuedConnection,
                QtCore.Q_ARG(str, f"API调用失败: {e.message}")
            )
        except Exception as e:
            LOGGER.error(f"API调用异常: {e}")
            import traceback
            traceback.print_exc()
            QtCore.QMetaObject.invokeMethod(
                self,
                "_show_api_error",
                QtCore.Qt.QueuedConnection,
                QtCore.Q_ARG(str, f"API调用异常: {str(e)}")
            )
    
    @QtCore.pyqtSlot(list, str)
    def _apply_api_results(self, annotation_data, api_type):
        """在主线程中应用API识别结果"""
        try:
            # annotation_data现在是MultiTableController格式的列表
            tables = annotation_data
            LOGGER.debug(f"开始应用API识别结果，表格数量: {len(tables)}")

            if not tables:
                self._show_api_error("API未识别到任何表格")
                return

            # 🆕 检查单元格总数，避免界面卡顿
            table_count = len(tables)
            total_cells = sum(len(table.get('cells', [])) for table in tables)

            if total_cells > 1000:
                # 单元格过多，不应用可视化，弹出警告
                LOGGER.warning(f"API识别结果单元格过多({total_cells}个)，跳过可视化应用")

                # 弹出警告对话框
                QtWidgets.QMessageBox.warning(
                    self,
                    "单元格数量过多",
                    f"API识别到 {table_count} 个表格，共 {total_cells} 个单元格。\n\n"
                    f"为避免界面卡顿，建议放弃该样本的打标工作。\n"
                    f"如需处理，请考虑使用其他工具或手动分割图片。"
                )

                # 显示错误状态
                self._show_api_error(f"跳过可视化：单元格过多({total_cells}个，超过1000个限制)")
                return

            # 单元格数量合理，正常应用可视化
            # 清除现有表格数据
            self.multi_table_controller.clear_all_tables()

            # 使用标准的加载逻辑
            self._create_table_from_api_data(annotation_data)

            # 刷新界面
            self._refresh_ui_after_api_labeling()

            # 显示成功状态
            self._show_api_success(f"成功识别{table_count}个表格，共{total_cells}个单元格")

            LOGGER.debug(f"API识别结果应用完成")
            
        except Exception as e:
            LOGGER.error(f"应用API结果失败: {e}")
            import traceback
            traceback.print_exc()
            self._show_api_error(f"应用识别结果失败: {str(e)}")
    
    def _create_table_from_api_data(self, annotation_data):
        """根据API数据创建表格
        
        Args:
            annotation_data: TableLabelMe标注格式的数据
        """
        try:
            # 保存临时标注文件
            temp_json = os.path.splitext(self.filename)[0] + '.json'
            with open(temp_json, 'w', encoding='utf-8') as f:
                json.dump(annotation_data, f, ensure_ascii=False, indent=2)
            
            LOGGER.debug(f"已保存临时标注文件: {temp_json}")
            
            # 使用标准的加载逻辑
            self.load_file(temp_json)
            
            # 删除临时文件
            os.remove(temp_json)
            LOGGER.debug("已删除临时标注文件")
            
        except Exception as e:
            LOGGER.error(f"应用API识别结果失败: {e}")
            raise
    
    def _create_canvas_shape_from_cell_data(self, cell_data):
        """根据单元格数据创建Canvas形状"""
        try:
            # 获取坐标点
            points = cell_data.get('points', [])
            if len(points) < 2:
                LOGGER.warning(f"单元格坐标点不足: {points}")
                return None
            
            # 转换为Canvas格式的点
            shape_points = [QtCore.QPointF(points[0][0], points[0][1]),
                           QtCore.QPointF(points[1][0], points[1][1])]
            
            # 创建TableCellShape
            shape = TableCellShape()
            shape.points = shape_points
            shape.label = cell_data.get('label', 'cell')
            shape.shape_type = 'table_cell'
            shape.closed = True
            
            # 设置表格属性
            table_properties = cell_data.get('table_properties', {})
            shape.table_properties = table_properties.copy()
            
            # 确保有默认属性
            if 'table_id' not in shape.table_properties:
                shape.table_properties['table_id'] = 0
            if 'cell_text' not in shape.table_properties:
                shape.table_properties['cell_text'] = table_properties.get('cell_text', '')
            
            LOGGER.debug(f"创建单元格形状: {shape.label}")
            return shape
            
        except Exception as e:
            LOGGER.error(f"创建单元格形状失败: {e}")
            return None
    
    def _save_api_response(self, image_path, api_response):
        """保存API原始响应到文件"""
        try:
            base_name = osp.splitext(image_path)[0]
            response_file = f"{base_name}-api-resp.json"
            
            with open(response_file, 'w', encoding='utf-8') as f:
                json.dump(api_response, f, ensure_ascii=False, indent=2)
            
            LOGGER.debug(f"API响应已保存到: {response_file}")
            
        except Exception as e:
            LOGGER.error(f"保存API响应失败: {e}")
    
    def _refresh_ui_after_api_labeling(self):
        """API打标后刷新界面"""
        # 重绘画布
        self.canvas.update()

        # 刷新结构视图
        self._auto_refresh_all_structure_views()

        # 🆕 自动切换到默认模式（选择模式）
        self._reset_to_default_mode("API识别完成")

        # 更新状态栏
        total_tables = self.multi_table_controller.get_table_count()
        self.statusBar().showMessage(f"API识别完成，共{total_tables}个表格")
    
    @QtCore.pyqtSlot(str)
    def _show_api_loading(self, message):
        """显示API加载状态"""
        if hasattr(self, 'properties_widget') and self.properties_widget:
            self.properties_widget.show_api_loading(message)
        
        self.statusBar().showMessage(message)
    
    @QtCore.pyqtSlot(str)
    def _show_api_success(self, message):
        """显示API成功状态"""
        if hasattr(self, 'properties_widget') and self.properties_widget:
            self.properties_widget.show_api_success(message)
        
        self.statusBar().showMessage(message, 5000)
    
    @QtCore.pyqtSlot(str)
    def _show_api_error(self, message):
        """显示API错误状态"""
        if hasattr(self, 'properties_widget') and self.properties_widget:
            self.properties_widget.show_api_error(message)
        
        self.statusBar().showMessage(message, 5000)
        
        # 同时弹出错误对话框
        QtWidgets.QMessageBox.warning(self, "API调用失败", message)

    # ===== 🆕 批量API打标功能 =====

    def batch_api_labeling(self):
        """批量API辅助打标功能"""
        # 检查是否有文件列表
        if not hasattr(self, 'imageList') or not self.imageList:
            QtWidgets.QMessageBox.warning(self, "警告", "请先打开图片目录")
            return

        # 统计需要处理的图片数量（排除已有缓存的）
        total_images = len(self.imageList)
        images_to_process = []

        for image_path in self.imageList:
            base_name = osp.splitext(image_path)[0]
            cache_file = f"{base_name}-api-resp.json"
            if not osp.exists(cache_file):
                images_to_process.append(image_path)

        images_to_process_count = len(images_to_process)

        # 创建确认对话框
        dialog = QtWidgets.QMessageBox(self)
        dialog.setWindowTitle("批量API辅助打标确认")
        dialog.setIcon(QtWidgets.QMessageBox.Question)

        # 设置主要文本
        dialog.setText(f"即将对 {images_to_process_count} 张图片进行批量API打标")

        # 设置详细信息文本，包含红色警告
        detail_text = f"""
总图片数量: {total_images} 张
需要处理: {images_to_process_count} 张
已有缓存: {total_images - images_to_process_count} 张

<span style="color: red; font-weight: bold;">
⚠️ 警告：API打标会消耗大量点数，请谨慎使用该功能！
</span>

确认要继续吗？
        """
        dialog.setDetailedText(detail_text.strip())
        dialog.setInformativeText("点击 'Show Details...' 查看详细信息")

        # 设置按钮
        dialog.setStandardButtons(QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No)
        dialog.setDefaultButton(QtWidgets.QMessageBox.No)

        # 显示对话框并获取用户选择
        result = dialog.exec_()

        if result == QtWidgets.QMessageBox.Yes:
            # 用户确认，开始批量处理
            self._start_batch_api_processing(images_to_process)
        else:
            LOGGER.debug("用户取消了批量API打标操作")

    def _start_batch_api_processing(self, images_to_process):
        """开始批量API处理"""
        if not images_to_process:
            QtWidgets.QMessageBox.information(self, "提示", "所有图片都已有API缓存，无需处理")
            return

        # 初始化批量处理状态
        self.batch_processing_state = {
            'is_processing': True,
            'total_count': len(images_to_process),
            'current_index': 0,
            'processed_count': 0,
            'failed_count': 0,
            'images_list': images_to_process
        }

        # 更新UI状态
        self._update_batch_processing_ui(True)

        # 启动后台线程
        import threading
        self.batch_thread = threading.Thread(
            target=self._execute_batch_api_processing,
            args=(images_to_process,)
        )
        self.batch_thread.daemon = True
        self.batch_thread.start()

        LOGGER.info(f"开始批量API处理，共 {len(images_to_process)} 张图片")

    def _execute_batch_api_processing(self, images_to_process):
        """在后台线程执行批量API处理"""
        try:
            for index, image_path in enumerate(images_to_process):
                # 检查是否需要停止处理
                if not getattr(self, 'batch_processing_state', {}).get('is_processing', False):
                    LOGGER.debug("批量处理被中断")
                    break

                # 更新当前处理索引
                self.batch_processing_state['current_index'] = index

                # 更新进度显示
                QtCore.QMetaObject.invokeMethod(
                    self,
                    "_update_batch_progress",
                    QtCore.Qt.QueuedConnection,
                    QtCore.Q_ARG(int, index + 1),
                    QtCore.Q_ARG(int, len(images_to_process)),
                    QtCore.Q_ARG(str, osp.basename(image_path))
                )

                try:
                    # 调用单个图片的API处理（复用现有逻辑）
                    self._execute_single_api_call_for_batch("textin", image_path)
                    self.batch_processing_state['processed_count'] += 1
                    LOGGER.debug(f"批量处理成功: {image_path}")

                except Exception as e:
                    self.batch_processing_state['failed_count'] += 1
                    LOGGER.error(f"批量处理失败: {image_path}, 错误: {e}")

                # 添加短暂延迟，避免API请求过于频繁
                import time
                time.sleep(0.5)

            # 批量处理完成
            QtCore.QMetaObject.invokeMethod(
                self,
                "_finish_batch_processing",
                QtCore.Qt.QueuedConnection
            )

        except Exception as e:
            LOGGER.error(f"批量API处理异常: {e}")
            QtCore.QMetaObject.invokeMethod(
                self,
                "_finish_batch_processing",
                QtCore.Qt.QueuedConnection,
                QtCore.Q_ARG(str, f"批量处理异常: {str(e)}")
            )

    def _execute_single_api_call_for_batch(self, api_type, image_path):
        """为批量处理执行单个API调用（不更新UI）"""
        try:
            # 检查缓存（与单次调用逻辑一致）
            base_name = osp.splitext(image_path)[0]
            cache_file = f"{base_name}-api-resp.json"

            if osp.exists(cache_file):
                LOGGER.debug(f"批量处理：使用缓存 {cache_file}")
                return  # 跳过已有缓存的文件

            # 创建API客户端
            if api_type == "textin":
                from labelme.api import TextInAPIClient, APIError
                client = TextInAPIClient.from_env()
            else:
                raise APIError(f"不支持的API类型: {api_type}")

            # 调用API
            api_response = client.recognize_table(image_path)

            # 保存原始响应
            self._save_api_response(image_path, api_response)

            LOGGER.debug(f"批量处理：API调用成功 {image_path}")

        except Exception as e:
            LOGGER.error(f"批量处理单个文件失败 {image_path}: {e}")
            raise

    @QtCore.pyqtSlot(int, int, str)
    def _update_batch_progress(self, current, total, filename):
        """更新批量处理进度显示"""
        if hasattr(self, 'properties_widget') and self.properties_widget:
            progress_text = f"正在批量打标 {current}/{total} 张图片..."
            self.properties_widget.show_api_loading(progress_text)

        # 更新状态栏
        self.statusBar().showMessage(f"批量API打标进度: {current}/{total} - {filename}")

    @QtCore.pyqtSlot()
    @QtCore.pyqtSlot(str)
    def _finish_batch_processing(self, error_message=None):
        """完成批量处理"""
        # 重置批量处理状态
        if hasattr(self, 'batch_processing_state'):
            state = self.batch_processing_state
            processed = state.get('processed_count', 0)
            failed = state.get('failed_count', 0)
            total = state.get('total_count', 0)

            self.batch_processing_state['is_processing'] = False
        else:
            processed = failed = total = 0

        # 更新UI状态
        self._update_batch_processing_ui(False)

        # 显示完成消息
        if error_message:
            message = f"批量处理异常终止: {error_message}"
            self.statusBar().showMessage(message, 10000)
            if hasattr(self, 'properties_widget') and self.properties_widget:
                self.properties_widget.show_api_error("批量处理异常终止")
        else:
            message = f"批量API打标完成！成功: {processed}, 失败: {failed}, 总计: {total}"
            self.statusBar().showMessage(message, 10000)
            if hasattr(self, 'properties_widget') and self.properties_widget:
                self.properties_widget.show_api_success("批量打标完成")

        LOGGER.info(f"批量API处理完成: 成功={processed}, 失败={failed}, 总计={total}")

    def _update_batch_processing_ui(self, is_processing):
        """更新批量处理时的UI状态"""
        # 禁用/启用API按钮
        if hasattr(self, 'properties_widget') and self.properties_widget:
            if hasattr(self.properties_widget, 'ai_prompt_widget'):
                self.properties_widget.ai_prompt_widget.execute_btn.setEnabled(not is_processing)

        # 更新菜单项状态
        if hasattr(self, 'action_batch_api_labeling'):
            self.action_batch_api_labeling.setEnabled(not is_processing)

    def _get_sample_quality_from_file(self, filename):
        """从文件获取样本质量状态（不更新UI）"""
        metadata = self._get_file_metadata_from_json(filename)
        return metadata['quality']

    # ===== 文件操作 =====

    def open_file(self):
        """打开图片文件"""
        filters = "图片文件 (*.png *.jpg *.jpeg *.bmp *.tiff)"
        default_dir = "../examples/table_recognition/data/"
        filename, _ = QtWidgets.QFileDialog.getOpenFileName(
            self, "打开图片", default_dir, filters
        )

        if filename:
            # 设置当前目录为文件所在目录
            self.lastOpenDir = osp.dirname(filename)
            # 加载该目录的浏览状态
            self._load_viewed_files_state()
            # 加载图片
            self.load_image(filename)

    def load_image(self, filename):
        """加载图片"""
        # 🔧 修复：使用与原版相同的图片加载逻辑，包括EXIF处理
        from labelme.label_file import LabelFile

        # 使用LabelFile.load_image_file来处理EXIF方向信息
        image_data = LabelFile.load_image_file(filename)
        if not image_data:
            QtWidgets.QMessageBox.critical(
                self, "错误", f"无法加载图片: {filename}"
            )
            return False

        # 从处理后的图片数据创建QImage
        image = QtGui.QImage.fromData(image_data)
        if image.isNull():
            QtWidgets.QMessageBox.critical(
                self, "错误", f"无法加载图片: {filename}"
            )
            return False

        self.image = image
        self.filename = filename

        # 🆕 记录浏览历史
        self.viewed_files.add(filename)
        # 🔧 优化：延迟保存浏览状态，避免频繁磁盘写入
        self._schedule_save_viewed_files_state()

        pixmap = QtGui.QPixmap.fromImage(image)
        self.canvas.loadPixmap(pixmap)
        self.enable_zoom_actions()

        # 🔧 修复：自动适应窗口显示
        QtCore.QTimer.singleShot(100, self.fit_window)  # 延时执行确保UI更新完成

        # 🆕 设置Canvas焦点，确保能接收键盘事件
        QtCore.QTimer.singleShot(200, self.canvas.setFocus)

        self.statusBar().showMessage(f"已加载图片: {osp.basename(filename)}")
        LOGGER.info(f"图片加载完成: {filename}")

        # 自动加载对应的标注数据
        self._auto_load_annotation_data(filename)

        # 🆕 加载样本质量状态
        self._load_sample_quality_from_file(filename)

        # 🆕 更新文件列表状态显示
        self._refresh_current_file_status()

        # 🆕 初始化撤销状态
        self._update_undo_redo_actions()

        # 🆕 自动切换到默认模式（选择模式）
        self._reset_to_default_mode("图片切换")

        # 🆕 恢复可视化状态（对比可视化和逻辑可视化）
        # 延迟执行，确保表格数据已经完全加载
        QtCore.QTimer.singleShot(200, self._restore_visualization_states)

        return True

    # ===== 🆕 图像旋转功能 =====

    def rotate_image_left(self):
        """向左旋转图像90度"""
        self._rotate_image_with_confirmation(-90)

    def rotate_image_right(self):
        """向右旋转图像90度"""
        self._rotate_image_with_confirmation(90)

    def _rotate_image_with_confirmation(self, angle):
        """旋转图像并确认清空标注数据

        Args:
            angle: 旋转角度，正数为顺时针，负数为逆时针
        """
        if not self.filename:
            QtWidgets.QMessageBox.warning(self, "警告", "请先打开图片文件")
            return

        # 检测当前标注格式
        annotation_info = self._detect_current_annotations()

        # 🔧 修复：每次旋转都弹出确认对话框，无论是否有标注数据
        confirmed = self._show_rotation_confirmation_dialog(annotation_info, angle)
        if not confirmed:
            return

        # 执行旋转
        success = self._execute_image_rotation(angle)
        if success:
            # 清空标注数据
            self._clear_all_annotation_files()
            # 重新加载图像
            self.load_image(self.filename)

            direction = "向左" if angle < 0 else "向右"
            self.statusBar().showMessage(f"✅ 图像已{direction}旋转90度，标注数据已清空")
        else:
            QtWidgets.QMessageBox.critical(self, "错误", "图像旋转失败")

    def _detect_current_annotations(self):
        """检测当前图像的标注情况

        Returns:
            dict: 包含标注信息的字典
        """
        if not self.filename:
            return {'has_annotations': False}

        json_file = self._get_annotation_json_path(self.filename)
        base_name = osp.splitext(self.filename)[0]
        api_cache_file = f"{base_name}-api-resp.json"

        info = {
            'has_annotations': False,
            'json_exists': False,
            'api_cache_exists': False,
            'json_file': json_file,
            'api_cache_file': api_cache_file,
            'table_count': 0,
            'cell_count': 0,
            'canvas_shapes': len(self.canvas.shapes) if self.canvas else 0
        }

        # 检查JSON文件
        if osp.exists(json_file):
            info['json_exists'] = True
            info['has_annotations'] = True
            try:
                import json
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                if isinstance(data, dict) and 'tables' in data:
                    # 新格式
                    tables = data['tables']
                    info['table_count'] = len(tables)
                    info['cell_count'] = sum(len(table.get('cells', [])) for table in tables)
                elif isinstance(data, list):
                    # 旧格式（表格列表）
                    info['table_count'] = len(data)
                    info['cell_count'] = sum(len(table.get('cells', [])) for table in data)
                elif isinstance(data, dict) and 'cells' in data:
                    # 旧格式（单表格）
                    info['table_count'] = 1
                    info['cell_count'] = len(data.get('cells', []))
            except Exception as e:
                LOGGER.error(f"读取JSON标注文件失败: {e}")

        # 检查API缓存文件
        if osp.exists(api_cache_file):
            info['api_cache_exists'] = True
            info['has_annotations'] = True

        # 检查Canvas中的shapes
        if info['canvas_shapes'] > 0:
            info['has_annotations'] = True

        return info

    def _show_rotation_confirmation_dialog(self, annotation_info, angle):
        """显示旋转确认对话框

        Args:
            annotation_info: 标注信息字典
            angle: 旋转角度

        Returns:
            bool: 用户是否确认旋转
        """
        direction = "向左" if angle < 0 else "向右"

        # 构建详细信息
        details = []
        if annotation_info['json_exists']:
            details.append(f"• JSON标注文件: {osp.basename(annotation_info['json_file'])}")
            if annotation_info['table_count'] > 0:
                details.append(f"  - {annotation_info['table_count']} 个表格")
                details.append(f"  - {annotation_info['cell_count']} 个单元格")

        if annotation_info['api_cache_exists']:
            details.append(f"• API缓存文件: {osp.basename(annotation_info['api_cache_file'])}")

        if annotation_info['canvas_shapes'] > 0:
            details.append(f"• 当前界面: {annotation_info['canvas_shapes']} 个标注对象")

        # 🔧 修复：无论是否有标注数据都显示相应信息
        if details:
            detail_text = "\n".join(details)
            warning_text = "⚠️ 警告：旋转图像会永久删除所有标注数据！"
        else:
            detail_text = "• 当前无标注数据"
            warning_text = "ℹ️ 提示：旋转图像会改变图像方向。"

        # 创建确认对话框
        dialog = QtWidgets.QMessageBox(self)
        dialog.setWindowTitle("图像旋转确认")
        dialog.setIcon(QtWidgets.QMessageBox.Warning)

        # 设置主要文本
        dialog.setText(f"即将{direction}旋转图像90度")

        # 🔧 修复：根据是否有标注数据显示不同的信息
        if annotation_info['has_annotations']:
            info_text = f"""
检测到以下标注数据：

{detail_text}

{warning_text}

确认要继续吗？
            """
        else:
            info_text = f"""
当前标注状态：

{detail_text}

{warning_text}

确认要旋转图像吗？
            """
        dialog.setInformativeText(info_text.strip())

        # 设置按钮
        dialog.setStandardButtons(QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No)
        dialog.setDefaultButton(QtWidgets.QMessageBox.No)

        # 显示对话框并获取用户选择
        result = dialog.exec_()
        return result == QtWidgets.QMessageBox.Yes

    def _execute_image_rotation(self, angle):
        """执行图像选中哈斯旋转

        Args:
            angle: 旋转角度

        Returns:
            bool: 是否旋转成功
        """
        try:
            from PIL import Image

            # 打开图像
            with Image.open(self.filename) as img:
                # 根据角度选择旋转方法
                if angle == 90:
                    # 向右旋转90度
                    rotated_img = img.transpose(Image.ROTATE_270)
                elif angle == -90:
                    # 向左旋转90度
                    rotated_img = img.transpose(Image.ROTATE_90)
                else:
                    LOGGER.error(f"不支持的旋转角度: {angle}")
                    return False

                # 保存旋转后的图像（覆盖原文件）
                rotated_img.save(self.filename)

            LOGGER.info(f"图像旋转成功: {angle}度")
            return True

        except Exception as e:
            LOGGER.error(f"图像旋转失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _clear_all_annotation_files(self):
        """清空所有标注相关文件"""
        if not self.filename:
            return

        base_name = osp.splitext(self.filename)[0]
        files_to_delete = [
            f"{base_name}_table_annotation.json",  # JSON标注文件
            f"{base_name}.json",                   # 同名JSON文件
            f"{base_name}-api-resp.json",          # API缓存文件
        ]

        deleted_files = []
        for file_path in files_to_delete:
            try:
                if osp.exists(file_path):
                    os.remove(file_path)
                    deleted_files.append(osp.basename(file_path))
                    LOGGER.debug(f"已删除文件: {file_path}")
            except Exception as e:
                LOGGER.error(f"删除文件失败 {file_path}: {e}")

        # 清空内存中的数据
        self._clear_current_data()

        if deleted_files:
            LOGGER.info(f"已清空标注文件: {', '.join(deleted_files)}")

    def save_file(self):
        """保存标注数据"""
        if not self.filename:
            QtWidgets.QMessageBox.warning(self, "警告", "请先打开图片")
            return

        # 🆕 在导出前自动判断并更新表格类型
        LOGGER.info("🔍 保存时自动判断表格类型...")

        # 导出所有表格数据（包含自动表格类型判断）
        all_data = self.multi_table_controller.export_all_tables()

        # 构造保存文件名 - 如果本地有同名JSON则直接保存到同名JSON
        save_filename = self._get_annotation_json_path(self.filename)

        try:
            import json
            
            # 🔧 修复：获取当前的质量状态
            current_quality = None
            
            # 首先尝试从属性面板获取当前质量状态
            if hasattr(self, 'properties_widget') and self.properties_widget:
                current_quality = self.properties_widget.quality_combo.currentText()
                LOGGER.debug(f"从属性面板获取质量状态: {current_quality}")
            
            # 如果没有获取到，从现有文件中读取（支持_table_annotation和同名JSON）
            if not current_quality:
                existing_json_path = self._get_annotation_json_path(self.filename)
                if osp.exists(existing_json_path):
                    try:
                        with open(existing_json_path, 'r', encoding='utf-8') as f:
                            existing_data = json.load(f)

                        # 从现有文件中提取质量字段
                        if isinstance(existing_data, dict) and 'quality' in existing_data:
                            current_quality = existing_data['quality']
                            LOGGER.debug(f"从现有文件获取质量状态: {current_quality}")
                    except Exception as e:
                        LOGGER.warning(f"读取现有质量状态失败: {e}")
            
            # 如果还是没有，使用默认值
            if not current_quality:
                current_quality = '待校准'
                LOGGER.debug(f"使用默认质量状态: {current_quality}")
            
            # 🔧 构建最终保存数据 - 根据表格数量决定格式
            if isinstance(all_data, dict) and 'table_ind' in all_data:
                # 单表格格式 -> 直接保存单表格，添加质量字段
                final_data = all_data.copy()
                final_data['quality'] = current_quality
                LOGGER.debug(f"单表格保存格式，质量状态: {final_data['quality']}")
            elif isinstance(all_data, list):
                if len(all_data) == 1:
                    # 只有一张表格 -> 直接保存单表格格式，不用tables包装
                    final_data = all_data[0].copy()
                    final_data['quality'] = current_quality
                    LOGGER.debug(f"单表格保存格式（从列表提取），质量状态: {final_data['quality']}")
                else:
                    # 多表格格式 -> 使用tables字段包装
                    final_data = {
                        'quality': current_quality,
                        'tables': all_data
                    }
                    LOGGER.debug(f"多表格保存格式，{len(all_data)}个表格，质量状态: {final_data['quality']}")
            else:
                # 其他格式，直接保存但尝试保留质量字段
                final_data = all_data
                if isinstance(final_data, dict):
                    final_data['quality'] = current_quality
                LOGGER.debug(f"其他格式保存，质量状态: {current_quality}")

            with open(save_filename, 'w', encoding='utf-8') as f:
                json.dump(final_data, f, indent=2, ensure_ascii=False)

            self.statusBar().showMessage(f"标注数据已保存: {save_filename}")

            # 🚀 修复：同步更新索引中的内容类型
            self._update_content_type_in_index_after_save(self.filename, final_data)

            # 🆕 更新文件列表中的状态显示
            self._refresh_current_file_status()

            # 🆕 保存成功后自动切换到下一张
            LOGGER.info(f"保存成功: {save_filename}")
            self._auto_go_to_next_image()

            # 🆕 保存浏览状态
            self._save_viewed_files_state()

        except Exception as e:
            QtWidgets.QMessageBox.critical(
                self, "保存失败", f"保存标注数据时出错:\n{e}"
            )
    # ===== 🆕 简化的模式切换 =====

    def set_select_mode(self):
        """设置选择模式"""
        # 🆕 如果当前在网格拖拽模式，先退出Canvas的网格拖拽状态
        if (self.mode_manager.current_mode == OperationMode.GRID_DRAG and
                self.canvas):
            self.canvas.exit_grid_drag_mode()


        success = self.mode_manager.switch_mode(OperationMode.SELECT)
        if success:
            # 更新按钮状态
            self.action_select_mode.setChecked(True)
            self.action_create_cell_mode.setChecked(False)
            self.action_grid_drag_mode.setChecked(False)  # 🆕
            self.action_quick_table.setChecked(False)  # 🆕 退出快速表格模式
            
            # 🆕 启用多选功能
            if self.canvas:
                self.canvas.set_multi_selection_enabled(True)
                # 🆕 如果之前在快速表格选区模式，则退出
                if hasattr(self.canvas, 'createMode') and self.canvas.createMode == "quick_table_region":
                    self.canvas.setEditing(True)
            
            # 通知表格控制器退出所有特殊模式
            active_controller = self.multi_table_controller.get_active_controller()
            if active_controller:
                active_controller.exit_all_modes()

            if self.canvas:
                self.canvas.setEditing(True)
            description = self.mode_manager.get_mode_description()
            self.statusBar().showMessage(f"已切换到选择模式 - {description}")

    def set_create_cell_mode(self):
        """设置单元格绘制模式"""
        # 🆕 如果当前在网格拖拽模式，先退出Canvas的网格拖拽状态
        if (self.mode_manager.current_mode == OperationMode.GRID_DRAG and
                self.canvas):
            self.canvas.exit_grid_drag_mode()
        success = self.mode_manager.switch_mode(OperationMode.CREATE_CELL)
        if success:
            # 更新按钮状态
            self.action_select_mode.setChecked(False)
            self.action_create_cell_mode.setChecked(True)
            self.action_grid_drag_mode.setChecked(False)  # 🆕
            self.action_quick_table.setChecked(False)  # 🆕 退出快速表格模式
            
            # 🆕 禁用多选功能（单元格模式下专注于创建）
            if self.canvas:
                self.canvas.set_multi_selection_enabled(False)
                # 🆕 如果之前在快速表格选区模式，则退出
                if hasattr(self.canvas, 'createMode') and self.canvas.createMode == "quick_table_region":
                    self.canvas.setEditing(True)
            
            # 确保有活动的表格控制器
            active_controller = self.multi_table_controller.get_active_controller()
            if not active_controller:
                # 创建默认表格
                table_id = self.multi_table_controller.create_table_instance((50, 50, 950, 750))
                active_controller = self.multi_table_controller.get_active_controller()

            if active_controller:
                active_controller.enter_cell_mode()
                description = self.mode_manager.get_mode_description()
                self.statusBar().showMessage(f"已切换到单元格模式 - {description}")

    # ===== 表格工具 =====

    def analyze_table_structure(self):
        """分析表格结构"""
        active_controller = self.multi_table_controller.get_active_controller()
        if not active_controller:
            self.statusBar().showMessage("请先创建表格或绘制单元格")
            return

        try:
            # 🔍 调试：分析前的对象状态
            from labelme.table_shape import is_table_cell
            canvas_table_cells = [s for s in self.canvas.shapes if is_table_cell(s)]

            LOGGER.info(f"🔍 分析表格结构前:")
            LOGGER.info(f"  - table_cells对象ID: {[id(cell) for cell in active_controller.table_cells[:3]]}")
            LOGGER.info(f"  - canvas.shapes对象ID: {[id(cell) for cell in canvas_table_cells[:3]]}")
            # 获取当前表格的所有单元格
            cells = active_controller.get_table_cells()
            if not cells:
                self.statusBar().showMessage("当前表格没有单元格")
                empty_grid_data = {'rows': 0, 'cols': 0, 'cells': []}
                self.structure_widget.set_grid_data(empty_grid_data)
                self.structure_widget.update()
                return

            # 分析表格结构
            result = TableAnalyzer.analyze_cells_to_grid(cells)
            TableAnalyzer.print_grid_analysis(result)
            # 应用分析结果到逻辑坐标
            active_controller._apply_analysis_result(result)

            rows = result['grid']['rows']
            cols = result['grid']['cols']
            self.statusBar().showMessage(f"表格结构分析完成: {rows}行 × {cols}列")

        except Exception as e:
            QtWidgets.QMessageBox.critical(self, "分析失败", f"表格结构分析失败:\n{e}")

    # <--- 🆕 在这里添加新方法 --->
    def apply_table_alignment(self):
        """应用表格对齐：将所有单元格对齐到理想网格"""
        active_controller = self.multi_table_controller.get_active_controller()
        if not active_controller:
            self.statusBar().showMessage("请先创建表格或绘制单元格")
            return

        cells = active_controller.get_table_cells()
        if not cells:
            self.statusBar().showMessage("当前表格没有单元格可供对齐")
            return

        self.statusBar().showMessage("正在准备表格对齐...")

        try:
            # --- 步骤 1: 分析当前表格结构以获取边界 ---
            LOGGER.debug("[ALIGN] 步骤 1: 分析当前表格结构...")
            analysis_result = TableAnalyzer.analyze_cells_to_grid(cells)
            if not analysis_result or 'boundaries' not in analysis_result:
                QtWidgets.QMessageBox.critical(self, "对齐失败", "无法分析当前表格结构。")
                return

            # --- 步骤 2: 从分析结果生成理想网格 ---
            LOGGER.debug("[ALIGN] 步骤 2: 生成理想网格...")
            ideal_grid = TableAnalyzer.generate_ideal_grid_from_boundaries(
                analysis_result['boundaries']
            )
            if not ideal_grid or not ideal_grid.get('ideal_vertical_lines'):
                QtWidgets.QMessageBox.critical(self, "对齐失败", "无法生成理想网格。")
                return

            self.statusBar().showMessage("正在应用对齐修正...")

            # 🆕 使用基于逻辑网格的智能对齐，避免重叠问题
            correction_report = TableAlignmentEngine.apply_logical_grid_alignment(
                cells, analysis_result, ideal_grid
            )

            summary = correction_report['summary']
            adjusted_count = summary['adjusted_count']
            conflict_count = summary['conflict_count']
            skipped_count = summary['skipped_count']

            # --- 步骤 5: 更新画布上的单元格 ---
            if adjusted_count > 0:
                LOGGER.debug(f"[ALIGN] 步骤 4: 更新 {adjusted_count} 个单元格的坐标...")
                for adjustment in correction_report['adjusted_cells']:
                    cell_object = adjustment['cell_object']
                    new_pos = adjustment['adjusted_position']

                    # 更新 TableCellShape 的 points
                    new_x1, new_y1, new_x2, new_y2 = new_pos
                    cell_object.points = [
                        QtCore.QPointF(new_x1, new_y1),
                        QtCore.QPointF(new_x2, new_y1),
                        QtCore.QPointF(new_x2, new_y2),
                        QtCore.QPointF(new_x1, new_y2),
                    ]
                    cell_object.close()  # 确保多边形闭合

                self.canvas.update()  # 强制重绘画布
                LOGGER.debug("[ALIGN] 画布更新完成")

                # 🆕 步骤 6: 应用边框一致性处理
                LOGGER.debug("[ALIGN] 步骤 5: 应用边框一致性处理...")
                self._apply_border_consistency_after_alignment(active_controller)

            # (可选) 对齐后自动重新分析并更新逻辑视图
            if adjusted_count > 0:
                LOGGER.debug("对齐后自动重新分析结构...")
                QtCore.QTimer.singleShot(100, self.analyze_table_structure)

        except Exception as e:
            LOGGER.error(f"表格对齐过程中发生严重错误: {e}")
            import traceback
            traceback.print_exc()
            QtWidgets.QMessageBox.critical(self, "对齐失败", f"应用表格对齐时发生错误:\n{e}")
            self.statusBar().showMessage("表格对齐失败")

    def _apply_border_consistency_after_alignment(self, controller):
        """在表格对齐后应用边框一致性处理

        规则：如果相邻边状态不一致，无边框的优先级更高
        """
        try:
            cells = controller.get_table_cells()
            if not cells:
                return

            LOGGER.debug(f"开始边框一致性处理，共 {len(cells)} 个单元格")

            # 收集所有需要处理的相邻边对
            adjacent_pairs = self._find_all_adjacent_pairs(cells)
            LOGGER.debug(f"找到 {len(adjacent_pairs)} 对相邻边")

            # 应用一致性规则：无边框优先级更高
            consistency_updates = []
            for cell1, cell2, border_side1, border_side2 in adjacent_pairs:
                border1 = cell1.get_border_style()
                border2 = cell2.get_border_style()

                value1 = border1[border_side1]
                value2 = border2[border_side2]

                # 如果状态不一致，应用无边框优先规则
                if value1 != value2:
                    # 无边框(0)优先级更高
                    target_value = min(value1, value2)

                    if value1 != target_value:
                        consistency_updates.append((cell1, border_side1, target_value))
                    if value2 != target_value:
                        consistency_updates.append((cell2, border_side2, target_value))

            # 应用更新
            if consistency_updates:
                LOGGER.debug(f"应用 {len(consistency_updates)} 个边框一致性更新")
                for cell, border_side, new_value in consistency_updates:
                    current_border = cell.get_border_style()
                    current_border[border_side] = new_value
                    cell.set_border_style(**current_border)

                self.canvas.update()
                self.statusBar().showMessage(f"✅ 已应用边框一致性处理，更新了 {len(consistency_updates)} 个边框")
            else:
                LOGGER.debug("边框状态已一致，无需处理")

        except Exception as e:
            LOGGER.error(f"边框一致性处理失败: {e}")

    def _find_all_adjacent_pairs(self, cells) -> List[Tuple]:
        """查找所有相邻的边对

        Returns:
            List[Tuple]: (cell1, cell2, border_side1, border_side2)
        """
        adjacent_pairs = []

        try:
            for i, cell1 in enumerate(cells):
                location1 = cell1.get_logical_location()
                if not location1:
                    continue

                for j, cell2 in enumerate(cells):
                    if i >= j:  # 避免重复检查
                        continue

                    location2 = cell2.get_logical_location()
                    if not location2:
                        continue

                    # 检查是否相邻并确定相邻边
                    adjacencies = self._check_adjacency(location1, location2)
                    for border_side1, border_side2 in adjacencies:
                        adjacent_pairs.append((cell1, cell2, border_side1, border_side2))

        except Exception as e:
            LOGGER.error(f"查找相邻边对失败: {e}")

        return adjacent_pairs

    def _check_adjacency(self, location1: dict, location2: dict) -> List[Tuple[str, str]]:
        """检查两个单元格是否相邻，返回所有相邻的边对

        🔧 修复：支持合并单元格的多边相邻情况

        Returns:
            List[Tuple[str, str]]: [(cell1的边, cell2的边), ...]
        """
        row1_start, row1_end = location1['start_row'], location1['end_row']
        col1_start, col1_end = location1['start_col'], location1['end_col']

        row2_start, row2_end = location2['start_row'], location2['end_row']
        col2_start, col2_end = location2['start_col'], location2['end_col']

        adjacent_edges = []

        # 检查右相邻：cell1的右边 = cell2的左边
        if (col1_end + 1 == col2_start and
            self._ranges_overlap(row1_start, row1_end, row2_start, row2_end)):
            adjacent_edges.append(('right', 'left'))

        # 检查左相邻：cell1的左边 = cell2的右边
        if (col2_end + 1 == col1_start and
            self._ranges_overlap(row1_start, row1_end, row2_start, row2_end)):
            adjacent_edges.append(('left', 'right'))

        # 检查下相邻：cell1的下边 = cell2的上边
        if (row1_end + 1 == row2_start and
            self._ranges_overlap(col1_start, col1_end, col2_start, col2_end)):
            adjacent_edges.append(('bottom', 'top'))

        # 检查上相邻：cell1的上边 = cell2的下边
        if (row2_end + 1 == row1_start and
            self._ranges_overlap(col1_start, col1_end, col2_start, col2_end)):
            adjacent_edges.append(('top', 'bottom'))

        return adjacent_edges

    def _ranges_overlap(self, start1: int, end1: int, start2: int, end2: int) -> bool:
        """检查两个范围是否有重叠"""
        return not (end1 < start2 or end2 < start1)

    # <--- 结束新增 --->


    def quick_generate_table(self):
        """🆕 P1阶段：进入快速生成表格模式"""
        if not self.filename:
            QtWidgets.QMessageBox.warning(self, "提示", "请先打开图片")
            return
        
        # 确保有活动的表格控制器
        active_controller = self.multi_table_controller.get_active_controller()
        if not active_controller:
            # 创建默认表格
            table_id = self.multi_table_controller.create_table_instance((50, 50, 950, 750))
            active_controller = self.multi_table_controller.get_active_controller()
        
        # 切换到快速表格选区模式
        if self.canvas:
            self.canvas.createMode = "quick_table_region"
            self.canvas.setEditing(False)
            
            # 更新按钮状态
            self.action_quick_table.setChecked(True)
            
            # 更新状态栏
            self.statusBar().showMessage("🎯 快速生成表格模式：请在图片中拖拽选择区域")
            
            LOGGER.debug("已进入快速表格选区模式")
        else:
            QtWidgets.QMessageBox.critical(self, "错误", "Canvas未初始化")

    def _show_quick_table_dialog(self, region_rect):
        """🆕 显示快速生成表格对话框"""
        LOGGER.debug(f"_show_quick_table_dialog被调用，区域: {region_rect}")
        
        # 重置按钮状态
        self.action_quick_table.setChecked(False)
        
        # 显示对话框
        dialog = QtWidgets.QDialog(self)
        dialog.setWindowTitle("快速生成表格")
        dialog.setModal(True)
        dialog.setFixedSize(280, 180)

        layout = QtWidgets.QFormLayout(dialog)

        # 显示选区信息
        x1, y1, x2, y2 = region_rect
        region_info = QtWidgets.QLabel(f"选区: ({x1:.0f}, {y1:.0f}) - ({x2:.0f}, {y2:.0f})")
        region_info.setStyleSheet("color: gray; font-size: 12px;")
        layout.addRow("", region_info)

        rows_spin = QtWidgets.QSpinBox()
        rows_spin.setRange(1, 20)
        rows_spin.setValue(3)

        cols_spin = QtWidgets.QSpinBox()
        cols_spin.setRange(1, 20)
        cols_spin.setValue(4)

        layout.addRow("行数:", rows_spin)
        layout.addRow("列数:", cols_spin)

        buttons = QtWidgets.QDialogButtonBox(
            QtWidgets.QDialogButtonBox.Ok | QtWidgets.QDialogButtonBox.Cancel
        )
        buttons.accepted.connect(dialog.accept)
        buttons.rejected.connect(dialog.reject)
        layout.addWidget(buttons)

        if dialog.exec_() == QtWidgets.QDialog.Accepted:
            rows = rows_spin.value()
            cols = cols_spin.value()
            LOGGER.debug(f"用户确认生成: {rows}×{cols}")
            self._execute_quick_table_generation(region_rect, rows, cols)
        else:
            LOGGER.debug("用户取消了快速生成表格")

    def _execute_quick_table_generation(self, region_rect, rows, cols):
        """🆕 执行快速表格生成（支持撤销重做）"""
        LOGGER.debug("_execute_quick_table_generation开始")
        try:
            active_controller = self.multi_table_controller.get_active_controller()
            if not active_controller:
                LOGGER.error("没有活动的表格控制器")
                self.statusBar().showMessage("没有活动的表格控制器")
                return

            # 🔧 计算参数用于BatchCreateCellCommand
            x1, y1, x2, y2 = region_rect
            region_width = x2 - x1
            region_height = y2 - y1
            
            # 计算单元格尺寸和中心点
            cell_width = region_width / cols
            cell_height = region_height / rows
            center_point = QtCore.QPointF((x1 + x2) / 2, (y1 + y2) / 2)
            
            LOGGER.debug(f"快速生成表格: 区域({x1:.0f},{y1:.0f})-({x2:.0f},{y2:.0f}), {rows}×{cols}")
            LOGGER.debug(f"计算参数: 单元格尺寸({cell_width:.1f}×{cell_height:.1f}), 中心点({center_point.x():.1f},{center_point.y():.1f})")
            
            # 🔧 获取正确的table_id
            table_id = getattr(active_controller, 'table_id', None)
            if table_id is None:
                # 如果TableController没有table_id，使用MultiTableController的active_table_id
                table_id = self.multi_table_controller.active_table_id or 1
            
            LOGGER.debug(f"使用table_id: {table_id}")
            
            # 🔧 修复：记录生成前的单元格数量
            cells_before = len(active_controller.get_table_cells())
            LOGGER.debug(f"生成前TableController中的单元格数量: {cells_before}")
            
            # 🆕 使用命令系统执行批量创建（支持撤销重做）
            success = self.execute_batch_create_command(
                rows=rows,
                cols=cols, 
                center_point=center_point,
                cell_width=cell_width,
                cell_height=cell_height,
                table_id=table_id
            )
            
            if success:
                LOGGER.debug("快速生成表格成功，支持撤销重做")
                
                # 🔧 立即强制同步新创建的单元格到TableController
                self._force_sync_canvas_shapes_to_controller()
                
                # 🔧 验证同步结果
                cells_after = len(active_controller.get_table_cells())
                LOGGER.debug(f"同步后TableController中的单元格数量: {cells_after}")
                
                if cells_after > cells_before:
                    # 同步成功，执行自动分析结构
                    LOGGER.debug(f"成功同步 {cells_after - cells_before} 个新单元格")
                    self.statusBar().showMessage(f"✅ 快速生成完成: {rows}×{cols} = {rows*cols}个单元格 (支持撤销)")
                    
                    # 自动分析结构
                    self.analyze_table_structure()
                else:
                    # 同步失败，使用备用方案
                    LOGGER.warning("常规同步失败，使用备用分析方案")
                    self._fallback_analysis_for_quick_generation(rows, cols)
                    self.statusBar().showMessage(f"✅ 快速生成完成: {rows}×{cols} = {rows*cols}个单元格 (备用分析)")
                    
            else:
                self.statusBar().showMessage("❌ 快速生成失败")
                LOGGER.error("批量创建命令执行失败")

        except Exception as e:
            LOGGER.error(f"快速生成表格异常: {e}")
            import traceback
            traceback.print_exc()
            QtWidgets.QMessageBox.critical(self, "生成失败", f"快速生成表格失败:\n{e}")

    def _force_sync_canvas_shapes_to_controller(self):
        """🔧 强制同步Canvas中的单元格到TableController（增强版本）"""
        try:
            active_controller = self.multi_table_controller.get_active_controller()
            if not active_controller:
                LOGGER.warning("没有活动的TableController，无法同步")
                return
            
            # 获取Canvas中所有的TableCellShape
            from labelme.table_shape import TableCellShape
            canvas_cells = [shape for shape in self.canvas.shapes if isinstance(shape, TableCellShape)]
            
            # 获取TableController中已有的单元格
            controller_cells = active_controller.get_table_cells()
            
            LOGGER.debug(f"强制同步: Canvas中有 {len(canvas_cells)} 个单元格, Controller中有 {len(controller_cells)} 个单元格")
            
            # 找出新增的单元格（在Canvas中但不在Controller中）
            new_cells = []
            for canvas_cell in canvas_cells:
                if canvas_cell not in controller_cells:
                    new_cells.append(canvas_cell)
            
            if new_cells:
                LOGGER.debug(f"发现 {len(new_cells)} 个新单元格需要同步")
                # 将新单元格添加到TableController
                for i, cell in enumerate(new_cells):
                    active_controller.add_cell_shape(cell)
                    LOGGER.debug(f"  已同步单元格 {i+1}/{len(new_cells)}: {cell.label}")
                
                LOGGER.debug(f"强制同步完成，新增 {len(new_cells)} 个单元格")
            else:
                LOGGER.debug("所有单元格已同步，无需操作")
                
        except Exception as e:
            LOGGER.error(f"强制同步单元格失败: {e}")
            import traceback
            traceback.print_exc()

    def _fallback_analysis_for_quick_generation(self, rows, cols):
        """🔧 快速生成表格的备用分析方案
        
        当常规同步失败时，直接从Canvas获取单元格进行分析
        """
        try:
            LOGGER.debug("启动备用分析方案...")
            
            # 直接从Canvas获取所有TableCellShape
            from labelme.table_shape import TableCellShape
            canvas_cells = [shape for shape in self.canvas.shapes if isinstance(shape, TableCellShape)]
            
            if not canvas_cells:
                LOGGER.error("Canvas中没有找到TableCellShape，备用分析失败")
                self.statusBar().showMessage("分析失败：没有找到表格单元格")
                return
            
            LOGGER.debug(f"从Canvas发现 {len(canvas_cells)} 个单元格，开始备用分析...")
            
            # 使用TableAnalyzer直接分析Canvas中的单元格
            from labelme.utils.table_analyzer import TableAnalyzer
            result = TableAnalyzer.analyze_cells_to_grid(canvas_cells)
            
            if result:
                analyzed_rows = result['grid']['rows']
                analyzed_cols = result['grid']['cols']
                LOGGER.debug(f"备用分析结果: {analyzed_rows}行 × {analyzed_cols}列")
                
                # 🔧 手动更新逻辑结构视图
                if hasattr(self, 'structure_widget') and self.structure_widget:
                    self._manual_update_structure_view(result, canvas_cells)
                
                self.statusBar().showMessage(f"备用分析完成: {analyzed_rows}行 × {analyzed_cols}列 (预期: {rows}×{cols})")
                LOGGER.debug("备用分析方案执行成功")
            else:
                LOGGER.error("备用分析失败：TableAnalyzer返回空结果")
                self.statusBar().showMessage("备用分析失败：无法分析表格结构")
                
        except Exception as e:
            LOGGER.error(f"备用分析方案执行失败: {e}")
            import traceback
            traceback.print_exc()
            self.statusBar().showMessage("备用分析失败：发生异常")

    def _manual_update_structure_view(self, analysis_result, canvas_cells):
        """🔧 手动更新逻辑结构视图
        
        Args:
            analysis_result: TableAnalyzer的分析结果
            canvas_cells: Canvas中的单元格列表
        """
        try:
            LOGGER.debug("手动更新逻辑结构视图...")
            
            # 构造结构视图需要的数据格式
            grid_data = {
                'rows': analysis_result['grid']['rows'],
                'cols': analysis_result['grid']['cols'],
                'cells': []
            }

            # 添加单元格数据
            if 'cell_positions' in analysis_result:
                for cell_id, pos_info in analysis_result['cell_positions'].items():
                    cell_obj = pos_info['cell_object']
                    
                    # ✅ 修复：正确获取单元格文本内容
                    cell_text = ""
                    if hasattr(cell_obj, 'get_cell_text'):
                        cell_text = cell_obj.get_cell_text()
                    elif hasattr(cell_obj, 'label'):
                        cell_text = cell_obj.label
                    else:
                        cell_text = f"Cell_{pos_info['row']}_{pos_info['col']}"
                    
                    cell_data = {
                        'row': pos_info['row'],
                        'col': pos_info['col'],
                        'text': cell_text,
                        'border': getattr(cell_obj, 'table_properties', {}).get('border', {}),
                        'cell_id': cell_id
                    }
                    grid_data['cells'].append(cell_data)
            
            # 更新结构视图
            if hasattr(self.structure_widget, 'set_grid_data'):
                self.structure_widget.set_grid_data(grid_data)
                LOGGER.debug("逻辑结构视图更新成功")
            else:
                LOGGER.warning("结构视图没有set_grid_data方法")
                
        except Exception as e:
            LOGGER.error(f"手动更新逻辑结构视图失败: {e}")
            import traceback
            traceback.print_exc()

    def _sync_canvas_shapes_to_controller(self):
        """🔧 同步Canvas中的单元格到TableController（保持兼容性）"""
        LOGGER.warning("使用旧版同步方法，建议使用_force_sync_canvas_shapes_to_controller")
        self._force_sync_canvas_shapes_to_controller()

    # ===== 对齐工具 =====

    def align_top(self):
        """顶端对齐"""
        active_controller = self.multi_table_controller.get_active_controller()
        if active_controller and active_controller.align_selected_cells_top():
            QtCore.QTimer.singleShot(100, self.analyze_table_structure)
            self.statusBar().showMessage("已执行顶端对齐")
            # 🆕 TODO: 在P2阶段实现对齐命令
            # align_cmd = AlignmentCommand(self.canvas, selected_cells, 'top')
            # self.history_manager.execute_command(align_cmd)
        else:
            self.statusBar().showMessage("对齐失败 - 请选择至少2个单元格")

    def align_bottom(self):
        """底端对齐"""
        active_controller = self.multi_table_controller.get_active_controller()
        if active_controller and active_controller.align_selected_cells_bottom():
            QtCore.QTimer.singleShot(100, self.analyze_table_structure)
            self.statusBar().showMessage("已执行底端对齐")
            # 🆕 TODO: 在P2阶段实现对齐命令
            # align_cmd = AlignmentCommand(self.canvas, selected_cells, 'bottom')
            # self.history_manager.execute_command(align_cmd)
        else:
            self.statusBar().showMessage("对齐失败 - 请选择至少2个单元格")

    def align_left(self):
        """左对齐"""
        active_controller = self.multi_table_controller.get_active_controller()
        if active_controller and active_controller.align_selected_cells_left():
            QtCore.QTimer.singleShot(100, self.analyze_table_structure)
            self.statusBar().showMessage("已执行左对齐")
            # 🆕 TODO: 在P2阶段实现对齐命令
            # align_cmd = AlignmentCommand(self.canvas, selected_cells, 'left')
            # self.history_manager.execute_command(align_cmd)
        else:
            self.statusBar().showMessage("对齐失败 - 请选择至少2个单元格")

    def align_right(self):
        """右对齐"""
        active_controller = self.multi_table_controller.get_active_controller()
        if active_controller and active_controller.align_selected_cells_right():
            QtCore.QTimer.singleShot(100, self.analyze_table_structure)
            self.statusBar().showMessage("已执行右对齐")
            # 🆕 TODO: 在P2阶段实现对齐命令
            # align_cmd = AlignmentCommand(self.canvas, selected_cells, 'right')
            # self.history_manager.execute_command(align_cmd)
        else:
            self.statusBar().showMessage("对齐失败 - 请选择至少2个单元格")

    # ===== 缩放操作 =====

    def enable_zoom_actions(self):
        """启用缩放操作"""
        self.action_zoom_in.setEnabled(True)
        self.action_zoom_out.setEnabled(True)
        self.action_fit_window.setEnabled(True)
        self.zoom_widget.setEnabled(True)

        # 🆕 启用表格优化功能
        self.action_optimize_table.setEnabled(True)
        # 🆕 启用旋转按钮
        self.action_rotate_left.setEnabled(True)
        self.action_rotate_right.setEnabled(True)
        self.action_save.setEnabled(True)
        self.action_compare_visualization.setEnabled(True)  # 🆕 启用对比可视化按钮
        self.action_logical_visualization.setEnabled(True)  # 🆕 启用逻辑可视化按钮

    def zoom_in(self):
        """放大"""
        current_value = self.zoom_widget.value()
        self.zoom_widget.setValue(int(current_value * 1.1))

    def zoom_out(self):
        """缩小"""
        current_value = self.zoom_widget.value()
        self.zoom_widget.setValue(int(current_value * 0.9))

    def fit_window(self):
        """适应窗口（优化自适应显示）"""
        if not self.canvas or not self.canvas.pixmap:
            return

        try:
            canvas_size = self.scroll_area.size()
            pixmap_size = self.canvas.pixmap.size()

            # 计算缩放比例，确保完整显示且最大化
            w_ratio = canvas_size.width() / pixmap_size.width()
            h_ratio = canvas_size.height() / pixmap_size.height()
            
            # 取较小的比例，确保图片完整显示，并留出少量边距
            ratio = min(w_ratio, h_ratio) * 0.90  # 减少边距，更大化显示
            
            # 设置最小缩放限制，避免图片过小
            ratio = max(ratio, 0.1)
            
            self.zoom_widget.setValue(int(ratio * 100))
            self.fit_window_mode = True
            
            LOGGER.debug(f"自适应显示: 缩放比例 {ratio:.2f} ({ratio*100:.1f}%)")
        except Exception as e:
            LOGGER.error(f"自适应显示失败: {e}")
            self.zoom_widget.setValue(100)  # 已经是int类型

    def zoom_request(self, delta, pos):
        """处理缩放请求"""
        factor = 1.1 if delta > 0 else 0.9
        current_value = self.zoom_widget.value()
        self.zoom_widget.setValue(int(current_value * factor))

    def paint_canvas(self):
        """绘制Canvas（修复崩溃问题）"""
        if not self.canvas or not self.canvas.pixmap:
            return
        
        try:
            # 获取缩放值，增加安全检查
            zoom_value = self.zoom_widget.value()
            if zoom_value <= 0:
                zoom_value = 100
            
            self.canvas.scale = zoom_value / 100.0
            self.canvas.adjustSize()
            self.canvas.update()
        except Exception as e:
            LOGGER.error(f"Canvas绘制错误: {e}")
            # 恢复默认缩放
            self.zoom_widget.setValue(100)  # 已经是int类型

    # ===== 事件处理 =====

    def _on_new_shape(self):
        """新形状创建事件"""
        self.statusBar().showMessage("检测到新形状创建")

    def delete_selected_shape(self):
        """删除选中的形状（使用新的命令系统）"""
        if not self.canvas.selectedShapes:
            self.statusBar().showMessage("请先选择要删除的单元格或表格")
            return

        # 统计选中的对象类型
        table_cells = [s for s in self.canvas.selectedShapes if isinstance(s, TableCellShape)]
        table_boundaries = [s for s in self.canvas.selectedShapes
                            if hasattr(s, 'label') and s.label == "表格区域"]
        other_shapes = [s for s in self.canvas.selectedShapes
                        if not isinstance(s, TableCellShape)
                        and not (hasattr(s, 'label') and s.label == "表格区域")]

        if not (table_cells or table_boundaries or other_shapes):
            return

        # 构建确认消息
        msg_parts = []
        if table_cells:
            msg_parts.append(f"{len(table_cells)} 个单元格")
        if table_boundaries:
            msg_parts.append(f"{len(table_boundaries)} 个表格边界框")
        if other_shapes:
            msg_parts.append(f"{len(other_shapes)} 个其他形状")

        msg = f"确定要删除 {' 和 '.join(msg_parts)} 吗？"

        # 确认对话框
        reply = QtWidgets.QMessageBox.question(
            self, "确认删除", msg,
            QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No
        )

        if reply == QtWidgets.QMessageBox.Yes:
            # ===== 🆕 使用命令系统执行删除 =====
            if table_cells:
                # 创建删除单元格命令
                delete_cmd = DeleteCellCommand(self.canvas, table_cells)
                success = self.history_manager.execute_command(delete_cmd)
                
                if success:
                    # 通知表格控制器更新
                    active_controller = self.multi_table_controller.get_active_controller()
                    if active_controller:
                        active_controller.handle_shapes_deleted(table_cells)
                    
                    self.statusBar().showMessage(f"✅ 已删除 {len(table_cells)} 个单元格")
                else:
                    self.statusBar().showMessage("❌ 删除操作失败")
                    return
            
            # 处理其他形状（暂时使用原有逻辑）
            if table_boundaries or other_shapes:
                # 🔗 复用Canvas的删除逻辑处理非单元格对象
                other_shapes_to_delete = table_boundaries + other_shapes
                for shape in other_shapes_to_delete:
                    if shape in self.canvas.shapes:
                        self.canvas.shapes.remove(shape)
                    if shape in self.canvas.selectedShapes:
                        self.canvas.selectedShapes.remove(shape)
                
                self.canvas.storeShapes()
                self.canvas.update()
                self.statusBar().showMessage(f"✅ 已删除 {len(other_shapes_to_delete)} 个其他对象")

            # 刷新界面
            self._refresh_ui_after_deletion()

    def delete_current_table(self):
        """删除当前活动表格及其所有单元格"""
        active_controller = self.multi_table_controller.get_active_controller()
        if not active_controller:
            self.statusBar().showMessage("没有活动的表格")
            return

        table_id = self.multi_table_controller.active_table_id
        table_cells = active_controller.get_table_cells()

        msg = f"确定要删除整个表格 ID={table_id} 及其 {len(table_cells)} 个单元格吗？\n此操作不可撤销！"

        reply = QtWidgets.QMessageBox.question(
            self, "确认删除表格", msg,
            QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No
        )

        if reply == QtWidgets.QMessageBox.Yes:
            # 🔗 调用MultiTableController的删除方法
            success = self.multi_table_controller.remove_table_instance(table_id)

            if success:
                self.statusBar().showMessage(f"已删除表格 ID={table_id}")
                self._refresh_ui_after_deletion()
            else:
                self.statusBar().showMessage("删除表格失败")

    def _refresh_ui_after_deletion(self):
        """删除后刷新UI状态"""
        # 禁用删除按钮（因为没有选中项了）
        self.action_delete.setEnabled(False)

        # 检查是否还有表格，如果没有则禁用表格删除
        if not self.multi_table_controller.table_controllers:
            self.action_delete_table.setEnabled(False)

        # 刷新属性面板和结构视图
        self.properties_widget.update_from_selection([])
        if hasattr(self, 'structure_widget'):
            active_controller = self.multi_table_controller.get_active_controller()
            if active_controller:
                active_controller._refresh_structure_widget()
    def _on_selection_changed(self, selected_shapes):
        """选择变更事件（现在由shapeSelectionChanged调用）"""
        # 筛选表格单元格
        table_cells = [s for s in selected_shapes if isinstance(s, TableCellShape)]
        # 筛选表格边界框
        table_boundaries = [s for s in selected_shapes
                            if hasattr(s, 'label') and s.label == "表格区域"]

        if table_cells:
            self.statusBar().showMessage(f"选中 {len(table_cells)} 个表格单元格")
            self.properties_widget.update_from_selection(table_cells)

            # 绑定属性面板到活动控制器
            active_controller = self.multi_table_controller.get_active_controller()
            if active_controller and not hasattr(self.properties_widget, 'controller'):
                self.properties_widget.bind_controller(active_controller)
        elif table_boundaries:
            # 🆕 显示表格边界框选中状态
            self.statusBar().showMessage(
                f"选中 {len(table_boundaries)} 个表格边界框 - 可Delete删除或Ctrl+Shift+Delete删除整个表格")
        else:
            other_shapes = [s for s in selected_shapes
                            if not isinstance(s, TableCellShape)
                            and not (hasattr(s, 'label') and s.label == "表格区域")]
            if other_shapes:
                self.statusBar().showMessage(f"选中 {len(other_shapes)} 个形状")

    def _on_structure_selection_changed(self, selected_cells_data):
        """结构视图选择变更事件"""
        # 显示状态信息
        if selected_cells_data:
            self.statusBar().showMessage(f"在逻辑视图中选中 {len(selected_cells_data)} 个单元格")
        else:
            self.statusBar().showMessage("清除逻辑视图选择")

        # 🔧 关键修复：转发给活动的TableController
        active_controller = self.multi_table_controller.get_active_controller()
        if active_controller:
            active_controller._on_structure_selection_changed(selected_cells_data)

    def _on_table_created(self, table_id):
        """表格创建事件"""
        self.statusBar().showMessage(f"创建新表格 ID={table_id}")

        # 🔧 启用删除表格按钮
        self.action_delete_table.setEnabled(True)

        # 🔗 新增：绑定属性面板到新的活动控制器
        self._bind_properties_to_active_controller()
        # 🔧 新增：绑定结构视图到新的活动控制器
        self._bind_structure_to_active_controller()

    def _on_table_switched(self, old_id, new_id):
        """表格切换事件"""
        self.statusBar().showMessage(f"切换表格: {old_id} -> {new_id}")

        # 启用删除表格按钮
        self.action_delete_table.setEnabled(True)

        # 🔗 新增：重新绑定属性面板
        self._bind_properties_to_active_controller()
        # 🔧 新增：重新绑定结构视图
        self._bind_structure_to_active_controller()

    def _bind_properties_to_active_controller(self):
        """绑定属性面板到当前活动的表格控制器"""
        active_controller = self.multi_table_controller.get_active_controller()
        if active_controller:
            self.properties_widget.bind_controller(active_controller)
            LOGGER.debug("属性面板已绑定到活动控制器")
        else:
            # 🔧 修复：没有活动控制器时，清空属性面板的控制器引用
            # 但保持UI状态（特别是自动推导复选框的状态）
            self.properties_widget.controller = None
            LOGGER.debug("没有活动控制器，已清空属性面板控制器引用")
    def _update_structure_view(self, analysis_result):
        """更新结构视图"""
        try:
            # 构造结构视图需要的数据格式
            grid_data = {
                'rows': analysis_result['grid']['rows'],
                'cols': analysis_result['grid']['cols'],
                'cells': []
            }

            # 添加单元格数据
            for cell_id, pos_info in analysis_result['cell_positions'].items():
                cell_obj = pos_info['cell_object']
                cell_data = {
                    'row': pos_info['row'],
                    'col': pos_info['col'],
                    'text': cell_obj.get_cell_text(),
                    'border': cell_obj.get_border_style(),
                    'cell_id': cell_id
                }
                grid_data['cells'].append(cell_data)

            # 更新结构视图
            self.structure_widget.set_grid_data(grid_data)

        except Exception as e:
            LOGGER.error(f"更新结构视图失败: {e}")

    # ===== 🆕 文件管理系统方法 =====

    def open_dir(self):
        """打开目录"""
        if not self.mayContinue():
            return

        if self.lastOpenDir and osp.exists(self.lastOpenDir):
            defaultOpenDirPath = self.lastOpenDir
        else:
            defaultOpenDirPath = osp.dirname(self.filename) if self.filename else "."

        targetDirPath = str(
            QtWidgets.QFileDialog.getExistingDirectory(
                self,
                "选择图片目录 - TableLabelMe",
                defaultOpenDirPath,
                QtWidgets.QFileDialog.ShowDirsOnly
                | QtWidgets.QFileDialog.DontResolveSymlinks,
            )
        )

        if targetDirPath:
            # 设置lastOpenDir，以便_load_viewed_files_state可以使用
            self.lastOpenDir = targetDirPath
            # 加载该目录的浏览状态
            self._load_viewed_files_state()
            # 导入目录中的图片
            self.importDirImages(targetDirPath)

    def importDirImages(self, dirpath, pattern=None, load=True):
        """批量导入目录中的图片"""
        if not dirpath:
            return

        self.lastOpenDir = dirpath
        self.filename = None
        self.fileListWidget.clear()
        self.imageList = []

        filenames = self.scanAllImages(dirpath)

        # 🚀 性能优化：使用统一元数据方法减少I/O操作
        self._load_directory_metadata(dirpath, filenames)

        # 应用搜索模式筛选
        if pattern:
            try:
                import re
                filenames = [f for f in filenames if re.search(pattern, f)]
            except re.error:
                pass

        # 应用内容类型和质量状态筛选
        filtered_filenames = []
        for filename in filenames:
            if self._matches_filters(filename):
                filtered_filenames.append(filename)

        for filename in filtered_filenames:
            item = QtWidgets.QListWidgetItem()
            item.setData(Qt.UserRole, filename)  # 存储完整路径
            item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)

            # 🆕 使用新的状态显示方法
            self._update_file_item_status(item, filename)

            self.fileListWidget.addItem(item)
            self.imageList.append(filename)

        if load and len(self.imageList) > 0:
            # 检查是否有上次打开的文件
            last_file = self.last_files_by_dir.get(dirpath)

            if last_file and last_file in self.imageList:
                # 找到上次文件在列表中的索引
                index = self.imageList.index(last_file)
                self.fileListWidget.setCurrentRow(index)
                LOGGER.info(f"自动跳转到上次打开的文件: {last_file}")
            else:
                # 没有上次文件或文件不存在，打开第一个
                self.fileListWidget.setCurrentRow(0)
                LOGGER.info("打开目录中的第一个文件")

            # 🆕 自动切换到默认模式（选择模式）
            self._reset_to_default_mode("导入目录")
            return

        self.statusBar().showMessage(f"已导入 {len(filtered_filenames)} 个图片文件（共 {len(filenames)} 个）")

    def scanAllImages(self, folderPath):
        """扫描文件夹中的所有图片文件"""
        import os
        from PyQt5 import QtGui
        import natsort

        extensions = [
            ".%s" % fmt.data().decode().lower()
            for fmt in QtGui.QImageReader.supportedImageFormats()
        ]

        images = []
        for root, dirs, files in os.walk(folderPath):
            for file in files:
                if file.lower().endswith(tuple(extensions)):
                    relativePath = os.path.normpath(osp.join(root, file))
                    images.append(relativePath)

        images = natsort.os_sorted(images)
        return images

    def _on_content_type_select_all(self):
        """内容类型全选按钮处理"""
        # 检查当前是否全部选中
        all_checked = all(self.content_type_filters.values())

        # 🚀 临时禁用信号，避免重复筛选
        for checkbox in self.content_type_checkboxes.values():
            checkbox.blockSignals(True)

        try:
            if all_checked:
                # 如果全部选中，则全部取消
                for name in self.content_type_filters:
                    self.content_type_filters[name] = False
                    self.content_type_checkboxes[name].setChecked(False)
                self.content_type_select_all_btn.setText("全选")
            else:
                # 如果不是全部选中，则全部选中
                for name in self.content_type_filters:
                    self.content_type_filters[name] = True
                    self.content_type_checkboxes[name].setChecked(True)
                self.content_type_select_all_btn.setText("取消")
        finally:
            # 🚀 恢复信号
            for checkbox in self.content_type_checkboxes.values():
                checkbox.blockSignals(False)

        # 🚀 只在最后执行一次筛选
        self._apply_filters()

    def _on_quality_select_all(self):
        """质量状态全选按钮处理"""
        # 检查当前是否全部选中
        all_checked = all(self.quality_filters.values())

        # 🚀 临时禁用信号，避免重复筛选
        for checkbox in self.quality_checkboxes.values():
            checkbox.blockSignals(True)

        try:
            if all_checked:
                # 如果全部选中，则全部取消
                for name in self.quality_filters:
                    self.quality_filters[name] = False
                    self.quality_checkboxes[name].setChecked(False)
                self.quality_select_all_btn.setText("全选")
            else:
                # 如果不是全部选中，则全部选中
                for name in self.quality_filters:
                    self.quality_filters[name] = True
                    self.quality_checkboxes[name].setChecked(True)
                self.quality_select_all_btn.setText("取消")
        finally:
            # 🚀 恢复信号
            for checkbox in self.quality_checkboxes.values():
                checkbox.blockSignals(False)

        # 🚀 只在最后执行一次筛选
        self._apply_filters()

    def _on_content_type_filter_changed(self, filter_name, state):
        """内容类型筛选变化处理"""
        is_checked = state == Qt.Checked
        self.content_type_filters[filter_name] = is_checked

        # 更新全选按钮状态
        self._update_content_type_select_all_button()

        self._apply_filters()

    def _on_quality_filter_changed(self, filter_name, state):
        """质量状态筛选变化处理"""
        is_checked = state == Qt.Checked
        self.quality_filters[filter_name] = is_checked

        # 更新全选按钮状态
        self._update_quality_select_all_button()

        self._apply_filters()

    def _update_content_type_select_all_button(self):
        """更新内容类型全选按钮状态"""
        all_checked = all(self.content_type_filters.values())
        if all_checked:
            self.content_type_select_all_btn.setText("取消")
        else:
            self.content_type_select_all_btn.setText("全选")

    def _update_quality_select_all_button(self):
        """更新质量状态全选按钮状态"""
        all_checked = all(self.quality_filters.values())
        if all_checked:
            self.quality_select_all_btn.setText("取消")
        else:
            self.quality_select_all_btn.setText("全选")

    def fileSearchChanged(self):
        """文件搜索变化处理"""
        if not self.lastOpenDir:
            return

        self._apply_filters()

    def _load_directory_metadata(self, dirpath, filenames):
        """🚀 使用索引系统加载目录元数据"""
        # 判断目录类型
        is_parent_dir = self._is_parent_directory(dirpath)

        if is_parent_dir:
            # 父目录：使用聚合索引（已在_load_viewed_files_state中加载）
            if hasattr(self, 'directory_aggregator') and self.file_index:
                LOGGER.info(f"使用父目录聚合索引，包含 {len(self.file_index.get('files', {}))} 个文件")
                self._populate_metadata_cache_from_index()
                return
            else:
                LOGGER.warning("父目录聚合索引未正确加载")
                return
        else:
            # 子目录：使用传统索引构建方式
            # 确保文件索引管理器已初始化
            if not self.file_index_manager:
                self.file_index_manager = FileIndexManager(dirpath)

            # 检查是否已有有效索引
            if self.file_index and self.file_index_manager.is_index_valid(self.file_index, filenames):
                LOGGER.info(f"使用现有索引，包含 {len(self.file_index['files'])} 个文件")
                self._populate_metadata_cache_from_index()
                return

            # 需要构建新索引
            LOGGER.info(f"构建新索引，扫描 {len(filenames)} 个文件...")

            # 如果有v1.0迁移的viewed_files，使用它们
            migrated_viewed_files = set()
            if (self.file_index and
                "migrated_viewed_files" in self.file_index):
                migrated_viewed_files = set(self.file_index["migrated_viewed_files"])
                self.viewed_files.update(migrated_viewed_files)

            # 🚀 构建索引（使用统一元数据方法，减少50%的I/O操作）
            self.file_index = self.file_index_manager.build_index(
                filenames=filenames,
                get_metadata_func=self._get_file_metadata_from_json,
                viewed_files=self.viewed_files
            )

            # 保存索引
            self.file_index_manager.save_index(self.file_index)

            # 填充内存缓存
            self._populate_metadata_cache_from_index()

    def _populate_metadata_cache_from_index(self):
        """从索引填充内存缓存"""
        self.metadata_cache.clear()

        if not self.file_index or "files" not in self.file_index:
            return

        for filename, file_data in self.file_index["files"].items():
            self.metadata_cache[filename] = {
                'content_type': file_data["content_type"],
                'quality': file_data["quality"]
            }

        LOGGER.debug(f"从索引填充缓存完成，共 {len(self.metadata_cache)} 个文件")

    def _apply_filters(self):
        """🚀 使用索引进行超快速筛选"""
        if not self.lastOpenDir:
            return

        # 🔧 检查UI是否已初始化
        if not hasattr(self, 'fileListWidget') or self.fileListWidget is None:
            LOGGER.debug("UI未完全初始化，跳过筛选")
            return

        # 如果有索引数据，使用索引筛选
        if self.file_index and "files" in self.file_index:
            self._apply_filters_with_index()
        else:
            # 兜底方案：使用原有方式
            LOGGER.warning("索引数据不可用，使用传统筛选方式")
            pattern = self.fileSearch.text() if hasattr(self, 'fileSearch') else None
            self.importDirImages(self.lastOpenDir, pattern=pattern, load=False)

    def _apply_filters_with_index(self):
        """🚀 使用索引进行超快速筛选"""
        # 获取搜索模式
        pattern = self.fileSearch.text() if hasattr(self, 'fileSearch') else None

        # 🚀 从索引筛选文件，传递完整数据避免重复查找
        filtered_files = []  # 改为存储(filename, file_data)元组
        total_files = 0

        for filename, file_data in self.file_index["files"].items():
            total_files += 1

            # 搜索筛选
            if pattern:
                try:
                    import re
                    if not re.search(pattern, filename):
                        continue
                except re.error:
                    # 正则表达式错误，跳过搜索筛选
                    pass

            # 内容类型和质量筛选
            if self._matches_filters_from_index_data(file_data):
                filtered_files.append((filename, file_data))  # 🚀 传递完整数据

        # 🚀 更新UI，传递完整数据
        self._update_file_list_ui_with_filtered_files(filtered_files)

        # 更新状态栏
        self.statusBar().showMessage(f"已筛选 {len(filtered_files)} 个文件（共 {total_files} 个）")

    def _matches_filters_from_index_data(self, file_data):
        """从索引数据检查筛选条件"""
        content_type = file_data["content_type"]
        quality = file_data["quality"]

        # 内容类型筛选
        content_type_match = (
            (content_type == 0 and self.content_type_filters["文本"]) or
            (content_type == 1 and self.content_type_filters["有线表"]) or
            (content_type == 2 and self.content_type_filters["无线表"]) or
            (content_type == -1 and self.content_type_filters["待确认"])
        )

        # 质量状态筛选
        quality_match = self.quality_filters.get(quality, False)

        return content_type_match and quality_match

    def _update_file_list_ui_with_filtered_files(self, filtered_files):
        """🚀 批量更新文件列表UI显示筛选后的文件（消除重复查找和磁盘I/O）"""
        if not hasattr(self, 'fileListWidget') or self.fileListWidget is None:
            LOGGER.error("fileListWidget不存在，无法更新UI")
            return

        # 🚀 暂停UI更新，避免频繁重绘
        self.fileListWidget.setUpdatesEnabled(False)

        try:
            # 清空现有列表
            self.fileListWidget.clear()
            self.imageList = []

            # 🚀 批量处理：先准备所有数据，再一次性添加到UI
            items_to_add = []
            valid_files = []

            for filename, file_data in filtered_files:
                # 🔧 修复路径处理：索引中的filename可能是各种格式
                full_path = self._normalize_file_path(filename)

                # 🚀 消除文件存在性检查：索引中的文件默认存在，避免磁盘I/O
                # 原代码：if not osp.exists(full_path): continue

                # 添加到有效文件列表
                valid_files.append(full_path)

                # 创建列表项
                display_name = osp.basename(full_path)
                item = QtWidgets.QListWidgetItem(display_name)
                item.setData(Qt.UserRole, full_path)  # 存储完整路径
                item.setFlags(Qt.ItemIsEnabled | Qt.ItemIsSelectable)

                # 🚀 设置文件状态显示，传递已知数据避免重复查找
                self._update_file_item_status_with_data(item, full_path, file_data)

                # 添加到待处理列表
                items_to_add.append(item)

            # 🚀 批量添加所有项目到UI（减少UI重绘次数）
            for item in items_to_add:
                self.fileListWidget.addItem(item)

            # 更新imageList
            self.imageList = valid_files

            LOGGER.debug(f"批量UI更新完成，显示 {len(items_to_add)} 个文件")

        finally:
            # 🚀 恢复UI更新，触发一次性重绘
            self.fileListWidget.setUpdatesEnabled(True)

    def _normalize_file_path(self, filename):
        """规范化文件路径，确保返回正确的完整路径"""
        if osp.isabs(filename):
            # 已经是绝对路径
            return filename

        # 相对路径，需要与当前目录拼接
        if self.lastOpenDir:
            full_path = osp.join(self.lastOpenDir, filename)
            return osp.normpath(full_path)

        # 兜底：返回原始路径
        return filename

    def _get_file_metadata_from_json(self, filename):
        """🚀 一次性从JSON文件获取所有元数据，避免重复I/O

        Returns:
            dict: {
                'content_type': int (0=文本, 1=有线表, 2=无线表, -1=待确认),
                'quality': str (合格/准合格/不合格/待校准),
                'has_annotation': bool,
                'json_path': str
            }
        """
        # 默认值
        result = {
            'content_type': -1,
            'quality': '待校准',
            'has_annotation': False,
            'json_path': None
        }

        try:
            json_filename = self._get_annotation_json_path(filename)
            result['json_path'] = json_filename

            if not osp.exists(json_filename):
                return result  # 没有JSON文件，返回默认值

            result['has_annotation'] = True

            with open(json_filename, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # 提取质量状态
            if isinstance(data, dict) and 'quality' in data:
                result['quality'] = data.get('quality', '待校准')
            elif isinstance(data, (list, dict)):
                result['quality'] = '待校准'  # 旧格式默认值

            # 提取内容类型
            if isinstance(data, dict):
                # 优先检查 type 字段（主要格式）
                if 'type' in data:
                    result['content_type'] = data['type']
                # 备用检查 table_type 字段
                elif 'table_type' in data:
                    result['content_type'] = data['table_type']
                elif 'tables' in data and data['tables']:
                    # 多表格格式，取第一个表格的类型
                    first_table = data['tables'][0]
                    if 'type' in first_table:
                        result['content_type'] = first_table['type']
                    else:
                        result['content_type'] = first_table.get('table_type', -1)

            return result

        except Exception as e:
            LOGGER.debug(f"获取文件元数据失败: {filename}, {e}")
            return result  # 返回默认值

    def _get_file_content_type(self, filename):
        """获取文件内容类型

        Returns:
            int: 0=文本, 1=有线表, 2=无线表, -1=待确认
        """
        metadata = self._get_file_metadata_from_json(filename)
        return metadata['content_type']

    def _matches_filters(self, filename):
        """🚀 检查文件是否匹配当前筛选条件（优先使用索引）"""
        # 优先从索引获取元数据
        if self.file_index and "files" in self.file_index:
            # 尝试直接匹配
            if filename in self.file_index["files"]:
                file_data = self.file_index["files"][filename]
                return self._matches_filters_from_index_data(file_data)

            # 如果直接匹配失败，尝试路径转换匹配（处理父目录聚合索引的情况）
            if hasattr(self, 'directory_aggregator') and self.directory_aggregator:
                # 父目录情况：将绝对路径转换为相对路径
                try:
                    relative_path = os.path.relpath(filename, self.lastOpenDir)
                    relative_path = os.path.normpath(relative_path)
                    if relative_path in self.file_index["files"]:
                        file_data = self.file_index["files"][relative_path]
                        return self._matches_filters_from_index_data(file_data)
                except ValueError:
                    pass

        # 兜底方案：从缓存获取元数据
        metadata = self.metadata_cache.get(filename)
        if metadata is None:
            # 缓存未命中，实时读取（兜底方案）
            LOGGER.debug(f"缓存和索引都未命中，实时读取: {osp.basename(filename)}")
            content_type = self._get_file_content_type(filename)
            quality = self._get_sample_quality_from_file(filename)
        else:
            # 从缓存读取（高性能）
            content_type = metadata['content_type']
            quality = metadata['quality']

        # 检查内容类型筛选
        content_type_match = False
        if content_type == 0 and self.content_type_filters["文本"]:
            content_type_match = True
        elif content_type == 1 and self.content_type_filters["有线表"]:
            content_type_match = True
        elif content_type == 2 and self.content_type_filters["无线表"]:
            content_type_match = True
        elif content_type == -1 and self.content_type_filters["待确认"]:
            content_type_match = True

        if not content_type_match:
            return False

        # 检查质量状态筛选
        quality_match = False
        if quality == "合格" and self.quality_filters["合格"]:
            quality_match = True
        elif quality == "准合格" and self.quality_filters["准合格"]:
            quality_match = True
        elif quality == "不合格" and self.quality_filters["不合格"]:
            quality_match = True
        elif quality == "待校准" and self.quality_filters["待校准"]:
            quality_match = True

        if not quality_match:
            return False

        return True

    def fileSelectionChanged(self):
        """文件选择变化处理（支持上一张/下一张自动适应）"""
        items = self.fileListWidget.selectedItems()
        if not items:
            return

        item = items[0]
        filename = item.data(Qt.UserRole)  # 获取完整路径

        if filename and osp.exists(filename):
            self.load_image(filename)
            # 🔧 自动适应窗口显示（支持上一张/下一张）
            QtCore.QTimer.singleShot(150, self.fit_window)
        else:
            self.statusBar().showMessage(f"文件不存在: {filename}")

    def _show_file_list_context_menu(self, position):
        """显示文件列表右键菜单"""
        item = self.fileListWidget.itemAt(position)
        if not item:
            return

        filename = item.data(Qt.UserRole)
        if not filename:
            return

        # 创建右键菜单
        menu = QtWidgets.QMenu(self)

        # 复制文件名（不含路径）
        copy_name_action = menu.addAction("复制文件名")
        copy_name_action.triggered.connect(lambda: self._copy_filename(filename, include_path=False))

        # 复制完整路径
        copy_path_action = menu.addAction("复制完整路径")
        copy_path_action.triggered.connect(lambda: self._copy_filename(filename, include_path=True))

        # 在鼠标位置显示菜单
        menu.exec_(self.fileListWidget.mapToGlobal(position))

    def _copy_filename(self, filename, include_path=False):
        """复制文件名到剪贴板"""
        if include_path:
            text_to_copy = filename
            message = "完整路径已复制到剪贴板"
        else:
            text_to_copy = osp.basename(filename)
            message = "文件名已复制到剪贴板"

        # 复制到剪贴板
        clipboard = QtWidgets.QApplication.clipboard()
        clipboard.setText(text_to_copy)

        # 显示提示信息
        self.statusBar().showMessage(f"✅ {message}: {text_to_copy}")
        LOGGER.info(f"复制到剪贴板: {text_to_copy}")

    @property
    def imageList(self):
        """获取当前图片列表"""
        return self._imageList if hasattr(self, '_imageList') else []

    @imageList.setter
    def imageList(self, value):
        """设置图片列表"""
        self._imageList = value

    def _auto_load_annotation_data(self, image_filename):
        """自动加载对应的标注数据"""
        # 1. 构造JSON文件路径（支持_table_annotation和同名JSON）
        json_filename = self._get_annotation_json_path(image_filename)
        base_name = osp.splitext(image_filename)[0]
        api_cache_filename = f"{base_name}-api-resp.json"

        # 2. 检查JSON文件是否存在
        if not osp.exists(json_filename):
            # 🆕 检查是否存在API缓存文件，如果有则自动转换并应用（不保存文件）
            if osp.exists(api_cache_filename):
                LOGGER.debug(f"发现API缓存文件，自动转换并应用: {api_cache_filename}")
                try:
                    self._auto_convert_and_apply_api_cache(image_filename, api_cache_filename)
                    return
                except Exception as e:
                    LOGGER.error(f"自动转换并应用API缓存失败: {e}")
                    # 转换失败，清空界面
                    self._clear_current_data()
                    return
            else:
                # 既没有标注文件也没有API缓存，清空界面
                self._clear_current_data()
                return

        # 3. 使用TableDataManager加载和转换数据
        try:
            from labelme.utils.table_data_manager import TableDataManager

            manager = TableDataManager()
            shapes = manager.load_from_file(json_filename)

            # 清空旧数据
            self._clear_current_data()

            if shapes:
                # 4. 直接调用MultiTableController的导入方法
                import_stats = self.multi_table_controller.import_table_data(shapes)

                total_cells = sum(import_stats.values())
                LOGGER.info(f"自动加载标注数据: {json_filename}, {len(import_stats)}个表格, {total_cells}个单元格")
                self.statusBar().showMessage(f"已加载标注数据: {osp.basename(json_filename)}")

                # 🆕 5. 自动显示逻辑结构（仅在成功导入时）
                self._auto_refresh_all_structure_views()
            else:
                # 🔧 修复：如果shapes为空，尝试处理空表格情况
                LOGGER.debug("shapes为空，检查是否有表格元数据")
                if hasattr(manager, 'last_loaded_table_metadata') and manager.last_loaded_table_metadata:
                    # 有表格元数据，创建空表格控制器
                    import_stats = self.multi_table_controller.import_table_metadata(manager.last_loaded_table_metadata)
                    LOGGER.info(f"加载空表格: {json_filename}, {len(import_stats)}个表格, 0个单元格")
                    self.statusBar().showMessage(f"已加载空表格: {osp.basename(json_filename)}")

                    # 刷新UI显示
                    self._auto_refresh_all_structure_views()
                else:
                    LOGGER.warning("导入的数据为空，且无表格元数据")
                    self.statusBar().showMessage("文件为空或格式不支持")

        except Exception as e:
            LOGGER.error(f"自动加载标注数据失败: {e}")
            # 🆕 导入失败时也要清空界面，避免显示上一张图片的残留信息
            self._clear_current_data()

    def _auto_convert_and_apply_api_cache(self, image_filename, api_cache_filename):
        """自动转换并应用API缓存（不保存标注文件）"""
        try:
            LOGGER.debug(f"开始自动转换并应用API缓存: {api_cache_filename}")

            # 1. 读取API缓存文件
            import json
            with open(api_cache_filename, 'r', encoding='utf-8') as f:
                api_response = json.load(f)

            # 2. 转换API响应数据
            from labelme.api import TextInResponseConverter
            converter = TextInResponseConverter()

            # 获取图像尺寸
            from PIL import Image
            with Image.open(image_filename) as img:
                image_size = img.size  # (width, height)

            # 转换数据为标准格式
            annotation_data = converter.convert(api_response, image_size)

            # 3. 清空现有数据
            self._clear_current_data()

            # 4. 使用与原来 _apply_api_results 相同的逻辑应用数据
            if annotation_data:
                tables = annotation_data
                LOGGER.debug(f"开始应用API缓存结果，表格数量: {len(tables)}")

                if not tables:
                    LOGGER.warning("API缓存未识别到任何表格")
                    self.statusBar().showMessage("⚠️ API缓存未识别到任何表格")
                    return

                # 🆕 检查单元格总数，避免界面卡顿
                table_count = len(tables)
                total_cells = sum(len(table.get('cells', [])) for table in tables)

                if total_cells > 1000:
                    # 单元格过多，不应用可视化，弹出警告
                    LOGGER.warning(f"API识别结果单元格过多({total_cells}个)，跳过可视化应用")

                    # 弹出警告对话框
                    QtWidgets.QMessageBox.warning(
                        self,
                        "单元格数量过多",
                        f"API识别到 {table_count} 个表格，共 {total_cells} 个单元格。\n\n"
                        f"为避免界面卡顿，建议放弃该样本的打标工作。\n"
                        f"如需处理，请考虑使用其他工具或手动分割图片。"
                    )

                    # 更新状态栏
                    self.statusBar().showMessage(f"⚠️ 跳过可视化：单元格过多({total_cells}个，超过1000个限制)")
                    return

                # 单元格数量合理，正常应用可视化
                self._create_table_from_api_data_without_save(annotation_data)

                # 刷新界面
                self._refresh_ui_after_api_labeling()

                # 显示成功状态
                self.statusBar().showMessage(f"✅ 已应用API识别结果（未保存）: {table_count}个表格，共{total_cells}个单元格")

                LOGGER.debug(f"API缓存结果应用完成")
            else:
                LOGGER.warning("API缓存数据为空")
                self.statusBar().showMessage("⚠️ API缓存数据为空")

        except Exception as e:
            LOGGER.error(f"自动转换并应用API缓存失败: {e}")
            raise

    def _create_table_from_api_data_without_save(self, annotation_data):
        """根据API数据创建表格（不保存临时文件）"""
        try:
            # 使用与原方法相同的逻辑，但不保存和删除临时文件
            # 创建临时文件路径（但不实际保存）
            temp_json = os.path.splitext(self.filename)[0] + '_temp_api_cache.json'

            # 保存临时标注文件（用于load_file）
            with open(temp_json, 'w', encoding='utf-8') as f:
                json.dump(annotation_data, f, ensure_ascii=False, indent=2)

            LOGGER.debug(f"已保存临时API缓存文件: {temp_json}")

            # 使用标准的加载逻辑（这是关键步骤）
            self.load_file(temp_json)

            # 删除临时文件
            os.remove(temp_json)
            LOGGER.debug("已删除临时API缓存文件")

        except Exception as e:
            LOGGER.error(f"应用API缓存结果失败: {e}")
            # 确保清理临时文件
            temp_json = os.path.splitext(self.filename)[0] + '_temp_api_cache.json'
            if os.path.exists(temp_json):
                try:
                    os.remove(temp_json)
                    LOGGER.debug("已清理临时API缓存文件")
                except:
                    pass
            raise

    def _auto_refresh_all_structure_views(self):
        """自动刷新所有表格的逻辑结构视图

        复用现有的_refresh_structure_widget方法，实现逻辑结构的实时可视化
        """
        try:
            # 获取所有表格控制器
            all_controllers = self.multi_table_controller.get_all_controllers()

            if not all_controllers:
                # 🆕 没有表格时清空属性面板显示
                self._refresh_table_type_visualization(None)
                return

            # 遍历所有表格，刷新逻辑结构
            refreshed_count = 0
            for table_id, controller in all_controllers.items():
                # 直接调用现有的刷新方法
                if hasattr(controller, '_refresh_structure_widget'):
                    controller._refresh_structure_widget()
                    refreshed_count += 1
                    LOGGER.debug(f"表格 {table_id} 逻辑结构已自动显示")

            # 确保界面组件绑定正确
            self._bind_structure_to_active_controller()
            self._bind_properties_to_active_controller()

            # 🆕 刷新表格类型可视化显示
            self._refresh_table_type_visualization(all_controllers)

            LOGGER.debug(f"自动显示逻辑结构完成: {refreshed_count}/{len(all_controllers)} 个表格")

            if refreshed_count > 0:
                self.statusBar().showMessage(f"已自动显示 {refreshed_count} 个表格的逻辑结构")

        except Exception as e:
            LOGGER.error(f"自动显示逻辑结构失败: {e}")

    def _refresh_table_type_visualization(self, all_controllers):
        """刷新表格类型可视化显示（仅显示，不修改数据）

        Args:
            all_controllers: 所有表格控制器字典，None表示清空显示
        """
        try:
            if not hasattr(self, 'properties_widget') or not self.properties_widget:
                return

            if not all_controllers:
                # 没有表格时清空属性面板
                self.properties_widget._clear_display()
                LOGGER.debug("已清空表格类型可视化显示")
                return

            # 🔧 关键修复：暂时阻塞属性变更信号，避免修改实际数据
            self.properties_widget._updating_from_selection = True

            try:
                # 获取默认显示的表格类型
                default_table_type = self._get_default_table_type(all_controllers)

                if default_table_type is not None:
                    # 🔧 修复：更新属性面板的表格类型按钮显示（仅UI显示，不触发信号）
                    if hasattr(self.properties_widget, '_update_table_type_button_state'):
                        self.properties_widget._update_table_type_button_state(default_table_type)

                    # 🔧 修复：同步更新所有控制器的表格类型（确保保存时数据一致）
                    for controller in all_controllers.values():
                        if hasattr(controller, 'set_table_type'):
                            controller.set_table_type(default_table_type)

                    # 根据表格数量显示不同的提示信息
                    table_count = len(all_controllers)
                    if table_count == 1:
                        LOGGER.debug(f"单表格类型可视化已刷新: 类型={default_table_type} (UI+控制器同步)")
                    else:
                        first_table_id = min(all_controllers.keys())
                        LOGGER.debug(f"多表格类型可视化已刷新: 使用第一张表格(ID={first_table_id})的类型={default_table_type} (UI+控制器同步)")
                else:
                    # 无法确定类型时清空显示
                    self.properties_widget._clear_display()
                    LOGGER.warning("无法确定表格类型，已清空显示")

            finally:
                # 🔧 恢复信号处理
                self.properties_widget._updating_from_selection = False

        except Exception as e:
            LOGGER.error(f"刷新表格类型可视化失败: {e}")
            # 确保信号恢复
            if hasattr(self, 'properties_widget') and self.properties_widget:
                self.properties_widget._updating_from_selection = False

    def _get_default_table_type(self, all_controllers):
        """获取默认显示的表格类型

        Args:
            all_controllers: 所有表格控制器字典

        Returns:
            int: 表格类型 (0=纯文本, 1=有线表格, 2=无线表格)，None表示无法确定
        """
        try:
            if not all_controllers:
                return None

            if len(all_controllers) == 1:
                # 单表格：分析表格中单元格的类型分布
                controller = list(all_controllers.values())[0]
                return self._get_controller_dominant_table_type(controller)
            else:
                # 多表格：使用第一张表格的主导类型作为默认显示
                first_table_id = min(all_controllers.keys())
                first_controller = all_controllers[first_table_id]
                return self._get_controller_dominant_table_type(first_controller)

        except Exception as e:
            LOGGER.error(f"获取默认表格类型失败: {e}")
            return None

    def _get_controller_dominant_table_type(self, controller):
        """从控制器获取主导表格类型（分析所有单元格，找出最常见的类型）

        Args:
            controller: TableController实例

        Returns:
            int: 主导表格类型，None表示无法获取
        """
        try:
            if not controller or not hasattr(controller, 'table_cells'):
                return None

            if not controller.table_cells:
                # 🆕 修复：没有单元格时使用控制器保存的表格类型
                return controller.get_table_type() if hasattr(controller, 'get_table_type') else 1

            # 统计各种类型的数量
            type_counts = {}
            for cell in controller.table_cells:
                if hasattr(cell, 'get_table_type'):
                    cell_type = cell.get_table_type()
                    type_counts[cell_type] = type_counts.get(cell_type, 0) + 1

            if not type_counts:
                return 1  # 无法获取类型时返回默认值

            # 找出数量最多的类型
            dominant_type = max(type_counts, key=type_counts.get)

            # 🔍 详细日志：显示类型分布情况
            total_cells = len(controller.table_cells)
            type_names = ["纯文本", "有线表格", "无线表格"]
            LOGGER.debug(f"表格类型分析: 总单元格={total_cells}")
            for table_type, count in sorted(type_counts.items()):
                type_name = type_names[table_type] if 0 <= table_type < len(type_names) else f"未知({table_type})"
                percentage = (count / total_cells) * 100
                LOGGER.debug(f"   {type_name}: {count}个 ({percentage:.1f}%)")

            dominant_name = type_names[dominant_type] if 0 <= dominant_type < len(type_names) else f"未知({dominant_type})"
            LOGGER.debug(f"主导类型: {dominant_name} (显示在UI中)")

            return dominant_type

        except Exception as e:
            LOGGER.error(f"从控制器获取主导表格类型失败: {e}")
            return None

    def _get_controller_table_type(self, controller):
        """从控制器获取表格类型（保留原方法，用于兼容性）

        Args:
            controller: TableController实例

        Returns:
            int: 表格类型，None表示无法获取
        """
        try:
            if not controller or not hasattr(controller, 'table_cells'):
                return None

            # 从第一个单元格获取表格类型
            if controller.table_cells:
                first_cell = controller.table_cells[0]
                if hasattr(first_cell, 'get_table_type'):
                    return first_cell.get_table_type()

            # 如果没有单元格，返回默认类型
            return 1  # 默认为有线表格

        except Exception as e:
            LOGGER.error(f"从控制器获取表格类型失败: {e}")
            return None

    # 🆕 可选：手动刷新逻辑结构的方法（可绑定到工具栏按钮）
    def refresh_current_logical_structure(self):
        """手动刷新当前表格的逻辑结构"""
        active_controller = self.multi_table_controller.get_active_controller()

        if not active_controller:
            QtWidgets.QMessageBox.information(self, "提示", "请先创建表格或选择表格")
            return

        if hasattr(active_controller, '_refresh_structure_widget'):
            active_controller._refresh_structure_widget()
            self.statusBar().showMessage("✅ 逻辑结构已刷新")
        else:
            QtWidgets.QMessageBox.critical(self, "错误", "当前表格控制器不支持刷新功能")
    def _clear_current_data(self):
        """清空当前标注数据（包含UI界面清空）"""
        # 清空Canvas中的所有shapes
        self.canvas.shapes.clear()

        # 重置MultiTableController
        if self.multi_table_controller:
            self.multi_table_controller.reset_all_controllers()

        # 🆕 清空逻辑结构界面
        if hasattr(self, 'structure_widget') and self.structure_widget:
            self.structure_widget.clear_grid()
            LOGGER.debug("已清空逻辑结构界面")

        # 🆕 清空属性面板
        if hasattr(self, 'properties_widget') and self.properties_widget:
            self.properties_widget._clear_display()
            LOGGER.debug("已清空属性面板")

        # 🆕 清空表格类型可视化
        self._refresh_table_type_visualization(None)

        # 🆕 自动切换到默认模式（选择模式）
        self._reset_to_default_mode("清空数据")

        # 更新Canvas显示
        self.canvas.update()
        LOGGER.debug("已清空当前标注数据和所有UI界面")

    def shapeSelectionChanged(self, selected_shapes):
        """Canvas选择变更处理（复制自app.py的逻辑）"""
        # 🔧 屏蔽过多的调试输出，只在调试模式下显示
        debug_selection = False  # 设置为 True 可开启选择调试
        
        if debug_selection:
            LOGGER.debug(f"[MAIN] shapeSelectionChanged被调用，shapes数量: {len(selected_shapes)}")

        # 🔧 修复：避免重复设置，检查Canvas的selectedShapes是否已经是最新的
        if self.canvas.selectedShapes != selected_shapes:
            # 清除之前的选中状态
            for shape in self.canvas.selectedShapes:
                shape.selected = False
                if debug_selection:
                    LOGGER.debug(f"[MAIN] 清除选中: {type(shape).__name__}")

            # 设置新的选中状态
            self.canvas.selectedShapes = selected_shapes
            
        # 确保所有选中的形状都设置了selected=True
        for shape in selected_shapes:
            if not shape.selected:
                shape.selected = True
                if debug_selection:
                    LOGGER.debug(f"[MAIN] 设置选中: {type(shape).__name__}")

        # 🆕 启用/禁用删除按钮
        n_selected = len(selected_shapes)
        self.action_delete.setEnabled(n_selected > 0)

        # 🔗 然后通知multi_table_controller
        if self.multi_table_controller:
            self.multi_table_controller.handle_canvas_event('selection_changed', selected_shapes)

        # 触发重绘
        self.canvas.update()

    def _auto_load_test_directory(self):
        """启动时自动加载测试数据目录"""
        # 🆕 尝试加载测试数据目录
        test_data_dir = "examples/table_recognition/data"
        
        # 检查相对路径和绝对路径
        possible_paths = [
            test_data_dir,
            osp.join(osp.dirname(__file__), "..", test_data_dir),
            osp.abspath(test_data_dir)
        ]
        
        for test_path in possible_paths:
            if osp.exists(test_path) and osp.isdir(test_path):
                LOGGER.info(f"自动加载测试数据目录: {test_path}")
                try:
                    # 设置当前目录
                    self.lastOpenDir = test_path
                    # 🔧 延时加载，确保UI完全初始化后再加载状态和导入文件
                    QtCore.QTimer.singleShot(300, lambda: self._delayed_load_directory(test_path))
                    self.statusBar().showMessage(f"自动加载测试数据: {osp.basename(test_path)}")
                    return
                except Exception as e:
                    LOGGER.error(f"自动加载失败: {e}")
                    continue
        
        # 如果没有找到测试数据，创建默认测试图像
        LOGGER.warning("未找到测试数据目录，创建默认测试图像")
        QtCore.QTimer.singleShot(300, self._create_test_image)

    def _delayed_load_directory(self, test_path):
        """延时加载目录，确保UI完全初始化"""
        try:
            LOGGER.info(f"开始延时加载目录: {test_path}")

            # 确保UI已初始化
            if not hasattr(self, 'fileListWidget') or self.fileListWidget is None:
                LOGGER.error("UI未初始化，无法加载目录")
                return

            # 加载该目录的浏览状态
            self._load_viewed_files_state()

            # 导入目录图片
            self.importDirImages(test_path)

            LOGGER.info(f"延时加载目录完成: {test_path}")

        except Exception as e:
            LOGGER.error(f"延时加载目录失败: {e}")
            import traceback
            traceback.print_exc()

    def _create_test_image(self):
        """创建测试图像"""
        pixmap = QtGui.QPixmap(1000, 800)
        pixmap.fill(Qt.white)

        painter = QtGui.QPainter(pixmap)

        # 绘制网格参考线
        painter.setPen(QtGui.QPen(Qt.lightGray, 1, Qt.DashLine))
        for x in range(0, 1000, 50):
            painter.drawLine(x, 0, x, 800)
        for y in range(0, 800, 50):
            painter.drawLine(0, y, 1000, y)

        # 绘制标题
        painter.setPen(QtGui.QPen(Qt.black, 2))
        painter.setFont(QtGui.QFont("Arial", 16, QtGui.QFont.Bold))
        painter.drawText(400, 50, "表格标注测试图像")

        # 绘制表格示例轮廓
        painter.setPen(QtGui.QPen(Qt.blue, 2, Qt.DashLine))
        painter.drawRect(100, 100, 300, 200)  # 第一个表格区域
        painter.drawRect(500, 150, 400, 300)  # 第二个表格区域

        painter.end()

        self.canvas.loadPixmap(pixmap)
        self.enable_zoom_actions()
        # 自动适应窗口
        QtCore.QTimer.singleShot(100, self.fit_window)
        LOGGER.debug("测试图像创建完成")

    def prev_image(self):
        """上一张图片"""
        if not self.fileListWidget or self.fileListWidget.count() == 0:
            self.statusBar().showMessage("没有图片列表")
            return
            
        current_row = self.fileListWidget.currentRow()
        if current_row <= 0:
            self.statusBar().showMessage("已经是第一张图片")
            return
            
        # 选择上一张
        self.fileListWidget.setCurrentRow(current_row - 1)
        self.statusBar().showMessage(f"上一张: {current_row}/{self.fileListWidget.count()}")

        # 🆕 自动切换到默认模式（选择模式）
        # 注意：不需要重复调用，因为fileSelectionChanged已经会调用_reset_to_default_mode("图片切换")
        # self._reset_to_default_mode("手动切换上一张")

    def next_image(self):
        """下一张图片"""
        if not self.fileListWidget or self.fileListWidget.count() == 0:
            self.statusBar().showMessage("没有图片列表")
            return
            
        current_row = self.fileListWidget.currentRow()
        if current_row >= self.fileListWidget.count() - 1:
            self.statusBar().showMessage("已经是最后一张图片")
            return
            
        # 选择下一张
        self.fileListWidget.setCurrentRow(current_row + 1)
        self.statusBar().showMessage(f"下一张: {current_row + 2}/{self.fileListWidget.count()}")

        # 🆕 自动切换到默认模式（选择模式）
        # 注意：不需要重复调用，因为fileSelectionChanged已经会调用_reset_to_default_mode("图片切换")
        # self._reset_to_default_mode("手动切换下一张")

    def _auto_go_to_next_image(self):
        """自动切换到下一张图片（保存后调用）"""
        if not self.fileListWidget or self.fileListWidget.count() == 0:
            LOGGER.debug("没有图片列表，无法自动切换")
            return
            
        current_row = self.fileListWidget.currentRow()
        if current_row >= self.fileListWidget.count() - 1:
            LOGGER.debug("已经是最后一张图片，无法自动切换")
            self.statusBar().showMessage("已保存 - 这是最后一张图片")
            return
            
        # 自动选择下一张
        self.fileListWidget.setCurrentRow(current_row + 1)
        next_filename = self.fileListWidget.currentItem().data(Qt.UserRole) if self.fileListWidget.currentItem() else "未知"
        self.statusBar().showMessage(f"已保存并自动切换到下一张: {osp.basename(next_filename) if next_filename else '未知'}")
        LOGGER.info(f"自动切换到下一张: {next_filename}")

        # 🆕 自动切换到默认模式（选择模式）
        # 注意：不需要重复调用，因为fileSelectionChanged已经会调用_reset_to_default_mode("图片切换")
        # self._reset_to_default_mode("自动保存切换")

    def _has_annotation_file(self, image_filename):
        """检查图片是否有对应的标注文件

        Returns:
            bool: True表示已保存过标注文件
        """
        metadata = self._get_file_metadata_from_json(image_filename)
        return metadata['has_annotation']

    def _get_file_view_status(self, filename, file_data=None):
        """获取文件浏览状态，优先使用索引数据避免磁盘I/O

        Args:
            filename: 文件路径
            file_data: 可选的索引数据，如果提供则优先使用

        Returns:
            str: 'never_viewed', 'viewed_no_save', 'saved'
        """
        # 🚀 优先使用传入的索引数据
        if file_data and "has_annotation" in file_data:
            has_annotation = file_data["has_annotation"]
            LOGGER.debug(f"使用索引数据获取状态: {osp.basename(filename)} -> has_annotation={has_annotation}")
        else:
            # 兜底：检查是否有标注文件（磁盘I/O）
            LOGGER.debug(f"索引数据不可用，回退到磁盘检查: {osp.basename(filename)}")
            has_annotation = self._has_annotation_file(filename)

        if has_annotation:
            return 'saved'
        elif filename in self.viewed_files:
            return 'viewed_no_save'
        else:
            return 'never_viewed'

    def _update_file_item_status(self, item, filename):
        """更新文件列表项的状态显示（向后兼容方法）"""
        # 🚀 优先从索引获取完整的文件数据
        file_data = {}

        # 获取质量状态
        quality = self._get_quality_from_index_or_file(filename)
        if quality is None:
            quality = "待校准"
        file_data["quality"] = quality

        # 🚀 获取has_annotation状态，优先从索引获取
        if self.file_index and "files" in self.file_index:
            index_key = self._find_file_in_index(filename)
            if index_key and index_key in self.file_index["files"]:
                file_data["has_annotation"] = self.file_index["files"][index_key].get("has_annotation", False)
            else:
                # 索引中没有，回退到磁盘检查
                file_data["has_annotation"] = self._has_annotation_file(filename)
        else:
            # 没有索引，回退到磁盘检查
            file_data["has_annotation"] = self._has_annotation_file(filename)

        # 🚀 调用优化方法
        self._update_file_item_status_with_data(item, filename, file_data)

    def _update_file_item_status_with_data(self, item, filename, file_data):
        """🚀 使用已知数据更新文件列表项状态，避免重复查找和磁盘I/O"""
        base_filename = osp.basename(filename)

        # 🚀 直接从file_data获取has_annotation，避免磁盘I/O
        has_annotation = file_data.get("has_annotation", False)
        quality = file_data.get("quality", "待校准")

        # 🚀 根据索引数据确定状态，无需磁盘检查
        if has_annotation:
            status = 'saved'
        elif filename in self.viewed_files:
            status = 'viewed_no_save'
        else:
            status = 'never_viewed'

        # 🆕 左侧状态标记：不同状态用不同emoji
        if status == 'saved':
            # 已保存：绿色勾勾
            item.setText(f"✅ {base_filename}")
            item.setToolTip(f"已保存标注: {filename}\n样本质量: {quality}")
        elif status == 'viewed_no_save':
            # 已浏览但未保存：绿色圆点
            item.setText(f"🟢 {base_filename}")
            item.setToolTip(f"已浏览未保存: {filename}\n样本质量: {quality}")
        else:  # never_viewed
            # 从未浏览：黄色圆点
            item.setText(f"🟡 {base_filename}")
            item.setToolTip(f"未浏览: {filename}\n样本质量: {quality}")

        # 🆕 根据样本质量设置背景色高亮
        if quality == "合格":
            # 合格：绿色高亮
            item.setBackground(QtGui.QColor(144, 238, 144, 100))  # 浅绿色
        elif quality == "准合格":
            # 准合格：稍深的蓝色高亮
            item.setBackground(QtGui.QColor(187, 222, 251, 120))  # 稍深的蓝色
        elif quality == "不合格":
            # 不合格：红色高亮
            item.setBackground(QtGui.QColor(255, 182, 193, 100))  # 浅红色
        elif quality == "待校准":
            # 待校准：黄色高亮
            item.setBackground(QtGui.QColor(255, 255, 224, 100))  # 浅黄色
        else:
            # 默认：无背景色
            item.setBackground(QtGui.QColor())  # 透明背景

    def _get_quality_from_index_or_file(self, filename):
        """🚀 从索引获取质量状态，使用O(1)basename映射表优化查找"""
        # 优先从索引获取
        if self.file_index and "files" in self.file_index:
            # 🔧 精确匹配
            if filename in self.file_index["files"]:
                quality = self.file_index["files"][filename]["quality"]
                LOGGER.debug(f"从索引精确匹配获取质量: {osp.basename(filename)} -> {quality}")
                return quality

            # 🚀 使用O(1)basename映射表查找
            if "basename_map" in self.file_index:
                base_name = osp.basename(filename)
                if base_name in self.file_index["basename_map"]:
                    full_path = self.file_index["basename_map"][base_name]
                    if full_path in self.file_index["files"]:
                        quality = self.file_index["files"][full_path]["quality"]
                        LOGGER.debug(f"通过basename映射表获取质量: {base_name} -> {quality}")
                        return quality

            # 🔧 兜底：线性搜索（向后兼容旧索引）
            base_name = osp.basename(filename)
            for indexed_filename in self.file_index["files"]:
                if osp.basename(indexed_filename) == base_name:
                    quality = self.file_index["files"][indexed_filename]["quality"]
                    LOGGER.debug(f"通过线性搜索获取质量: {base_name} -> {quality}")
                    return quality

            LOGGER.warning(f"索引中未找到文件: {filename}")
            LOGGER.debug(f"索引中的文件: {list(self.file_index['files'].keys())[:3]}...")

        # 🔧 修复：索引中找不到时返回None，让调用方决定如何处理
        LOGGER.debug(f"索引中未找到文件，返回None: {osp.basename(filename)}")
        return None

    def _refresh_current_file_status(self):
        """🔧 优化：刷新当前文件在列表中的状态显示（避免重复加载索引）"""
        if not self.filename or not self.fileListWidget:
            LOGGER.debug("无法刷新文件状态：filename或fileListWidget为空")
            return

        LOGGER.debug(f"开始刷新文件状态: {osp.basename(self.filename)}")

        # 🔧 优化：不再每次都重新加载索引，直接使用内存中的状态
        # 只有在索引为空时才加载（避免重复加载）
        if not hasattr(self, 'file_index') or self.file_index is None:
            LOGGER.debug("索引为空，需要加载")
            self._load_viewed_files_state()

        # 查找当前文件对应的列表项
        found = False
        for i in range(self.fileListWidget.count()):
            item = self.fileListWidget.item(i)
            if item and item.data(Qt.UserRole) == self.filename:
                LOGGER.debug(f"找到文件列表项，更新状态: {osp.basename(self.filename)}")
                self._update_file_item_status(item, self.filename)
                found = True
                break

        if not found:
            LOGGER.warning(f"在文件列表中未找到文件: {osp.basename(self.filename)}")

    def eventFilter(self, source, event):
        """🆕 事件过滤器处理键盘事件"""
        if source == self.canvas:
            # 处理按键按下事件
            if event.type() == QEvent.KeyPress:
                LOGGER.debug(f"Canvas键盘事件: key={event.key()}, selectedShapes={len(self.canvas.selectedShapes) if self.canvas.selectedShapes else 0}")
                #C键盘进入网格拖拽模式
                if event.key() == QtCore.Qt.Key_C and not event.isAutoRepeat():
                    if self.mode_manager.current_mode != OperationMode.GRID_DRAG:
                        self.set_grid_drag_mode()
                        return True

                # 🆕 处理空格键按下 - 隐藏表格可视化（按住模式）
                if event.key() == QtCore.Qt.Key_Space:
                    # 🔧 修复：空格键与工具栏按钮行为一致
                    if not hasattr(self, '_space_key_pressed') or not self._space_key_pressed:
                        LOGGER.debug("空格键按下：隐藏表格")
                        self._space_key_pressed = True
                        self._hide_table_visualization()
                        # 注意：不设置按钮状态，避免与工具栏按钮冲突
                    return True

                # 🆕 处理M键按下 - 切换逻辑可视化
                if event.key() == QtCore.Qt.Key_M and not event.isAutoRepeat():
                    LOGGER.debug("M键按下：切换逻辑可视化")
                    self.toggle_logical_visualization()
                    return True

                # ESC键退出快速表格选区模式
                if event.key() == Qt.Key_Escape:
                    if hasattr(self.canvas, 'createMode') and self.canvas.createMode == "quick_table_region":
                        self.set_select_mode()
                        return True

                # Delete键或Backspace键删除选中的形状
                if event.key() in (Qt.Key_Delete, Qt.Key_Backspace):
                    key_name = "Delete" if event.key() == Qt.Key_Delete else "Backspace"
                    LOGGER.debug(f"{key_name}键被按下，选中形状数量: {len(self.canvas.selectedShapes) if self.canvas.selectedShapes else 0}")
                    if self.canvas.selectedShapes:
                        LOGGER.debug(f"调用delete_selected_shape ({key_name})")
                        self.delete_selected_shape()
                        return True
                    else:
                        LOGGER.debug(f"没有选中的形状，不执行删除 ({key_name})")

            # 处理按键释放事件
            elif event.type() == QEvent.KeyRelease:
                # 🆕 处理空格键释放 - 恢复表格可视化（松开模式）
                if event.key() == QtCore.Qt.Key_Space and not event.isAutoRepeat():
                    # 🔧 修复：空格键与工具栏按钮行为一致
                    if hasattr(self, '_space_key_pressed') and self._space_key_pressed:
                        LOGGER.debug("空格键松开：恢复表格")
                        self._space_key_pressed = False
                        self._show_table_visualization()
                        # 注意：不设置按钮状态，避免与工具栏按钮冲突
                    return True
                    
        # 调用原始事件过滤器
        return super().eventFilter(source, event)

    def keyPressEvent(self, event):
        """🆕 主窗口键盘事件处理"""


        # Delete键或Backspace键删除选中的形状
        if event.key() in (Qt.Key_Delete, Qt.Key_Backspace):
            if self.canvas and self.canvas.selectedShapes:
                key_name = "Delete" if event.key() == Qt.Key_Delete else "Backspace"
                LOGGER.debug(f"[MAIN] {key_name}键处理")
                self.delete_selected_shape()
                return
        
        # 调用父类的键盘事件处理
        super().keyPressEvent(event)

    def keyReleaseEvent(self, event):
        """处理键盘释放事件"""
        # 调用父类的键盘释放事件处理
        super().keyReleaseEvent(event)

    def closeEvent(self, event):
        """关闭事件"""
        reply = QtWidgets.QMessageBox.question(
            self, "确认退出", "确定要退出 TableLabelMe 吗？",
            QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No
        )

        if reply == QtWidgets.QMessageBox.Yes:
            # 保存当前目录和文件的关系
            if self.filename and self.lastOpenDir:
                self.last_files_by_dir[self.lastOpenDir] = self.filename
                settings = QtCore.QSettings("labelme", "tableme")
                settings.setValue("last_files_by_dir", self.last_files_by_dir)

            # 🔧 优化：应用退出时强制保存浏览状态
            if hasattr(self, '_save_timer') and self._save_timer:
                self._save_timer.stop()  # 停止延迟保存定时器
            self._save_viewed_files_state()  # 立即保存
            event.accept()
        else:
            event.ignore()

    # ===== 🆕 撤销/重做功能 =====

    def undo_last_operation(self):
        """撤销上一步操作（使用新的命令系统）"""
        if not self.history_manager.can_undo():
            self.statusBar().showMessage("没有可撤销的操作")
            return

        try:
            success = self.history_manager.undo()

            if success:
                # 🔧 修复：使用Canvas的deSelectShape方法正确清除选中状态
                if self.canvas:
                    self.canvas.deSelectShape()  # 这会同时清除selectedShapes列表和每个shape的selected属性

                # 🚫 彻底修复：不再同步MultiTableController
                # 避免重复导入和指数级增长问题

                # 刷新界面
                if self.canvas:
                    self.canvas.update()
                self.statusBar().showMessage("✅ 已撤销上一步操作")
                LOGGER.debug("命令系统撤销操作成功")
            else:
                self.statusBar().showMessage("❌ 撤销操作失败")
                LOGGER.error("命令系统撤销操作失败")

        except Exception as e:
            self.statusBar().showMessage(f"撤销操作失败: {e}")
            LOGGER.error(f"撤销操作异常: {e}")

    def redo_last_operation(self):
        """重做上一步操作（使用新的命令系统）"""
        if not self.history_manager.can_redo():
            self.statusBar().showMessage("没有可重做的操作")
            return

        try:
            success = self.history_manager.redo()

            if success:
                # 🔧 修复：使用Canvas的deSelectShape方法正确清除选中状态
                if self.canvas:
                    self.canvas.deSelectShape()  # 这会同时清除selectedShapes列表和每个shape的selected属性

                # 🔧 修复：不需要额外同步，命令本身已处理
                # 重做操作由命令系统完成，不需要额外同步

                # 刷新界面
                if self.canvas:
                    self.canvas.update()
                self.statusBar().showMessage("✅ 已重做上一步操作")
                LOGGER.debug("命令系统重做操作成功")
            else:
                self.statusBar().showMessage("❌ 重做操作失败")
                LOGGER.error("命令系统重做操作失败")

        except Exception as e:
            self.statusBar().showMessage(f"重做操作失败: {e}")
            LOGGER.error(f"重做操作异常: {e}")

    def _update_undo_redo_actions(self):
        """更新撤销/重做按钮的启用状态（使用新的命令系统）"""
        if self.history_manager:
            # 使用命令系统的状态
            can_undo = self.history_manager.can_undo()
            can_redo = self.history_manager.can_redo()
            
            self.action_undo.setEnabled(can_undo)
            self.action_redo.setEnabled(can_redo)
            
            # 更新工具提示显示当前命令信息
            if can_undo:
                current_cmd = self.history_manager.get_current_command()
                if current_cmd:
                    self.action_undo.setToolTip(f"撤销: {current_cmd.description}")
            else:
                self.action_undo.setToolTip("撤销上一步操作")
                
            if can_redo:
                next_cmd = self.history_manager.get_next_command()
                if next_cmd:
                    self.action_redo.setToolTip(f"重做: {next_cmd.description}")
            else:
                self.action_redo.setToolTip("重做上一步操作")
        else:
            # 回退到原有逻辑
            can_undo = self.canvas.isShapeRestorable if self.canvas else False
            self.action_undo.setEnabled(can_undo)
            self.action_redo.setEnabled(False)

    def _store_shapes_for_undo(self):
        """为撤销功能存储当前状态（保持兼容性，但优先使用命令系统）"""
        # 🔧 保持向后兼容，但提示应使用命令系统
        if self.canvas:
            self.canvas.storeShapes()
            self._update_undo_redo_actions()
            LOGGER.warning("使用了传统备份机制，建议改用命令系统")

    # ===== 🆕 命令系统事件处理 =====

    def _on_command_executed(self, command):
        """命令执行事件处理"""
        LOGGER.debug(f"命令已执行: {command.description}")
      #  QtCore.QTimer.singleShot(100, self.analyze_table_structure)


    def _on_command_undone(self, command):
        """命令撤销事件处理"""
        LOGGER.debug(f"命令已撤销: {command.description}")

        # 🚫 彻底修复：撤销重做时完全不同步MultiTableController
        # 避免重复导入和指数级增长问题
        # MultiTableController会在用户下次操作时自动同步

        # 统一的撤销后处理
        self._refresh_ui_after_undo_redo()
       # QtCore.QTimer.singleShot(100, self.analyze_table_structure)


    def _on_command_redone(self, command):
        """命令重做事件处理"""
        LOGGER.debug(f"命令已重做: {command.description}")

        # 🔧 修复：重做后不需要同步，因为命令本身已经正确处理了
        # 只需要刷新UI即可

        # 统一的重做后处理
        self._refresh_ui_after_undo_redo()
       # QtCore.QTimer.singleShot(100, self.analyze_table_structure)


    def _refresh_ui_after_undo_redo(self):
        """撤销/重做后的UI刷新"""
        # 🔧 修复：使用Canvas的deSelectShape方法正确清除选中状态
        if self.canvas:
            self.canvas.deSelectShape()  # 这会同时清除selectedShapes列表和每个shape的selected属性

        # 刷新属性面板
        if hasattr(self, 'properties_widget') and self.properties_widget:
            # 检查属性面板是否有clear_selection方法
            if hasattr(self.properties_widget, 'clear_selection'):
                self.properties_widget.clear_selection()
            elif hasattr(self.properties_widget, 'update_from_selection'):
                # 使用空选择更新属性面板
                self.properties_widget.update_from_selection([])

        # 刷新结构视图
        self._auto_refresh_all_structure_views()

    # ===== 🆕 提供命令系统接入点 =====

    def execute_create_cell_command(self, points, label="", **properties):
        """执行创建单元格命令的便捷方法"""
        try:
            create_cmd = CreateCellCommand(
                self.canvas, 
                points, 
                label or f"cell_{len(self.canvas.shapes) + 1}",
                properties
            )
            return self.history_manager.execute_command(create_cmd)
        except Exception as e:
            LOGGER.error(f"创建单元格命令失败: {e}")
            return False

    def execute_batch_create_command(self, rows, cols, center_point, **options):
        """执行批量创建单元格命令的便捷方法"""
        try:
            batch_cmd = BatchCreateCellCommand(
                self.canvas, rows, cols, center_point, **options
            )
            return self.history_manager.execute_command(batch_cmd)
        except Exception as e:
            LOGGER.error(f"批量创建命令失败: {e}")
            return False

    def get_history_statistics(self):
        """获取历史管理器统计信息"""
        if self.history_manager:
            return self.history_manager.get_statistics()
        return None

    # ===== 🆕 模式管理器事件处理 =====

    def _on_mode_changed(self, old_mode, new_mode):
        """模式切换事件处理"""
        LOGGER.debug(f"模式切换: {old_mode.value if old_mode else 'None'} -> {new_mode.value}")
        
        # 更新界面状态
        self._update_mode_ui_state(new_mode)

    def _update_mode_ui_state(self, mode):
        """根据当前模式更新UI状态"""
        if mode == OperationMode.SELECT:
            # 选择模式：允许选择、移动、多选
            self.action_select_mode.setChecked(True)
            self.action_create_cell_mode.setChecked(False)
            self.action_grid_drag_mode.setChecked(False)  # 🆕
            self.action_quick_table.setChecked(False)
            
        elif mode == OperationMode.CREATE_CELL:
            # 单元格模式：允许创建新单元格
            self.action_select_mode.setChecked(False)
            self.action_create_cell_mode.setChecked(True)
            self.action_grid_drag_mode.setChecked(False)  # 🆕
            self.action_quick_table.setChecked(False)

        elif mode == OperationMode.GRID_DRAG:
            # 单元格模式：允许创建新单元格
            self.action_select_mode.setChecked(False)
            self.action_create_cell_mode.setChecked(False)
            self.action_grid_drag_mode.setChecked(True)  # 🆕
            self.action_quick_table.setChecked(False)

    def _initialize_mode_state(self):
        """初始化模式状态"""
        # 默认设置为选择模式
        self.set_select_mode()

    def _reset_to_default_mode(self, reason="未知原因"):
        """重置到默认模式（选择模式）

        Args:
            reason: 重置原因，用于日志记录
        """
        if hasattr(self, 'mode_manager') and self.mode_manager:
            current_mode = self.mode_manager.current_mode
            if current_mode != OperationMode.SELECT:
                # 直接调用set_select_mode方法，它会处理所有必要的状态更新
                self.set_select_mode()
                LOGGER.debug(f"已自动重置到默认模式（选择模式），原因: {reason}")
                self.statusBar().showMessage(f"已自动切换到选择模式 - {reason}", 2000)
            else:
                LOGGER.debug(f"当前已是默认模式，无需重置，原因: {reason}")
        else:
            LOGGER.warning("模式管理器未初始化，无法重置模式")

        # 🆕 重置对比可视化状态（每次都重置，不保持状态）
        self._reset_visualization_comparison(reason)

        # 🆕 重置逻辑可视化状态（但在图片切换和清空数据时保持状态）
        if reason not in ["图片切换", "清空数据"]:
            self._reset_logical_visualization(reason)

    def _reset_visualization_comparison(self, reason="未知原因"):
        """重置对比可视化状态

        Args:
            reason: 重置原因，用于日志记录
        """
        # 重置按钮状态为未选中
        if hasattr(self, 'action_compare_visualization'):
            if self.action_compare_visualization.isChecked():
                self.action_compare_visualization.setChecked(False)
                LOGGER.debug(f"已重置对比可视化按钮状态，原因: {reason}")

        # 清理备份的shapes数据
        if hasattr(self, '_backup_shapes'):
            if self._backup_shapes:
                LOGGER.debug(f"清理备份shapes数据: {len(self._backup_shapes)}个，原因: {reason}")
            self._backup_shapes = []

        # 确保Canvas显示正常（如果有表格数据应该正常显示）
        if hasattr(self, 'canvas') and self.canvas:
            self.canvas.update()

    # ===== 🆕 可视化对比功能 =====

    def toggle_visualization_comparison(self):
        """切换可视化对比模式（点击按钮时调用）"""
        # 🔧 修复：工具栏按钮与空格键行为一致 - 按压式交互
        LOGGER.debug("工具栏按钮对比模式（按压式）")

        # 🔧 修复：更智能的数据检测 - 检查Canvas中的shapes或备份shapes
        current_table_shapes = []
        backup_table_shapes = []

        if hasattr(self, 'canvas') and self.canvas.shapes:
            current_table_shapes = [shape for shape in self.canvas.shapes
                                   if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell']

        if hasattr(self, '_backup_shapes') and self._backup_shapes:
            backup_table_shapes = self._backup_shapes

        # 只要有当前显示的表格或有备份的表格，就可以进行对比
        has_table_data = len(current_table_shapes) > 0 or len(backup_table_shapes) > 0

        if not has_table_data:
            LOGGER.warning("没有表格数据，无法进行对比可视化")
            self.action_compare_visualization.setChecked(False)
            return

        if self.action_compare_visualization.isChecked():
            # 按钮被按下 → 隐藏表格（与空格键按下一致）
            LOGGER.debug("工具栏按钮按下：隐藏表格")
            self._hide_table_visualization()
            self.statusBar().showMessage("👁️ 对比模式：显示原图（再次点击恢复）")
        else:
            # 按钮被释放 → 显示表格（与空格键松开一致）
            LOGGER.debug("工具栏按钮释放：恢复表格")
            self._show_table_visualization()
            self.statusBar().showMessage("👁️ 已恢复表格显示")

    def _hide_table_visualization(self):
        """隐藏表格可视化，显示原图"""
        if not self.canvas or not hasattr(self.canvas, 'shapes'):
            return

        LOGGER.debug("隐藏表格可视化")

        # 🔧 修复：无论如何都执行隐藏，但先检查是否需要备份
        # 如果没有备份，先备份；如果有备份但Canvas还有表格，说明恢复过了需要重新备份
        current_table_shapes = [shape for shape in self.canvas.shapes
                               if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell']

        if current_table_shapes:
            # 有表格需要隐藏，先备份
            self._backup_shapes = current_table_shapes.copy()

            # 从Canvas移除表格shapes
            for shape in current_table_shapes:
                if shape in self.canvas.shapes:
                    self.canvas.shapes.remove(shape)

            LOGGER.debug(f"已隐藏 {len(current_table_shapes)} 个表格单元格")
        else:
            LOGGER.debug("Canvas中无表格单元格，已是隐藏状态")

        # 刷新Canvas
        self.canvas.update()

        # 更新状态栏
        self.statusBar().showMessage("👁️ 对比模式：显示原图")

    def _show_table_visualization(self):
        """恢复表格可视化"""
        if not self.canvas:
            return

        LOGGER.debug("恢复表格可视化")

        # 🔧 修复：检查是否有备份可恢复
        if hasattr(self, '_backup_shapes') and self._backup_shapes:
            # 恢复备份的TableCellShape到Canvas
            restored_count = 0
            for shape in self._backup_shapes:
                if shape not in self.canvas.shapes:
                    self.canvas.shapes.append(shape)
                    restored_count += 1

            LOGGER.debug(f"已恢复 {restored_count} 个表格单元格")

            # 清理备份
            self._backup_shapes = []
        else:
            LOGGER.debug("无备份数据可恢复，Canvas可能已显示表格")

        # 刷新Canvas
        self.canvas.update()

        # 恢复状态栏
        self.statusBar().showMessage("表格可视化已恢复")

    # ===== 🆕 逻辑可视化功能 =====

    def toggle_logical_visualization(self):
        """切换逻辑可视化模式（点击按钮或按M键时调用）"""
        LOGGER.debug("切换逻辑可视化模式")

        if self.action_logical_visualization.isChecked():
            # 按钮被按下 → 显示逻辑坐标
            LOGGER.debug("启用逻辑可视化：显示逻辑坐标")

            # 检查是否有表格数据
            if not self._has_table_data():
                LOGGER.warning("没有表格数据，无法进行逻辑可视化")
                self.statusBar().showMessage("⚠️ 没有表格数据，无法显示逻辑坐标")
                return

            self._show_logical_coordinates()
            self.statusBar().showMessage("🔢 逻辑可视化：显示逻辑坐标（再次点击关闭）")
        else:
            # 按钮被释放 → 隐藏逻辑坐标
            LOGGER.debug("关闭逻辑可视化：隐藏逻辑坐标")
            self._hide_logical_coordinates()
            self.statusBar().showMessage("🔢 已关闭逻辑可视化")

    def _has_table_data(self):
        """检查是否有表格数据"""
        if not hasattr(self, 'canvas') or not self.canvas.shapes:
            return False

        table_shapes = [shape for shape in self.canvas.shapes
                       if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell']
        return len(table_shapes) > 0

    def _show_logical_coordinates(self):
        """在物理单元格上显示逻辑坐标"""
        if not self.canvas:
            return

        LOGGER.debug("显示逻辑坐标")

        # 获取所有表格单元格
        table_cells = [shape for shape in self.canvas.shapes
                      if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell']

        if not table_cells:
            LOGGER.debug("没有表格单元格可显示逻辑坐标")
            return

        # 为每个单元格设置逻辑坐标显示标志
        for cell in table_cells:
            if hasattr(cell, 'table_properties'):
                cell.table_properties['show_logical_coords'] = True

        # 刷新Canvas显示
        self.canvas.update()
        LOGGER.debug(f"已为 {len(table_cells)} 个单元格启用逻辑坐标显示")

    def _hide_logical_coordinates(self):
        """隐藏物理单元格上的逻辑坐标"""
        if not self.canvas:
            return

        LOGGER.debug("隐藏逻辑坐标")

        # 获取所有表格单元格
        table_cells = [shape for shape in self.canvas.shapes
                      if hasattr(shape, 'shape_type') and shape.shape_type == 'table_cell']

        # 为每个单元格取消逻辑坐标显示标志
        for cell in table_cells:
            if hasattr(cell, 'table_properties'):
                cell.table_properties['show_logical_coords'] = False

        # 刷新Canvas显示
        self.canvas.update()
        LOGGER.debug(f"已为 {len(table_cells)} 个单元格关闭逻辑坐标显示")

    def _reset_logical_visualization(self, reason="未知原因"):
        """重置逻辑可视化状态

        Args:
            reason: 重置原因，用于日志记录
        """
        # 重置按钮状态为未选中
        if hasattr(self, 'action_logical_visualization'):
            if self.action_logical_visualization.isChecked():
                self.action_logical_visualization.setChecked(False)
                LOGGER.debug(f"已重置逻辑可视化按钮状态，原因: {reason}")

        # 隐藏逻辑坐标显示
        self._hide_logical_coordinates()

        LOGGER.debug(f"逻辑可视化状态已重置，原因: {reason}")

    def _restore_visualization_states(self):
        """在图片切换后恢复可视化状态（仅恢复逻辑可视化）"""
        self._restore_logical_visualization_state()
        # 注意：对比可视化不需要恢复状态，每次切换图片都重置

    def _restore_logical_visualization_state(self):
        """在图片切换后恢复逻辑可视化状态"""
        if not hasattr(self, 'action_logical_visualization'):
            LOGGER.debug("逻辑可视化按钮不存在，跳过状态恢复")
            return

        # 检查按钮状态
        is_checked = self.action_logical_visualization.isChecked()
        LOGGER.debug(f"逻辑可视化按钮状态: {is_checked}")

        # 如果逻辑可视化按钮是选中状态，重新应用到新加载的单元格
        if is_checked:
            LOGGER.debug("图片切换后恢复逻辑可视化状态")

            # 检查是否有表格数据
            if self._has_table_data():
                self._show_logical_coordinates()
                self.statusBar().showMessage("🔢 逻辑可视化：显示逻辑坐标（已保持状态）")
                LOGGER.debug("逻辑可视化状态恢复成功")
            else:
                LOGGER.debug("没有表格数据，无法恢复逻辑可视化状态")
        else:
            LOGGER.debug("逻辑可视化按钮未选中，无需恢复状态")



    def _toggle_double_click_edit(self):
        """切换双击文本编辑功能"""
        enabled = self.action_double_click_edit.isChecked()
        
        if hasattr(self.canvas, '_double_click_edit_enabled'):
            self.canvas._double_click_edit_enabled = enabled
        else:
            # 为Canvas添加属性
            self.canvas._double_click_edit_enabled = enabled
            
        status = "启用" if enabled else "禁用"
        self.statusBar().showMessage(f"双击文本编辑已{status}")
        LOGGER.info(f"双击文本编辑功能已{status}")

    # ===== 🆕 文件浏览状态持久化 =====

    def _schedule_save_viewed_files_state(self):
        """🔧 优化：延迟保存已浏览文件状态，避免频繁磁盘写入"""
        # 取消之前的定时器
        if hasattr(self, '_save_timer') and self._save_timer:
            self._save_timer.stop()

        # 创建新的定时器，5秒后保存
        self._save_timer = QtCore.QTimer()
        self._save_timer.setSingleShot(True)
        self._save_timer.timeout.connect(self._save_viewed_files_state)
        self._save_timer.start(5000)  # 5秒延迟
        LOGGER.debug("已安排延迟保存浏览状态")

    def _save_viewed_files_state(self):
        """保存文件索引状态到当前数据目录"""
        if not self.lastOpenDir:
            LOGGER.debug("没有当前打开的目录，无法保存索引状态")
            return

        if not self.file_index:
            LOGGER.debug("文件索引数据为空，无法保存索引状态")
            return

        # 更新viewed_files到索引中
        self._update_viewed_files_in_index()

        # 判断当前是父目录还是子目录
        if hasattr(self, 'directory_aggregator') and self.directory_aggregator:
            # 🔧 优化：父目录不立即使缓存失效，只标记为需要更新
            if hasattr(self.directory_aggregator, '_mark_cache_dirty'):
                self.directory_aggregator._mark_cache_dirty()
                LOGGER.debug("父目录浏览状态已更新，缓存标记为需要更新")
            else:
                # 兜底：使缓存失效
                self.directory_aggregator.invalidate_cache()
                LOGGER.debug("父目录浏览状态已更新，聚合缓存已失效")
        else:
            # 子目录：直接保存索引文件
            if self.file_index_manager:
                success = self.file_index_manager.save_index(self.file_index)
                if success:
                    LOGGER.debug(f"已保存文件索引，共 {len(self.file_index.get('files', {}))} 个文件")
                else:
                    LOGGER.error("保存文件索引失败")
    
    def _is_parent_directory(self, directory_path: str) -> bool:
        """
        🔧 修复：判断是否为父目录（不直接包含图片，但有包含图片的子目录）

        Args:
            directory_path: 目录路径

        Returns:
            bool: 是否为父目录
        """
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}

        def has_images_in_directory(dir_path):
            """检查目录是否直接包含图片文件"""
            try:
                files = os.listdir(dir_path)
                return any(
                    os.path.splitext(f)[1].lower() in image_extensions
                    for f in files
                    if os.path.isfile(os.path.join(dir_path, f))
                )
            except (OSError, PermissionError):
                return False

        def has_image_subdirectories(dir_path):
            """递归检查是否有包含图片的子目录"""
            try:
                for item in os.listdir(dir_path):
                    item_path = os.path.join(dir_path, item)
                    if os.path.isdir(item_path):
                        if has_images_in_directory(item_path):
                            return True
                        # 递归检查更深层的子目录
                        if has_image_subdirectories(item_path):
                            return True
                return False
            except (OSError, PermissionError):
                return False

        try:
            # 如果当前目录直接包含图片，则不是父目录
            if has_images_in_directory(directory_path):
                LOGGER.debug(f"目录直接包含图片，不是父目录: {directory_path}")
                return False

            # 检查是否有包含图片的子目录
            if has_image_subdirectories(directory_path):
                LOGGER.debug(f"目录包含图片子目录，是父目录: {directory_path}")
                return True

            LOGGER.debug(f"目录既不包含图片也不包含图片子目录: {directory_path}")
            return False

        except Exception as e:
            LOGGER.error(f"判断目录类型时出错 {directory_path}: {e}")
            return False

    def _load_viewed_files_state(self):
        """从当前数据目录加载文件索引状态"""
        if not self.lastOpenDir:
            LOGGER.debug("没有当前打开的目录，无法加载索引状态")
            self.viewed_files = set()
            return

        # 🔧 检查UI是否已初始化，避免初始化时序问题
        if not hasattr(self, 'fileListWidget') or self.fileListWidget is None:
            LOGGER.debug("UI未完全初始化，延迟加载索引状态")
            self.viewed_files = set()
            return

        # 判断目录类型并选择加载策略
        is_parent_dir = self._is_parent_directory(self.lastOpenDir)

        if is_parent_dir:
            # 父目录：使用DirectoryAggregator聚合子目录索引
            LOGGER.info(f"检测到父目录，使用聚合器加载索引: {self.lastOpenDir}")
            self.directory_aggregator = DirectoryAggregator(self.lastOpenDir)

            try:
                # 聚合子目录索引
                aggregated_index = self.directory_aggregator.aggregate_subdirectories()

                if aggregated_index and "files" in aggregated_index:
                    self.file_index = aggregated_index
                    # 从聚合索引中提取viewed_files
                    files_data = aggregated_index.get("files", {})
                    self.viewed_files = {filename for filename, data in files_data.items()
                                       if data.get("viewed", False)}
                    LOGGER.info(f"聚合索引加载完成，共 {len(files_data)} 个文件，{len(self.viewed_files)} 个已浏览")
                else:
                    LOGGER.debug("聚合索引为空，将在导入目录时创建")
                    self.viewed_files = set()
                    self.file_index = None

            except Exception as e:
                LOGGER.error(f"聚合索引加载失败: {e}")
                self.viewed_files = set()
                self.file_index = None

        else:
            # 子目录：直接加载本地索引
            LOGGER.info(f"检测到子目录，直接加载本地索引: {self.lastOpenDir}")
            self.file_index_manager = FileIndexManager(self.lastOpenDir)

            # 加载索引文件
            index_data = self.file_index_manager.load_index()

            if not index_data:
                LOGGER.debug("索引文件不存在，将在导入目录时创建")
                self.viewed_files = set()
                self.file_index = None
                return

            # 检查版本并处理迁移
            version = index_data.get("version", "1.0")

            if version == "1.0":
                LOGGER.info("检测到v1.0格式索引文件，准备迁移到v2.0")
                # 从v1.0迁移，先提取viewed_files
                viewed_files_list = index_data.get("viewed_files", [])
                self.viewed_files = set(viewed_files_list) if isinstance(viewed_files_list, list) else set()
                # 索引数据将在importDirImages时重建
                self.file_index = None
                LOGGER.info(f"从v1.0迁移，保留 {len(self.viewed_files)} 个已浏览文件")

            elif version == FILE_INDEX_VERSION:
                # v2.0格式，直接使用
                self.file_index = index_data
                # 从索引中提取viewed_files
                files_data = index_data.get("files", {})
                self.viewed_files = {filename for filename, data in files_data.items()
                                   if data.get("viewed", False)}
                LOGGER.debug(f"加载v2.0索引，共 {len(files_data)} 个文件，{len(self.viewed_files)} 个已浏览")

            else:
                LOGGER.warning(f"不支持的索引版本: {version}，将重建索引")
                self.viewed_files = set()
                self.file_index = None

    def _update_viewed_files_in_index(self):
        """更新索引中的viewed状态"""
        if not self.file_index or "files" not in self.file_index:
            return

        # 更新所有文件的viewed状态
        for filename in self.file_index["files"]:
            self.file_index["files"][filename]["viewed"] = filename in self.viewed_files



    def mayContinue(self):
        """检查是否可以继续操作（如果有未保存的更改，提示用户）"""
        if not self.filename:
            return True
            
        if self._has_annotation_file(self.filename):
            return True
            
        # 如果有未保存的更改，提示用户
        reply = QtWidgets.QMessageBox.question(
            self,
            "未保存的更改",
            "当前图片有未保存的标注，是否继续？",
            QtWidgets.QMessageBox.Yes | QtWidgets.QMessageBox.No,
            QtWidgets.QMessageBox.No,
        )
        return reply == QtWidgets.QMessageBox.Yes

    def load_file(self, json_file):
        """加载标注文件
        
        Args:
            json_file: 标注文件路径
        """
        try:
            LOGGER.debug(f"开始加载标注文件: {json_file}")
            
            # 使用TableDataManager加载和转换数据
            from labelme.utils.table_data_manager import TableDataManager
            
            manager = TableDataManager()
            shapes = manager.load_from_file(json_file)
            
            if shapes:
                # 清空旧数据
                self._clear_current_data()
                
                # 导入表格数据
                import_stats = self.multi_table_controller.import_table_data(shapes)
                
                total_cells = sum(import_stats.values())
                LOGGER.info(f"加载标注文件: {json_file}, {len(import_stats)}个表格, {total_cells}个单元格")
                self.statusBar().showMessage(f"已加载标注数据: {osp.basename(json_file)}")
                
                # 自动显示逻辑结构
                self._auto_refresh_all_structure_views()
                
                return True
            else:
                LOGGER.warning("加载的标注数据为空")
                self._clear_current_data()
                return False
                
        except Exception as e:
            LOGGER.error(f"加载标注文件失败: {e}")
            import traceback
            traceback.print_exc()
            self._clear_current_data()
            return False
     #  网格拖拽模式
    def set_grid_drag_mode(self):
        """设置网格拖拽模式"""
        self.canvas.restoreCursor()

        success = self.mode_manager.switch_mode(OperationMode.GRID_DRAG)
        if success:
            # 启动Canvas的网格拖拽
            if self.canvas:
                # 🆕 清理Canvas状态
                if self.canvas:
                    # 清理单元格模式的十字光标
                    self.canvas.setEditing(True)  # 退出CREATE模式

                    # 清理选择模式的选中状态
                    self.canvas.deSelectShape()  # 清除所有选中

                    # 清理其他可能的状态
                    if hasattr(self.canvas, 'createMode') and self.canvas.createMode == "quick_table_region":
                        self.canvas.setEditing(True)

                self.canvas.enter_grid_drag_mode()

            self.statusBar().showMessage("网格拖拽模式 - 悬停在网格线上拖拽调整")

    def exit_grid_drag_mode(self):
        """退出网格拖拽模式，回到选择模式"""

        if self.mode_manager.current_mode == OperationMode.GRID_DRAG:
            self.set_select_mode()  # 复用现有的选择模式
        self.canvas.restoreCursor()

    def _update_optimization_mode_display(self):
        """更新优化模式显示"""
        if not hasattr(self, 'optimize_button'):
            return

        strength_value = self.optimization_strength_slider.value()
        mode_names = [
            "微度角点对齐",
            "保守角点对齐",
            "常用角点对齐",
            "激进角点对齐"
        ]

        mode_descriptions = [
            "极小幅调整，最大程度保持原状",
            "小幅调整，保持原有结构",
            "适中调整，平衡效果与保真",
            "大幅调整，强力对齐优化"
        ]

        mode_name = mode_names[strength_value]
        mode_desc = mode_descriptions[strength_value]

        # 更新按钮文本和工具提示
        self.optimize_button.setText(f"🔧 {mode_name}")
        self.optimize_button.setToolTip(f"{mode_desc}\n点击执行表格角点对齐优化")

    def _get_optimization_parameters(self):
        """根据选择的优化强度获取参数配置"""
        strength_value = self.optimization_strength_slider.value()
        mode_configs = {
            0: {  # 微度角点对齐
                'tolerance': 2.0,
                'alignment_strength': 0.3,
                'merge_threshold_factor': 1.0,
                'adaptive_threshold': True,
                'preserve_perspective': True,
                'name': '微度角点对齐'
            },
            1: {  # 保守角点对齐
                'tolerance': 3.0,
                'alignment_strength': 0.5,
                'merge_threshold_factor': 1.2,
                'adaptive_threshold': True,
                'preserve_perspective': True,
                'name': '保守角点对齐'
            },
            2: {  # 常用角点对齐
                'tolerance': 6.0,
                'alignment_strength': 0.9,
                'merge_threshold_factor': 3.0,
                'adaptive_threshold': True,
                'preserve_perspective': True,
                'name': '常用角点对齐'
            },
            3: {  # 激进角点对齐
                'tolerance': 10.0,
                'alignment_strength': 1.0,
                'merge_threshold_factor': 5.0,
                'adaptive_threshold': True,
                'preserve_perspective': True,
                'name': '激进角点对齐'
            }
        }
        return mode_configs.get(strength_value, mode_configs[2])  # 默认常用角点对齐

    def _import_optimizer_safely(self):
        """安全导入优化器 - 使用多种方法确保导入成功"""
        import sys
        import os
        import importlib.util

        # 方法1: 尝试标准导入
        print("🔍 尝试标准导入...")
        try:
            from labelme.utils import SimplifiedTableOptimizer
            print("✅ 标准导入成功")
            return SimplifiedTableOptimizer
        except Exception as e:
            print(f"⚠️ 标准导入失败: {e}")

        # 方法2: 尝试直接从utils模块导入
        print("🔍 尝试直接从utils模块导入...")
        try:
            from labelme.utils.table_annotation_optimizer import SimplifiedTableOptimizer
            print("✅ 直接导入成功")
            return SimplifiedTableOptimizer
        except Exception as e:
            print(f"⚠️ 直接导入失败: {e}")

        # 方法3: 使用绝对路径导入
        print("🔍 尝试绝对路径导入...")
        try:
            # 获取当前文件的目录
            current_dir = os.path.dirname(os.path.abspath(__file__))
            optimizer_path = os.path.join(current_dir, 'utils', 'table_annotation_optimizer.py')

            if os.path.exists(optimizer_path):
                spec = importlib.util.spec_from_file_location("table_annotation_optimizer", optimizer_path)
                module = importlib.util.module_from_spec(spec)
                spec.loader.exec_module(module)
                SimplifiedTableOptimizer = module.SimplifiedTableOptimizer
                print("✅ 绝对路径导入成功")
                return SimplifiedTableOptimizer
            else:
                print(f"⚠️ 优化器文件不存在: {optimizer_path}")
        except Exception as e:
            print(f"⚠️ 绝对路径导入失败: {e}")

        # 方法4: 尝试修复sys.path后导入
        print("🔍 尝试修复sys.path后导入...")
        try:
            # 确保项目根目录在sys.path中
            current_dir = os.path.dirname(os.path.abspath(__file__))
            project_root = os.path.dirname(current_dir)

            if project_root not in sys.path:
                sys.path.insert(0, project_root)
                print(f"添加项目根目录到sys.path: {project_root}")

            # 清理可能的模块缓存
            modules_to_clear = [m for m in list(sys.modules.keys()) if m.startswith('labelme.utils')]
            for m in modules_to_clear:
                if m in sys.modules:
                    del sys.modules[m]
                    print(f"清理模块缓存: {m}")

            # 重新尝试导入
            from labelme.utils import SimplifiedTableOptimizer
            print("✅ 修复sys.path后导入成功")
            return SimplifiedTableOptimizer
        except Exception as e:
            print(f"⚠️ 修复sys.path后导入失败: {e}")

        print("❌ 所有导入方式都失败")
        return None

    def optimize_current_table(self):
        """优化当前显示的表格标注"""
        try:
            # 安全导入优化器
            print("🔍 安全导入优化器...")
            SimplifiedTableOptimizer = self._import_optimizer_safely()

            if SimplifiedTableOptimizer is None:
                print("❌ 所有导入方式都失败")
                QtWidgets.QMessageBox.warning(
                    self, "警告", "表格优化器导入失败：所有导入方式都失败\n请检查optimizer模块是否正确安装"
                )
                return

            print("✅ 优化器导入成功")

            print("✅ 优化器可用，继续执行")

            # 检查是否有当前图片
            if not self.filename:
                QtWidgets.QMessageBox.warning(
                    self, "警告", "请先打开一张图片"
                )
                return

            # 检查是否有标注数据
            if not self.canvas.shapes:
                QtWidgets.QMessageBox.warning(
                    self, "警告", "当前图片没有表格标注数据"
                )
                return

            # 获取优化参数配置
            config = self._get_optimization_parameters()

            # 显示进度对话框
            progress = QtWidgets.QProgressDialog("正在优化表格标注...", "取消", 0, 100, self)
            progress.setWindowModality(Qt.WindowModal)
            progress.setMinimumDuration(0)
            progress.setValue(10)

            # 执行实际的优化逻辑
            success = self._perform_table_optimization(config, progress)

            if success:
                progress.setValue(100)
                # 在状态栏显示成功提示
                self.statusBar().showMessage(f"✅ 已应用角点对齐 ({config['name']})", 5000)  # 显示5秒
            else:
                # 在状态栏显示失败提示
                self.statusBar().showMessage("❌ 角点对齐失败，请检查日志", 5000)

            progress.close()

        except Exception as e:
            LOGGER.error(f"优化表格时发生错误: {e}")
            import traceback
            traceback.print_exc()
            QtWidgets.QMessageBox.critical(
                self, "错误", f"优化表格时发生错误：{str(e)}"
            )

    def _perform_table_optimization(self, config, progress):
        """执行实际的表格优化"""
        try:
            progress.setValue(20)

            # 1. 保存当前标注数据到临时文件
            temp_dir = tempfile.mkdtemp()
            temp_input_file = osp.join(temp_dir, "input_annotation.json")
            temp_output_file = osp.join(temp_dir, "output_annotation.json")

            # 导出当前标注数据
            self._export_current_annotation_to_file(temp_input_file)
            progress.setValue(30)

            # 2. 创建优化器实例（使用安全导入）
            SimplifiedTableOptimizer = self._import_optimizer_safely()
            if SimplifiedTableOptimizer is None:
                raise ImportError("无法导入SimplifiedTableOptimizer")
            optimizer = SimplifiedTableOptimizer(
                tolerance=config['tolerance'],
                adaptive_threshold=config['adaptive_threshold'],
                merge_threshold_factor=config['merge_threshold_factor'],
                alignment_strength=config['alignment_strength'],
                preserve_perspective=config['preserve_perspective']
            )
            progress.setValue(40)

            # 3. 执行优化
            result = optimizer.optimize_table_annotation(
                input_file=temp_input_file,
                output_file=temp_output_file,
                image_file=self.filename
            )
            progress.setValue(70)

            if result.get('success', False):
                # 4. 加载优化后的数据
                self._import_optimized_annotation_from_file(temp_output_file)
                progress.setValue(90)

                # 5. 清理临时文件
                try:
                    os.remove(temp_input_file)
                    os.remove(temp_output_file)
                    os.rmdir(temp_dir)
                except:
                    pass  # 忽略清理错误

                LOGGER.info(f"表格优化成功: {result}")
                return True
            else:
                LOGGER.error(f"表格优化失败: {result}")
                return False

        except Exception as e:
            LOGGER.error(f"执行表格优化时发生错误: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _export_current_annotation_to_file(self, output_file):
        """导出当前标注数据到文件"""
        try:
            print(f"🔍 导出标注数据到: {output_file}")

            # 简化的导出逻辑，直接使用canvas中的shapes
            shapes_data = []

            if hasattr(self, 'canvas') and hasattr(self.canvas, 'shapes'):
                for shape in self.canvas.shapes:
                    # 转换shape为字典格式
                    shape_dict = {
                        "label": getattr(shape, 'label', 'table_cell'),
                        "points": [[float(p.x()), float(p.y())] for p in shape.points] if hasattr(shape, 'points') else [],
                        "group_id": getattr(shape, 'group_id', None),
                        "shape_type": "polygon",
                        "flags": getattr(shape, 'flags', {}),
                        # 添加表格相关属性（如果存在）
                        "table_ind": getattr(shape, 'table_ind', "table_1"),
                        "logical_row": getattr(shape, 'logical_row', 0),
                        "logical_col": getattr(shape, 'logical_col', 0),
                        "row_span": getattr(shape, 'row_span', 1),
                        "col_span": getattr(shape, 'col_span', 1)
                    }
                    shapes_data.append(shape_dict)

            # 转换为优化器期望的格式
            cells_data = []
            for shape_dict in shapes_data:
                points = shape_dict["points"]

                # 转换points数组为bbox格式（p1, p2, p3, p4）
                bbox = {}
                if len(points) >= 4:
                    bbox = {
                        "p1": points[0],  # 左上
                        "p2": points[1],  # 右上
                        "p3": points[2],  # 右下
                        "p4": points[3]   # 左下
                    }
                elif len(points) == 2:
                    # 如果只有两个点（矩形的对角），生成四个角点
                    x1, y1 = points[0]
                    x2, y2 = points[1]
                    bbox = {
                        "p1": [x1, y1],  # 左上
                        "p2": [x2, y1],  # 右上
                        "p3": [x2, y2],  # 右下
                        "p4": [x1, y2]   # 左下
                    }

                # 转换为优化器期望的cell格式
                cell = {
                    "bbox": bbox,
                    "table_ind": shape_dict.get("table_ind", "table_1"),
                    "logical_row": shape_dict.get("logical_row", 0),
                    "logical_col": shape_dict.get("logical_col", 0),
                    "row_span": shape_dict.get("row_span", 1),
                    "col_span": shape_dict.get("col_span", 1),
                    "label": shape_dict.get("label", "table_cell")
                }
                cells_data.append(cell)

            # 创建优化器期望的数据结构
            annotation_data = {
                "cells": cells_data,
                "table_ind": "table_1",
                "image_path": getattr(self, 'filename', 'unknown.jpg')
            }

            # 如果有图片信息，获取实际尺寸
            if hasattr(self, 'image') and self.image is not None:
                annotation_data["imageHeight"] = self.image.height()
                annotation_data["imageWidth"] = self.image.width()

            # 保存到文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(annotation_data, f, ensure_ascii=False, indent=2)

            print(f"✅ 导出成功: {len(shapes_data)}个标注")

        except Exception as e:
            print(f"❌ 导出标注数据失败: {e}")
            import traceback
            traceback.print_exc()
            raise

    def _import_optimized_annotation_from_file(self, input_file):
        """从文件导入优化后的标注数据"""
        try:
            print(f"🔍 导入优化后的数据: {input_file}")

            # 读取优化后的标注文件
            with open(input_file, 'r', encoding='utf-8') as f:
                annotation_data = json.load(f)

            # 优化器输出的是cells格式，需要转换
            cells_data = annotation_data.get('cells', [])
            if not cells_data:
                print("⚠️ 优化后的标注数据为空")
                return False

            print(f"🔍 找到 {len(cells_data)} 个优化后的标注")

            # 真正的导入逻辑 - 更新canvas中的shapes
            print(f"🔍 开始更新 {len(cells_data)} 个优化后的标注")

            if hasattr(self, 'canvas') and hasattr(self.canvas, 'shapes'):
                # 创建一个映射，将优化后的数据与原始shapes对应
                updated_count = 0

                for i, cell in enumerate(cells_data):
                    if i < len(self.canvas.shapes):
                        shape = self.canvas.shapes[i]
                        bbox = cell.get('bbox', {})

                        if bbox and all(key in bbox for key in ['p1', 'p2', 'p3', 'p4']):
                            # 将bbox转换回points格式
                            new_points = [
                                bbox['p1'],  # 左上
                                bbox['p2'],  # 右上
                                bbox['p3'],  # 右下
                                bbox['p4']   # 左下
                            ]

                            # 更新shape的points
                            try:
                                from PyQt5.QtCore import QPointF
                                qt_points = []
                                for point in new_points:
                                    qt_points.append(QPointF(float(point[0]), float(point[1])))

                                shape.points = qt_points
                                updated_count += 1
                                print(f"  ✅ 更新第{i+1}个标注: {bbox['p1']} -> {bbox['p3']}")

                            except Exception as e:
                                print(f"  ⚠️ 更新第{i+1}个标注失败: {e}")

                if updated_count > 0:
                    # 刷新canvas显示
                    self.canvas.update()
                    self.canvas.repaint()

                    print(f"✅ 成功更新 {updated_count} 个标注的可视化")
                    print("✅ 优化过程完成，界面已更新")

                    # 如果有其他需要刷新的组件，也在这里调用
                    if hasattr(self, 'labelList'):
                        self.labelList.update()

                    return True
                else:
                    print("⚠️ 没有成功更新任何标注")
                    return False
            else:
                print("❌ Canvas不可用，无法更新可视化")
                return False

        except Exception as e:
            print(f"❌ 导入优化后的标注数据失败: {e}")
            import traceback
            traceback.print_exc()
            raise


def main():
    """主函数"""
    # 🔧 屏蔽macOS IMK相关警告信息
    import os
    import logging
    os.environ.setdefault('QT_LOGGING_RULES', '*.debug=false;qt.qpa.cocoa.text=false')
    
    app = QtWidgets.QApplication(sys.argv)
    app.setApplicationName("TableLabelMe")
    app.setOrganizationName("TableLabelMe")

    try:
        window = TableLabelMainWindow()
        window.show()

        LOGGER.debug("=" * 80)
        LOGGER.info("TableLabelMe - 表格标注工具启动成功!")
        LOGGER.debug("=" * 80)
        LOGGER.debug("功能说明 (P1版本 - 简化UI):")
        LOGGER.debug("  文件 -> 打开图片: 加载要标注的图片")
        LOGGER.debug("  简化模式切换:")
        LOGGER.debug("    选择模式 (快捷键: 1) - 选择、移动、调整已有单元格，支持多选")
        LOGGER.debug("    单元格模式 (快捷键: 2) - 拖拽绘制新的表格单元格")
        LOGGER.debug("  表格工具:")
        LOGGER.debug("    快速生成表格 (快捷键: Q) - 在选中区域生成M×N表格")
        LOGGER.debug("    分析表格结构 (快捷键: A)")
        LOGGER.debug("  对齐工具:")
        LOGGER.debug("    顶端对齐 (Ctrl+T)")
        LOGGER.debug("    底端对齐 (Ctrl+B)")
        LOGGER.debug("    左对齐 (Ctrl+L)")
        LOGGER.debug("    右对齐 (Ctrl+R)")
        LOGGER.debug("  保存: Ctrl+S")
        LOGGER.debug("  撤销/重做: Ctrl+Z / Ctrl+Shift+Z")
        LOGGER.debug("=" * 80)
        LOGGER.debug("使用建议:")
        LOGGER.debug("  1. 打开图片后，按 2 进入单元格模式绘制表格区域")
        LOGGER.debug("  2. 按 Q 在选中区域快速生成M×N表格（推荐）")
        LOGGER.debug("  3. 按 1 进入选择模式，支持Ctrl+点击多选和框选")
        LOGGER.debug("  4. 使用属性面板编辑单元格属性")
        LOGGER.debug("  5. 使用分析表格结构功能自动识别网格")
        LOGGER.debug("  6. 使用对齐工具整理单元格位置")
        LOGGER.debug("=" * 80)

        sys.exit(app.exec_())

    except Exception as e:
        LOGGER.error(f"应用启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)




if __name__ == "__main__":
    load_dotenv()
    main()