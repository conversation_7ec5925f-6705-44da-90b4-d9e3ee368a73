#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/6/18 16:52  
# <AUTHOR> cascade
# @FileName: table_data_manager.py

"""
表格数据统一管理器
核心职责：多格式数据加载、转换、验证和应用
支持格式：MultiTableController导出格式、公共数据集格式
"""

import json
import os
from typing import Dict, List, Optional, Any, Union
from abc import ABC, abstractmethod

from labelme.table_shape import TableCellShape
from labelme.utils.log import get_logger

LOGGER = get_logger()


class FormatConverter(ABC):
    """格式转换器基类"""
    
    @abstractmethod
    def can_handle(self, data: Any) -> bool:
        """检查是否能处理该格式的数据"""
        pass
    
    @abstractmethod
    def convert(self, data: Any) -> List[TableCellShape]:
        """转换数据为TableCellShape列表"""
        pass
    
    @abstractmethod
    def get_format_name(self) -> str:
        """获取格式名称"""
        pass


class MultiTableFormatConverter(FormatConverter):
    """MultiTableController导出格式转换器"""
    
    def can_handle(self, data: Any) -> bool:
        """检查是否为MultiTableController导出格式"""
        return (isinstance(data, list) and 
                len(data) > 0 and 
                isinstance(data[0], dict) and 
                "table_ind" in data[0] and 
                "cells" in data[0])
    
    def convert(self, data: List[Dict]) -> List[TableCellShape]:
        """转换MultiTableController格式为TableCellShape列表"""
        shapes = []
        # 🆕 保存表格元数据，支持空表格
        self.table_metadata = []

        for table_data in data:
            table_id = table_data.get("table_ind", 0)
            table_type = table_data.get("type", 1)
            cells = table_data.get("cells", [])

            # 保存表格元数据
            self.table_metadata.append({
                "table_id": table_id,
                "table_type": table_type,
                "cell_count": len(cells),
                "has_cells": len(cells) > 0
            })

            for cell_data in cells:
                shape = self._convert_cell_data(cell_data, table_id, table_type)
                if shape:
                    shapes.append(shape)

        LOGGER.debug(f"MultiTable格式转换完成: {len(shapes)} 个单元格, {len(self.table_metadata)} 个表格")
        return shapes
    
    def _convert_cell_data(self, cell_data: Dict, table_id: int, table_type: int) -> Optional[TableCellShape]:
        """转换单个cell数据"""
        try:
            return TableCellShape.from_multi_table_cell(cell_data, table_id, table_type)
        except ValueError as e:
            LOGGER.error(f"MultiTable单元格转换失败: {e}")
            return None
    
    def get_format_name(self) -> str:
        return "MultiTableController导出格式"


class PublicDatasetConverter(FormatConverter):
    """公共数据集格式转换器"""
    
    def can_handle(self, data: Any) -> bool:
        """检查是否为公共数据集格式"""
        return (isinstance(data, dict) and 
                "html" in data and 
                isinstance(data["html"], dict) and
                "structure" in data["html"] and
                "cell" in data["html"])
    
    def convert(self, data: Dict) -> List[TableCellShape]:
        """转换公共数据集格式为TableCellShape列表"""
        shapes = []
        
        filename = data.get("filename", "unknown")
        html_data = data["html"]
        cells_data = html_data.get("cell", [])
        
        for i, cell_data in enumerate(cells_data):
            shape = self._convert_cell_data(cell_data, i, filename)
            if shape:
                shapes.append(shape)
        
        LOGGER.debug(f"公共数据集格式转换完成: {len(shapes)} 个单元格")
        return shapes
    
    def _convert_cell_data(self, cell_data: Dict, index: int, filename: str) -> Optional[TableCellShape]:
        """转换单个cell数据"""
        try:
            return TableCellShape.from_public_dataset_cell(cell_data, index, filename)
        except ValueError as e:
            LOGGER.error(f"公共数据集单元格转换失败: {e}")
            return None
    
    def get_format_name(self) -> str:
        return "公共数据集格式"


class TableDataManager:
    """表格数据统一管理器"""
    
    def __init__(self):
        # 注册格式转换器
        self.converters: List[FormatConverter] = [
            MultiTableFormatConverter(),
            PublicDatasetConverter()
        ]
        
        # 统计信息
        self.last_loaded_format = None
        self.last_loaded_count = 0
        # 🆕 保存最近加载的表格元数据（支持空表格）
        self.last_loaded_table_metadata = []
    
    def load_from_file(self, file_path: str) -> List[TableCellShape]:
        """从文件加载表格数据
        
        Args:
            file_path: 数据文件路径
            
        Returns:
            TableCellShape对象列表
            
        Raises:
            FileNotFoundError: 文件不存在
            ValueError: 不支持的数据格式
            json.JSONDecodeError: JSON解析错误
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        # 读取文件
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
        except json.JSONDecodeError as e:
            raise json.JSONDecodeError(f"JSON解析失败: {e}")
        
        # 🎯 格式标准化：单表格dict自动包装成list
        if self._is_single_table_format(data):
            data = [data]
            LOGGER.debug(f"检测到单表格格式，自动包装为标准格式")
        
        # 格式检测和转换
        return self.convert_data(data)
    
    def _is_single_table_format(self, data: Any) -> bool:
        """检测是否为单表格dict格式
        
        Args:
            data: 待检测的数据
            
        Returns:
            True如果是单表格dict格式，False否则
        """
        return (isinstance(data, dict) and 
                "table_ind" in data and 
                "cells" in data and
                isinstance(data["cells"], list))
    
    def convert_data(self, data: Any) -> List[TableCellShape]:
        """转换数据为TableCellShape列表
        
        Args:
            data: 原始数据
            
        Returns:
            TableCellShape对象列表
            
        Raises:
            ValueError: 不支持的数据格式
        """
        # 🔧 修复：处理包含quality字段的格式
        if isinstance(data, dict) and 'quality' in data:
            if 'tables' in data:
                # 多表格格式：{quality: "xxx", tables: [...]}
                LOGGER.debug(f"检测到多表格质量字段格式，质量状态: {data['quality']}")
                tables_data = data['tables']
                return self.convert_data(tables_data)
            elif 'table_ind' in data and 'cells' in data:
                # 单表格格式：{quality: "xxx", table_ind: 0, cells: [...]}
                LOGGER.debug(f"检测到单表格质量字段格式，质量状态: {data['quality']}")
                # 移除quality字段，转换为标准单表格格式
                table_data = {k: v for k, v in data.items() if k != 'quality'}
                return self.convert_data([table_data])  # 包装为列表格式
        
        # 尝试各个转换器
        for converter in self.converters:
            if converter.can_handle(data):
                LOGGER.info(f"检测到格式: {converter.get_format_name()}")
                shapes = converter.convert(data)

                # 更新统计信息
                self.last_loaded_format = converter.get_format_name()
                self.last_loaded_count = len(shapes)

                # 🆕 保存表格元数据（如果转换器支持）
                if hasattr(converter, 'table_metadata'):
                    self.last_loaded_table_metadata = converter.table_metadata
                    LOGGER.debug(f"保存表格元数据: {len(self.last_loaded_table_metadata)} 个表格")

                return shapes
        
        # 没有匹配的转换器
        raise ValueError("不支持的数据格式。支持的格式: MultiTableController导出格式、公共数据集格式、包含质量字段格式")
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的格式列表"""
        return [converter.get_format_name() for converter in self.converters]
    
    def get_load_info(self) -> Dict[str, Any]:
        """获取最后一次加载的统计信息"""
        return {
            "format": self.last_loaded_format,
            "count": self.last_loaded_count,
            "supported_formats": self.get_supported_formats()
        }


# 方便导入的工厂函数
def create_table_data_manager() -> TableDataManager:
    """创建表格数据管理器实例"""
    return TableDataManager()


if __name__ == "__main__":
    # 简单测试
    manager = create_table_data_manager()
    LOGGER.debug(f"支持的格式:{manager.get_supported_formats()}")
