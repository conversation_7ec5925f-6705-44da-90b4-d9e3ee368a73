#!/usr/bin/env python3
"""
简化优化器的批量处理脚本

主要功能:
1. 批量处理表格标注文件
2. 自动查找对应的图片文件
3. 支持多种图片格式
4. 生成处理报告

作者: AI Assistant
版本: 1.0
更新日期: 2025-01-08
"""

import os
import json
import time
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse
from typing import List, Dict, Any
import shutil

from .table_annotation_optimizer import SimplifiedTableOptimizer


class BatchSimplifiedProcessor:
    """简化优化器的批量处理器"""

    def __init__(self, tolerance: float = 3.0, adaptive_threshold: bool = True,
                 merge_threshold_factor: float = 1.5, alignment_strength: float = 0.8,
                 max_workers: int = 1, copy_images: bool = True):
        """
        初始化批量处理器

        Args:
            tolerance: 基础容差阈值（像素）
            adaptive_threshold: 是否使用自适应阈值
            merge_threshold_factor: 合并阈值因子
            alignment_strength: 对齐强度 (0.0-1.0)
            max_workers: 并行处理线程数
            copy_images: 是否复制图片文件到输出目录
        """
        self.tolerance = tolerance
        self.adaptive_threshold = adaptive_threshold
        self.merge_threshold_factor = merge_threshold_factor
        self.alignment_strength = alignment_strength
        self.max_workers = max_workers
        self.copy_images = copy_images
        
        # 支持的图片格式
        self.image_extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']

    def find_corresponding_image(self, annotation_file: Path) -> str:
        """
        查找对应的图片文件

        Args:
            annotation_file: 标注文件路径

        Returns:
            图片文件路径，如果未找到返回None
        """
        # 从标注文件名推断图片文件名
        base_name = annotation_file.stem.replace('_table_annotation', '')
        
        for ext in self.image_extensions:
            image_path = annotation_file.parent / f"{base_name}{ext}"
            if image_path.exists():
                return str(image_path)
        
        return None

    def copy_image_file(self, image_file: str, output_dir: Path) -> bool:
        """
        复制图片文件到输出目录，正确处理EXIF方向信息（修复旋转bug）

        Args:
            image_file: 图片文件路径
            output_dir: 输出目录

        Returns:
            是否复制成功
        """
        try:
            from PIL import Image, ImageOps

            image_path = Path(image_file)
            output_image_path = output_dir / image_path.name

            if not output_image_path.exists():
                with Image.open(image_file) as img:
                    # 使用PIL的ImageOps.exif_transpose自动处理EXIF方向
                    # 这是最可靠的方法，会自动应用正确的旋转并移除EXIF方向标签
                    try:
                        img = ImageOps.exif_transpose(img)
                        print(f"    🔄 已处理EXIF方向信息: {image_path.name}")
                    except Exception as exif_error:
                        print(f"    ⚠️  EXIF处理失败，保持原图: {exif_error}")
                        # 如果EXIF处理失败，保持原图不变
                        pass

                    # 保存图片，移除EXIF信息以避免重复旋转
                    # 不保存EXIF信息，因为已经应用了方向变换
                    save_kwargs = {
                        'quality': 95,
                        'optimize': True
                    }

                    # 根据图片格式设置保存参数
                    if img.format == 'JPEG':
                        save_kwargs['format'] = 'JPEG'
                    elif img.format == 'PNG':
                        save_kwargs['format'] = 'PNG'

                    img.save(output_image_path, **save_kwargs)

            return True

        except Exception as e:
            print(f"PIL处理图片失败 {image_file}: {e}")
            # 如果PIL处理失败，回退到简单复制
            try:
                image_path = Path(image_file)
                output_image_path = output_dir / image_path.name
                if not output_image_path.exists():
                    shutil.copy2(image_file, output_image_path)
                print(f"    📋 回退到简单复制: {image_path.name}")
                return True
            except Exception as e2:
                print(f"简单复制也失败 {image_file}: {e2}")
                return False

    def process_single_file(self, annotation_file: Path, output_dir: Path) -> Dict[str, Any]:
        """
        处理单个标注文件

        Args:
            annotation_file: 标注文件路径
            output_dir: 输出目录

        Returns:
            处理结果字典
        """
        try:
            # 查找对应的图片文件
            image_file = self.find_corresponding_image(annotation_file)
            
            # 确定输出文件路径
            base_name = annotation_file.stem.replace('_table_annotation', '')
            output_file = output_dir / f"{base_name}_table_annotation.json"
            
            # 创建优化器
            optimizer = SimplifiedTableOptimizer(
                tolerance=self.tolerance,
                adaptive_threshold=self.adaptive_threshold,
                merge_threshold_factor=self.merge_threshold_factor,
                alignment_strength=self.alignment_strength
            )
            
            # 记录开始时间
            start_time = time.time()
            
            # 执行优化
            result = optimizer.optimize_table_annotation(
                str(annotation_file),
                str(output_file),
                image_file
            )
            
            # 计算处理时间
            processing_time = time.time() - start_time
            
            # 添加处理时间到结果中
            result['processing_time'] = processing_time
            result['file'] = str(annotation_file)
            result['output_file'] = str(output_file)
            result['image_file'] = image_file
            
            return result
            
        except Exception as e:
            return {
                'file': str(annotation_file),
                'success': False,
                'error': str(e),
                'processing_time': 0
            }

    def process_batch(self, input_pattern: str, output_dir: str) -> Dict[str, Any]:
        """
        批量处理标注文件

        Args:
            input_pattern: 输入文件模式（支持通配符）
            output_dir: 输出目录

        Returns:
            批量处理结果
        """
        # 查找匹配的标注文件
        if '*' in input_pattern:
            # 通配符模式
            input_path = Path(input_pattern)
            annotation_files = list(input_path.parent.glob(input_path.name))
        else:
            # 单个文件或目录
            input_path = Path(input_pattern)
            if input_path.is_file():
                annotation_files = [input_path]
            elif input_path.is_dir():
                annotation_files = list(input_path.glob('*_table_annotation.json'))
            else:
                raise ValueError(f"输入路径不存在: {input_pattern}")

        if not annotation_files:
            raise ValueError(f"未找到匹配的标注文件: {input_pattern}")

        # 创建输出目录
        output_path = Path(output_dir)
        output_path.mkdir(parents=True, exist_ok=True)

        print(f"找到 {len(annotation_files)} 个标注文件")
        print(f"输出目录: {output_path}")
        print(f"使用参数: 容差={self.tolerance}, 自适应阈值={self.adaptive_threshold}")
        print(f"合并因子={self.merge_threshold_factor}, 对齐强度={self.alignment_strength}")
        print("-" * 60)

        # 统计信息
        stats = {
            'total': len(annotation_files),
            'successful': 0,
            'failed': 0,
            'images_copied': 0,
            'images_failed': 0,
            'total_time': 0,
            'results': []
        }

        start_time = time.time()

        # 处理文件
        if self.max_workers == 1:
            # 单线程处理
            for i, annotation_file in enumerate(annotation_files, 1):
                print(f"[{i:3d}/{len(annotation_files)}] 处理: {annotation_file.name}")
                
                result = self.process_single_file(annotation_file, output_path)
                stats['results'].append(result)

                if result['success']:
                    stats['successful'] += 1
                    print(f"    ✅ 成功 - 处理时间: {result['processing_time']:.3f}s")
                    if result.get('adaptive_threshold'):
                        print(f"    🎯 自适应阈值: {result['adaptive_threshold']:.2f}px")

                    # 复制对应的图片文件（如果启用）
                    if self.copy_images and result.get('image_file'):
                        if self.copy_image_file(result['image_file'], output_path):
                            stats['images_copied'] += 1
                            print(f"    📷 图片已复制: {Path(result['image_file']).name}")
                        else:
                            stats['images_failed'] += 1
                    elif self.copy_images:
                        stats['images_failed'] += 1
                        print(f"    ⚠️  未找到对应图片")
                else:
                    stats['failed'] += 1
                    print(f"    ❌ 失败: {result['error']}")
        else:
            # 多线程处理
            with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
                future_to_file = {
                    executor.submit(self.process_single_file, annotation_file, output_path): annotation_file
                    for annotation_file in annotation_files
                }

                for i, future in enumerate(as_completed(future_to_file), 1):
                    annotation_file = future_to_file[future]
                    result = future.result()
                    stats['results'].append(result)

                    print(f"[{i:3d}/{len(annotation_files)}] {annotation_file.name}")
                    
                    if result['success']:
                        stats['successful'] += 1
                        print(f"    ✅ 成功 - 处理时间: {result['processing_time']:.3f}s")
                    else:
                        stats['failed'] += 1
                        print(f"    ❌ 失败: {result['error']}")

        # 计算总时间
        stats['total_time'] = time.time() - start_time

        # 生成报告
        self.generate_report(stats, output_path)

        return stats

    def generate_report(self, stats: Dict[str, Any], output_dir: Path):
        """
        生成处理报告

        Args:
            stats: 统计信息
            output_dir: 输出目录
        """
        print("\n" + "=" * 60)
        print("批量处理完成报告")
        print("=" * 60)
        print(f"总文件数: {stats['total']}")
        print(f"成功处理: {stats['successful']} ({stats['successful']/stats['total']*100:.1f}%)")
        print(f"处理失败: {stats['failed']} ({stats['failed']/stats['total']*100:.1f}%)")
        
        if self.copy_images:
            print(f"图片复制: {stats['images_copied']} 成功, {stats['images_failed']} 失败")
        
        print(f"总处理时间: {stats['total_time']:.2f}秒")
        
        if stats['successful'] > 0:
            avg_time = sum(r['processing_time'] for r in stats['results'] if r['success']) / stats['successful']
            print(f"平均处理时间: {avg_time:.3f}秒/文件")

        # 保存详细报告到文件
        report_file = output_dir / "batch_processing_report.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细报告已保存到: {report_file}")


def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(description='简化优化器批量处理脚本')
    parser.add_argument('input_pattern', help='输入文件模式（支持通配符）')
    parser.add_argument('-o', '--output', required=True, help='输出目录')
    parser.add_argument('-t', '--tolerance', type=float, default=3.0, help='基础容差阈值（像素）')
    parser.add_argument('--no-adaptive', action='store_true', help='禁用自适应阈值')
    parser.add_argument('--merge-factor', type=float, default=1.5, help='合并阈值因子')
    parser.add_argument('--alignment-strength', type=float, default=0.8, help='对齐强度 (0.0-1.0)')
    parser.add_argument('--max-workers', type=int, default=1, help='并行处理线程数')
    parser.add_argument('--no-copy-images', action='store_true', help='不复制图片文件')

    args = parser.parse_args()

    # 创建批量处理器
    processor = BatchSimplifiedProcessor(
        tolerance=args.tolerance,
        adaptive_threshold=not args.no_adaptive,
        merge_threshold_factor=args.merge_factor,
        alignment_strength=args.alignment_strength,
        max_workers=args.max_workers,
        copy_images=not args.no_copy_images
    )

    try:
        # 执行批量处理
        stats = processor.process_batch(args.input_pattern, args.output)
        
        if stats['failed'] == 0:
            print("\n🎉 所有文件处理成功！")
            return 0
        else:
            print(f"\n⚠️  有 {stats['failed']} 个文件处理失败")
            return 1
        
    except Exception as e:
        print(f"批量处理失败: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
