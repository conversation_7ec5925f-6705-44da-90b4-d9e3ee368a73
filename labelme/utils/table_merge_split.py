#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/6/24 10:30
# <AUTHOR> oyy
# @FileName: table_merge_split.py

"""
表格合并和拆分算法工具类

提供表格单元格合并和拆分的核心算法，独立于UI层，便于测试和复用。
"""

from typing import List, Dict, Any
from labelme.table_shape import TableCellShape
from labelme.utils.log import get_logger

LOGGER = get_logger()


class TableMergeEngine:
    """表格合并算法引擎"""

    @staticmethod
    def calculate_merge_bbox(cells: List[TableCellShape]) -> Dict[str, float]:
        """
        计算多个单元格合并后的边界框

        Args:
            cells: 要合并的单元格列表

        Returns:
            边界框字典: {"min_x": float, "max_x": float, "min_y": float, "max_y": float}
        """
        if not cells:
            return {}

        # 收集所有单元格的物理坐标
        all_x_coords = []
        all_y_coords = []

        for cell in cells:
            if hasattr(cell, 'points') and cell.points:
                for point in cell.points:
                    all_x_coords.append(point.x())
                    all_y_coords.append(point.y())

        if not all_x_coords or not all_y_coords:
            LOGGER.error("❌ 无法获取单元格物理坐标")
            return {}

        # 计算合并后的边界框
        bbox = {
            "min_x": min(all_x_coords),
            "max_x": max(all_x_coords),
            "min_y": min(all_y_coords),
            "max_y": max(all_y_coords)
        }

        LOGGER.debug(f"🔧 计算合并边界框: {bbox}")
        return bbox

    @staticmethod
    def calculate_merge_logical_position(cells: List[TableCellShape]) -> Dict[str, int]:
        """
        计算多个单元格合并后的逻辑位置

        Args:
            cells: 要合并的单元格列表

        Returns:
            逻辑位置字典: {"start_row": int, "end_row": int, "start_col": int, "end_col": int}
        """
        logical_positions = [cell.get_logical_location() for cell in cells]

        min_start_row = min(pos["start_row"] for pos in logical_positions)
        max_end_row = max(pos["end_row"] for pos in logical_positions)
        min_start_col = min(pos["start_col"] for pos in logical_positions)
        max_end_col = max(pos["end_col"] for pos in logical_positions)

        return {
            "start_row": min_start_row,
            "end_row": max_end_row,
            "start_col": min_start_col,
            "end_col": max_end_col
        }

    @staticmethod
    def create_merged_cell_data(cells: List[TableCellShape], bbox: Dict[str, float],
                                logical_pos: Dict[str, int]) -> TableCellShape:
        """创建合并单元格对象"""
        from qtpy.QtCore import QPointF
        from labelme.table_shape import TableCellShape

        # 获取第一个单元格作为属性继承的基准
        base_cell = cells[0]

        # 创建新的TableCellShape，继承基准单元格的属性
        merged_cell = TableCellShape(
            label=f"merged_{logical_pos['start_row']}_{logical_pos['start_col']}",
            line_color=base_cell.line_color,  # 继承线条颜色
            flags=base_cell.flags,  # 继承标志
            group_id=base_cell.group_id,  # 继承组ID
            description=base_cell.description  # 继承描述
        )

        # 设置物理坐标（4个顶点）
        merged_cell.points = [
            QPointF(bbox["min_x"], bbox["min_y"]),  # 左上
            QPointF(bbox["max_x"], bbox["min_y"]),  # 右上
            QPointF(bbox["max_x"], bbox["max_y"]),  # 右下
            QPointF(bbox["min_x"], bbox["max_y"])  # 左下
        ]

        # 继承视觉属性
        merged_cell.fill = base_cell.fill
        merged_cell.selected = False  # 新建的不要选中状态

        # 继承表格属性
        merged_cell.set_table_type(base_cell.get_table_type())  # 继承表格类型
        merged_cell.set_border_style(**base_cell.get_border_style())  # 继承边框样式

        # 设置逻辑位置
        merged_cell.set_logical_location(**logical_pos)

        # 合并文本内容
        texts = [cell.get_cell_text() for cell in cells if cell.get_cell_text()]
        merged_text = " | ".join(texts) if texts else ""
        merged_cell.set_cell_text(merged_text)

        # 设置合并标识
        merged_cell.table_properties['cell_id'] = f"merged_{logical_pos['start_row']}_{logical_pos['start_col']}"

        return merged_cell

    @staticmethod
    def are_cells_adjacent(cells: List[TableCellShape]) -> bool:
        """
        验证单元格是否相邻可合并（支持已合并单元格的再次合并）

        Args:
            cells: 要验证的单元格列表

        Returns:
            bool: True表示可以合并，False表示不可合并
        """
        if len(cells) < 2:
            return False

        # 1. 收集所有被占用的逻辑位置
        occupied_positions = set()

        for cell in cells:
            lloc = cell.get_logical_location()
            for r in range(lloc["start_row"], lloc["end_row"] + 1):
                for c in range(lloc["start_col"], lloc["end_col"] + 1):
                    position = (r, c)
                    # 检查重叠：同一逻辑位置不能被多个单元格占用
                    if position in occupied_positions:
                        print(f"❌ 检测到重叠位置: {position}")
                        return False
                    occupied_positions.add(position)

        # 2. 计算最小包围矩形
        all_rows = {pos[0] for pos in occupied_positions}
        all_cols = {pos[1] for pos in occupied_positions}

        min_row, max_row = min(all_rows), max(all_rows)
        min_col, max_col = min(all_cols), max(all_cols)

        # 3. 检查是否形成完整矩形（无空隙）
        expected_positions = set()
        for r in range(min_row, max_row + 1):
            for c in range(min_col, max_col + 1):
                expected_positions.add((r, c))

        # 4. 验证完整性
        if occupied_positions == expected_positions:
            return True
        else:
            missing = expected_positions - occupied_positions
            print(f"❌ 单元格不能合并: 缺少位置 {missing}")
            return False


class TableSplitEngine:
    """表格单元格拆分引擎"""

    @staticmethod
    def can_split_cell(cell) -> bool:
        """检查单元格是否可以拆分"""
        # 检查is_merged标识
        return cell.table_properties.get('is_merged', False)

    @staticmethod
    def calculate_split_parameters(cell) -> Dict[str, Any]:
        """计算拆分参数"""
        if not TableSplitEngine.can_split_cell(cell):
            raise ValueError("该单元格不可拆分")

        # 🔧 修复：使用get_logical_location()方法
        logical_pos = cell.get_logical_location()

        row_span = logical_pos["end_row"] - logical_pos["start_row"] + 1
        col_span = logical_pos["end_col"] - logical_pos["start_col"] + 1

        # 计算每个子单元格的尺寸
        merged_bbox = cell.boundingRect()
        cell_width = merged_bbox.width() / col_span
        cell_height = merged_bbox.height() / row_span

        return {
            'row_span': row_span,
            'col_span': col_span,
            'cell_width': cell_width,
            'cell_height': cell_height,
            'merged_bbox': merged_bbox,
            'start_row': logical_pos["start_row"],
            'start_col': logical_pos["start_col"],
            'end_row': logical_pos["end_row"],
            'end_col': logical_pos["end_col"]
        }

    @staticmethod
    def create_split_cells(cell) -> List:
        """创建拆分后的单元格列表"""
        from qtpy.QtCore import QPointF
        from labelme.table_shape import TableCellShape

        split_params = TableSplitEngine.calculate_split_parameters(cell)
        split_cells = []

        for row in range(split_params['start_row'], split_params['end_row'] + 1):
            for col in range(split_params['start_col'], split_params['end_col'] + 1):
                # 计算子单元格的位置
                relative_row = row - split_params['start_row']
                relative_col = col - split_params['start_col']

                x = split_params['merged_bbox'].x() + relative_col * split_params['cell_width']
                y = split_params['merged_bbox'].y() + relative_row * split_params['cell_height']

                # 创建子单元格的四个顶点
                points = [
                    QPointF(x, y),  # 左上
                    QPointF(x + split_params['cell_width'], y),  # 右上
                    QPointF(x + split_params['cell_width'], y + split_params['cell_height']),  # 右下
                    QPointF(x, y + split_params['cell_height'])  # 左下
                ]

                # 创建新的单元格（继承原单元格属性）
                split_cell = TableCellShape(
                    label=f"Cell_R{row}C{col}",
                    line_color=cell.line_color,
                    flags=cell.flags,
                    group_id=cell.group_id,
                    description=cell.description
                )
                split_cell.points = points

                # 继承视觉属性
                split_cell.fill = cell.fill
                split_cell.selected = False

                # 继承表格属性
                split_cell.set_table_type(cell.get_table_type())
                split_cell.set_border_style(**cell.get_border_style())

                # 设置逻辑位置（单个单元格）
                split_cell.set_logical_location(
                    start_row=row,
                    end_row=row,
                    start_col=col,
                    end_col=col
                )

                # 设置单元格ID
                split_cell.table_properties['cell_id'] = f"cell_{row}_{col}"
                split_cell.set_cell_text("")  # 清空文本内容

                split_cells.append(split_cell)

        return split_cells