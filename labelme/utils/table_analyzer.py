#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
表格结构分析器 - 智能增强版本（向后兼容）
将物理单元格转换为逻辑表格矩阵
"""

import numpy as np
from typing import List, Dict, Tuple, Any, Optional
from qtpy import QtCore

# 导入我们的表格单元格类
from labelme.table_shape import TableCellShape
from .log import get_logger

LOGGER = get_logger()


class TableAnalyzer:
    """智能表格结构分析器

    向后兼容设计：
    - 保持所有现有接口签名不变
    - 内部默认使用智能分析（容差修正）
    - 可通过类属性控制分析行为
    """

    # 🎛️ 分析行为控制（类属性）
    enable_smart_analysis = True  # 是否启用智能分析
    default_tolerance = 10.0  # 默认容差值
    debug_output = True  # 是否输出调试信息

    @staticmethod
    def analyze_cells_to_grid(cells: List[TableCellShape]) -> Dict[str, Any]:
        """将单元格列表分析成逻辑表格网格

        🔧 智能增强：内部自动使用容差修正，但保持接口完全兼容

        Args:
            cells: TableCellShape对象列表

        Returns:
            dict: {
                'grid': {
                    'rows': int,           # 总行数
                    'cols': int,           # 总列数
                    'matrix': list[list],  # 逻辑矩阵 matrix[row][col] = cell_id
                },
                'cell_positions': {
                    cell_id: {
                        'row': int,        # 逻辑行索引
                        'col': int,        # 逻辑列索引
                        'row_span': int,   # 跨行数
                        'col_span': int,   # 跨列数
                        'is_merged': bool, # 是否合并单元格
                        'physical_bounds': (x1, y1, x2, y2),  # 物理边界
                        'cell_object': cell  # 保留引用便于后续处理
                    }
                },
                'boundaries': {
                    'rows': [y1, y2, ...],    # 行边界坐标
                    'cols': [x1, x2, ...]     # 列边界坐标
                }
            }
        """
        # 1. 输入验证
        if not cells or len(cells) < 1:
            raise ValueError("需要至少1个单元格")

        if not all(isinstance(cell, TableCellShape) for cell in cells):
            raise TypeError("所有输入必须是TableCellShape对象")

        # 2. 根据设置选择分析模式
        if TableAnalyzer.enable_smart_analysis:
            return TableAnalyzer._analyze_with_smart_mode(cells)
        else:
            return TableAnalyzer._analyze_with_precise_mode(cells)

    @staticmethod
    def _analyze_with_smart_mode(cells: List[TableCellShape]) -> Dict[str, Any]:
        """智能分析模式 - 带容差修正"""
        tolerance = TableAnalyzer.default_tolerance

        if TableAnalyzer.debug_output:
            LOGGER.debug(f"[SMART] 智能分析模式，容差={tolerance}px，单元格数={len(cells)}")

        # 提取物理边界
        cell_bounds = TableAnalyzer._extract_physical_bounds(cells)

        # 检测边界问题
        boundary_issues = TableAnalyzer._detect_boundary_issues(cell_bounds, tolerance)

        # 如果没有问题，使用精确分析
        if not boundary_issues['has_issues']:
            if TableAnalyzer.debug_output:
                LOGGER.debug("[SMART] 边界完美，使用精确分析")
            return TableAnalyzer._analyze_with_precise_mode(cells)

        # 有问题时，使用容差修正
        if TableAnalyzer.debug_output:
            LOGGER.debug(f"[SMART] 检测到边界问题，应用容差修正")

        # 生成修正后的边界
        raw_row_coords = [coord for _, y1, _, y2, _, _ in cell_bounds for coord in [y1, y2]]
        raw_col_coords = [coord for x1, _, x2, _, _, _ in cell_bounds for coord in [x1, x2]]

        corrected_row_boundaries = TableAnalyzer._merge_close_boundaries(raw_row_coords, tolerance)
        corrected_col_boundaries = TableAnalyzer._merge_close_boundaries(raw_col_coords, tolerance)

        # 构建修正后的逻辑网格
        corrected_grid = TableAnalyzer._build_logical_grid_smart(
            cell_bounds, corrected_row_boundaries, corrected_col_boundaries, tolerance
        )

        # 构建结果（格式与原版完全兼容）
        result = {
            'grid': {
                'rows': len(corrected_row_boundaries) - 1,
                'cols': len(corrected_col_boundaries) - 1,
                'matrix': corrected_grid['matrix']
            },
            'cell_positions': corrected_grid['cell_positions'],
            'boundaries': {
                'rows': corrected_row_boundaries,
                'cols': corrected_col_boundaries
            }
        }

        # 🆕 附加智能分析元数据（不影响兼容性）
        result['_smart_metadata'] = {
            'analysis_mode': 'smart',
            'boundary_issues': boundary_issues,
            'tolerance_used': tolerance
        }

        if TableAnalyzer.debug_output:
            LOGGER.debug(f"[SMART] 修正完成，网格大小: {result['grid']['rows']}x{result['grid']['cols']}")
            TableAnalyzer._print_boundary_issues(boundary_issues)

        return result

    @staticmethod
    def _analyze_with_precise_mode(cells: List[TableCellShape]) -> Dict[str, Any]:
        """精确分析模式 - 原版逻辑"""
        if TableAnalyzer.debug_output:
            LOGGER.debug(f"[PRECISE] 精确分析模式，单元格数={len(cells)}")

        # 2. 提取物理边界
        cell_bounds = TableAnalyzer._extract_physical_bounds(cells)

        # 3. 检测行列结构 - 精确模式，不做聚类
        row_boundaries = TableAnalyzer._detect_row_boundaries(cell_bounds)
        col_boundaries = TableAnalyzer._detect_col_boundaries(cell_bounds)

        # 4. 构建逻辑矩阵 - 支持合并单元格
        grid_result = TableAnalyzer._build_logical_grid(
            cell_bounds, row_boundaries, col_boundaries
        )

        # 5. 构建返回结果
        result = {
            'grid': {
                'rows': len(row_boundaries) - 1,
                'cols': len(col_boundaries) - 1,
                'matrix': grid_result['matrix']
            },
            'cell_positions': grid_result['cell_positions'],
            'boundaries': {
                'rows': row_boundaries,
                'cols': col_boundaries
            }
        }

        return result

    @staticmethod
    def _extract_physical_bounds(cells: List[TableCellShape]) -> List[Tuple]:
        """提取每个单元格的物理边界框

        Returns:
            list: [(x1, y1, x2, y2, cell_id, cell_obj), ...]
        """
        bounds = []

        for cell in cells:
            if not cell.points or len(cell.points) < 4:
                continue

            # 获取边界框坐标
            x_coords = [point.x() for point in cell.points]
            y_coords = [point.y() for point in cell.points]

            x1, x2 = min(x_coords), max(x_coords)
            y1, y2 = min(y_coords), max(y_coords)

            bounds.append((x1, y1, x2, y2, id(cell), cell))

        # 按位置排序（从上到下，从左到右）
        bounds.sort(key=lambda b: (b[1], b[0]))  # (y1, x1)

        return bounds

    @staticmethod
    def _detect_row_boundaries(cell_bounds: List[Tuple]) -> List[float]:
        """检测行边界坐标 - 精确模式，不做聚类

        收集所有Y坐标，去重排序，保持精确
        """
        y_coords = set()
        for x1, y1, x2, y2, cell_id, cell in cell_bounds:
            y_coords.add(y1)
            y_coords.add(y2)

        # 直接排序，不做聚类
        return sorted(y_coords)

    @staticmethod
    def _detect_col_boundaries(cell_bounds: List[Tuple]) -> List[float]:
        """检测列边界坐标 - 精确模式，不做聚类

        收集所有X坐标，去重排序，保持精确
        """
        x_coords = set()
        for x1, y1, x2, y2, cell_id, cell in cell_bounds:
            x_coords.add(x1)
            x_coords.add(x2)

        # 直接排序，不做聚类
        return sorted(x_coords)

    @staticmethod
    def _build_logical_grid(cell_bounds: List[Tuple],
                            row_boundaries: List[float],
                            col_boundaries: List[float]) -> Dict:
        """构建逻辑网格矩阵 - 支持合并单元格检测

        将每个物理单元格精确分配到逻辑网格位置，支持跨行跨列
        """
        rows = len(row_boundaries) - 1
        cols = len(col_boundaries) - 1

        # 初始化矩阵
        matrix = [[None for _ in range(cols)] for _ in range(rows)]
        cell_positions = {}

        for x1, y1, x2, y2, cell_id, cell in cell_bounds:
            # 精确查找边界索引
            start_row = TableAnalyzer._find_boundary_index(y1, row_boundaries)
            end_row = TableAnalyzer._find_boundary_index(y2, row_boundaries)
            start_col = TableAnalyzer._find_boundary_index(x1, col_boundaries)
            end_col = TableAnalyzer._find_boundary_index(x2, col_boundaries)

            # 边界检查
            start_row = max(0, min(start_row, rows - 1))
            start_col = max(0, min(start_col, cols - 1))

            # end_row/end_col 需要-1，因为边界索引指向的是结束边界
            end_row = max(start_row, min(end_row - 1, rows - 1))
            end_col = max(start_col, min(end_col - 1, cols - 1))

            # 计算跨度
            row_span = end_row - start_row + 1
            col_span = end_col - start_col + 1
            is_merged = row_span > 1 or col_span > 1

            # 在矩阵中标记所有覆盖的位置
            for row in range(start_row, end_row + 1):
                for col in range(start_col, end_col + 1):
                    if 0 <= row < rows and 0 <= col < cols:
                        matrix[row][col] = cell_id

            # 记录位置信息
            cell_positions[cell_id] = {
                'row': start_row,
                'col': start_col,
                'row_span': row_span,
                'col_span': col_span,
                'is_merged': is_merged,
                'physical_bounds': (x1, y1, x2, y2),
                'cell_object': cell  # 保留引用便于后续处理
            }

        return {
            'matrix': matrix,
            'cell_positions': cell_positions
        }

    @staticmethod
    def _find_boundary_index(coord: float, boundaries: List[float]) -> int:
        """在边界列表中找到坐标对应的网格索引 - 精确匹配"""
        try:
            # 精确查找坐标在边界列表中的位置
            return boundaries.index(coord)
        except ValueError:
            # 如果找不到精确匹配，找到最近的边界
            for i, boundary in enumerate(boundaries):
                if coord <= boundary:
                    return i
            return len(boundaries) - 1

    # ===== 🆕 智能分析相关方法 =====

    @staticmethod
    def _detect_boundary_issues(cell_bounds: List[Tuple], tolerance: float) -> Dict:
        """检测边界问题"""
        # 提取所有坐标
        row_coords = [coord for _, y1, _, y2, _, _ in cell_bounds for coord in [y1, y2]]
        col_coords = [coord for x1, _, x2, _, _, _ in cell_bounds for coord in [x1, x2]]

        # 检测相近边界
        row_issues = TableAnalyzer._find_close_coordinates(row_coords, tolerance)
        col_issues = TableAnalyzer._find_close_coordinates(col_coords, tolerance)

        has_issues = len(row_issues) > 0 or len(col_issues) > 0

        return {
            'has_issues': has_issues,
            'row_issues': row_issues,
            'col_issues': col_issues,
            'tolerance_used': tolerance
        }

    @staticmethod
    def _find_close_coordinates(coords: List[float], tolerance: float) -> List[Dict]:
        """找到相近的坐标对"""
        unique_coords = sorted(set(coords))
        issues = []

        for i in range(len(unique_coords) - 1):
            coord1, coord2 = unique_coords[i], unique_coords[i + 1]
            distance = abs(coord2 - coord1)

            if distance <= tolerance:
                suggested_merge = (coord1 + coord2) / 2.0
                issues.append({
                    'coord1': coord1,
                    'coord2': coord2,
                    'distance': distance,
                    'suggested_merge': suggested_merge
                })

        return issues

    @staticmethod
    def _merge_close_boundaries(coords: List[float], tolerance: float) -> List[float]:
        """合并相近边界"""
        if not coords:
            return []

        unique_coords = sorted(set(coords))
        if len(unique_coords) <= 1:
            return unique_coords

        merged = [unique_coords[0]]
        merge_count = 0

        for current in unique_coords[1:]:
            if abs(current - merged[-1]) <= tolerance:
                # 合并：取平均值
                old_boundary = merged[-1]
                merged[-1] = (merged[-1] + current) / 2.0
                merge_count += 1
                if TableAnalyzer.debug_output:
                    LOGGER.debug(f"[MERGE] {old_boundary:.1f} + {current:.1f} → {merged[-1]:.1f}")
            else:
                # 保留：距离足够大
                merged.append(current)

        if TableAnalyzer.debug_output and merge_count > 0:
            LOGGER.debug(f"[MERGE] 边界合并: {len(unique_coords)} → {len(merged)} (合并了{merge_count}对)")

        return merged

    @staticmethod
    def _build_logical_grid_smart(cell_bounds: List[Tuple], row_boundaries: List[float],
                                  col_boundaries: List[float], tolerance: float) -> Dict:
        """构建逻辑网格 - 智能版本（修复版）"""
        rows = len(row_boundaries) - 1
        cols = len(col_boundaries) - 1

        # 初始化矩阵
        matrix = [[None for _ in range(cols)] for _ in range(rows)]
        cell_positions = {}

        for x1, y1, x2, y2, cell_id, cell in cell_bounds:
            # 使用容差查找边界索引
            start_row = TableAnalyzer._find_boundary_index_smart(y1, row_boundaries, tolerance)
            end_row = TableAnalyzer._find_boundary_index_smart(y2, row_boundaries, tolerance)
            start_col = TableAnalyzer._find_boundary_index_smart(x1, col_boundaries, tolerance)
            end_col = TableAnalyzer._find_boundary_index_smart(x2, col_boundaries, tolerance)

            # 🔧 调试输出：原始边界索引
            if TableAnalyzer.debug_output:
                LOGGER.debug(f"[BOUNDARY] Cell {str(cell_id)[-4:]}: "
                      f"物理({x1:.1f},{y1:.1f},{x2:.1f},{y2:.1f}) → "
                      f"边界索引(row:{start_row}-{end_row}, col:{start_col}-{end_col})")

            # 边界检查和修正（保持与精确模式一致的逻辑）
            start_row = max(0, min(start_row, rows - 1))
            start_col = max(0, min(start_col, cols - 1))

            # 🔧 修复：统一边界索引到网格位置的转换逻辑
            # 边界索引需要转换为网格位置，end位置需要减1
            end_row = max(start_row, min(end_row - 1, rows - 1))
            end_col = max(start_col, min(end_col - 1, cols - 1))

            # 计算跨度
            row_span = end_row - start_row + 1
            col_span = end_col - start_col + 1
            is_merged = row_span > 1 or col_span > 1

            # 🔧 详细调试输出：验证计算过程
            if TableAnalyzer.debug_output:
                LOGGER.debug(f"[GRID] Cell {str(cell_id)[-4:]}: "
                      f"网格位置({start_row},{start_col},{end_row},{end_col}) → "
                      f"跨度({row_span}x{col_span})")

            # 填充矩阵
            for row in range(start_row, end_row + 1):
                for col in range(start_col, end_col + 1):
                    if 0 <= row < rows and 0 <= col < cols:
                        matrix[row][col] = cell_id

            # 记录位置信息
            cell_positions[cell_id] = {
                'row': start_row,
                'col': start_col,
                'row_span': row_span,
                'col_span': col_span,
                'is_merged': is_merged,
                'physical_bounds': (x1, y1, x2, y2),
                'cell_object': cell
            }

        return {
            'matrix': matrix,
            'cell_positions': cell_positions
        }

    @staticmethod
    def _find_boundary_index_smart(coord: float, boundaries: List[float], tolerance: float) -> int:
        """容差边界索引查找"""
        # 优先精确匹配
        for i, boundary in enumerate(boundaries):
            if abs(coord - boundary) <= tolerance:
                return i

        # 降级：最近匹配
        min_distance = float('inf')
        best_index = 0
        for i, boundary in enumerate(boundaries):
            distance = abs(coord - boundary)
            if distance < min_distance:
                min_distance = distance
                best_index = i

        return best_index

    @staticmethod
    def _print_boundary_issues(boundary_issues: Dict):
        """打印边界问题详情"""
        if not boundary_issues['has_issues']:
            return

        row_issues = boundary_issues.get('row_issues', [])
        col_issues = boundary_issues.get('col_issues', [])

        LOGGER.debug(f"[ISSUES] 边界问题统计: {len(row_issues)} 行冲突 + {len(col_issues)} 列冲突")

        if row_issues:
            LOGGER.debug(f"   行边界冲突详情:")
            for issue in row_issues:
                LOGGER.debug(f"      {issue['coord1']:.1f} ↔ {issue['coord2']:.1f} "
                      f"(距离: {issue['distance']:.1f}px → 修正为: {issue['suggested_merge']:.1f})")

        if col_issues:
            LOGGER.debug(f"   列边界冲突详情:")
            for issue in col_issues:
                LOGGER.debug(f"      {issue['coord1']:.1f} ↔ {issue['coord2']:.1f} "
                      f"(距离: {issue['distance']:.1f}px → 修正为: {issue['suggested_merge']:.1f})")

    @staticmethod
    def print_grid_analysis(result: Dict) -> None:
        """打印表格分析结果，便于调试 - 兼容原版 + 智能增强"""
        LOGGER.debug(f"表格大小: {result['grid']['rows']}行 x {result['grid']['cols']}列")
        LOGGER.debug(f"行边界: {result['boundaries']['rows']}")
        LOGGER.debug(f"列边界: {result['boundaries']['cols']}")

        LOGGER.debug("\n逻辑矩阵:")
        matrix = result['grid']['matrix']
        for i, row in enumerate(matrix):
            row_str = " | ".join([f"{str(cell)[-4:]:>4}" if cell else "   ." for cell in row])
            LOGGER.debug(f"Row {i}: {row_str}")

        LOGGER.debug("\n单元格信息:")
        for cell_id, pos in result['cell_positions'].items():
            merge_info = f" (合并 {pos['row_span']}x{pos['col_span']})" if pos['is_merged'] else ""
            LOGGER.debug(f"Cell {str(cell_id)[-4:]}: ({pos['row']}, {pos['col']}){merge_info}")

        # 🆕 智能分析额外信息
        smart_metadata = result.get('_smart_metadata')
        if smart_metadata and TableAnalyzer.debug_output:
            LOGGER.debug(f"\n智能分析信息:")
            LOGGER.debug(f"   分析模式: {smart_metadata.get('analysis_mode', 'precise')}")
            if smart_metadata.get('analysis_mode') == 'smart':
                LOGGER.debug(f"   容差设置: {smart_metadata.get('tolerance_used', 0)} 像素")
                boundary_issues = smart_metadata.get('boundary_issues', {})
                if boundary_issues.get('has_issues', False):
                    LOGGER.debug(f"   修正状态: 已自动修正边界问题")
                else:
                    LOGGER.debug(f"   检测结果: 无边界问题")

    # ===== 🆕 对齐修正专用方法 =====

    @staticmethod
    def generate_ideal_grid_from_boundaries(boundaries: Dict[str, List[float]],
                                            tolerance: float = None) -> Dict[str, Any]:
        """基于边界生成理想网格 - 对齐修正专用

        复用现有的_merge_close_boundaries方法来生成理想网格坐标

        Args:
            boundaries: 边界信息 {'rows': [y坐标], 'cols': [x坐标]}
            tolerance: 容差值（像素）

        Returns:
            dict: {
                'ideal_vertical_lines': [x坐标列表],
                'ideal_horizontal_lines': [y坐标列表],
                'applied_tolerance': float
            }
        """
        if tolerance is None:
            tolerance = TableAnalyzer.default_tolerance

        if not boundaries or not boundaries.get('rows') or not boundaries.get('cols'):
            return {
                'ideal_vertical_lines': [],
                'ideal_horizontal_lines': [],
                'applied_tolerance': tolerance
            }

        # 复用现有的边界合并方法
        ideal_rows = TableAnalyzer._merge_close_boundaries(boundaries['rows'], tolerance)
        ideal_cols = TableAnalyzer._merge_close_boundaries(boundaries['cols'], tolerance)

        return {
            'ideal_vertical_lines': ideal_cols,  # x坐标（垂直线）
            'ideal_horizontal_lines': ideal_rows,  # y坐标（水平线）
            'applied_tolerance': tolerance
        }

    @staticmethod
    def calculate_alignment_report(cells: List[TableCellShape], ideal_grid: Dict[str, Any],
                                   tolerance: float = None) -> Dict[str, Any]:
        """计算对齐报告 - 对齐修正专用

        检测每个单元格边界与理想网格的对齐情况

        Args:
            cells: TableCellShape对象列表
            ideal_grid: 理想网格数据
            tolerance: 对齐判断容差

        Returns:
            dict: {
                'misaligned_cells': List[Dict],  # 不对齐的单元格详情
                'boundary_errors': Dict,         # 边界错误信息
                'total_misalignment_count': int, # 不对齐单元格总数
                'applied_tolerance': float       # 使用的容差值
            }
        """
        if tolerance is None:
            tolerance = TableAnalyzer.default_tolerance

        if not cells or not ideal_grid:
            return {
                'misaligned_cells': [],
                'boundary_errors': {'horizontal': [], 'vertical': []},
                'total_misalignment_count': 0,
                'applied_tolerance': tolerance
            }

        # 复用现有的边界提取方法
        cell_bounds = TableAnalyzer._extract_physical_bounds(cells)

        misaligned_cells = []
        boundary_errors = {'horizontal': [], 'vertical': []}

        ideal_h_lines = ideal_grid.get('ideal_horizontal_lines', [])
        ideal_v_lines = ideal_grid.get('ideal_vertical_lines', [])

        if not ideal_h_lines or not ideal_v_lines:
            return {
                'misaligned_cells': [],
                'boundary_errors': boundary_errors,
                'total_misalignment_count': 0,
                'applied_tolerance': tolerance
            }

        # 检查每个单元格的对齐情况
        for x1, y1, x2, y2, cell_id, cell in cell_bounds:
            cell_errors = {'cell_id': cell_id, 'misaligned_edges': []}

            # 获取单元格逻辑位置（如果有的话）
            logical_loc = cell.get_logical_location() if hasattr(cell, 'get_logical_location') else {}

            # 检查水平边界对齐
            for y_coord, edge_name in [(y1, 'top'), (y2, 'bottom')]:
                min_distance = min(abs(y_coord - line) for line in ideal_h_lines)
                if min_distance > tolerance:
                    cell_errors['misaligned_edges'].append({
                        'edge': edge_name,
                        'coordinate': y_coord,
                        'distance_to_ideal': min_distance,
                        'logical_position': logical_loc
                    })
                    boundary_errors['horizontal'].append({
                        'cell_id': cell_id,
                        'edge': edge_name,
                        'coord': y_coord,
                        'distance': min_distance
                    })

            # 检查垂直边界对齐
            for x_coord, edge_name in [(x1, 'left'), (x2, 'right')]:
                min_distance = min(abs(x_coord - line) for line in ideal_v_lines)
                if min_distance > tolerance:
                    cell_errors['misaligned_edges'].append({
                        'edge': edge_name,
                        'coordinate': x_coord,
                        'distance_to_ideal': min_distance,
                        'logical_position': logical_loc
                    })
                    boundary_errors['vertical'].append({
                        'cell_id': cell_id,
                        'edge': edge_name,
                        'coord': x_coord,
                        'distance': min_distance
                    })

            if cell_errors['misaligned_edges']:
                misaligned_cells.append(cell_errors)

        return {
            'misaligned_cells': misaligned_cells,
            'boundary_errors': boundary_errors,
            'total_misalignment_count': len(misaligned_cells),
            'applied_tolerance': tolerance
        }

# 移除聚类器类，因为精确模式不需要聚类
# class CoordinateClusterer 已删除

# ===== 🎛️ 全局控制接口 =====

def set_analysis_mode(smart: bool = True, tolerance: float = 5.0, debug: bool = True):
    """设置全局分析模式

    Args:
        smart: 是否启用智能分析
        tolerance: 容差值（像素）
        debug: 是否输出调试信息
    """
    TableAnalyzer.enable_smart_analysis = smart
    TableAnalyzer.default_tolerance = tolerance
    TableAnalyzer.debug_output = debug

    mode_name = "智能分析" if smart else "精确分析"
    LOGGER.debug(f"[CONFIG] 分析模式设置: {mode_name}, 容差={tolerance}px, 调试={'开启' if debug else '关闭'}")


# 工具函数：验证分析结果
def validate_grid_result(result: Dict) -> bool:
    """验证网格分析结果的完整性"""
    try:
        # 检查必需的键
        required_keys = ['grid', 'cell_positions', 'boundaries']
        if not all(key in result for key in required_keys):
            return False

        # 检查网格信息
        grid = result['grid']
        if grid['rows'] <= 0 or grid['cols'] <= 0:
            return False

        # 检查矩阵尺寸
        matrix = grid['matrix']
        if len(matrix) != grid['rows']:
            return False

        for row in matrix:
            if len(row) != grid['cols']:
                return False

        return True

    except Exception:
        return False


# 使用示例
if __name__ == "__main__":
    # 🎛️ 可选：配置分析模式
    # set_analysis_mode(smart=True, tolerance=5.0, debug=True)  # 智能模式
    # set_analysis_mode(smart=False, debug=True)               # 精确模式

    # 示例：如何使用分析器（接口完全不变）
    # cells = [cell1, cell2, cell3, ...]  # TableCellShape对象列表
    # result = TableAnalyzer.analyze_cells_to_grid(cells)    # 接口完全相同！
    # TableAnalyzer.print_grid_analysis(result)             # 接口完全相同！
    pass