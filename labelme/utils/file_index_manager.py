"""
文件索引管理器
负责管理viewed_files_state.json的读写和索引构建
"""

import os
import json
import datetime
from typing import Dict, List, Optional, Tuple

# 使用标准logging而不是loguru，避免依赖问题
import logging
LOGGER = logging.getLogger(__name__)


class FileIndexManager:
    """文件索引管理器"""
    
    INDEX_VERSION = "2.0"
    INDEX_FILENAME = "viewed_files_state.json"
    
    def __init__(self, directory_path: str):
        """
        初始化文件索引管理器
        
        Args:
            directory_path: 目录路径
        """
        self.directory_path = directory_path
        self.index_file_path = os.path.join(directory_path, self.INDEX_FILENAME)
        
    def load_index(self) -> Optional[Dict]:
        """
        加载索引文件

        Returns:
            索引数据字典，如果文件不存在或格式错误返回None
        """
        if not os.path.exists(self.index_file_path):
            LOGGER.debug(f"索引文件不存在: {self.index_file_path}")
            return None

        try:
            with open(self.index_file_path, 'r', encoding='utf-8') as f:
                index_data = json.load(f)

            # 验证基本格式
            if not isinstance(index_data, dict):
                LOGGER.warning("索引文件格式无效：不是字典类型")
                return None
                
            return index_data
            
        except json.JSONDecodeError as e:
            LOGGER.error(f"索引文件JSON格式错误: {e}")
            return None
        except Exception as e:
            LOGGER.error(f"加载索引文件失败: {e}")
            return None

    def load_index_from_directory(self, directory_path: str) -> Optional[Dict]:
        """
        从指定目录加载索引文件

        Args:
            directory_path: 目录路径

        Returns:
            索引数据字典，如果文件不存在或格式错误返回None
        """
        index_file_path = os.path.join(directory_path, self.INDEX_FILENAME)

        if not os.path.exists(index_file_path):
            LOGGER.debug(f"索引文件不存在: {index_file_path}")
            return None

        try:
            with open(index_file_path, 'r', encoding='utf-8') as f:
                index_data = json.load(f)

            # 验证基本格式
            if not isinstance(index_data, dict):
                LOGGER.warning("索引文件格式无效：不是字典类型")
                return None

            return index_data

        except json.JSONDecodeError as e:
            LOGGER.error(f"索引文件JSON格式错误: {e}")
            return None
        except Exception as e:
            LOGGER.error(f"加载索引文件失败: {e}")
            return None

    def save_index(self, index_data: Dict) -> bool:
        """
        保存索引文件
        
        Args:
            index_data: 索引数据
            
        Returns:
            是否保存成功
        """
        try:
            # 更新时间戳
            index_data["last_updated"] = datetime.datetime.now().isoformat()
            
            with open(self.index_file_path, 'w', encoding='utf-8') as f:
                json.dump(index_data, f, indent=2, ensure_ascii=False)
                
            LOGGER.debug(f"索引文件保存成功: {self.index_file_path}")
            return True
            
        except Exception as e:
            LOGGER.error(f"保存索引文件失败: {e}")
            return False

    def update_file_in_index(self, filename: str, new_status: dict) -> bool:
        """
        更新索引中的文件状态

        Args:
            filename: 文件名
            new_status: 新的状态数据

        Returns:
            bool: 更新是否成功
        """
        # 加载当前索引
        index_data = self.load_index()
        if not index_data or "files" not in index_data:
            LOGGER.warning(f"索引不存在或格式错误，无法更新文件状态: {filename}")
            return False

        # 查找文件
        if filename not in index_data["files"]:
            LOGGER.warning(f"文件不在索引中: {filename}")
            return False

        # 更新文件状态
        old_data = index_data["files"][filename]
        index_data["files"][filename].update(new_status)
        index_data["files"][filename]["json_last_modified"] = datetime.datetime.now().isoformat()

        # 更新统计信息
        old_quality = old_data.get("quality", "待校准")
        new_quality = new_status.get("quality", old_quality)
        old_content_type = old_data.get("content_type", -1)
        new_content_type = new_status.get("content_type", old_content_type)

        self._update_stats_for_file_change(
            index_data, old_quality, new_quality, old_content_type, new_content_type
        )

        # 保存索引
        success = self.save_index(index_data)
        if success:
            LOGGER.debug(f"文件状态已更新: {filename}")

        return success

    def is_index_valid(self, index_data: Dict, current_filenames: List[str]) -> bool:
        """
        检查索引是否有效
        
        Args:
            index_data: 索引数据
            current_filenames: 当前文件列表
            
        Returns:
            索引是否有效
        """
        # 检查版本
        if index_data.get("version") != self.INDEX_VERSION:
            LOGGER.debug(f"索引版本不匹配: {index_data.get('version')} != {self.INDEX_VERSION}")
            return False
        
        # 检查目录路径
        if index_data.get("directory_path") != self.directory_path:
            LOGGER.debug("索引目录路径不匹配")
            return False
        
        # 检查文件列表是否匹配
        indexed_files = set(index_data.get("files", {}).keys())
        current_files = set(current_filenames)
        
        if indexed_files != current_files:
            LOGGER.debug("文件列表发生变化，需要重建索引")
            return False
        
        return True
    
    def migrate_from_v1(self, v1_data: Dict) -> Dict:
        """
        从v1.0格式迁移到v2.0格式
        
        Args:
            v1_data: v1.0格式的数据
            
        Returns:
            v2.0格式的数据结构（仅包含viewed_files信息）
        """
        viewed_files = set(v1_data.get("viewed_files", []))
        
        # 创建v2.0基础结构，files字段将在build_index中填充
        v2_data = {
            "version": self.INDEX_VERSION,
            "last_updated": datetime.datetime.now().isoformat(),
            "directory_path": self.directory_path,
            "files": {},
            "stats": {
                "total_files": 0,
                "content_type_counts": {
                    "文本": 0,
                    "有线表": 0,
                    "无线表": 0,
                    "待确认": 0
                },
                "quality_counts": {
                    "合格": 0,
                    "准合格": 0,
                    "不合格": 0,
                    "待校准": 0
                }
            },
            "migrated_viewed_files": list(viewed_files)  # 临时保存，用于build_index
        }
        
        LOGGER.info(f"从v1.0迁移到v2.0，保留 {len(viewed_files)} 个已浏览文件")
        return v2_data
    
    def create_empty_index(self) -> Dict:
        """
        创建空的索引结构
        
        Returns:
            空的v2.0索引结构
        """
        return {
            "version": self.INDEX_VERSION,
            "last_updated": datetime.datetime.now().isoformat(),
            "directory_path": self.directory_path,
            "files": {},
            "basename_map": {},  # 🚀 basename -> full_path 的O(1)映射表
            "stats": {
                "total_files": 0,
                "content_type_counts": {
                    "文本": 0,
                    "有线表": 0,
                    "无线表": 0,
                    "待确认": 0
                },
                "quality_counts": {
                    "合格": 0,
                    "准合格": 0,
                    "不合格": 0,
                    "待校准": 0
                }
            }
        }

    def build_index(self, filenames: List[str],
                   get_metadata_func,
                   viewed_files: set) -> Dict:
        """
        构建文件索引（智能选择单线程或多线程）

        Args:
            filenames: 文件名列表
            get_metadata_func: 获取文件元数据的统一函数
            viewed_files: 已浏览文件集合

        Returns:
            构建的索引数据
        """
        LOGGER.info(f"开始构建文件索引，共 {len(filenames)} 个文件")

        # 🚀 智能选择处理方式：文件数量超过100时使用多线程（降低阈值）
        if len(filenames) > 100:
            LOGGER.info("🚀 文件数量较多，使用多线程加速处理")
            return self._build_index_multithreaded(
                filenames, get_metadata_func, viewed_files
            )
        else:
            LOGGER.info("文件数量较少，使用单线程处理")
            return self._build_index_single_threaded(
                filenames, get_metadata_func, viewed_files
            )

    def _build_index_single_threaded(self, filenames: List[str],
                                   get_metadata_func,
                                   viewed_files: set) -> Dict:
        """单线程构建索引（包含子目录状态合并）"""
        files_data = {}
        basename_map = {}  # 🚀 basename -> full_path 映射表
        content_type_counts = {"文本": 0, "有线表": 0, "无线表": 0, "待确认": 0}
        quality_counts = {"合格": 0, "准合格": 0, "不合格": 0, "待校准": 0}
        content_type_map = {0: "文本", 1: "有线表", 2: "无线表", -1: "待确认"}

        for i, filename in enumerate(filenames):
            try:
                # 🚀 获取文件元数据（使用统一方法，减少I/O）
                metadata = get_metadata_func(filename)
                content_type = metadata['content_type']
                quality = metadata['quality']
                has_annotation = metadata['has_annotation']
                viewed = filename in viewed_files
                json_mtime = self._get_json_modification_time(filename, has_annotation)

                files_data[filename] = {
                    "content_type": content_type,
                    "quality": quality,
                    "has_annotation": has_annotation,
                    "viewed": viewed,
                    "json_last_modified": json_mtime
                }

                # 🚀 构建basename映射表
                basename = os.path.basename(filename)
                basename_map[basename] = filename

                # 更新统计
                content_type_name = content_type_map.get(content_type, "待确认")
                content_type_counts[content_type_name] += 1
                quality_counts[quality] += 1

                # 进度提示
                if (i + 1) % 100 == 0:
                    LOGGER.debug(f"已处理 {i + 1}/{len(filenames)} 个文件")

            except Exception as e:
                LOGGER.warning(f"处理文件 {filename} 失败: {e}")
                # 设置默认值
                files_data[filename] = {
                    "content_type": -1,
                    "quality": "待校准",
                    "has_annotation": False,
                    "viewed": filename in viewed_files,
                    "json_last_modified": None
                }
                # 🚀 构建basename映射表（异常情况）
                basename = os.path.basename(filename)
                basename_map[basename] = filename

                content_type_counts["待确认"] += 1
                quality_counts["待校准"] += 1

        index_data = {
            "version": self.INDEX_VERSION,
            "last_updated": datetime.datetime.now().isoformat(),
            "directory_path": self.directory_path,
            "files": files_data,
            "basename_map": basename_map,  # 🚀 添加basename映射表
            "stats": {
                "total_files": len(filenames),
                "content_type_counts": content_type_counts,
                "quality_counts": quality_counts
            }
        }

        LOGGER.info(f"单线程索引构建完成，包含 {len(files_data)} 个文件")
        return index_data

    def _get_json_modification_time(self, filename: str, has_annotation: bool) -> Optional[str]:
        """
        获取JSON文件的修改时间

        Args:
            filename: 图片文件名
            has_annotation: 是否有标注文件

        Returns:
            修改时间的ISO格式字符串，如果文件不存在返回None
        """
        if not has_annotation:
            return None

        # 构造JSON文件路径（这里需要与主应用的逻辑保持一致）
        base_name = os.path.splitext(filename)[0]
        json_path = f"{base_name}.json"

        if os.path.exists(json_path):
            mtime = os.path.getmtime(json_path)
            return datetime.datetime.fromtimestamp(mtime).isoformat()

        return None









    def _update_stats_for_file_change(self, index: Dict, old_quality: str, new_quality: str,
                                     old_content_type: int, new_content_type: int):
        """更新索引统计信息（文件状态变化时）"""
        if "stats" not in index:
            return

        stats = index["stats"]

        # 更新质量统计
        if "quality_counts" in stats and old_quality != new_quality:
            stats["quality_counts"][old_quality] = max(0, stats["quality_counts"].get(old_quality, 0) - 1)
            stats["quality_counts"][new_quality] = stats["quality_counts"].get(new_quality, 0) + 1

        # 更新内容类型统计
        if "content_type_counts" in stats and old_content_type != new_content_type:
            content_type_map = {0: "文本", 1: "有线表", 2: "无线表", -1: "待确认"}
            old_type_name = content_type_map.get(old_content_type, "待确认")
            new_type_name = content_type_map.get(new_content_type, "待确认")

            stats["content_type_counts"][old_type_name] = max(0, stats["content_type_counts"].get(old_type_name, 0) - 1)
            stats["content_type_counts"][new_type_name] = stats["content_type_counts"].get(new_type_name, 0) + 1







    def _build_index_multithreaded(self, filenames: List[str],
                                 get_metadata_func,
                                 viewed_files: set) -> Dict:
        """
        🚀 多线程构建索引，大幅提升大量文件处理性能

        Args:
            filenames: 文件名列表
            get_metadata_func: 获取文件元数据的统一函数
            viewed_files: 已浏览文件集合

        Returns:
            索引数据字典
        """
        import concurrent.futures
        import threading
        from collections import defaultdict

        LOGGER.info(f"🚀 使用多线程构建索引，共 {len(filenames)} 个文件")

        # 线程安全的数据结构
        files_data = {}
        basename_map = {}  # 🚀 basename -> full_path 映射表
        content_type_counts = defaultdict(int)
        quality_counts = defaultdict(int)
        content_type_map = {0: "文本", 1: "有线表", 2: "无线表", -1: "待确认"}

        # 线程锁
        data_lock = threading.Lock()

        # 计算线程数：IO密集型任务，线程数可以设置为CPU核心数的2倍
        import os
        max_workers = min(32, (os.cpu_count() or 1) * 2)  # 最多32个线程

        def process_file(filename: str) -> Dict:
            """处理单个文件的函数"""
            try:
                # 🚀 获取文件元数据（使用统一方法，减少I/O）
                metadata = get_metadata_func(filename)
                content_type = metadata['content_type']
                quality = metadata['quality']
                has_annotation = metadata['has_annotation']

                # 获取JSON修改时间
                json_mtime = self._get_json_modification_time(filename, has_annotation)

                return {
                    "filename": filename,
                    "success": True,
                    "data": {
                        "content_type": content_type,
                        "quality": quality,
                        "has_annotation": has_annotation,
                        "viewed": filename in viewed_files,
                        "json_last_modified": json_mtime
                    }
                }

            except Exception as e:
                LOGGER.warning(f"处理文件 {filename} 失败: {e}")
                return {
                    "filename": filename,
                    "success": False,
                    "error": str(e)
                }

        # 使用ThreadPoolExecutor进行并发处理
        processed_count = 0
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_filename = {
                executor.submit(process_file, filename): filename
                for filename in filenames
            }

            # 处理完成的任务
            for future in concurrent.futures.as_completed(future_to_filename):
                result = future.result()

                with data_lock:
                    if result["success"]:
                        # 成功处理的文件
                        filename = result["filename"]
                        file_data = result["data"]

                        files_data[filename] = file_data

                        # 🚀 构建basename映射表
                        basename = os.path.basename(filename)
                        basename_map[basename] = filename

                        # 更新统计
                        content_type_name = content_type_map.get(file_data["content_type"], "待确认")
                        content_type_counts[content_type_name] += 1
                        quality_counts[file_data["quality"]] += 1

                    else:
                        # 处理失败的文件，设置默认值
                        filename = result["filename"]
                        files_data[filename] = {
                            "content_type": -1,
                            "quality": "待校准",
                            "has_annotation": False,
                            "viewed": filename in viewed_files,
                            "json_last_modified": None
                        }
                        # 🚀 构建basename映射表（失败情况）
                        basename = os.path.basename(filename)
                        basename_map[basename] = filename

                        content_type_counts["待确认"] += 1
                        quality_counts["待校准"] += 1

                    processed_count += 1

                    # 进度提示
                    if processed_count % 100 == 0:
                        LOGGER.info(f"🚀 多线程处理进度: {processed_count}/{len(filenames)} ({processed_count/len(filenames)*100:.1f}%)")

        # 转换为普通字典
        final_content_type_counts = {"文本": 0, "有线表": 0, "无线表": 0, "待确认": 0}
        final_quality_counts = {"合格": 0, "准合格": 0, "不合格": 0, "待校准": 0}

        for key in final_content_type_counts:
            final_content_type_counts[key] = content_type_counts[key]
        for key in final_quality_counts:
            final_quality_counts[key] = quality_counts[key]

        index_data = {
            "version": self.INDEX_VERSION,
            "last_updated": datetime.datetime.now().isoformat(),
            "directory_path": self.directory_path,
            "files": files_data,
            "basename_map": basename_map,  # 🚀 添加basename映射表
            "stats": {
                "total_files": len(filenames),
                "content_type_counts": final_content_type_counts,
                "quality_counts": final_quality_counts
            }
        }

        LOGGER.info(f"🚀 多线程索引构建完成，包含 {len(files_data)} 个文件，使用了 {max_workers} 个线程")
        return index_data
