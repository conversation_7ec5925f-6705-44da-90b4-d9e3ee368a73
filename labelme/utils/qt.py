import os.path as osp
from math import sqrt

import numpy as np
from PyQt5 import QtCore
from PyQt5 import QtGui
from PyQt5 import QtWidgets

from labelme.app_tableme import LOGGER

here = osp.dirname(osp.abspath(__file__))


def newIcon(icon):
    """创建图标对象，支持文件系统路径、特殊图标名称和高DPI显示"""
    icons_dir = osp.join(here, "../icons")
    
    # 🔧 修复：处理特殊图标名称（以@开头的完整文件名）
    if icon.startswith("@"):
        # 去掉@前缀，直接使用文件名
        icon_path = osp.join(icons_dir, icon[1:])
    else:
        # 常规图标名称，添加.png扩展名
        icon_path = osp.join(icons_dir, f"{icon}.png")
    
    # 🔧 修复：使用文件系统路径而不是Qt资源系统路径
    if osp.exists(icon_path):
        # 🆕 优化：创建支持高DPI的图标
        icon_obj = QtGui.QIcon(icon_path)
        
        # 🆕 为不同尺寸添加图标（确保缩放清晰）
        pixmap = QtGui.QPixmap(icon_path)
        if not pixmap.isNull():
            # 添加常用尺寸的图标
            icon_obj.addPixmap(pixmap.scaled(16, 16, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))
            icon_obj.addPixmap(pixmap.scaled(24, 24, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))
            icon_obj.addPixmap(pixmap.scaled(32, 32, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))
            icon_obj.addPixmap(pixmap.scaled(48, 48, QtCore.Qt.KeepAspectRatio, QtCore.Qt.SmoothTransformation))
        
        return icon_obj
    else:
        # 如果图标文件不存在，记录警告并返回空图标
        LOGGER.debug(f"⚠️ 图标文件不存在: {icon_path}")
        return QtGui.QIcon()  # 返回空图标而不是崩溃


def newButton(text, icon=None, slot=None):
    b = QtWidgets.QPushButton(text)
    if icon is not None:
        b.setIcon(newIcon(icon))
    if slot is not None:
        b.clicked.connect(slot)
    return b


def newAction(
    parent,
    text,
    slot=None,
    shortcut=None,
    icon=None,
    tip=None,
    checkable=False,
    enabled=True,
    checked=False,
):
    """Create a new action and assign callbacks, shortcuts, etc."""
    a = QtWidgets.QAction(text, parent)
    if icon is not None:
        a.setIconText(text.replace(" ", "\n"))
        a.setIcon(newIcon(icon))
    if shortcut is not None:
        if isinstance(shortcut, (list, tuple)):
            a.setShortcuts(shortcut)
        else:
            a.setShortcut(shortcut)
    if tip is not None:
        a.setToolTip(tip)
        a.setStatusTip(tip)
    if slot is not None:
        a.triggered.connect(slot)
    if checkable:
        a.setCheckable(True)
    a.setEnabled(enabled)
    a.setChecked(checked)
    return a


def addActions(widget, actions):
    for action in actions:
        if action is None:
            widget.addSeparator()
        elif isinstance(action, QtWidgets.QMenu):
            widget.addMenu(action)
        else:
            widget.addAction(action)


def labelValidator():
    return QtGui.QRegExpValidator(QtCore.QRegExp(r"^[^ \t].+"), None)


class struct(object):
    def __init__(self, **kwargs):
        self.__dict__.update(kwargs)


def distance(p):
    return sqrt(p.x() * p.x() + p.y() * p.y())


def distancetoline(point, line):
    p1, p2 = line
    p1 = np.array([p1.x(), p1.y()])
    p2 = np.array([p2.x(), p2.y()])
    p3 = np.array([point.x(), point.y()])
    if np.dot((p3 - p1), (p2 - p1)) < 0:
        return np.linalg.norm(p3 - p1)
    if np.dot((p3 - p2), (p1 - p2)) < 0:
        return np.linalg.norm(p3 - p2)
    if np.linalg.norm(p2 - p1) == 0:
        return np.linalg.norm(p3 - p1)
    return np.linalg.norm(np.cross(p2 - p1, p1 - p3)) / np.linalg.norm(p2 - p1)


def fmtShortcut(text):
    mod, key = text.split("+", 1)
    return "<b>%s</b>+<b>%s</b>" % (mod, key)
