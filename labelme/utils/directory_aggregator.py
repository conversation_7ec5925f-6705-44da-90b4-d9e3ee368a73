"""
目录聚合器
负责聚合多个子目录的索引文件，为父目录提供统一的文件视图
"""

import os
import json
import time
import datetime
from typing import Dict, List, Optional

# 使用标准logging而不是loguru，避免依赖问题
import logging
LOGGER = logging.getLogger(__name__)

from .file_index_manager import FileIndexManager


class AggregationCache:
    """聚合缓存管理器"""
    
    def __init__(self, parent_path: str, ttl_seconds: int = 300):
        """
        初始化聚合缓存
        
        Args:
            parent_path: 父目录路径
            ttl_seconds: 缓存TTL时间（秒），默认5分钟
        """
        self.parent_path = parent_path
        self.cache_file = os.path.join(parent_path, ".aggregated_cache.json")
        self.ttl_seconds = ttl_seconds

    def is_valid(self) -> bool:
        """
        检查缓存是否有效

        Returns:
            bool: 缓存是否有效
        """
        LOGGER.info(f"🔍 检查缓存文件存在性: {self.cache_file}")
        if not os.path.exists(self.cache_file):
            LOGGER.info("❌ 缓存文件不存在")
            return False

        cache_mtime = os.path.getmtime(self.cache_file)
        current_time = time.time()
        age_seconds = current_time - cache_mtime

        LOGGER.info(f"🔍 缓存文件年龄: {age_seconds:.1f}秒, TTL: {self.ttl_seconds}秒")

        # 检查TTL
        if age_seconds > self.ttl_seconds:
            LOGGER.info(f"❌ 缓存已过期（TTL）: {age_seconds:.1f}s > {self.ttl_seconds}s")
            return False

        # 检查子目录索引是否有更新
        LOGGER.info("🔍 检查子目录索引文件更新时间...")
        index_files_checked = 0
        for root, dirs, files in os.walk(self.parent_path):
            index_file = os.path.join(root, "viewed_files_state.json")
            if os.path.exists(index_file):
                index_files_checked += 1
                index_mtime = os.path.getmtime(index_file)
                LOGGER.info(f"🔍 检查索引文件: {index_file}, 修改时间: {index_mtime}, 缓存时间: {cache_mtime}")
                if index_mtime > cache_mtime:
                    LOGGER.info(f"❌ 子目录索引有更新: {root}")
                    return False

        LOGGER.info(f"✅ 缓存有效，检查了 {index_files_checked} 个索引文件")
        return True

    def save(self, data: dict) -> None:
        """
        保存缓存数据
        
        Args:
            data: 要缓存的数据
        """
        try:
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            LOGGER.debug(f"聚合缓存已保存: {self.cache_file}")
        except Exception as e:
            LOGGER.warning(f"保存聚合缓存失败: {e}")

    def load(self) -> dict:
        """
        加载缓存数据
        
        Returns:
            dict: 缓存的数据，失败返回空字典
        """
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            LOGGER.debug(f"聚合缓存已加载: {self.cache_file}")
            return data
        except Exception as e:
            LOGGER.warning(f"加载聚合缓存失败: {e}")
            return {}

    def invalidate(self) -> None:
        """使缓存失效"""
        try:
            if os.path.exists(self.cache_file):
                os.remove(self.cache_file)
                LOGGER.debug(f"聚合缓存已删除: {self.cache_file}")
        except Exception as e:
            LOGGER.warning(f"删除聚合缓存失败: {e}")


class DirectoryAggregator:
    """目录聚合器"""

    def __init__(self, parent_path: str):
        """
        初始化目录聚合器

        Args:
            parent_path: 父目录路径
        """
        self.parent_path = parent_path
        self.index_manager = FileIndexManager(parent_path)
        self.cache = AggregationCache(parent_path)

    def aggregate_subdirectories(self) -> dict:
        """
        聚合所有子目录索引

        Returns:
            dict: 聚合后的索引数据
        """
        # 🔧 修复：恢复正常的缓存检查逻辑
        LOGGER.debug("检查聚合缓存有效性")
        cache_valid = self.cache.is_valid()
        LOGGER.debug(f"缓存有效性结果: {cache_valid}")

        if cache_valid:
            cached_data = self.cache.load()
            if cached_data:
                LOGGER.debug(f"使用聚合缓存，包含 {len(cached_data.get('files', {}))} 个文件")
                return cached_data
            else:
                LOGGER.warning("缓存文件存在但加载失败")

        # 扫描子目录
        subdirs = self._scan_subdirectories()
        LOGGER.debug(f"扫描到 {len(subdirs)} 个包含图片的子目录")

        if not subdirs:
            LOGGER.debug("未找到包含图片的子目录")
            return self._create_empty_aggregated_index()

        LOGGER.debug(f"开始聚合 {len(subdirs)} 个子目录的索引")
        indices = []

        # 加载所有子目录索引
        for i, subdir in enumerate(subdirs):
            print(f"🔍 处理子目录 {i+1}/{len(subdirs)}: {subdir}")
            LOGGER.error(f"🔍 处理子目录 {i+1}/{len(subdirs)}: {subdir}")

            # 标准化子目录路径
            normalized_subdir = os.path.normpath(subdir)
            index_file_path = os.path.join(normalized_subdir, "viewed_files_state.json")

            print(f"🔍 索引文件路径: {index_file_path}")
            LOGGER.error(f"🔍 索引文件路径: {index_file_path}")

            print(f"🔍 检查索引文件是否存在...")
            LOGGER.error(f"🔍 检查索引文件是否存在...")

            if os.path.exists(index_file_path):
                print(f"✅ 找到现有索引文件: {index_file_path}")
                LOGGER.error(f"✅ 找到现有索引文件: {index_file_path}")

                print(f"🔍 开始加载索引数据...")
                LOGGER.error(f"🔍 开始加载索引数据...")
                index_data = self.index_manager.load_index_from_directory(normalized_subdir)

                if index_data:
                    print(f"✅ 索引数据加载成功")
                    LOGGER.error(f"✅ 索引数据加载成功")

                    # 🔧 修复：检查版本并处理迁移
                    version = index_data.get("version", "1.0")
                    if version == "1.0":
                        print(f"🔄 检测到v1.0索引，开始迁移: {normalized_subdir}")
                        LOGGER.error(f"🔄 检测到v1.0索引，开始迁移: {normalized_subdir}")

                        # 保存v1的viewed_files信息
                        viewed_files_from_v1 = set(index_data.get("viewed_files", []))

                        # 获取子目录中的图片文件列表用于重建索引
                        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
                        subdir_files = []
                        for file in os.listdir(normalized_subdir):
                            if os.path.splitext(file)[1].lower() in image_extensions:
                                file_path = os.path.join(normalized_subdir, file)
                                if os.path.isfile(file_path):
                                    subdir_files.append(os.path.normpath(file_path))

                        if subdir_files:
                            # 创建子目录管理器并重建v2.0索引
                            subdir_manager = FileIndexManager(normalized_subdir)

                            # 重新构建v2.0索引
                            new_index_data = subdir_manager.build_index(
                                filenames=subdir_files,
                                get_metadata_func=self._extract_file_metadata,
                                viewed_files=viewed_files_from_v1
                            )

                            # 保存新索引
                            subdir_manager.save_index(new_index_data)
                            print(f"✅ v1.0索引已迁移到v2.0: {normalized_subdir}")
                            LOGGER.error(f"✅ v1.0索引已迁移到v2.0: {normalized_subdir}")
                            index_data = new_index_data

                    # 添加源目录信息
                    index_data['_source_directory'] = normalized_subdir
                    indices.append(index_data)

                    # 🔍 调试：检查加载的索引数据
                    files_data = index_data.get('files', {})
                    sample_files = list(files_data.items())[:3]  # 取前3个文件作为样本
                    print(f"✅ 已加载子目录索引: {normalized_subdir}, 包含 {len(files_data)} 个文件")
                    LOGGER.error(f"✅ 已加载子目录索引: {normalized_subdir}, 包含 {len(files_data)} 个文件")

                    for filename, file_data in sample_files:
                        print(f"📄 样本文件 {os.path.basename(filename)}: content_type={file_data.get('content_type')}, quality={file_data.get('quality')}, has_annotation={file_data.get('has_annotation')}")
                        LOGGER.error(f"📄 样本文件 {os.path.basename(filename)}: content_type={file_data.get('content_type')}, quality={file_data.get('quality')}, has_annotation={file_data.get('has_annotation')}")
                else:
                    print(f"❌ 索引文件存在但加载失败: {normalized_subdir}")
                    LOGGER.error(f"❌ 索引文件存在但加载失败: {normalized_subdir}")
            else:
                # 索引文件不存在，主动创建
                print(f"❌ 索引文件不存在，为子目录创建索引: {normalized_subdir}")
                LOGGER.error(f"❌ 索引文件不存在，为子目录创建索引: {normalized_subdir}")
                try:
                    # 扫描子目录中的图片文件
                    subdir_manager = FileIndexManager(normalized_subdir)

                    # 获取子目录中的图片文件列表
                    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
                    subdir_files = []

                    for file in os.listdir(normalized_subdir):
                        if os.path.splitext(file)[1].lower() in image_extensions:
                            file_path = os.path.join(normalized_subdir, file)
                            if os.path.isfile(file_path):
                                # 使用绝对路径，与现有逻辑保持一致
                                subdir_files.append(os.path.normpath(file_path))

                    if subdir_files:
                        # 🔧 修复：使用与正常打开子目录相同的元数据获取逻辑
                        def get_real_metadata(filename):
                            """从JSON标注文件中读取真实的状态信息（与app_tableme._get_file_metadata_from_json保持一致）"""
                            # 默认值
                            result = {
                                'content_type': -1,
                                'quality': '待校准',
                                'has_annotation': False,
                                'json_path': None
                            }

                            try:
                                # 🚀 修复：使用与app_tableme._get_annotation_json_path相同的逻辑
                                base_name = os.path.splitext(os.path.basename(filename))[0]

                                # 优先使用_table_annotation格式
                                table_annotation_json = os.path.join(normalized_subdir, f"{base_name}_table_annotation.json")
                                same_name_json = os.path.join(normalized_subdir, f"{base_name}.json")

                                json_path = None
                                if os.path.exists(table_annotation_json):
                                    json_path = table_annotation_json
                                    LOGGER.debug(f"✅ 找到_table_annotation格式JSON: {json_path}")
                                elif os.path.exists(same_name_json):
                                    json_path = same_name_json
                                    LOGGER.debug(f"✅ 找到同名JSON: {json_path}")
                                else:
                                    LOGGER.debug(f"❌ 未找到JSON文件: {table_annotation_json} 或 {same_name_json}")
                                    return result

                                result['has_annotation'] = True
                                result['json_path'] = json_path

                                # 读取JSON文件
                                import json
                                with open(json_path, 'r', encoding='utf-8') as f:
                                    data = json.load(f)

                                LOGGER.debug(f"📄 JSON数据类型: {type(data)}, 键: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")

                                # 提取质量状态（与app_tableme逻辑一致）
                                if isinstance(data, dict) and 'quality' in data:
                                    result['quality'] = data['quality']
                                    LOGGER.debug(f"🎯 提取到质量状态: {result['quality']}")
                                else:
                                    LOGGER.debug(f"⚠️ 未找到quality字段，使用默认值: {result['quality']}")

                                # 提取内容类型（与app_tableme逻辑一致）
                                if isinstance(data, dict):
                                    # 优先检查 type 字段（主要格式）
                                    if 'type' in data:
                                        result['content_type'] = data['type']
                                        LOGGER.debug(f"🎯 从type字段提取内容类型: {result['content_type']}")
                                    # 备用检查 table_type 字段
                                    elif 'table_type' in data:
                                        result['content_type'] = data['table_type']
                                        LOGGER.debug(f"🎯 从table_type字段提取内容类型: {result['content_type']}")
                                    elif 'tables' in data and data['tables']:
                                        # 多表格格式，取第一个表格的类型
                                        first_table = data['tables'][0]
                                        if 'type' in first_table:
                                            result['content_type'] = first_table['type']
                                            LOGGER.debug(f"🎯 从tables[0].type字段提取内容类型: {result['content_type']}")
                                        else:
                                            result['content_type'] = first_table.get('table_type', -1)
                                            LOGGER.debug(f"🎯 从tables[0].table_type字段提取内容类型: {result['content_type']}")
                                    else:
                                        LOGGER.debug(f"⚠️ 未找到内容类型字段，使用默认值: {result['content_type']}")

                                LOGGER.info(f"✅ 成功提取元数据 {os.path.basename(filename)}: content_type={result['content_type']}, quality={result['quality']}, has_annotation={result['has_annotation']}")
                                return result

                            except Exception as e:
                                LOGGER.error(f"❌ 获取文件元数据失败: {filename}, 错误: {e}")
                                import traceback
                                traceback.print_exc()
                                return result  # 返回默认值

                        # 构建索引
                        LOGGER.info(f"🔨 开始为子目录构建索引: {normalized_subdir}, 包含 {len(subdir_files)} 个文件")
                        index_data = subdir_manager.build_index(
                            filenames=subdir_files,
                            get_metadata_func=get_real_metadata,
                            viewed_files=set()
                        )

                        # 🔍 调试：检查新创建的索引数据
                        files_data = index_data.get('files', {})
                        sample_files = list(files_data.items())[:3]  # 取前3个文件作为样本
                        LOGGER.info(f"🔨 索引构建完成，包含 {len(files_data)} 个文件")
                        for filename, file_data in sample_files:
                            LOGGER.info(f"📄 新建索引样本 {os.path.basename(filename)}: content_type={file_data.get('content_type')}, quality={file_data.get('quality')}, has_annotation={file_data.get('has_annotation')}")

                        # 保存索引
                        subdir_manager.save_index(index_data)
                        LOGGER.info(f"💾 索引已保存到: {os.path.join(normalized_subdir, 'viewed_files_state.json')}")

                        # 添加到聚合列表
                        index_data['_source_directory'] = normalized_subdir
                        indices.append(index_data)
                        LOGGER.info(f"✅ 已为子目录创建索引: {normalized_subdir}, 包含 {len(index_data.get('files', {}))} 个文件")
                    else:
                        LOGGER.info(f"子目录中没有图片文件: {normalized_subdir}")

                except Exception as e:
                    LOGGER.error(f"为子目录创建索引失败: {normalized_subdir}, 错误: {e}")

        LOGGER.info(f"✅ 成功加载 {len(indices)} 个子目录索引")

        # 合并索引
        LOGGER.info(f"🔄 开始合并 {len(indices)} 个子目录索引")
        aggregated = self._merge_indices(indices)

        # 🔍 调试：检查聚合结果
        aggregated_files = aggregated.get('files', {})
        sample_files = list(aggregated_files.items())[:3]  # 取前3个文件作为样本
        LOGGER.info(f"🔄 聚合完成，包含 {len(aggregated_files)} 个文件")
        for filename, file_data in sample_files:
            LOGGER.info(f"📄 聚合样本 {filename}: content_type={file_data.get('content_type')}, quality={file_data.get('quality')}, has_annotation={file_data.get('has_annotation')}")

        # 缓存结果
        self.cache.save(aggregated)
        LOGGER.info(f"💾 聚合结果已缓存")
        return aggregated

    def update_file_status(self, filename: str, new_status: dict) -> bool:
        """
        🔧 优化：更新文件状态（智能缓存管理）

        Args:
            filename: 文件名（相对于父目录的路径）
            new_status: 新的状态数据

        Returns:
            bool: 更新是否成功
        """
        # 定位源目录
        source_dir = self._locate_source_directory(filename)
        if not source_dir:
            LOGGER.warning(f"无法定位文件的源目录: {filename}")
            return False

        # 获取文件的绝对路径（子目录索引中存储的是绝对路径）
        try:
            full_path = os.path.join(self.parent_path, filename)
            actual_filename = os.path.normpath(full_path)
        except Exception as e:
            LOGGER.warning(f"无法构建绝对路径: {filename}, 错误: {e}")
            return False

        # 更新源索引
        source_manager = FileIndexManager(source_dir)
        success = source_manager.update_file_in_index(actual_filename, new_status)

        if success:
            # 🔧 优化：标记缓存需要更新，但不立即失效
            # 让上层应用决定何时重新聚合，避免频繁的重新聚合
            self._mark_cache_dirty()
            LOGGER.debug(f"文件状态已更新，缓存标记为需要更新: {filename}")

        return success

    def _mark_cache_dirty(self):
        """标记缓存为脏数据，需要更新"""
        self._cache_dirty = True

    def is_cache_dirty(self) -> bool:
        """检查缓存是否为脏数据"""
        return getattr(self, '_cache_dirty', False)

    def force_refresh_cache(self):
        """强制刷新缓存（在需要最新数据时调用）"""
        self.cache.invalidate()
        self._cache_dirty = False

    def invalidate_cache(self) -> None:
        """使缓存失效"""
        self.cache.invalidate()

    def _scan_subdirectories(self) -> List[str]:
        """
        🔧 修复：递归扫描所有包含图片文件的最底层目录

        Returns:
            List[str]: 包含图片文件的最底层目录路径列表
        """
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        subdirs_with_images = []

        def scan_directory_recursive(dir_path):
            """递归扫描目录，找到所有直接包含图片的目录"""
            try:
                items = os.listdir(dir_path)

                # 检查当前目录是否直接包含图片文件
                has_direct_images = any(
                    os.path.splitext(f)[1].lower() in image_extensions
                    for f in items
                    if os.path.isfile(os.path.join(dir_path, f))
                )

                if has_direct_images:
                    # 当前目录直接包含图片，添加到结果中
                    normalized_path = os.path.normpath(dir_path)
                    subdirs_with_images.append(normalized_path)
                    LOGGER.debug(f"发现包含图片的目录: {normalized_path}")
                else:
                    # 当前目录不包含图片，递归检查子目录
                    for item in items:
                        item_path = os.path.join(dir_path, item)
                        if os.path.isdir(item_path):
                            scan_directory_recursive(item_path)

            except (OSError, PermissionError) as e:
                LOGGER.warning(f"无法访问目录 {dir_path}: {e}")

        try:
            scan_directory_recursive(self.parent_path)
        except Exception as e:
            LOGGER.error(f"扫描父目录失败 {self.parent_path}: {e}")

        LOGGER.info(f"扫描到 {len(subdirs_with_images)} 个包含图片的最底层目录")
        return subdirs_with_images

    def _merge_indices(self, indices: List[dict]) -> dict:
        """
        合并多个索引

        Args:
            indices: 索引列表

        Returns:
            dict: 合并后的索引
        """
        merged = {
            "version": "2.0",
            "files": {},
            "basename_map": {},  # 🔧 添加basename映射表以支持快速查找
            "stats": {
                "total_files": 0,
                "content_type_counts": {"文本": 0, "有线表": 0, "无线表": 0, "待确认": 0},
                "quality_counts": {"合格": 0, "准合格": 0, "不合格": 0, "待校准": 0}
            },
            "source_directories": [],
            "last_aggregated": datetime.datetime.now().isoformat()
        }

        for index in indices:
            source_dir = index.get('_source_directory', '')
            merged["source_directories"].append(source_dir)

            # 合并文件数据
            for filename, file_data in index.get("files", {}).items():
                # 转换为相对于父目录的路径
                try:
                    # 如果filename已经是绝对路径，直接使用；否则与source_dir拼接
                    if os.path.isabs(filename):
                        full_path = filename
                    else:
                        full_path = os.path.join(source_dir, filename)

                    relative_path = os.path.relpath(full_path, self.parent_path)
                    relative_path = os.path.normpath(relative_path)
                except ValueError:
                    # 无法计算相对路径，跳过
                    continue

                # 添加源索引信息
                file_data_copy = file_data.copy()
                file_data_copy["_source_index"] = os.path.join(
                    source_dir, "viewed_files_state.json"
                )

                merged["files"][relative_path] = file_data_copy

                # 🔧 构建basename映射表，支持快速查找
                basename = os.path.basename(relative_path)
                merged["basename_map"][basename] = relative_path

                # 更新统计
                quality = file_data.get("quality", "待校准")
                content_type = file_data.get("content_type", -1)

                merged["stats"]["quality_counts"][quality] += 1

                content_type_map = {0: "文本", 1: "有线表", 2: "无线表", -1: "待确认"}
                type_name = content_type_map.get(content_type, "待确认")
                merged["stats"]["content_type_counts"][type_name] += 1

                merged["stats"]["total_files"] += 1

        LOGGER.info(f"聚合索引构建完成: {merged['stats']['total_files']} 个文件, basename_map包含 {len(merged['basename_map'])} 个映射")
        return merged

    def _locate_source_directory(self, filename: str) -> Optional[str]:
        """
        根据文件名定位源目录
        
        Args:
            filename: 文件名（相对于父目录的路径）
            
        Returns:
            Optional[str]: 源目录路径，未找到返回None
        """
        full_path = os.path.join(self.parent_path, filename)
        return os.path.dirname(full_path)

    def _extract_file_metadata(self, filename):
        """从JSON标注文件中读取真实的状态信息（与app_tableme._get_file_metadata_from_json保持一致）"""
        # 默认值
        result = {
            'content_type': -1,
            'quality': '待校准',
            'has_annotation': False,
            'json_path': None
        }

        try:
            # 🚀 修复：使用与app_tableme._get_annotation_json_path相同的逻辑
            base_name = os.path.splitext(os.path.basename(filename))[0]
            dir_path = os.path.dirname(filename)

            # 优先使用_table_annotation格式
            table_annotation_json = os.path.join(dir_path, f"{base_name}_table_annotation.json")
            same_name_json = os.path.join(dir_path, f"{base_name}.json")

            json_path = None
            if os.path.exists(table_annotation_json):
                json_path = table_annotation_json
                LOGGER.debug(f"✅ 找到_table_annotation格式JSON: {json_path}")
            elif os.path.exists(same_name_json):
                json_path = same_name_json
                LOGGER.debug(f"✅ 找到同名JSON: {json_path}")
            else:
                LOGGER.debug(f"❌ 未找到JSON文件: {table_annotation_json} 或 {same_name_json}")
                return result

            result['has_annotation'] = True
            result['json_path'] = json_path

            # 读取JSON文件
            import json
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            LOGGER.debug(f"📄 JSON数据类型: {type(data)}, 键: {list(data.keys()) if isinstance(data, dict) else 'N/A'}")

            # 提取质量状态（与app_tableme逻辑一致）
            if isinstance(data, dict) and 'quality' in data:
                result['quality'] = data['quality']
                LOGGER.debug(f"🎯 提取到质量状态: {result['quality']}")
            else:
                LOGGER.debug(f"⚠️ 未找到quality字段，使用默认值: {result['quality']}")

            # 提取内容类型（与app_tableme逻辑一致）
            if isinstance(data, dict):
                # 优先检查 type 字段（主要格式）
                if 'type' in data:
                    result['content_type'] = data['type']
                    LOGGER.debug(f"🎯 从type字段提取内容类型: {result['content_type']}")
                # 备用检查 table_type 字段
                elif 'table_type' in data:
                    result['content_type'] = data['table_type']
                    LOGGER.debug(f"🎯 从table_type字段提取内容类型: {result['content_type']}")
                elif 'tables' in data and data['tables']:
                    # 多表格格式，取第一个表格的类型
                    first_table = data['tables'][0]
                    if 'type' in first_table:
                        result['content_type'] = first_table['type']
                        LOGGER.debug(f"🎯 从tables[0].type字段提取内容类型: {result['content_type']}")
                    else:
                        result['content_type'] = first_table.get('table_type', -1)
                        LOGGER.debug(f"🎯 从tables[0].table_type字段提取内容类型: {result['content_type']}")
                else:
                    LOGGER.debug(f"⚠️ 未找到内容类型字段，使用默认值: {result['content_type']}")

            LOGGER.info(f"✅ 成功提取元数据 {os.path.basename(filename)}: content_type={result['content_type']}, quality={result['quality']}, has_annotation={result['has_annotation']}")
            return result

        except Exception as e:
            LOGGER.error(f"❌ 获取文件元数据失败: {filename}, 错误: {e}")
            import traceback
            traceback.print_exc()
            return result  # 返回默认值

    def _create_empty_aggregated_index(self) -> dict:
        """创建空的聚合索引"""
        return {
            "version": "2.0",
            "files": {},
            "stats": {
                "total_files": 0,
                "content_type_counts": {"文本": 0, "有线表": 0, "无线表": 0, "待确认": 0},
                "quality_counts": {"合格": 0, "准合格": 0, "不合格": 0, "待校准": 0}
            },
            "source_directories": [],
            "last_aggregated": datetime.datetime.now().isoformat()
        }
