#!/usr/bin/env python3
"""
表格标注优化器 - 修复版本

主要功能:
1. 智能角点对齐，减少表格线分叉
2. 自适应阈值，基于图像和表格特征
3. 保留所有原始属性不变
4. 高效批量处理

优化策略：
- 简化算法逻辑，专注核心问题
- 智能阈值计算，避免过度复杂
- 渐进式优化，确保稳定性

作者: AI Assistant
版本: 4.1 (修复版)
更新日期: 2025-01-08
"""

import os
import json
import time
import math
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse
from typing import List, Dict, Tuple, Optional, Any
from PIL import Image


class SimplifiedTableOptimizer:
    """简化高效的表格标注优化器"""

    def __init__(self, tolerance: float = 100.0, adaptive_threshold: bool = False,
                 merge_threshold_factor: float = 50.0, alignment_strength: float = 1.0,
                 preserve_perspective: bool = False):
        """
        初始化优化器

        Args:
            tolerance: 基础容差阈值（像素）
            adaptive_threshold: 是否使用自适应阈值
            merge_threshold_factor: 合并阈值因子
            alignment_strength: 对齐强度 (0.0-1.0)
            preserve_perspective: 是否保持透视变换
        """
        self.base_tolerance = tolerance
        self.adaptive_threshold = adaptive_threshold
        self.merge_threshold_factor = merge_threshold_factor
        self.alignment_strength = alignment_strength
        self.preserve_perspective = preserve_perspective
        
        # 运行时变量
        self.tolerance = tolerance
        self.image_info = None
        self.cells = []
        self.table_ind = ""
        self.image_path = ""
        self.original_data = None

    def get_image_info(self, image_path: str) -> Optional[Dict[str, int]]:
        """
        获取图片信息

        Args:
            image_path: 图片文件路径

        Returns:
            包含图片宽度和高度的字典，失败时返回None
        """
        try:
            if image_path and Path(image_path).exists():
                with Image.open(image_path) as img:
                    return {'width': img.size[0], 'height': img.size[1]}
        except Exception as e:
            print(f"无法读取图片 {image_path}: {e}")
        return None

    def calculate_smart_threshold(self, img_width: int = None, img_height: int = None,
                                table_width: float = None, table_height: float = None) -> float:
        """
        智能阈值计算，引入表格面积占比规则

        Args:
            img_width: 图片宽度
            img_height: 图片高度
            table_width: 表格宽度
            table_height: 表格高度

        Returns:
            计算得到的阈值
        """
        # 如果禁用自适应阈值，直接返回基础阈值
        if not self.adaptive_threshold:
            return self.base_tolerance

        # 如果没有图片或表格尺寸信息，使用基础阈值
        if not all([img_width, img_height, table_width, table_height]):
            return self.base_tolerance

        # 计算表格面积占图片的比例
        table_area = table_width * table_height
        image_area = img_width * img_height
        area_ratio = table_area / image_area

        # 基于表格面积占比调整阈值
        if area_ratio > 0.8:
            # 大表格（占80%以上）：需要更精确的阈值
            area_factor = 0.7
        elif area_ratio > 0.6:
            # 较大表格（占60-80%）：标准阈值
            area_factor = 0.85
        elif area_ratio > 0.4:
            # 中等表格（占40-60%）：标准阈值
            area_factor = 1.0
        elif area_ratio > 0.2:
            # 较小表格（占20-40%）：稍微宽松的阈值
            area_factor = 1.2
        else:
            # 小表格（占20%以下）：更宽松的阈值
            area_factor = 1.5

        # 计算最终阈值
        adaptive_threshold = self.base_tolerance * area_factor

        # 限制阈值范围在合理区间
        adaptive_threshold = max(1.0, min(8.0, adaptive_threshold))

        return adaptive_threshold

    def calculate_table_bounds(self, cells: List[Dict]) -> Tuple[float, float]:
        """
        计算表格边界尺寸

        Args:
            cells: 单元格列表

        Returns:
            表格宽度和高度的元组
        """
        if not cells:
            return 0.0, 0.0

        all_x = []
        all_y = []

        for cell in cells:
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                if point_name in bbox:
                    point = bbox[point_name]
                    all_x.append(point[0])
                    all_y.append(point[1])

        if not all_x or not all_y:
            return 0.0, 0.0

        width = max(all_x) - min(all_x)
        height = max(all_y) - min(all_y)

        return width, height

    def load_annotation(self, json_path: str, image_path: Optional[str] = None):
        """
        加载表格标注文件

        Args:
            json_path: 标注文件路径
            image_path: 对应的图片文件路径（用于自适应阈值计算）
        """
        with open(json_path, 'r', encoding='utf-8') as f:
            data = json.load(f)

        # 保存原始数据以便保护所有属性
        self.original_data = data.copy()

        self.cells = data['cells']
        self.table_ind = data.get('table_ind', '')
        self.image_path = data.get('image_path', '')

        # 获取图片信息用于智能阈值计算
        if image_path:
            self.image_info = self.get_image_info(image_path)

            if self.image_info:
                table_width, table_height = self.calculate_table_bounds(self.cells)

                # 使用智能阈值计算（包含面积占比规则）
                self.tolerance = self.calculate_smart_threshold(
                    self.image_info['width'],
                    self.image_info['height'],
                    table_width,
                    table_height
                )

                # 显示详细信息
                if self.adaptive_threshold:
                    print(f"智能阈值计算结果: {self.tolerance:.2f}px（基于面积占比优化）")
                else:
                    print(f"固定阈值: {self.tolerance:.2f}px")

                print(f"  图片尺寸: {self.image_info['width']}x{self.image_info['height']}")
                print(f"  表格尺寸: {table_width:.1f}x{table_height:.1f}")

                if table_width > 0 and table_height > 0:
                    area_ratio = (table_width * table_height) / (self.image_info['width'] * self.image_info['height'])
                    print(f"  表格占比: {area_ratio:.1%}")

                    if self.adaptive_threshold:
                        # 显示面积占比对阈值的影响
                        if area_ratio > 0.8:
                            print(f"  占比分析: 大表格，使用精确阈值")
                        elif area_ratio > 0.6:
                            print(f"  占比分析: 较大表格，使用标准阈值")
                        elif area_ratio > 0.4:
                            print(f"  占比分析: 中等表格，使用标准阈值")
                        elif area_ratio > 0.2:
                            print(f"  占比分析: 较小表格，使用宽松阈值")
                        else:
                            print(f"  占比分析: 小表格，使用更宽松阈值")
            else:
                print(f"无法获取图片信息，使用基础阈值: {self.base_tolerance:.2f}px")
                self.tolerance = self.base_tolerance
        else:
            # 没有图片信息时使用基础阈值
            self.tolerance = self.base_tolerance
            print(f"使用基础阈值: {self.tolerance:.2f}px")

        print(f"加载了 {len(self.cells)} 个单元格")

    def find_nearby_points(self) -> List[List[Dict]]:
        """
        查找相近的角点组

        Returns:
            相近角点组列表
        """
        all_points = []

        # 收集所有角点
        for cell_idx, cell in enumerate(self.cells):
            bbox = cell['bbox']
            for point_name in ['p1', 'p2', 'p3', 'p4']:
                if point_name in bbox:
                    point = bbox[point_name]
                    all_points.append({
                        'coords': point,
                        'cell_idx': cell_idx,
                        'point_name': point_name,
                        'x': point[0],
                        'y': point[1]
                    })

        # 使用合并阈值查找相近点组
        merge_threshold = self.tolerance * self.merge_threshold_factor
        point_groups = []
        used_indices = set()

        for i, point1 in enumerate(all_points):
            if i in used_indices:
                continue

            group = [point1]
            used_indices.add(i)

            for j, point2 in enumerate(all_points):
                if j in used_indices:
                    continue

                # 计算欧几里得距离
                distance = math.sqrt(
                    (point1['x'] - point2['x'])**2 +
                    (point1['y'] - point2['y'])**2
                )

                if distance <= merge_threshold:
                    group.append(point2)
                    used_indices.add(j)

            if len(group) > 1:
                point_groups.append(group)

        return point_groups

    def align_nearby_points(self):
        """
        对齐相近的角点
        """
        point_groups = self.find_nearby_points()
        
        if not point_groups:
            print("  未发现需要对齐的相近角点")
            return

        print(f"  发现 {len(point_groups)} 组相近角点")
        
        aligned_count = 0
        for group in point_groups:
            if len(group) < 2:
                continue

            # 计算组的加权中心
            center_x = sum(p['x'] for p in group) / len(group)
            center_y = sum(p['y'] for p in group) / len(group)

            # 应用对齐强度
            for point in group:
                original_x = point['x']
                original_y = point['y']
                
                # 计算新坐标（应用对齐强度）
                new_x = original_x + (center_x - original_x) * self.alignment_strength
                new_y = original_y + (center_y - original_y) * self.alignment_strength
                
                # 更新坐标
                self.cells[point['cell_idx']]['bbox'][point['point_name']] = [new_x, new_y]
                aligned_count += 1

        if aligned_count > 0:
            print(f"  对齐了 {aligned_count} 个角点")

    def optimize_table_annotation(self, input_file: str, output_file: str, 
                                image_file: Optional[str] = None) -> Dict[str, Any]:
        """
        优化表格标注

        Args:
            input_file: 输入标注文件路径
            output_file: 输出标注文件路径
            image_file: 对应的图片文件路径

        Returns:
            处理结果字典
        """
        try:
            print(f"开始优化: {Path(input_file).name}")
            
            # 加载标注数据
            self.load_annotation(input_file, image_file)
            
            # 执行优化
            print("执行角点对齐优化...")
            self.align_nearby_points()
            
            # 保存结果
            self.save_annotation(output_file)
            
            result = {
                'success': True,
                'cell_count': len(self.cells),
                'adaptive_threshold': self.tolerance if self.adaptive_threshold else None,
                'image_info': self.image_info
            }
            
            print(f"优化完成: {Path(output_file).name}")
            return result
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'cell_count': 0
            }

    def save_annotation(self, output_file: str):
        """
        保存优化后的标注文件，保护所有原始属性

        Args:
            output_file: 输出文件路径
        """
        # 使用原始数据作为基础，只更新cells
        output_data = self.original_data.copy()
        output_data['cells'] = self.cells
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)


def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(description='表格标注优化器')
    parser.add_argument('input_file', nargs='?',
                       default=r"F:\workspace\datasets\Relabel_TALOCRTable\curved_tables",
                       help='输入标注文件路径或目录')
    parser.add_argument('-o', '--output',
                       default=r"F:\workspace\datasets\Relabel_TALOCRTable\curved_tables_1",
                       help='输出文件路径或目录')
    parser.add_argument('-i', '--image', help='对应的图片文件路径')
    parser.add_argument('-t', '--tolerance', type=float, default=4.0, help='基础容差阈值（像素）')
    parser.add_argument('--disable-adaptive', action='store_true', help='禁用自适应阈值（默认启用）')
    parser.add_argument('--merge-factor', type=float, default=2.0, help='合并阈值因子')
    parser.add_argument('--alignment-strength', type=float, default=0.7, help='对齐强度 (0.0-1.0)')

    args = parser.parse_args()

    input_path = Path(args.input_file)
    output_path = Path(args.output)

    # 检查输入是文件还是目录
    if input_path.is_file():
        # 单文件处理
        if not args.output or output_path.is_dir():
            # 保持原始文件名格式，只是放到输出目录
            if output_path.is_dir():
                output_file = output_path / input_path.name
            else:
                # 如果没有指定输出，在同目录生成优化版本
                base_name = input_path.stem.replace('_table_annotation', '')
                output_file = input_path.parent / f"{base_name}_table_annotation_optimized{input_path.suffix}"
        else:
            output_file = output_path

        # 创建优化器
        optimizer = SimplifiedTableOptimizer(
            tolerance=args.tolerance,
            adaptive_threshold=not args.disable_adaptive,
            merge_threshold_factor=args.merge_factor,
            alignment_strength=args.alignment_strength
        )

        try:
            # 执行优化
            result = optimizer.optimize_table_annotation(
                str(input_path),
                str(output_file),
                args.image
            )

            if result['success']:
                print(f"\n优化完成！")
                print(f"输入文件: {input_path}")
                print(f"输出文件: {output_file}")
                print(f"单元格数: {result['cell_count']}")
                if result['adaptive_threshold']:
                    print(f"自适应阈值: {result['adaptive_threshold']:.2f} 像素")
                    if result['image_info']:
                        print(f"图片尺寸: {result['image_info']['width']}x{result['image_info']['height']}")
                else:
                    print(f"固定阈值: {args.tolerance} 像素")
            else:
                print(f"优化失败: {result['error']}")
                return 1

        except Exception as e:
            print(f"错误: {e}")
            return 1

    elif input_path.is_dir():
        # 目录批量处理
        print(f"检测到目录输入，启动批量处理模式")
        print(f"输入目录: {input_path}")
        print(f"输出目录: {output_path}")

        # 使用批量处理器
        try:
            from .batch_simplified_optimizer import BatchSimplifiedProcessor

            processor = BatchSimplifiedProcessor(
                tolerance=args.tolerance,
                adaptive_threshold=not args.disable_adaptive,
                merge_threshold_factor=args.merge_factor,
                alignment_strength=args.alignment_strength,
                max_workers=1,
                copy_images=True
            )

            # 查找标注文件
            pattern = str(input_path / "*_table_annotation.json")
            stats = processor.process_batch(pattern, str(output_path))

            if stats['failed'] == 0:
                print(f"\n🎉 批量处理完成！处理了 {stats['successful']} 个文件")
                return 0
            else:
                print(f"\n⚠️ 批量处理完成，但有 {stats['failed']} 个文件失败")
                return 1

        except ImportError:
            print("❌ 批量处理模块未找到，请确保 batch_simplified_optimizer.py 存在")
            return 1
        except Exception as e:
            print(f"批量处理错误: {e}")
            return 1
    else:
        print(f"❌ 输入路径不存在: {input_path}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
