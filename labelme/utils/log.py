#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2022/12/24 00:01
# <AUTHOR> <EMAIL>
# @FileName: logging.py

import os
import time
import logging
import logzero
from functools import wraps
# from uvicorn.config import LOGGING_CONFIG

LOG_LEVEL = os.getenv('TABLELABELME_LOG_LEVEL', 'INFO')

_nameToLevel = {
    'CRITICAL': logging.CRITICAL,
    'FATAL': logging.FATAL,
    'ERROR': logging.ERROR,
    'WARN': logging.WARNING,
    'WARNING': logging.WARNING,
    'INFO': logging.INFO,
    'DEBUG': logging.DEBUG,
    'NOTSET': logging.NOTSET,
}

DEFAULT_FORMAT = {
    "fmt": "[%(levelname)s %(asctime)s] %(message)s",
    "datefmt": "%Y%m%d %H:%M:%S"
}
# LOGGING_CONFIG["formatters"]["default"]["fmt"] = "%(levelprefix)s [%(asctime)s] %(message)s"
# LOGGING_CONFIG["formatters"]["access"]["fmt"] = "%(levelprefix)s [%(asctime)s] %(client_addr)s %(request_line)s %(status_code)s"

LOGGER = logzero.setup_logger(__file__, level=_nameToLevel[LOG_LEVEL], formatter=logging.Formatter(**DEFAULT_FORMAT))
_modelscope_logger = logging.getLogger('modelscope')
_modelscope_logger.setLevel(_nameToLevel[LOG_LEVEL])


def measure_time(func):
    @wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        LOGGER.debug(f"{func.__name__} time cost: {execution_time:.4f}s")
        return result
    return wrapper


def create_file_logger(log_path, level=LOG_LEVEL):
    file_logger = logzero.setup_logger(
        name="file_logger",
        logfile=log_path,
        level=_nameToLevel[level],
        disableStderrLogger=True,# 日志信息不在控制台显示
        backupCount=5,# 保留的日志文件数量
        maxBytes=10 * 1024 * 1024,# 每个文件最大的尺寸 20MB
        formatter=logging.Formatter(**DEFAULT_FORMAT)
    )

    return file_logger


def get_logger():
    return LOGGER