"""
网格线检测器 - 统一网格线拖动功能的核心组件（步骤1基础实现）

功能：
1. 检测像素级完全对齐的连续线段
2. 复用现有TableAnalyzer的边界检测算法
3. 提供连续线的数据结构和查找方法

复用策略：
- 连续线检测 = 现有边界检测 + 共享边缘识别
- 像素级对齐判断 = 现有容差合并算法
- 受影响单元格查找 = 现有边界框相交算法

作者：AI Assistant
版本：1.0 - 基础实现
"""

from typing import List, Dict, Tuple, Optional, NamedTuple
from PyQt5 import QtCore
from labelme.utils.log import get_logger

# 复用现有的TableAnalyzer
from labelme.utils.table_analyzer import TableAnalyzer

LOGGER = get_logger()


class GridLine(NamedTuple):
    """网格线数据结构"""
    direction: str  # "horizontal" 或 "vertical"
    coordinate: float  # 线的坐标值（y坐标用于水平线，x坐标用于垂直线）
    start: float  # 线段起始位置
    end: float  # 线段结束位置
    affected_cells: List  # 构成该线的单元格列表


class GridLineDetector:
    """网格线检测器 - 复用TableAnalyzer的边界检测算法"""

    def __init__(self, tolerance: float = 2.0):
        """
        Args:
            tolerance: 像素级对齐的容差阈值
        """
        self.tolerance = tolerance
        self.detected_grid_lines = []  # 当前检测到的所有网格线

    def detect_continuous_lines(self, table_cells: List) -> List[GridLine]:
        """
        检测表格中的所有连续网格线

        复用TableAnalyzer的核心算法：
        1. _extract_physical_bounds() - 提取单元格边界框
        2. _detect_row_boundaries() - 检测水平边界
        3. _detect_col_boundaries() - 检测垂直边界
        4. _merge_close_boundaries() - 容差合并

        Args:
            table_cells: TableCellShape列表

        Returns:
            GridLine列表，每个代表一条连续的网格线
        """
        if not table_cells:
            LOGGER.debug("输入单元格列表为空")
            return []

        LOGGER.debug(f"开始检测网格线，单元格数量: {len(table_cells)}")

        # 1. 复用TableAnalyzer._extract_physical_bounds()
        cell_bounds = TableAnalyzer._extract_physical_bounds(table_cells)
        if not cell_bounds:
            LOGGER.debug("无法提取单元格边界")
            return []

        # 2. 复用TableAnalyzer的边界检测算法
        row_boundaries = TableAnalyzer._detect_row_boundaries(cell_bounds)
        col_boundaries = TableAnalyzer._detect_col_boundaries(cell_bounds)

        # 3. 应用容差合并（复用现有算法）
        merged_row_boundaries = TableAnalyzer._merge_close_boundaries(row_boundaries, self.tolerance)
        merged_col_boundaries = TableAnalyzer._merge_close_boundaries(col_boundaries, self.tolerance)

        # 4. 新增：将边界转换为连续线段
        horizontal_lines = self._convert_boundaries_to_grid_lines(
            merged_row_boundaries, "horizontal", cell_bounds
        )
        vertical_lines = self._convert_boundaries_to_grid_lines(
            merged_col_boundaries, "vertical", cell_bounds
        )

        all_lines = horizontal_lines + vertical_lines

        LOGGER.debug(f"检测完成 - 水平线: {len(horizontal_lines)}, 垂直线: {len(vertical_lines)}")

        self.detected_grid_lines = all_lines
        return all_lines

    def _convert_boundaries_to_grid_lines(self, boundaries: List[float], direction: str,
                                        cell_bounds: List[Tuple]) -> List[GridLine]:
        """
        将边界坐标转换为网格线对象

        Args:
            boundaries: 边界坐标列表
            direction: "horizontal" 或 "vertical"
            cell_bounds: 单元格边界数据 [(x1, y1, x2, y2, cell_id, cell_obj), ...]

        Returns:
            GridLine列表
        """
        grid_lines = []

        for boundary_coord in boundaries:
            # 找到位于此边界上的所有单元格
            affected_cells = self._find_cells_on_boundary(boundary_coord, direction, cell_bounds)

            if len(affected_cells) >= 2:  # 至少由2个单元格构成的连续线
                # 计算线段的起始和结束位置
                start_pos, end_pos = self._calculate_line_segment_range(
                    affected_cells, direction
                )

                grid_line = GridLine(
                    direction=direction,
                    coordinate=boundary_coord,
                    start=start_pos,
                    end=end_pos,
                    affected_cells=[cell_obj for _, _, _, _, _, cell_obj in affected_cells]
                )
                grid_lines.append(grid_line)

                LOGGER.debug(f"创建{direction}网格线: 坐标={boundary_coord:.1f}, "
                           f"范围={start_pos:.1f}-{end_pos:.1f}, "
                           f"影响{len(affected_cells)}个单元格")

        return grid_lines

    def _find_cells_on_boundary(self, boundary_coord: float, direction: str,
                              cell_bounds: List[Tuple]) -> List[Tuple]:
        """
        找到位于指定边界上的所有单元格

        Args:
            boundary_coord: 边界坐标
            direction: "horizontal" 或 "vertical"
            cell_bounds: 单元格边界数据

        Returns:
            位于该边界上的单元格边界数据列表
        """
        cells_on_boundary = []

        for x1, y1, x2, y2, cell_id, cell_obj in cell_bounds:
            if direction == "horizontal":
                # 水平线：检查单元格的上边界或下边界是否在此坐标上
                if (abs(y1 - boundary_coord) <= self.tolerance or
                    abs(y2 - boundary_coord) <= self.tolerance):
                    cells_on_boundary.append((x1, y1, x2, y2, cell_id, cell_obj))
            else:  # vertical
                # 垂直线：检查单元格的左边界或右边界是否在此坐标上
                if (abs(x1 - boundary_coord) <= self.tolerance or
                    abs(x2 - boundary_coord) <= self.tolerance):
                    cells_on_boundary.append((x1, y1, x2, y2, cell_id, cell_obj))

        return cells_on_boundary

    def _calculate_line_segment_range(self, affected_cells: List[Tuple],
                                    direction: str) -> Tuple[float, float]:
        """
        计算连续线段的起始和结束位置

        Args:
            affected_cells: 受影响的单元格边界数据
            direction: "horizontal" 或 "vertical"

        Returns:
            (start_position, end_position) 元组
        """
        if not affected_cells:
            return 0.0, 0.0

        if direction == "horizontal":
            # 水平线：计算X坐标的范围
            x_coords = []
            for x1, y1, x2, y2, cell_id, cell_obj in affected_cells:
                x_coords.extend([x1, x2])
            return min(x_coords), max(x_coords)
        else:  # vertical
            # 垂直线：计算Y坐标的范围
            y_coords = []
            for x1, y1, x2, y2, cell_id, cell_obj in affected_cells:
                y_coords.extend([y1, y2])
            return min(y_coords), max(y_coords)

    def find_line_at_position(self, pos: QtCore.QPointF, detection_radius: float = 8.0) -> Optional[GridLine]:
        """
        在指定点附近查找网格线

        Args:
            pos: 查找位置
            detection_radius: 检测半径

        Returns:
            找到的GridLine，如果没有则返回None
        """
        px, py = pos.x(), pos.y()

        for grid_line in self.detected_grid_lines:
            if self._is_point_near_grid_line(px, py, grid_line, detection_radius):
                return grid_line

        return None

    def _is_point_near_grid_line(self, px: float, py: float, grid_line: GridLine,
                                radius: float) -> bool:
        """检查点是否靠近网格线"""
        if grid_line.direction == "horizontal":
            # 水平线：检查y坐标距离和x坐标范围
            y_distance = abs(py - grid_line.coordinate)
            if y_distance > radius:
                return False

            # 检查x坐标是否在线段范围内
            return grid_line.start - radius <= px <= grid_line.end + radius

        else:  # vertical
            # 垂直线：检查x坐标距离和y坐标范围
            x_distance = abs(px - grid_line.coordinate)
            if x_distance > radius:
                return False

            # 检查y坐标是否在线段范围内
            return grid_line.start - radius <= py <= grid_line.end + radius

    def get_detected_grid_lines(self) -> List[GridLine]:
        """获取当前检测到的所有网格线"""
        return self.detected_grid_lines.copy()

    def update_tolerance(self, new_tolerance: float):
        """更新容差阈值"""
        if new_tolerance > 0:
            self.tolerance = new_tolerance
            LOGGER.debug(f"更新容差阈值为: {new_tolerance}px")

    def clear_cache(self):
        """清除缓存的检测结果"""
        self.detected_grid_lines.clear()
        LOGGER.debug("已清除网格线检测缓存")


# ===== 便捷函数 =====

def create_grid_line_detector(tolerance: float = 2.0) -> GridLineDetector:
    """
    创建网格线检测器的便捷函数

    Args:
        tolerance: 容差阈值

    Returns:
        GridLineDetector实例
    """
    return GridLineDetector(tolerance)


def detect_grid_lines_from_cells(table_cells: List, tolerance: float = 2.0) -> List[GridLine]:
    """
    从单元格列表直接检测网格线的便捷函数

    Args:
        table_cells: TableCellShape列表
        tolerance: 容差阈值

    Returns:
        GridLine列表
    """
    detector = GridLineDetector(tolerance)
    return detector.detect_continuous_lines(table_cells)


# ===== 调试和验证函数 =====

def print_grid_lines_info(grid_lines: List[GridLine]):
    """打印网格线信息，用于调试"""
    if not grid_lines:
        LOGGER.debug("没有检测到网格线")
        return

    LOGGER.debug(f"检测到 {len(grid_lines)} 条网格线:")

    for i, line in enumerate(grid_lines):
        LOGGER.debug(f"  [{i+1}] {line.direction}线: "
                   f"坐标={line.coordinate:.1f}, "
                   f"范围={line.start:.1f}-{line.end:.1f}, "
                   f"影响{len(line.affected_cells)}个单元格")


def validate_grid_line_detection(table_cells: List, tolerance: float = 2.0) -> bool:
    """
    验证网格线检测功能

    Args:
        table_cells: 单元格列表
        tolerance: 容差阈值

    Returns:
        验证是否成功
    """
    try:
        LOGGER.debug("开始验证网格线检测功能...")

        # 创建检测器并执行检测
        detector = GridLineDetector(tolerance)
        grid_lines = detector.detect_continuous_lines(table_cells)

        # 打印检测结果
        print_grid_lines_info(grid_lines)

        # 基本验证
        if not grid_lines:
            LOGGER.debug("⚠️ 未检测到任何网格线")
            return len(table_cells) == 0  # 如果没有单元格，这是正常的

        # 检查网格线的基本属性
        for line in grid_lines:
            if line.start >= line.end:
                LOGGER.error(f"❌ 网格线范围错误: {line.start} >= {line.end}")
                return False

            if len(line.affected_cells) < 2:
                LOGGER.error(f"❌ 网格线影响的单元格数量不足: {len(line.affected_cells)}")
                return False

        LOGGER.debug("✅ 网格线检测功能验证成功")
        return True

    except Exception as e:
        LOGGER.error(f"❌ 网格线检测功能验证失败: {e}")
        return False