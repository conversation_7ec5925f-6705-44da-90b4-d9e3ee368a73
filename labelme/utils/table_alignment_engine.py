#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
表格对齐修正引擎 - 独立模块
负责根据理想网格调整单元格位置，检测和处理调整冲突
"""

import copy
from typing import List, Dict, Tuple, Any, Optional
from .log import get_logger

LOGGER = get_logger()


class TableAlignmentEngine:
    """独立的表格对齐修正引擎

    核心功能：
    1. 根据理想网格计算每个单元格的理想位置
    2. 应用位置调整并检测冲突
    3. 生成详细的修正报告

    设计原则：
    - 静态方法为主，便于测试和复用
    - fail-fast原则，严格的输入验证
    - 低层模块，只依赖基础数据结构
    """

    # 默认配置参数
    DEFAULT_TOLERANCE = 5.0
    DEFAULT_MAX_ADJUSTMENT_DISTANCE = 50.0

    def __init__(self, tolerance: float = DEFAULT_TOLERANCE,
                 max_adjustment_distance: float = DEFAULT_MAX_ADJUSTMENT_DISTANCE):
        """初始化对齐引擎

        Args:
            tolerance: 对齐容差（像素）
            max_adjustment_distance: 最大调整距离（像素）

        Raises:
            ValueError: 如果参数无效
        """
        if tolerance < 0:
            raise ValueError(f"tolerance必须非负，实际值: {tolerance}")

        if max_adjustment_distance < 0:
            raise ValueError(f"max_adjustment_distance必须非负，实际值: {max_adjustment_distance}")

        self.tolerance = tolerance
        self.max_adjustment_distance = max_adjustment_distance

    @staticmethod
    def apply_alignment_correction(cells: List, ideal_grid: Dict,
                                   max_adjustment_distance: float = 50.0) -> Dict:
        """应用对齐修正 - 调试增强版本（保持向后兼容）"""
        return TableAlignmentEngine.apply_alignment_correction_legacy(
            cells, ideal_grid, max_adjustment_distance
        )

    @staticmethod
    def apply_logical_grid_alignment(cells: List, grid_analysis: Dict, ideal_grid: Dict,
                                     max_adjustment_distance: float = 50.0) -> Dict:
        """基于逻辑网格的智能对齐 - 避免重叠问题

        Args:
            cells: 单元格列表
            grid_analysis: TableAnalyzer.analyze_cells_to_grid() 的分析结果
            ideal_grid: 理想网格数据
            max_adjustment_distance: 最大调整距离

        Returns:
            dict: 对齐结果报告
        """
        LOGGER.debug("[LOGICAL_ALIGN] 开始基于逻辑网格的智能对齐...")

        # 输入验证
        if not isinstance(cells, list) or not cells:
            return TableAlignmentEngine._create_standard_result(total_cells=0)

        if not isinstance(grid_analysis, dict) or 'cell_positions' not in grid_analysis:
            LOGGER.error("[LOGICAL_ALIGN] 缺少逻辑网格分析数据")
            return TableAlignmentEngine._create_standard_result(total_cells=len(cells))

        if not isinstance(ideal_grid, dict):
            LOGGER.error("[LOGICAL_ALIGN] 理想网格数据格式错误")
            return TableAlignmentEngine._create_standard_result(total_cells=len(cells))

        try:
            # 获取理想网格线
            ideal_v_lines = ideal_grid.get('ideal_vertical_lines', [])
            ideal_h_lines = ideal_grid.get('ideal_horizontal_lines', [])

            if not ideal_v_lines or not ideal_h_lines:
                LOGGER.error("[LOGICAL_ALIGN] 理想网格线数据为空")
                return TableAlignmentEngine._create_standard_result(total_cells=len(cells))

            LOGGER.debug(f"[LOGICAL_ALIGN] 理想网格: {len(ideal_h_lines)}行 × {len(ideal_v_lines)}列")

            # 执行基于逻辑位置的对齐
            alignment_result = TableAlignmentEngine._execute_logical_alignment(
                cells, grid_analysis['cell_positions'], ideal_v_lines, ideal_h_lines, max_adjustment_distance
            )

            LOGGER.debug(f"[LOGICAL_ALIGN] 对齐完成: 调整{alignment_result['summary']['adjusted_count']}个单元格")
            return alignment_result

        except Exception as e:
            LOGGER.error(f"[LOGICAL_ALIGN] 对齐过程异常: {e}")
            import traceback
            LOGGER.error(f"[LOGICAL_ALIGN] 堆栈跟踪: {traceback.format_exc()}")
            return TableAlignmentEngine._create_standard_result(total_cells=len(cells))

    @staticmethod
    def apply_alignment_correction_legacy(cells: List, ideal_grid: Dict,
                                   max_adjustment_distance: float = 50.0) -> Dict:
        """应用对齐修正 - 调试增强版本（原版逻辑）"""

        # 🔧 调试：详细输出输入参数信息
        LOGGER.debug("[DEBUG] 输入参数诊断:")
        LOGGER.debug(f"   cells类型: {type(cells)}, 长度: {len(cells) if isinstance(cells, list) else 'N/A'}")
        LOGGER.debug(f"   ideal_grid类型: {type(ideal_grid)}")

        if isinstance(ideal_grid, dict):
            LOGGER.debug(f"   ideal_grid键: {list(ideal_grid.keys())}")
            for key in ['ideal_vertical_lines', 'ideal_horizontal_lines']:
                if key in ideal_grid:
                    value = ideal_grid[key]
                    LOGGER.debug(
                        f"   {key}类型: {type(value)}, 长度: {len(value) if isinstance(value, (list, tuple)) else 'N/A'}")
                    if isinstance(value, (list, tuple)) and len(value) > 0:
                        LOGGER.debug(f"   {key}前3个元素: {value[:3]}")
                else:
                    LOGGER.debug(f"   {key}: 缺失")
        else:
            LOGGER.debug(f"   ideal_grid内容: {str(ideal_grid)[:200]}...")

        # 🔧 调试：检查单元格对象
        if isinstance(cells, list) and len(cells) > 0:
            first_cell = cells[0]
            LOGGER.debug(f"   第一个单元格类型: {type(first_cell)}")
            LOGGER.debug(f"   第一个单元格属性: {dir(first_cell)[:10]}...")  # 只显示前10个属性
            if hasattr(first_cell, 'points'):
                points = first_cell.points
                LOGGER.debug(f"   points类型: {type(points)}")
                if isinstance(points, (list, tuple)):
                    LOGGER.debug(f"   points长度: {len(points)}")
                    if len(points) > 0:
                        LOGGER.debug(f"   第一个point类型: {type(points[0])}")
                        LOGGER.debug(f"   第一个point属性: {dir(points[0])[:10]}...")
                else:
                    LOGGER.debug(f"   points内容: {str(points)[:100]}...")

        # 标准的输入验证

        # 输入验证
        if not isinstance(cells, list):
            LOGGER.error(f"[DEBUG] cells不是list类型: {type(cells)}")
            raise TypeError(f"cells必须是list类型，实际类型: {type(cells)}")

        if not cells:
            LOGGER.debug("[DEBUG] cells为空列表")
            return TableAlignmentEngine._create_standard_result(total_cells=0)

        if not isinstance(ideal_grid, dict):
            LOGGER.error(f"[DEBUG] ideal_grid不是dict类型: {type(ideal_grid)}")
            LOGGER.error(f"   ideal_grid内容: {ideal_grid}")
            raise TypeError(f"ideal_grid必须是dict类型，实际类型: {type(ideal_grid)}")

        required_keys = ['ideal_vertical_lines', 'ideal_horizontal_lines']
        for key in required_keys:
            if key not in ideal_grid:
                LOGGER.error(f"[DEBUG] ideal_grid缺少键: {key}")
                LOGGER.error(f"   现有键: {list(ideal_grid.keys())}")
                raise ValueError(f"ideal_grid必须包含'{key}'键")

        # 初始化结果容器
        adjusted_cells = []
        conflicting_cells = []
        skipped_cells = []

        try:
            # 获取理想网格线条
            ideal_v_lines = ideal_grid['ideal_vertical_lines']
            ideal_h_lines = ideal_grid['ideal_horizontal_lines']

            LOGGER.debug("[DEBUG] 获取理想网格线条成功")
            LOGGER.debug(f"   垂直线数量: {len(ideal_v_lines)}, 类型: {type(ideal_v_lines)}")
            LOGGER.debug(f"   水平线数量: {len(ideal_h_lines)}, 类型: {type(ideal_h_lines)}")

            # 🔧 调试：验证线条数据格式
            if not isinstance(ideal_v_lines, (list, tuple)):
                LOGGER.error(f"[DEBUG] ideal_vertical_lines不是列表: {type(ideal_v_lines)}")
                raise TypeError(f"ideal_vertical_lines应该是列表，实际: {type(ideal_v_lines)}")

            if not isinstance(ideal_h_lines, (list, tuple)):
                LOGGER.error(f"[DEBUG] ideal_horizontal_lines不是列表: {type(ideal_h_lines)}")
                raise TypeError(f"ideal_horizontal_lines应该是列表，实际: {type(ideal_h_lines)}")

            # 为每个单元格计算理想位置
            cell_adjustments = []
            for i, cell in enumerate(cells):
                try:
                    LOGGER.debug(f"[DEBUG] 处理单元格 {i + 1}/{len(cells)}")

                    # 🔧 调试增强的理想位置计算
                    adjustment_info = TableAlignmentEngine._calculate_ideal_position_debug(
                        cell, ideal_v_lines, ideal_h_lines, i
                    )

                    # 检查调整距离
                    if adjustment_info['adjustment_distance'] > max_adjustment_distance:
                        skipped_cells.append({
                            'cell_id': adjustment_info['cell_id'],
                            'reason': 'distance_too_large',
                            'suggested_distance': adjustment_info['adjustment_distance']
                        })
                        LOGGER.debug(f"   跳过单元格{i}：距离过大 ({adjustment_info['adjustment_distance']:.1f}px)")
                        continue

                    cell_adjustments.append(adjustment_info)
                    LOGGER.debug(f"   单元格{i}处理成功")

                except Exception as e:
                    LOGGER.error(f"[DEBUG] 单元格{i}处理失败: {e}")
                    skipped_cells.append({
                        'cell_id': id(cell),
                        'reason': f'calculation_error: {str(e)}',
                        'suggested_distance': 0.0
                    })

            LOGGER.debug(f"[DEBUG] 单元格处理完成，成功: {len(cell_adjustments)}, 跳过: {len(skipped_cells)}")

            # 检测位置调整冲突
            conflicts = TableAlignmentEngine._detect_position_conflicts(cell_adjustments)

            # 分离有冲突和无冲突的调整
            conflicting_cell_ids = set()
            for conflict in conflicts:
                conflicting_cell_ids.update(conflict['involved_cells'])
                conflicting_cells.append({
                    'cell_id': conflict['primary_cell'],
                    'conflict_type': conflict['conflict_type'],
                    'conflicting_with': conflict['conflicting_with'],
                    'conflict_details': conflict['details']
                })

            # 生成最终的调整列表
            for adjustment in cell_adjustments:
                if adjustment['cell_id'] not in conflicting_cell_ids:
                    adjusted_cells.append(adjustment)

            LOGGER.debug(
                f"[DEBUG] 最终结果: 调整{len(adjusted_cells)}个，冲突{len(conflicting_cells)}个，跳过{len(skipped_cells)}个")

            return TableAlignmentEngine._create_standard_result(
                adjusted_cells=adjusted_cells,
                conflicting_cells=conflicting_cells,
                skipped_cells=skipped_cells,
                total_cells=len(cells)
            )

        except Exception as e:
            LOGGER.error(f"[DEBUG] TableAlignmentEngine处理异常: {e}")
            LOGGER.error(f"   异常类型: {type(e)}")
            import traceback
            LOGGER.error(f"   堆栈跟踪: {traceback.format_exc()}")

            # 将所有单元格标记为跳过
            error_skipped = [
                {
                    'cell_id': id(cell),
                    'reason': f'processing_error: {str(e)}',
                    'suggested_distance': 0.0
                }
                for cell in cells
            ]

            return TableAlignmentEngine._create_standard_result(
                skipped_cells=error_skipped,
                total_cells=len(cells)
            )

    @staticmethod
    def _calculate_ideal_position_debug(cell, ideal_v_lines: List[float],
                                        ideal_h_lines: List[float], cell_index: int) -> Dict:
        """计算单元格的理想位置 - 调试增强版本"""

        LOGGER.debug(f"[DEBUG] _calculate_ideal_position_debug 单元格{cell_index}")

        # 1. 详细检查单元格对象
        LOGGER.debug(f"   单元格类型: {type(cell)}")
        LOGGER.debug(f"   单元格ID: {id(cell)}")

        if not hasattr(cell, 'points'):
            LOGGER.error(f"[DEBUG] 单元格没有points属性")
            LOGGER.error(f"   单元格所有属性: {dir(cell)}")
            raise ValueError(f"单元格缺少points属性")

        points = cell.points
        LOGGER.debug(f"   points类型: {type(points)}")
        LOGGER.debug(f"   points值: {points}")

        if points is None:
            raise ValueError(f"单元格points为None")

        if isinstance(points, str):
            LOGGER.error(f"[DEBUG] points是字符串而不是点列表: {points}")
            raise ValueError(f"单元格points是字符串，应该是点对象列表")

        if not isinstance(points, (list, tuple)):
            LOGGER.error(f"[DEBUG] points不是列表或元组: {type(points)}")
            raise ValueError(f"单元格points应该是列表，实际类型: {type(points)}")

        if len(points) < 4:
            LOGGER.error(f"[DEBUG] points数量不足: {len(points)}")
            raise ValueError(f"单元格points数量不足: {len(points)}")

        # 2. 详细检查点对象
        LOGGER.debug(f"   points长度: {len(points)}")

        try:
            x_coords = []
            y_coords = []

            for i, point in enumerate(points):
                LOGGER.debug(f"   检查点{i}: 类型={type(point)}")

                if isinstance(point, str):
                    LOGGER.error(f"[DEBUG] 点{i}是字符串: {point}")
                    raise ValueError(f"点{i}是字符串，应该是点对象")

                # 尝试不同的点对象格式
                x_val = None
                y_val = None

                if hasattr(point, 'x') and callable(point.x):
                    x_val = point.x()
                    y_val = point.y()
                    LOGGER.debug(f"   点{i}: x()={x_val}, y()={y_val}")
                elif hasattr(point, 'x') and not callable(point.x):
                    x_val = point.x
                    y_val = point.y
                    LOGGER.debug(f"   点{i}: x={x_val}, y={y_val}")
                elif isinstance(point, (tuple, list)) and len(point) >= 2:
                    x_val = point[0]
                    y_val = point[1]
                    LOGGER.debug(f"   点{i}: tuple/list=({x_val}, {y_val})")
                else:
                    LOGGER.error(f"[DEBUG] 无法解析点{i}的坐标格式")
                    LOGGER.error(f"   点{i}属性: {dir(point)}")
                    raise ValueError(f"无法解析点{i}的坐标格式")

                x_coords.append(float(x_val))
                y_coords.append(float(y_val))

            LOGGER.debug(f"   X坐标: {x_coords}")
            LOGGER.debug(f"   Y坐标: {y_coords}")

        except Exception as e:
            LOGGER.error(f"[DEBUG] 提取坐标失败: {e}")
            LOGGER.error(f"   points详细信息: {[str(p)[:50] for p in points]}")
            raise ValueError(f"提取单元格坐标失败: {e}")

        # 3. 计算边界框
        x1, x2 = min(x_coords), max(x_coords)
        y1, y2 = min(y_coords), max(y_coords)

        original_position = (x1, y1, x2, y2)
        LOGGER.debug(f"   原始位置: {original_position}")

        # 4. 计算理想位置（简化处理，避免更多错误）
        try:
            # 找到最近的理想网格线
            nearest_left = min(ideal_v_lines, key=lambda line: abs(line - x1))
            nearest_right = min(ideal_v_lines, key=lambda line: abs(line - x2))
            nearest_top = min(ideal_h_lines, key=lambda line: abs(line - y1))
            nearest_bottom = min(ideal_h_lines, key=lambda line: abs(line - y2))

            adjusted_position = (nearest_left, nearest_top, nearest_right, nearest_bottom)
            LOGGER.debug(f"   调整位置: {adjusted_position}")

            # 计算调整距离
            import math
            dx = nearest_left - x1
            dy = nearest_top - y1
            adjustment_distance = math.sqrt(dx * dx + dy * dy)

            return {
                'cell_id': id(cell),
                'cell_object': cell,
                'original_position': original_position,
                'adjusted_position': adjusted_position,
                'adjustment_vector': (dx, dy),
                'adjustment_distance': adjustment_distance
            }

        except Exception as e:
            LOGGER.error(f"[DEBUG] 计算理想位置失败: {e}")
            raise ValueError(f"计算理想位置失败: {e}")

    @staticmethod
    def _detect_position_conflicts(adjustments: List[Dict]) -> List[Dict]:
        """检测位置调整冲突 - 修复版本

        Args:
            adjustments: 单元格调整信息列表

        Returns:
            list: 冲突信息列表
        """
        conflicts = []

        # 🔧 修复：添加输入验证
        if not adjustments or not isinstance(adjustments, list):
            LOGGER.debug("[DEBUG] _detect_position_conflicts: 输入为空或格式错误")
            return []

        LOGGER.debug(f"[DEBUG] _detect_position_conflicts: 检查{len(adjustments)}个调整")

        # 1. 检查重叠冲突
        for i, adj1 in enumerate(adjustments):
            try:
                # 🔧 修复：验证调整对象格式
                if not isinstance(adj1, dict):
                    LOGGER.warning(f"[DEBUG] 调整{i}不是字典格式: {type(adj1)}")
                    continue

                # 🔧 修复：使用正确的键名 'adjusted_position' 而不是 'new_position'
                if 'adjusted_position' not in adj1:
                    LOGGER.warning(f"[DEBUG] 调整{i}缺少'adjusted_position'键")
                    LOGGER.warning(f"   可用键: {list(adj1.keys())}")
                    continue

                adjusted_pos1 = adj1['adjusted_position']
                if not isinstance(adjusted_pos1, (tuple, list)) or len(adjusted_pos1) != 4:
                    LOGGER.warning(f"[DEBUG] 调整{i}的adjusted_position格式错误: {adjusted_pos1}")
                    continue

                x1_1, y1_1, x2_1, y2_1 = adjusted_pos1
                cell_id1 = adj1.get('cell_id', f'unknown_{i}')

                # 检查边界有效性
                if x2_1 <= x1_1 or y2_1 <= y1_1:
                    conflicts.append({
                        'conflict_type': 'invalid_bounds',
                        'primary_cell': cell_id1,
                        'conflicting_with': [],
                        'involved_cells': [cell_id1],
                        'details': {
                            'adjusted_position': adjusted_pos1,
                            'reason': 'width_or_height_zero_or_negative'
                        }
                    })
                    LOGGER.warning(f"   发现无效边界: 单元格{cell_id1}")
                    continue

                # 检查与其他单元格的重叠
                overlapping_cells = []
                for j, adj2 in enumerate(adjustments):
                    if i >= j:  # 避免重复检查
                        continue

                    try:
                        # 🔧 修复：同样使用正确的键名
                        if not isinstance(adj2, dict) or 'adjusted_position' not in adj2:
                            continue

                        adjusted_pos2 = adj2['adjusted_position']
                        if not isinstance(adjusted_pos2, (tuple, list)) or len(adjusted_pos2) != 4:
                            continue

                        x1_2, y1_2, x2_2, y2_2 = adjusted_pos2
                        cell_id2 = adj2.get('cell_id', f'unknown_{j}')

                        # 检查矩形重叠
                        if TableAlignmentEngine._rectangles_overlap(
                                (x1_1, y1_1, x2_1, y2_1),
                                (x1_2, y1_2, x2_2, y2_2)
                        ):
                            overlapping_cells.append(cell_id2)
                            LOGGER.warning(f"   发现重叠: 单元格{cell_id1} 与 {cell_id2}")

                    except Exception as e:
                        LOGGER.warning(f"[DEBUG] 检查重叠时出错(adj{j}): {e}")
                        continue

                # 如果发现重叠，记录冲突
                if overlapping_cells:
                    conflicts.append({
                        'conflict_type': 'overlap',
                        'primary_cell': cell_id1,
                        'conflicting_with': overlapping_cells,
                        'involved_cells': [cell_id1] + overlapping_cells,
                        'details': {
                            'primary_position': adjusted_pos1,
                            'overlapping_positions': [
                                adj2['adjusted_position'] for adj2 in adjustments
                                if adj2.get('cell_id') in overlapping_cells and
                                   'adjusted_position' in adj2
                            ]
                        }
                    })

            except Exception as e:
                LOGGER.warning(f"[DEBUG] 处理调整{i}时出错: {e}")
                # 继续处理其他调整，不中断整个过程
                continue

        LOGGER.debug(f"[DEBUG] 冲突检测完成: 发现{len(conflicts)}个冲突")
        return conflicts

    @staticmethod
    def _rectangles_overlap(rect1: Tuple[float, float, float, float],
                            rect2: Tuple[float, float, float, float]) -> bool:
        """检查两个矩形是否重叠 - 增强验证版本"""

        try:
            # 🔧 修复：添加输入验证
            if not isinstance(rect1, (tuple, list)) or len(rect1) != 4:
                LOGGER.warning(f"[DEBUG] rect1格式错误: {rect1}")
                return False

            if not isinstance(rect2, (tuple, list)) or len(rect2) != 4:
                LOGGER.warning(f"[DEBUG] rect2格式错误: {rect2}")
                return False

            x1_1, y1_1, x2_1, y2_1 = rect1
            x1_2, y1_2, x2_2, y2_2 = rect2

            # 确保所有值都是数字
            for val in [x1_1, y1_1, x2_1, y2_1, x1_2, y1_2, x2_2, y2_2]:
                if not isinstance(val, (int, float)):
                    LOGGER.warning(f"[DEBUG] 矩形坐标包含非数字值: {val}")
                    return False

            # 检查是否不重叠（更容易判断）
            no_overlap = (x2_1 <= x1_2 or x2_2 <= x1_1 or y2_1 <= y1_2 or y2_2 <= y1_1)

            return not no_overlap

        except Exception as e:
            LOGGER.warning(f"[DEBUG] 矩形重叠检查出错: {e}")
            return False
    @staticmethod
    def _calculate_overlap(rect1: Tuple[float, float, float, float],
                           rect2: Tuple[float, float, float, float]) -> Dict:
        """计算两个矩形的重叠情况

        Args:
            rect1: (x1, y1, x2, y2)
            rect2: (x1, y1, x2, y2)

        Returns:
            dict: {
                'has_overlap': bool,
                'overlap_area': float,
                'overlap_rect': (x1, y1, x2, y2) or None
            }
        """
        x1_1, y1_1, x2_1, y2_1 = rect1
        x1_2, y1_2, x2_2, y2_2 = rect2

        # 计算重叠区域
        overlap_x1 = max(x1_1, x1_2)
        overlap_y1 = max(y1_1, y1_2)
        overlap_x2 = min(x2_1, x2_2)
        overlap_y2 = min(y2_1, y2_2)

        # 检查是否有重叠
        if overlap_x1 < overlap_x2 and overlap_y1 < overlap_y2:
            overlap_area = (overlap_x2 - overlap_x1) * (overlap_y2 - overlap_y1)
            return {
                'has_overlap': True,
                'overlap_area': overlap_area,
                'overlap_rect': (overlap_x1, overlap_y1, overlap_x2, overlap_y2)
            }
        else:
            return {
                'has_overlap': False,
                'overlap_area': 0.0,
                'overlap_rect': None
            }

    @staticmethod
    def _execute_logical_alignment(cells: List, cell_positions: Dict,
                                   ideal_v_lines: List[float], ideal_h_lines: List[float],
                                   max_adjustment_distance: float) -> Dict:
        """执行基于逻辑位置的对齐

        Args:
            cells: 单元格列表
            cell_positions: 逻辑位置信息 (来自TableAnalyzer)
            ideal_v_lines: 理想垂直网格线
            ideal_h_lines: 理想水平网格线
            max_adjustment_distance: 最大调整距离

        Returns:
            dict: 对齐结果
        """
        adjusted_cells = []
        conflicting_cells = []
        skipped_cells = []

        LOGGER.debug(f"[LOGICAL_ALIGN] 开始处理{len(cells)}个单元格")

        for i, cell in enumerate(cells):
            try:
                cell_id = id(cell)

                # 查找该单元格的逻辑位置信息
                logical_info = None
                for pos_id, pos_data in cell_positions.items():
                    if pos_data.get('cell_object') is cell:
                        logical_info = pos_data
                        break

                if not logical_info:
                    LOGGER.warning(f"[LOGICAL_ALIGN] 单元格{i}未找到逻辑位置信息")
                    skipped_cells.append({
                        'cell_id': cell_id,
                        'reason': 'no_logical_position',
                        'suggested_distance': 0.0
                    })
                    continue

                # 根据逻辑位置计算理想物理位置
                ideal_position = TableAlignmentEngine._calculate_ideal_position_from_logical(
                    logical_info, ideal_v_lines, ideal_h_lines
                )

                if not ideal_position:
                    LOGGER.warning(f"[LOGICAL_ALIGN] 单元格{i}无法计算理想位置")
                    skipped_cells.append({
                        'cell_id': cell_id,
                        'reason': 'calculation_failed',
                        'suggested_distance': 0.0
                    })
                    continue

                # 检查调整距离
                adjustment_distance = ideal_position['adjustment_distance']
                if adjustment_distance > max_adjustment_distance:
                    LOGGER.debug(f"[LOGICAL_ALIGN] 单元格{i}调整距离过大: {adjustment_distance:.1f}px")
                    skipped_cells.append({
                        'cell_id': cell_id,
                        'reason': 'distance_too_large',
                        'suggested_distance': adjustment_distance
                    })
                    continue

                # 添加到调整列表（逻辑对齐不会产生重叠）
                adjusted_cells.append(ideal_position)
                LOGGER.debug(f"[LOGICAL_ALIGN] 单元格{i}准备调整: {adjustment_distance:.1f}px")

            except Exception as e:
                LOGGER.error(f"[LOGICAL_ALIGN] 处理单元格{i}时出错: {e}")
                skipped_cells.append({
                    'cell_id': id(cell),
                    'reason': f'processing_error: {str(e)}',
                    'suggested_distance': 0.0
                })

        LOGGER.debug(f"[LOGICAL_ALIGN] 处理完成: 调整{len(adjusted_cells)}个，跳过{len(skipped_cells)}个")

        return TableAlignmentEngine._create_standard_result(
            adjusted_cells=adjusted_cells,
            conflicting_cells=conflicting_cells,
            skipped_cells=skipped_cells,
            total_cells=len(cells)
        )

    @staticmethod
    def _calculate_ideal_position_from_logical(logical_info: Dict,
                                               ideal_v_lines: List[float],
                                               ideal_h_lines: List[float]) -> Dict:
        """根据逻辑位置信息计算理想物理位置

        Args:
            logical_info: 逻辑位置信息 (来自TableAnalyzer)
            ideal_v_lines: 理想垂直网格线
            ideal_h_lines: 理想水平网格线

        Returns:
            dict: 理想位置信息，格式与原版兼容
        """
        try:
            # 获取逻辑位置
            logical_row = logical_info['row']
            logical_col = logical_info['col']
            row_span = logical_info['row_span']
            col_span = logical_info['col_span']
            cell_object = logical_info['cell_object']
            current_bounds = logical_info['physical_bounds']

            LOGGER.debug(f"[LOGICAL_CALC] 逻辑位置: 行[{logical_row}:{logical_row+row_span-1}], 列[{logical_col}:{logical_col+col_span-1}]")

            # 计算理想边界
            # 左边界：对应逻辑列的起始位置
            if logical_col < len(ideal_v_lines):
                ideal_left = ideal_v_lines[logical_col]
            else:
                LOGGER.warning(f"[LOGICAL_CALC] 逻辑列{logical_col}超出理想网格范围")
                ideal_left = current_bounds[0]

            # 右边界：对应逻辑列+跨度的结束位置
            right_col_index = logical_col + col_span
            if right_col_index < len(ideal_v_lines):
                ideal_right = ideal_v_lines[right_col_index]
            else:
                LOGGER.warning(f"[LOGICAL_CALC] 右边界列{right_col_index}超出理想网格范围")
                ideal_right = current_bounds[2]

            # 上边界：对应逻辑行的起始位置
            if logical_row < len(ideal_h_lines):
                ideal_top = ideal_h_lines[logical_row]
            else:
                LOGGER.warning(f"[LOGICAL_CALC] 逻辑行{logical_row}超出理想网格范围")
                ideal_top = current_bounds[1]

            # 下边界：对应逻辑行+跨度的结束位置
            bottom_row_index = logical_row + row_span
            if bottom_row_index < len(ideal_h_lines):
                ideal_bottom = ideal_h_lines[bottom_row_index]
            else:
                LOGGER.warning(f"[LOGICAL_CALC] 下边界行{bottom_row_index}超出理想网格范围")
                ideal_bottom = current_bounds[3]

            # 验证理想边界的有效性
            if ideal_right <= ideal_left or ideal_bottom <= ideal_top:
                LOGGER.error(f"[LOGICAL_CALC] 计算出的理想边界无效: ({ideal_left}, {ideal_top}, {ideal_right}, {ideal_bottom})")
                return None

            ideal_position = (ideal_left, ideal_top, ideal_right, ideal_bottom)

            # 计算调整距离（使用左上角点的移动距离）
            import math
            dx = ideal_left - current_bounds[0]
            dy = ideal_top - current_bounds[1]
            adjustment_distance = math.sqrt(dx * dx + dy * dy)

            LOGGER.debug(f"[LOGICAL_CALC] 当前边界: {current_bounds}")
            LOGGER.debug(f"[LOGICAL_CALC] 理想边界: {ideal_position}")
            LOGGER.debug(f"[LOGICAL_CALC] 调整距离: {adjustment_distance:.1f}px")

            return {
                'cell_id': id(cell_object),
                'cell_object': cell_object,
                'original_position': current_bounds,
                'adjusted_position': ideal_position,
                'adjustment_vector': (dx, dy),
                'adjustment_distance': adjustment_distance,
                'logical_position': {
                    'row': logical_row,
                    'col': logical_col,
                    'row_span': row_span,
                    'col_span': col_span
                }
            }

        except Exception as e:
            LOGGER.error(f"[LOGICAL_CALC] 计算理想位置失败: {e}")
            return None

    @staticmethod
    def _create_standard_result(adjusted_cells=None, conflicting_cells=None,
                                skipped_cells=None, total_cells=0):
        """创建标准格式的返回结果"""
        adjusted_cells = adjusted_cells or []
        conflicting_cells = conflicting_cells or []
        skipped_cells = skipped_cells or []

        return {
            'adjusted_cells': adjusted_cells,
            'conflicting_cells': conflicting_cells,
            'skipped_cells': skipped_cells,
            'summary': {
                'total_cells': total_cells,
                'adjusted_count': len(adjusted_cells),
                'conflict_count': len(conflicting_cells),
                'skipped_count': len(skipped_cells)
            }
        }




# ===== 测试代码 =====
class MockTableCellShape:
    """模拟TableCellShape类，用于测试"""

    def __init__(self, x1, y1, x2, y2):
        self.points = [
            MockPoint(x1, y1),
            MockPoint(x2, y1),
            MockPoint(x2, y2),
            MockPoint(x1, y2)
        ]


class MockPoint:
    """模拟Point类，用于测试"""

    def __init__(self, x, y):
        self._x = x
        self._y = y

    def x(self):
        return self._x

    def y(self):
        return self._y

