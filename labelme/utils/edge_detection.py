#!/usr/bin/python3
# -*- coding: utf-8 -*-
# @Time    : 2025/6/23
# <AUTHOR> oyy
# @FileName: edge_detection.py

"""
边检测工具类 - 专门用于TableCellShape的边检测算法
提供矩形边检测、顶点映射等功能
"""

from enum import Enum
from typing import Optional, List, Tuple
from PyQt5 import QtCore


class EdgeType(Enum):
    """边类型枚举"""
    LEFT = "left"
    RIGHT = "right"
    TOP = "top"
    BOTTOM = "bottom"


class EdgeDetectionHelper:
    """边检测助手类 - 提供矩形形状的边检测算法"""

    # 默认检测阈值（像素）
    DEFAULT_THRESHOLD = 8.0

    @staticmethod
    def detect_nearest_edge(points: List[QtCore.QPointF], pos: QtCore.QPointF,
                            threshold: float = None) -> Optional[EdgeType]:
        """检测点位置最接近矩形的哪条边

        Args:
            points: 矩形的4个顶点坐标 [左上, 右上, 右下, 左下]
            pos: 待检测的点位置
            threshold: 检测阈值，超过此距离不认为接近边

        Returns:
            EdgeType枚举值或None（如果不接近任何边）
        """
        if threshold is None:
            threshold = EdgeDetectionHelper.DEFAULT_THRESHOLD

        if len(points) != 4:
            return None

        # 确保点按矩形顺序排列：左上(0) → 右上(1) → 右下(2) → 左下(3)
        top_left, top_right, bottom_right, bottom_left = points

        # 计算矩形边界
        min_x = min(top_left.x(), bottom_left.x())
        max_x = max(top_right.x(), bottom_right.x())
        min_y = min(top_left.y(), top_right.y())
        max_y = max(bottom_left.y(), bottom_right.y())

        # 检查点是否在矩形范围内（允许一定外延）
        extended_threshold = threshold * 1.5  # 稍微放宽范围检查
        if (pos.x() < min_x - extended_threshold or pos.x() > max_x + extended_threshold or
                pos.y() < min_y - extended_threshold or pos.y() > max_y + extended_threshold):
            return None

        # 计算到各边的距离
        distances = {}

        # 左边距离（垂直线）
        if min_y <= pos.y() <= max_y:  # Y坐标在有效范围内
            distances[EdgeType.LEFT] = abs(pos.x() - min_x)

        # 右边距离（垂直线）
        if min_y <= pos.y() <= max_y:  # Y坐标在有效范围内
            distances[EdgeType.RIGHT] = abs(pos.x() - max_x)

        # 上边距离（水平线）
        if min_x <= pos.x() <= max_x:  # X坐标在有效范围内
            distances[EdgeType.TOP] = abs(pos.y() - min_y)

        # 下边距离（水平线）
        if min_x <= pos.x() <= max_x:  # X坐标在有效范围内
            distances[EdgeType.BOTTOM] = abs(pos.y() - max_y)

        # 找到最近的边
        if not distances:
            return None

        nearest_edge = min(distances.keys(), key=lambda edge: distances[edge])
        nearest_distance = distances[nearest_edge]

        # 只有距离在阈值内才返回
        if nearest_distance <= threshold:
            return nearest_edge

        return None

    @staticmethod
    def get_edge_vertex_indices(edge_type: EdgeType) -> Tuple[int, int]:
        """获取指定边对应的顶点索引

        Args:
            edge_type: 边类型

        Returns:
            该边的两个顶点索引 (index1, index2)
            矩形顶点顺序：[0]左上 → [1]右上 → [2]右下 → [3]左下
        """
        vertex_mapping = {
            EdgeType.LEFT: (0, 3),  # 左边：左上(0) 和 左下(3)
            EdgeType.RIGHT: (1, 2),  # 右边：右上(1) 和 右下(2)
            EdgeType.TOP: (0, 1),  # 上边：左上(0) 和 右上(1)
            EdgeType.BOTTOM: (2, 3),  # 下边：右下(2) 和 左下(3)
        }
        return vertex_mapping[edge_type]

    @staticmethod
    def get_edge_coordinate_axis(edge_type: EdgeType) -> str:
        """获取边拖动时需要改变的坐标轴

        Args:
            edge_type: 边类型

        Returns:
            'x' 或 'y'，表示拖动该边主要改变哪个坐标轴
        """
        if edge_type in [EdgeType.LEFT, EdgeType.RIGHT]:
            return 'x'  # 左右边拖动改变X坐标
        else:
            return 'y'  # 上下边拖动改变Y坐标

    @staticmethod
    def calculate_edge_center(points: List[QtCore.QPointF], edge_type: EdgeType) -> QtCore.QPointF:
        """计算指定边的中心点坐标

        Args:
            points: 矩形的4个顶点
            edge_type: 边类型

        Returns:
            边中心点的坐标
        """
        if len(points) != 4:
            return QtCore.QPointF()

        vertex1_idx, vertex2_idx = EdgeDetectionHelper.get_edge_vertex_indices(edge_type)
        vertex1 = points[vertex1_idx]
        vertex2 = points[vertex2_idx]

        # 计算中点
        center_x = (vertex1.x() + vertex2.x()) / 2.0
        center_y = (vertex1.y() + vertex2.y()) / 2.0

        return QtCore.QPointF(center_x, center_y)

    @staticmethod
    def get_edge_description(edge_type: EdgeType) -> str:
        """获取边的中文描述

        Args:
            edge_type: 边类型

        Returns:
            边的中文名称
        """
        descriptions = {
            EdgeType.LEFT: "左边",
            EdgeType.RIGHT: "右边",
            EdgeType.TOP: "上边",
            EdgeType.BOTTOM: "下边",
        }
        return descriptions[edge_type]


class EdgeDetectionResult:
    """边检测结果类"""

    def __init__(self, edge_type: EdgeType, distance: float, vertex_indices: Tuple[int, int]):
        self.edge_type = edge_type
        self.distance = distance
        self.vertex_indices = vertex_indices
        self.edge_description = EdgeDetectionHelper.get_edge_description(edge_type)
        self.coordinate_axis = EdgeDetectionHelper.get_edge_coordinate_axis(edge_type)

    def __str__(self):
        return f"EdgeDetection({self.edge_description}, 距离={self.distance:.1f}, 顶点索引={self.vertex_indices})"