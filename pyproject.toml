[build-system]
requires = ["hatchling>=1.20.0", "hatch-vcs", "hatch-fancy-pypi-readme"]
build-backend = "hatchling.build"

[project]
name = "labelme"
description = "Image Polygonal Annotation with Python"
license = { text = "GPL-3.0-only" }
requires-python = ">=3.9"
version = "5.8.1"
authors = [{ name = "Kentaro Wada", email = "<EMAIL>" }]
classifiers = [
  "Development Status :: 5 - Production/Stable",
  "Intended Audience :: Developers",
  "Intended Audience :: Science/Research",
  "License :: OSI Approved :: GNU General Public License v3 (GPLv3)",
  "Operating System :: OS Independent",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3 :: Only",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3.13",
]
dependencies = [
  "imgviz",
  "loguru",
  "matplotlib",
  "natsort>=7.1.0",
  "numpy",
  "osam>=0.2.3",
  "pillow>=2.8",
  "pyqt5>=5.14.0",
  "pyqt5-qt5!=5.15.13 ; sys_platform == 'linux'",
  "pyqt5-qt5<5.15.11 ; sys_platform == 'win32'",
  "pyyaml",
  "scikit-image",
  "qtpy (>=2.4.3,<3.0.0)",
  "logzero (>=1.7.0,<2.0.0)",
  "python-dotenv (>=1.1.1,<2.0.0)",
  "dotenv (>=0.9.9,<0.10.0)",
]

[tool.hatch.metadata.hooks.fancy-pypi-readme]
content-type = "text/markdown"
fragments = [{ path = "README.md" }]


[[tool.poetry.source]]
name = "pypi-tuna"
url = "https://pypi.tuna.tsinghua.edu.cn/simple"
priority = "primary"

[dependency-groups]
dev = [
  "mypy>=1.15.0",
  "pyqt5-stubs>=********",
  "pytest>=8.3.4",
  "pytest-qt>=4.4.0",
  "ruff==0.1.9",
  "twine>=6.1.0",
  "types-pillow>=10.2.0.20240822",
  "types-pyyaml>=6.0.12.20241230",
]

[project.scripts]
labelme = "labelme.__main__:main"
labelme_draw_json = "labelme.cli.draw_json:main"
labelme_draw_label_png = "labelme.cli.draw_label_png:main"
labelme_export_json = "labelme.cli.export_json:main"
labelme_on_docker = "labelme.cli.on_docker:main"

[tool.pytest.ini_options]
qt_api = "pyqt5"
markers = [
  "gui: mark a test as a GUI test.",
]

[tool.ruff.lint]
select = ["E", "F", "I"]

[tool.ruff.lint.isort]
force-single-line = true

[tool.mypy]
check_untyped_defs = true
ignore_missing_imports = true
