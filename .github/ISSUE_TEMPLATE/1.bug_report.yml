name: Bug Report
description: Create a bug report
labels: 'bug'
body:
  - type: markdown
    attributes:
      value: Thanks for taking the time to file a bug report! Please fill out this form as completely as possible.
  - type: markdown
    attributes:
      value: If you leave out sections there is a high likelihood it will be moved to the GitHub Discussions ["Q&A / Help" section](https://github.com/wkentaro/labelme/discussions/categories/q-a-help).
  - type: textarea
    attributes:
      label: Provide environment information
      description: Please run `which python; python --version; python -m pip list | grep labelme` in the root directory of your project and paste the results.
    validations:
      required: true
  - type: input
    attributes:
      label: What OS are you using?
      description: 'Please specify the exact version. For example: macOS 12.4, Ubuntu 20.04.4'
    validations:
      required: true
  - type: textarea
    attributes:
      label: Describe the Bug
      description: A clear and concise description of what the bug is.
    validations:
      required: true
  - type: textarea
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
  - type: textarea
    attributes:
      label: To Reproduce
      description: Steps to reproduce the behavior, please provide a clear description of how to reproduce the issue, based on the linked minimal reproduction. Screenshots can be provided in the issue body below. If using code blocks, make sure that [syntax highlighting is correct](https://docs.github.com/en/get-started/writing-on-github/working-with-advanced-formatting/creating-and-highlighting-code-blocks#syntax-highlighting) and double check that the rendered preview is not broken.
  - type: markdown
    attributes:
      value: Before posting the issue go through the steps you've written down to make sure the steps provided are detailed and clear.
  - type: markdown
    attributes:
      value: Contributors should be able to follow the steps provided in order to reproduce the bug.
  - type: markdown
    attributes:
      value: These steps are used to add integration tests to ensure the same issue does not happen again. Thanks in advance!
