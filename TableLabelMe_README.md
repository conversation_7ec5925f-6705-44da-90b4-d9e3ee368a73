# TableLabelMe - 专业表格标注工具

<div align="center">
  <img src="examples/table_recognition/images/app_tableme.png" width="300"><br/>
  <h3>基于 LabelMe 架构的专业表格标注工具</h3>
  <p>为机器学习训练提供高质量的表格标注数据集</p>
</div>

<div align="center">
  <a href="#安装"><b>安装</b></a>
  | <a href="#使用说明"><b>使用说明</b></a>
  | <a href="#快捷键"><b>快捷键</b></a>
  | <a href="#逻辑可视化功能"><b>逻辑可视化</b></a>
  | <a href="#自动表格类型推断"><b>智能推断</b></a>
  | <a href="#API集成"><b>API集成</b></a>
</div>

---

## 项目概述

TableLabelMe 是一个专业的表格标注工具，基于开源项目 [LabelMe](https://github.com/wkentaro/labelme) 架构开发，专注于图像中表格的精确标注。它支持物理边界标注、逻辑结构分析、属性编辑和批量操作，为构建高质量的表格识别模型数据集提供完整的解决方案。

### 核心特性

- ✅ **精确标注**：支持在图像上绘制表格单元格，进行精确的物理边界标注
- ✅ **多表格管理**：支持在同一图像中标注多个独立的表格实例
- ✅ **结构分析**：自动分析表格的行列结构，将物理单元格映射到逻辑网格
- ✅ **属性编辑**：支持编辑单元格的边框样式、文本内容、表头状态等属性
- ✅ **批量操作**：提供单元格对齐、快速生成表格、批量选择等高效工具
- ✅ **撤销重做**：基于命令模式实现的完整操作历史管理
- ✅ **API集成**：支持第三方表格识别API辅助打标，提高标注效率
- ✅ **智能推断**：自动表格类型推断功能，根据边框状态智能判断表格类型
- ✅ **逻辑可视化**：在物理单元格上显示逻辑坐标，方便查看单元格在表格中的位置
- ✅ **数据持久化**：支持标注数据的保存和加载，采用JSON格式存储

---

## 安装

### 系统要求

- **操作系统**：Windows 10+, macOS 10.14+, Ubuntu 18.04+
- **Python 版本**：Python 3.9+

### 依赖库

核心依赖包括：

```
PyQt5>=5.14.0
Pillow>=2.8
numpy
matplotlib
natsort>=7.1.0
scikit-image
pyyaml
imgviz
loguru
qtpy>=2.4.3
logzero>=1.7.0
osam>=0.2.3
```

### 安装方式

#### 使用 Poetry 安装项目

```bash
# 克隆项目
<NAME_EMAIL>:datax/cf_labelme.git
cd cf_labelme

# 安装项目依赖
poetry install 

```

### 启动应用

### 🚀 运行与配置

我们使用 `.env` 文件来管理环境变量，这种方法安全、便捷且跨平台。

#### 第一步：配置环境变量 

1.  在项目根目录下，将模板文件 `.env.example` 复制一份并重命名为 `.env`。
    ```bash
    # 在 Linux, macOS, 或 Windows PowerShell 中:
    cp .env.example .env

    # 如果你使用旧版 Windows CMD:
    copy .env.example .env
    ```

2.  使用文本编辑器打开你刚刚创建的 `.env` 文件，它看起来像这样：
    ```ini
    # 日志级别 (e.g., DEBUG, INFO, WARNING, ERROR)
    TABLELABELME_LOG_LEVEL="INFO"
    
    # TextIn API 密钥 (请替换为你的真实密钥)
    TEXTIN_APP_ID="your_app_id_here"
    TEXTIN_SECRET_CODE="your_secret_code_here"
    ```

3.  **重要**: 将 `.env` 文件中的 `your_app_id_here` 和 `your_secret_code_here` 替换为你的真实 API 密钥。


#### 第二步：启动应用 


**方式一：直接运行 (推荐)**

这是最简单直接的方式。
```bash
poetry run python labelme/app_tableme.py
```

**方式二：激活虚拟环境后运行**

如果你计划在虚拟环境中执行多个命令，可以先激活它。
```bash
# 1. 激活 Poetry 虚拟环境
poetry shell

# 2. 在激活的环境中直接运行 Python 脚本
python labelme/app_tableme.py
```

#### 第三步：验证运行 

应用成功启动后，您将看到 **TableLabelMe** 的主界面，并且状态栏会显示"TableLabelMe - 表格标注工具启动成功!"。

如需验证 API 密钥是否生效，可以尝试使用需要 API 的辅助标注功能，并留意应用的日志输出。

---

## 使用说明

### 快速开始

1. **启动应用**
   ```bash
   # 根据不同的平台，Linux or Windows 设置环境变量后（TABLELABELME_LOG_LEVEL，TEXTIN_APP_ID，TEXTIN_SECRET_CODE）
   poetry run python labelme/app_tableme.py
   ```

2. **加载图片**
   - 方式一：菜单 → 文件 → 打开图片 (Ctrl+O)
   - 方式二：菜单 → 文件 → 打开目录 (Ctrl+Shift+O) - 批量处理
   - 方式三：应用启动时会自动加载 `examples/table_recognition/data/` 测试数据

3. **开始标注**
   - 按 `2` 进入单元格模式，拖拽绘制表格区域
   - 按 `Q` 在选中区域快速生成 M×N 表格（推荐）
   - 按 `1` 进入选择模式，支持多选和调整

4. **保存结果**
   - 按 `Ctrl+S` 保存标注数据
   - 数据保存为 `{图片名}_table_annotation.json` 格式

### 标注工作流程

#### 详细操作步骤

1. **图片加载**
   - 支持 PNG, JPG, JPEG, BMP, TIFF 格式
   - 可批量导入目录中的所有图片
   - 左侧文件列表显示标注状态（🟡未浏览、🟢已浏览、✅已保存）

2. **表格标注**
   
   **方法一：快速生成表格（推荐）**
   - 按 `Q` 进入快速表格模式
   - 在图片中拖拽选择表格区域
   - 在弹出对话框中设置行数和列数
   - 系统自动生成规整的 M×N 表格

   **方法二：手动绘制单元格**
   - 按 `2` 进入单元格模式
   - 在图片上拖拽绘制每个单元格的边界框
   - 系统自动创建 TableCellShape 对象

3. **结构分析**
   - 按 `A` 或点击"分析表格结构"
   - 系统自动分析表格的行列结构
   - 在右下角逻辑结构视图中显示网格

4. **对齐和调整**
   - 选中多个单元格（Ctrl+点击 或 框选）
   - 使用对齐工具：顶端对齐(Ctrl+T)、底端对齐(Ctrl+B)、左对齐(Ctrl+L)、右对齐(Ctrl+R)
   - 使用"应用表格对齐"功能将所有单元格对齐到理想网格

5. **属性编辑**
   - 在右侧属性面板中编辑单元格属性
   - 支持设置边框样式（实线/虚线/无边框）
   - 支持设置表格类型（纯文本/有线表格/无线表格）
   - 支持自动表格类型推断（可选开启/关闭）
   - 支持编辑单元格文本内容

6. **质量控制**
   - 在属性面板中设置样本质量（合格/准合格/不合格/待校准）
   - 支持快速保存并切换到下一张图片

### 界面布局

```
┌─────────────────────────────────────────────────────────────────┐
│ 菜单栏：文件 编辑 表格 视图                                         │
├─────────────────────────────────────────────────────────────────┤
│ 工具栏：文件操作 | 模式工具 | 表格工具 | 对齐工具 | 缩放工具            │
├──────────┬─────────────────────────────────┬────────────────────┤
│ 文件列表  │                                 │ 表格属性面板         │
│          │          画布区域                │                    │
│ 🟡 1.png │                                │ 边框样式             │
│ ✅ 2.png │                                │ 表格类型             │
│ 🟢 3.png │                                │ 样本质量             │
│          │                                │ API辅助打标          │
│          │                                │                     │
│          ├────────────────────────────────┤                     │
│          │     逻辑结构视图                 │                     │
│          │   ┌───┬───┬───┐                │                     │
│          │   │ A │ B │ C │                │                     │
│          │   ├───┼───┼───┤                │                     │
│          │   │ D │ E │ F │                │                     │
│          │   └───┴───┴───┘                │                     │
└──────────┴─────────────────────────────────┴────────────────────┤
│ 状态栏：显示当前操作状态和提示信息                                    │
└─────────────────────────────────────────────────────────────────┘
```

---

## 快捷键

### 文件操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+O` | 打开图片 | 打开单个图片文件 |
| `Ctrl+Shift+O` | 打开目录 | 批量导入图片目录 |
| `Ctrl+S` | 保存标注 | 保存当前图片的标注数据 |
| `Ctrl+Q` | 退出应用 | 关闭应用程序 |
| `Left` | 上一张图片 | 切换到上一张图片 |
| `Right` | 下一张图片 | 切换到下一张图片 |

### 编辑操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+Z` | 撤销 | 撤销上一步操作 |
| `Ctrl+Shift+Z` | 重做 | 重做上一步操作 |
| `Delete` / `Backspace` | 删除选中 | 删除选中的单元格 |
| `Ctrl+Shift+Delete` | 删除整个表格 | 删除当前活动表格及所有单元格 |

### 模式切换
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `1` | 选择模式 | 选择、移动、调整已有单元格，支持多选 |
| `2` | 单元格模式 | 拖拽绘制新的表格单元格 |
| `Q` | 快速生成表格 | 在选中区域快速生成 M×N 表格 |
| `Z` | 拖动表格边 | 按住Z键可以拖动表格单元格的边缘进行精细调整 |
| `Esc` | 退出当前模式 | 退出快速表格选区模式等 |

### 表格工具
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `A` | 分析表格结构 | 自动分析表格的行列结构 |
| `Ctrl+Shift+A` | 应用表格对齐 | 将所有单元格对齐到理想网格 |
| `Ctrl+H` | 按行拆分 | 将选中单元格按行拆分 |
| `Ctrl+V` | 按列拆分 | 将选中单元格按列拆分 |

### 对齐工具
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+T` | 顶端对齐 | 将选中单元格顶端对齐 |
| `Ctrl+B` | 底端对齐 | 将选中单元格底端对齐 |
| `Ctrl+L` | 左对齐 | 将选中单元格左对齐 |
| `Ctrl+R` | 右对齐 | 将选中单元格右对齐 |

### 视图操作
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+=` | 放大 | 放大图像 |
| `Ctrl+-` | 缩小 | 缩小图像 |
| `Ctrl+F` | 适应窗口 | 图像适应窗口大小 |
| `Space` | 对比可视化 | 按住显示原图，松开显示标注 |
| `M` | 逻辑可视化 | 切换逻辑坐标显示，在物理单元格上显示逻辑位置 |

### 多选操作
| 操作 | 功能 | 说明 |
|------|------|------|
| `Ctrl+点击` | 多选单元格 | 按住Ctrl点击多个单元格 |
| `拖拽框选` | 框选多个单元格 | 在选择模式下拖拽框选 |
| `Shift+点击` | 范围选择 | 选择从上次点击到当前点击的范围 |

### 网格拖拽模式高级操作

#### 多选边框操作
| 操作 | 功能 | 说明 |
|------|------|------|
| `Shift+点击` | 多选网格线 | 逐个选择多条网格线，选中线显示红色高亮 |
| `拖拽框选` | 批量选择网格线 | 通过矩形选择区域批量选择网格线 |

**多选规则:**
- 只能选择同类型的线（水平线或垂直线）
- 选择错误类型时自动清除之前的选择
- 支持取消选择（点击空白处）

#### 多线旋转操作
| 操作 | 功能 | 说明 |
|------|------|------|
| `选择旋转点` | 设置旋转中心 | 为多条平行线设置统一的旋转中心点 |
| `同步旋转` | 多线协同旋转 | 所有选中线围绕各自对应点同时旋转 |

**旋转特性:**
- **垂直线**: 根据相对位置选择顶部或底部点作为旋转中心
- **水平线**: 根据相对位置选择左侧或右侧点作为旋转中心
- **平行保持**: 多条线旋转时保持平行关系，无重叠风险

#### 表格整体旋转
| 快捷键 | 功能 | 说明 |
|--------|------|------|
| `Ctrl+Alt+拖拽` | 表格整体旋转 | 按住组合键在表格上拖拽进行整体旋转 |

**旋转特性:**
- **自动中心**: 表格几何中心自动计算作为旋转点
- **精细控制**: 15%低灵敏度设计，适合微调表格角度
- **角度限制**: ±45°限制，防止过度旋转
- **视觉反馈**: 鼠标光标变为旋转图标
- **撤销支持**: 所有旋转操作支持Ctrl+Z撤销

#### 操作模式优先级
1. **Ctrl+Alt**: 表格整体旋转模式（最高优先级）
2. **Shift+选择**: 多线选择模式
3. **Alt+拖拽**: 单线自由拖拽模式
4. **默认**: 标准网格线拖拽模式

#### 使用建议
- **表格校正**: 使用Ctrl+Alt旋转纠正拍摄角度偏差
- **批量调整**: 使用Shift+鼠标框选进行区域性网格线调整
- **精细操作**: 使用小容差设置进行精确的单线调整
- **注意**: 旋转后的网格线可能无法被重新检测（系统限制）

---

## 逻辑可视化功能

### 功能概述

逻辑可视化功能允许用户在物理单元格上直接显示逻辑坐标信息，帮助用户快速理解单元格在表格逻辑结构中的位置关系。这对于复杂表格的标注和验证特别有用。

### 使用方法

#### 开启/关闭逻辑可视化
- **快捷键**：按 `M` 键切换逻辑可视化模式
- **工具栏**：点击"逻辑可视化"按钮
- **菜单**：视图 → 逻辑可视化

#### 状态保持
- **图片切换保持**：开启逻辑可视化后，切换到其他图片时状态会自动保持
- **自动应用**：新加载的图片如果有表格数据，会自动显示逻辑坐标
- **智能隐藏**：没有表格数据的图片不会显示坐标信息

### 显示格式

#### 普通单元格
- **格式**：`(行,列)`
- **示例**：`(0,1)` 表示第0行第1列的单元格

#### 合并单元格
- **格式**：`(起始行-结束行,起始列-结束列)`
- **示例**：`(0-1,2-3)` 表示跨越第0-1行、第2-3列的合并单元格
- **单行/列合并**：`(0,1-2)` 表示第0行、跨越第1-2列

### 视觉效果

- **文本颜色**：红色粗体，确保在各种背景下清晰可见
- **背景**：白色半透明背景，提高文字可读性
- **位置**：显示在单元格的几何中心
- **字体**：Arial 12号粗体字体

### 应用场景

1. **结构验证**：快速检查表格的逻辑结构是否正确
2. **合并单元格确认**：直观查看哪些单元格被合并
3. **坐标定位**：在复杂表格中快速定位特定位置的单元格
4. **质量检查**：验证物理标注与逻辑结构的一致性

---

## 自动表格类型推断

### 功能概述

TableLabelMe 提供智能的自动表格类型推断功能，能够根据单元格的边框状态自动判断表格类型，减少手动设置的工作量，提高标注效率。

### 推断逻辑

#### 表格类型分类

系统支持三种表格类型：

| 类型 | 值 | 描述 | 特征 |
|------|----|--
--- |------|
| **纯文本** | 0 | 无表格结构的纯文本内容 | 没有任何单元格 |
| **有线表格** | 1 | 完整边框的标准表格 | 所有单元格都有完整的四边边框 |
| **无线表格** | 2 | 部分边框或无边框的表格 | 存在缺少边框的单元格 |
#### 自动推断规则

系统按照以下优先级顺序进行判断：

```
1. 检查单元格数量
   └─ 如果没有单元格 → 纯文本 (type=0)

2. 检查所有单元格的边框完整性
   ├─ 如果所有单元格都有完整四边边框 → 有线表格 (type=1)
   └─ 如果存在任何不完整边框的单元格 → 无线表格 (type=2)
```

#### 边框完整性判断

对于每个单元格，系统检查四个边框（上、右、下、左）：

### 使用方式

#### 开启/关闭自动推断

在表格属性面板中：

1. **开启自动推断**：勾选"保存时自动推导表格类型"复选框
2. **关闭自动推断**：取消勾选复选框，使用手动设置的类型

#### 用户体验设计

- **默认启用**：新创建的表格默认开启自动推断
- **状态保持**：用户的选择在图片切换时保持不变
- **优先级**：自动推断优先级高于手动设置

---

## API集成

### 第三方API支持

TableLabelMe 支持集成第三方表格识别API，实现自动化的辅助打标功能，大幅提高标注效率。

#### 支持的API服务

##### TextIn API
- **服务商**：TextIn（合合信息）
- **功能**：表格结构识别和文字识别
- **精度**：商业级别的识别精度
- **配置**：需要提供 app_id 和 secret_code

## 数据格式

### 标注数据格式

TableLabelMe 采用JSON格式存储标注数据，文件命名可兼容格式：
`{图片名}_table_annotation.json`
`{图片名}.json`

#### 单表格格式
```json
{
  "quality": "合格",
  "table_ind": 0,
  "type": 1,
  "cells": [
    {
      "cell_id": 0,
      "points": [[x1, y1], [x2, y2]],
      "label": "cell_0_0",
      "table_properties": {
        "table_id": 0,
        "cell_text": "表头",
        "border": {"top": 1, "bottom": 1, "left": 1, "right": 1},
        "is_header": true,
        "table_type": 1
      }
    }
  ],
  "logical_grid": {
    "rows": 3,
    "cols": 4,
    "cell_positions": {
      "0": {"row": 0, "col": 0}
    }
  }
}
```

#### 字段说明

| 字段 | 类型 | 说明 | 示例值 |
|------|------|------|--------|
| `quality` | string | 样本质量标记 | "合格", "准合格", "不合格", "待校准" |
| `table_ind` | number | 表格索引（从0开始） | 0, 1, 2... |
| `type` | number | **表格类型**（自动推断结果） | 0=纯文本, 1=有线表格, 2=无线表格 |
| `cells` | array | 单元格数组 | [...] |
| `border` | object | **边框状态**（推断依据） | {"top": 1, "right": 0, "bottom": 1, "left": 1} |

#### 自动推断示例

**有线表格示例**（所有单元格都有完整边框）：
```json
{
  "type": 1,
  "cells": [
    {
      "table_properties": {
        "border": {"top": 1, "right": 1, "bottom": 1, "left": 1}
      }
    },
    {
      "table_properties": {
        "border": {"top": 1, "right": 1, "bottom": 1, "left": 1}
      }
    }
  ]
}
```

**无线表格示例**（存在不完整边框的单元格）：
```json
{
  "type": 2,
  "cells": [
    {
      "table_properties": {
        "border": {"top": 1, "right": 0, "bottom": 1, "left": 0}
      }
    },
    {
      "table_properties": {
        "border": {"top": 0, "right": 1, "bottom": 0, "left": 1}
      }
    }
  ]
}
```

**纯文本示例**（没有单元格）：
```json
{
  "type": 0,
  "cells": []
}
```

#### 多表格格式
```json
{
  "quality": "合格",
  "tables": [
    {
      "table_ind": 0,
      "type": 1,
      "cells": [...]
    },
    {
      "table_ind": 1,
      "type": 2,
      "cells": [...]
    }
  ]
}
```

> **注意**：在多表格场景中，每个表格都会独立进行自动类型推断，根据各自的单元格边框状态确定类型。

---

## 致谢

- 感谢 [LabelMe](https://github.com/wkentaro/labelme) 项目提供的优秀基础架构
- 感谢 PyQt5 社区提供的强大GUI框架
- 感谢所有贡献者和用户的支持与反馈

---

## 联系方式

- **项目维护者**：<EMAIL>
- **问题反馈**：请在项目仓库中提交 Issue
- **功能建议**：欢迎提交 Pull Request 或 Feature Request

---

<div align="center">
  <p>如果这个项目对您有帮助，请给我们一个 ⭐️</p>
  <p>TableLabelMe - 让表格标注更简单、更高效</p>
</div>
