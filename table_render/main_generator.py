"""
主生成器

协调整个表格生成流程的核心类。
"""

import asyncio
import logging
import numpy as np
import os
import psutil  # V5.1新增：用于内存监控
import time    # V5.1新增：用于性能监控
from pathlib import Path
from typing import Tuple

from .config import RenderConfig
from .resolver import Resolver
from .builders.structure_builder import StructureBuilder
from .builders.content_builder import ContentBuilder
from .builders.style_builder import StyleBuilder
from .renderers.html_renderer import HtmlRenderer
from .utils.file_utils import FileUtils
from .utils.annotation_converter import AnnotationConverter
from .postprocessors.image_augmentor import ImageAugmentor  # V4.0新增


class MainGenerator:
    """
    主生成器类
    
    负责协调整个表格生成流水线，包括结构构建、内容填充、样式生成和渲染。
    """
    
    def __init__(self, config: RenderConfig, debug_mode: bool = False):
        """
        初始化主生成器

        Args:
            config: 经过验证的配置对象
            debug_mode: 是否启用调试模式
        """
        self.config = config
        self.debug_mode = debug_mode
        self.logger = logging.getLogger(__name__)
        # 为了可复现性，创建一个主随机状态
        self.random_state = np.random.RandomState(config.seed)

        if self.debug_mode:
            self.logger.info("调试模式已启用")
            # 确保调试输出目录存在
            debug_dir = config.debug.debug_output_dir if config.debug else './debug_output'
            os.makedirs(debug_dir, exist_ok=True)
            self.logger.info(f"调试输出目录: {debug_dir}")
        # V3.1新增：创建配置解析器
        self.resolver = Resolver()

        # V5.1新增：性能监控和内存管理
        self.performance_monitor = LargeTablePerformanceMonitor()
        self.memory_threshold_mb = 1000  # 1GB内存阈值
        
    def generate(self, num_samples: int):
        """
        生成指定数量的表格样本

        Args:
            num_samples: 要生成的样本数量
        """
        # 使用asyncio运行异步生成过程
        asyncio.run(self._async_generate(num_samples))

    async def _async_generate(self, num_samples: int):
        """
        异步生成过程

        Args:
            num_samples: 要生成的样本数量
        """
        self.logger.info(f"主生成器已初始化，配置种子: {self.config.seed}")

        # 确保输出目录存在
        output_dirs = FileUtils.ensure_output_dirs(self.config.output.output_dir)

        # 初始化渲染器和转换器（不在这里设置调试目录，每个样本单独设置）
        renderer = await HtmlRenderer.create_async()
        annotation_converter = AnnotationConverter()

        try:
            for i in range(num_samples):
                # 设置当前样本索引用于调试
                self._current_sample_index = i

                self.logger.info(f"正在生成第 {i + 1}/{num_samples} 个样本...")

                # V5.1新增：开始性能监控
                sample_start_time = time.time()
                initial_memory = self.performance_monitor.get_memory_usage()

                # 为每个样本生成一个独立的随机种子
                sample_seed = self.random_state.randint(0, 2**32 - 1)

                # V3.1新工作流：首先解析配置为具体参数
                resolved_params = self.resolver.resolve(self.config, sample_seed)

                # V5.1新增：检查是否为大表格并进行预检查
                table_size_info = self._analyze_table_size(resolved_params.structure)
                if table_size_info['is_large_table']:
                    self.logger.info(f"[LARGE_TABLE] 检测到大表格: {table_size_info['description']}")

                    # 内存预检查
                    if not self.performance_monitor.check_memory_availability(self.memory_threshold_mb):
                        self.logger.warning(f"[LARGE_TABLE] 内存不足，跳过样本 {i + 1}")
                        continue

                # 构建表格结构
                structure_builder = StructureBuilder(sample_seed)
                table_model = structure_builder.build(
                    resolved_params.structure,
                    resolved_params.style.border_mode,
                    resolved_params.style.border_details
                )

                # 填充表格内容
                content_builder = ContentBuilder(sample_seed)
                table_model = content_builder.build(table_model, resolved_params.content)

                # 生成样式
                style_builder = StyleBuilder(sample_seed)
                # V4.3新增：传递透明度配置
                transparency_config = resolved_params.postprocessing if resolved_params.postprocessing else None
                css_string = style_builder.build(resolved_params.style, table_model, transparency_config)

                # 设置当前样本的调试目录
                if self.debug_mode:
                    sample_debug_dir = os.path.join(self.config.output.output_dir, f"debug_sample_{i:06d}")
                    renderer.debug_mode = True
                    renderer.debug_output_dir = sample_debug_dir
                    renderer.debug_stage_counter = 0  # 重置计数器
                    # 确保调试目录存在
                    Path(sample_debug_dir).mkdir(parents=True, exist_ok=True)
                else:
                    renderer.debug_mode = False
                    renderer.debug_output_dir = None

                # V4.0新增：检查是否使用CSS背景图渲染模式
                if (resolved_params.postprocessing and
                    resolved_params.postprocessing.apply_background):

                    # CSS渲染模式：直接在HTML阶段集成背景图和透视变换
                    # V5.1改进：传递config参数以支持统一尺寸估算
                    image_bytes, raw_annotations = await renderer.render(
                        table_model, css_string, resolved_params.postprocessing, self.config
                    )



                    # 转换标注格式
                    image_filename = f"{i:06d}.png"
                    final_annotations = annotation_converter.convert_to_final_format(
                        raw_annotations, table_model, image_filename
                    )

                    # 设置调试输出目录
                    debug_output_dir = None
                    if self.debug_mode and self.config.debug:
                        base_debug_dir = self.config.debug.debug_output_dir
                        debug_output_dir = os.path.join(base_debug_dir, f"debug_sample_{i:06d}")

                    image_augmentor = ImageAugmentor(
                        sample_seed,
                        debug_mode=self.debug_mode and self.config.debug is not None,
                        debug_output_dir=debug_output_dir
                    )

                    # V4.5修改：CSS模式下先应用透视变换（如果启用）
                    if (resolved_params.postprocessing.apply_perspective and
                        resolved_params.postprocessing.perspective_offset_ratio):

                        self.logger.info("CSS模式：开始应用OpenCV透视变换")
                        # 创建仅包含透视变换的参数
                        perspective_params = self._create_perspective_only_params(resolved_params.postprocessing)
                        image_bytes, final_annotations = image_augmentor.process(
                            image_bytes, final_annotations, perspective_params
                        )
                        self.logger.info("OpenCV透视变换完成")

                    # 然后应用其他非背景效果（模糊、噪声）
                    if (resolved_params.postprocessing.apply_blur or
                        resolved_params.postprocessing.apply_noise):

                        # 创建仅包含模糊和噪声的参数
                        blur_noise_params = self._create_blur_noise_params(resolved_params.postprocessing)
                        image_bytes, final_annotations = image_augmentor.process(
                            image_bytes, final_annotations, blur_noise_params
                        )
                        self.logger.debug("模糊和噪声效果应用完成")

                    # V4.5新增：CSS模式下的降质效果处理
                    if (resolved_params.postprocessing.apply_degradation_blur or
                        resolved_params.postprocessing.apply_degradation_noise or
                        resolved_params.postprocessing.apply_degradation_fade_global or
                        resolved_params.postprocessing.apply_degradation_fade_local or
                        resolved_params.postprocessing.apply_degradation_uneven_lighting or
                        resolved_params.postprocessing.apply_degradation_jpeg or
                        resolved_params.postprocessing.apply_degradation_darker_brighter or
                        resolved_params.postprocessing.apply_degradation_gamma_correction):

                        self.logger.info("CSS模式：开始应用降质效果")
                        # 创建包含降质效果的参数
                        degradation_params = self._create_degradation_params(resolved_params.postprocessing)
                        image_bytes, final_annotations = image_augmentor.process(
                            image_bytes, final_annotations, degradation_params
                        )
                        self.logger.info("CSS模式：降质效果应用完成")

                    # V4.2修改：CSS模式下的强制margin_control裁剪
                    if (resolved_params.postprocessing.margin_control and
                        resolved_params.postprocessing.margin_control.range_list):
                        self.logger.info("[MARGIN_CONTROL] CSS模式：开始强制margin_control裁剪")
                        image_bytes, final_annotations = self._apply_margin_control_crop(
                            image_bytes, final_annotations, resolved_params.postprocessing.margin_control, sample_seed
                        )

                else:
                    # 传统模式：先渲染表格，再进行图像后处理
                    # V5.1改进：传递config参数以支持统一尺寸估算
                    image_bytes, raw_annotations = await renderer.render(table_model, css_string, None, self.config)

                    # 转换标注格式
                    image_filename = f"{i:06d}.png"
                    self.logger.debug(f"原始标注: {raw_annotations}")
                    final_annotations = annotation_converter.convert_to_final_format(
                        raw_annotations, table_model, image_filename
                    )
                    self.logger.debug(f"转换后标注: {final_annotations}")

                    # 图像后处理（同时处理图像和标注）
                    if resolved_params.postprocessing is not None:
                        self.logger.debug(f"开始图像后处理，输入标注: {final_annotations}")

                        # 设置调试输出目录
                        debug_output_dir = None
                        if self.debug_mode and self.config.debug:
                            base_debug_dir = self.config.debug.debug_output_dir
                            debug_output_dir = os.path.join(base_debug_dir, f"debug_sample_{i:06d}")

                        image_augmentor = ImageAugmentor(
                            sample_seed,
                            debug_mode=self.debug_mode and self.config.debug is not None,
                            debug_output_dir=debug_output_dir
                        )
                        image_bytes, final_annotations = image_augmentor.process(
                            image_bytes, final_annotations, resolved_params.postprocessing
                        )
                        self.logger.debug(f"传统模式后处理完成，最终标注: {final_annotations}")

                # V3.1步骤5：准备元数据，使用resolved_params而不是原始config
                metadata = {
                    'resolved_params': resolved_params.dict(),  # 精确的、可复现的参数
                    'original_config': self.config.dict(),     # 保留原始配置用于参考
                    'sample_seed': sample_seed,
                    'sample_index': i
                }

                # V5.0新增：添加CSV采样信息到metadata
                if hasattr(table_model, 'csv_sampling_metadata') and table_model.csv_sampling_metadata:
                    csv_metadata = table_model.csv_sampling_metadata
                    metadata['csv_sampling_info'] = {
                        'source_file': csv_metadata.source_file,
                        'selected_columns': csv_metadata.selected_columns,
                        'selected_rows': csv_metadata.selected_rows,
                        'csv_structure': {
                            'total_columns': csv_metadata.csv_total_columns,
                            'total_data_rows': csv_metadata.csv_total_rows
                        },
                        'sampling_mode': csv_metadata.sampling_mode
                    }

                # V4.4.2修复：确保annotations可以JSON序列化
                serializable_annotations = self._make_json_serializable(final_annotations)

                # 保存文件
                self.logger.debug(f"保存文件前的最终标注: {final_annotations}")
                FileUtils.save_sample(
                    sample_index=i,
                    image_bytes=image_bytes,
                    annotations=serializable_annotations,
                    metadata=metadata,
                    output_dirs=output_dirs,
                    label_suffix=resolved_params.output.label_suffix
                )
                self.logger.debug(f"文件保存完成: 样本 {i}")

                # V5.1新增：记录性能监控信息
                sample_end_time = time.time()
                final_memory = self.performance_monitor.get_memory_usage()
                sample_duration = sample_end_time - sample_start_time
                memory_delta = final_memory - initial_memory

                # 记录性能信息
                perf_info = {
                    'sample_index': i,
                    'duration_seconds': round(sample_duration, 2),
                    'initial_memory_mb': round(initial_memory, 2),
                    'final_memory_mb': round(final_memory, 2),
                    'memory_delta_mb': round(memory_delta, 2),
                    'table_size': table_size_info
                }

                self.performance_monitor.record_sample_performance(perf_info)

                # 记录日志
                if table_size_info['is_large_table']:
                    self.logger.info(f"[LARGE_TABLE_PERF] 样本 {i + 1} 完成")
                    self.logger.info(f"[LARGE_TABLE_PERF] 耗时: {sample_duration:.2f}秒")
                    self.logger.info(f"[LARGE_TABLE_PERF] 内存变化: {memory_delta:+.2f}MB")

                    # 检查内存使用是否过高
                    if final_memory > self.memory_threshold_mb:
                        self.logger.warning(f"[LARGE_TABLE_PERF] 内存使用过高: {final_memory:.2f}MB")
                        self.logger.warning(f"[LARGE_TABLE_PERF] 建议减少并发或表格大小")

                self.logger.info(f"样本 {i + 1}/{num_samples} 生成完成 (耗时: {sample_duration:.2f}秒)")

                self.logger.info(f"样本 {i + 1} 生成完成")

        except Exception as e:
            # V5.1新增：增强的错误处理，特别针对大表格
            self.logger.error(f"生成样本时发生错误: {e}")

            # 分析错误类型并提供具体建议
            error_analysis = self._analyze_generation_error(e, locals().get('table_size_info', {}))

            for suggestion in error_analysis['suggestions']:
                self.logger.error(f"建议: {suggestion}")

            # 如果是大表格相关错误，提供详细信息
            if error_analysis['is_large_table_error']:
                self.logger.error("[LARGE_TABLE_ERROR] 这可能是由于表格过大导致的问题")
                self.logger.error("[LARGE_TABLE_ERROR] 请考虑以下解决方案:")
                self.logger.error("[LARGE_TABLE_ERROR] 1. 减少配置文件中的最大行列数")
                self.logger.error("[LARGE_TABLE_ERROR] 2. 增加系统内存")
                self.logger.error("[LARGE_TABLE_ERROR] 3. 减少并发生成的样本数量")

            # 记录性能监控信息（如果可用）
            if hasattr(self, 'performance_monitor'):
                summary = self.performance_monitor.get_performance_summary()
                self.logger.info(f"性能摘要: {summary}")

            raise  # 重新抛出异常

        finally:
            # V5.1新增：清理临时背景图文件
            try:
                self.resolver.cleanup_temp_files()
            except Exception as e:
                self.logger.warning(f"清理临时文件时发生错误: {e}")

            # 确保关闭渲染器
            await renderer.close_async()

        self.logger.info("所有样本生成完毕")





    def _create_perspective_only_params(self, postprocessing_params):
        """
        创建仅包含透视变换的后处理参数

        Args:
            postprocessing_params: 原始后处理参数

        Returns:
            仅包含透视变换的后处理参数
        """
        from .config import ResolvedPostprocessingParams

        return ResolvedPostprocessingParams(
            apply_blur=False,
            blur_radius=None,
            apply_noise=False,
            noise_intensity=None,
            apply_perspective=postprocessing_params.apply_perspective,
            perspective_offset_ratio=postprocessing_params.perspective_offset_ratio,
            apply_background=False,   # 背景图已在HTML中完成
            background_image_path=None,
            max_scale_factor=None,
            css_background_width=None,
            css_background_height=None,
            css_table_left=None,
            css_table_top=None,
            css_crop_width=None,
            css_crop_height=None,
            css_bg_offset_x=None,
            css_bg_offset_y=None,
            margin_control=postprocessing_params.margin_control,
            enable_transparency=postprocessing_params.enable_transparency,
            default_color_transparency=postprocessing_params.default_color_transparency,
            meaningful_color_transparency=postprocessing_params.meaningful_color_transparency,
            # V4.5新增：降质效果参数
            apply_degradation_blur=False,  # 透视变换阶段不应用降质效果
            apply_degradation_noise=False,
            apply_degradation_fade_global=False,
            apply_degradation_fade_local=False,
            apply_degradation_uneven_lighting=False,
            apply_degradation_jpeg=False,
            apply_degradation_darker_brighter=False,
            apply_degradation_gamma_correction=False
        )

    def _create_blur_noise_params(self, postprocessing_params):
        """
        创建仅包含模糊和噪声的后处理参数

        Args:
            postprocessing_params: 原始后处理参数

        Returns:
            仅包含模糊和噪声效果的后处理参数
        """
        from .config import ResolvedPostprocessingParams

        return ResolvedPostprocessingParams(
            apply_blur=postprocessing_params.apply_blur,
            blur_radius=postprocessing_params.blur_radius,
            apply_noise=postprocessing_params.apply_noise,
            noise_intensity=postprocessing_params.noise_intensity,
            apply_perspective=False,  # 透视变换已单独处理
            perspective_offset_ratio=None,
            apply_background=False,   # 背景图已在HTML中完成
            background_image_path=None,
            max_scale_factor=None,
            css_background_width=None,
            css_background_height=None,
            css_table_left=None,
            css_table_top=None,
            css_crop_width=None,
            css_crop_height=None,
            css_bg_offset_x=None,
            css_bg_offset_y=None,
            margin_control=postprocessing_params.margin_control,
            enable_transparency=postprocessing_params.enable_transparency,
            default_color_transparency=postprocessing_params.default_color_transparency,
            meaningful_color_transparency=postprocessing_params.meaningful_color_transparency,
            # V4.5新增：降质效果参数 - 在模糊噪声阶段不应用降质效果
            apply_degradation_blur=False,
            apply_degradation_noise=False,
            apply_degradation_fade_global=False,
            apply_degradation_fade_local=False,
            apply_degradation_uneven_lighting=False,
            apply_degradation_jpeg=False,
            apply_degradation_darker_brighter=False,
            apply_degradation_gamma_correction=False
        )

    def _create_degradation_params(self, postprocessing_params):
        """
        创建仅包含降质效果的后处理参数

        Args:
            postprocessing_params: 原始后处理参数

        Returns:
            仅包含降质效果的后处理参数
        """
        from .config import ResolvedPostprocessingParams

        return ResolvedPostprocessingParams(
            apply_blur=False,  # 模糊和噪声已单独处理
            blur_radius=None,
            apply_noise=False,
            noise_intensity=None,
            apply_perspective=False,  # 透视变换已单独处理
            perspective_offset_ratio=None,
            apply_background=False,   # 背景图已在HTML中完成
            background_image_path=None,
            max_scale_factor=None,
            css_background_width=None,
            css_background_height=None,
            css_table_left=None,
            css_table_top=None,
            css_crop_width=None,
            css_crop_height=None,
            css_bg_offset_x=None,
            css_bg_offset_y=None,
            margin_control=None,  # 边距裁剪将单独处理
            enable_transparency=postprocessing_params.enable_transparency,
            default_color_transparency=postprocessing_params.default_color_transparency,
            meaningful_color_transparency=postprocessing_params.meaningful_color_transparency,
            # V4.5新增：降质效果参数 - 传递所有降质效果
            apply_degradation_blur=postprocessing_params.apply_degradation_blur,
            apply_degradation_noise=postprocessing_params.apply_degradation_noise,
            apply_degradation_fade_global=postprocessing_params.apply_degradation_fade_global,
            apply_degradation_fade_local=postprocessing_params.apply_degradation_fade_local,
            apply_degradation_uneven_lighting=postprocessing_params.apply_degradation_uneven_lighting,
            apply_degradation_jpeg=postprocessing_params.apply_degradation_jpeg,
            apply_degradation_darker_brighter=postprocessing_params.apply_degradation_darker_brighter,
            apply_degradation_gamma_correction=postprocessing_params.apply_degradation_gamma_correction
        )

    def _apply_margin_control_crop(self, image_bytes: bytes, annotations: dict, margin_config, sample_seed: int) -> Tuple[bytes, dict]:
        """
        强制应用margin_control裁剪

        优先级顺序：
        1. 表格区域完整性（最高优先级，绝不能裁剪表格内容）
        2. margin_control配置（强制执行用户配置的边距）

        Args:
            image_bytes: 图像字节数据
            annotations: 标注数据
            margin_config: 边距控制配置
            sample_seed: 样本种子

        Returns:
            (裁剪后的图像字节, 更新后的标注)
        """
        try:
            from PIL import Image
            import io
            import copy
            import numpy as np

            # 将字节数据转换为PIL图像
            image = Image.open(io.BytesIO(image_bytes))
            original_width, original_height = image.size
            self.logger.info(f"[MARGIN_CONTROL] 原始图像尺寸: {original_width}x{original_height}")

            # V5.1调试：检查标注坐标范围
            if 'cells' in annotations:
                all_x_coords = []
                all_y_coords = []
                for cell in annotations['cells']:
                    if 'bbox' in cell:
                        bbox = cell['bbox']
                        for point_key in ['p1', 'p2', 'p3', 'p4']:
                            if point_key in bbox:
                                x, y = bbox[point_key]
                                all_x_coords.append(x)
                                all_y_coords.append(y)

                if all_x_coords and all_y_coords:
                    min_x, max_x = min(all_x_coords), max(all_x_coords)
                    min_y, max_y = min(all_y_coords), max(all_y_coords)
                    self.logger.info(f"[MARGIN_CONTROL] 裁剪前标注坐标范围:")
                    self.logger.info(f"[MARGIN_CONTROL] X: [{min_x:.1f}, {max_x:.1f}] (图像宽度: {original_width})")
                    self.logger.info(f"[MARGIN_CONTROL] Y: [{min_y:.1f}, {max_y:.1f}] (图像高度: {original_height})")

                    # 检查是否有坐标超出边界
                    out_of_bounds_x = [x for x in all_x_coords if x < 0 or x >= original_width]
                    out_of_bounds_y = [y for y in all_y_coords if y < 0 or y >= original_height]

                    if out_of_bounds_x or out_of_bounds_y:
                        self.logger.warning(f"[MARGIN_CONTROL] 发现超出边界的坐标:")
                        if out_of_bounds_x:
                            self.logger.warning(f"[MARGIN_CONTROL] 超界X坐标数量: {len(out_of_bounds_x)}")
                        if out_of_bounds_y:
                            self.logger.warning(f"[MARGIN_CONTROL] 超界Y坐标数量: {len(out_of_bounds_y)}")
                            self.logger.warning(f"[MARGIN_CONTROL] 最大Y坐标: {max(out_of_bounds_y):.1f}")
                    else:
                        self.logger.info(f"[MARGIN_CONTROL] 所有坐标均在图像边界内")

            # 检查是否有黑边信息
            black_edge_info = annotations.get('black_edge_info', None)

            if black_edge_info:
                self.logger.info("[MARGIN_CONTROL] 执行margin_control配置，优先级高于黑边移除")
                self.logger.info(f"[MARGIN_CONTROL] 检测到黑边信息: {black_edge_info['has_black_edges']}")
                crop_box = self._calculate_margin_control_priority_crop(
                    image, annotations, margin_config, sample_seed, black_edge_info
                )
            else:
                self.logger.info("[MARGIN_CONTROL] 执行margin_control配置，无黑边信息")
                crop_box = self._calculate_margin_control_crop_box(
                    image, annotations, margin_config, sample_seed
                )

            self.logger.info(f"[MARGIN_CONTROL] 最终裁剪区域: {crop_box}")

            # 执行裁剪
            cropped_image = image.crop(crop_box)
            self.logger.info(f"[MARGIN_CONTROL] 裁剪后尺寸: {cropped_image.size}")

            # 更新标注坐标
            updated_annotations = self._update_annotations_for_crop(annotations, crop_box)

            # V5.1验证：检查裁剪后的坐标范围
            cropped_width, cropped_height = cropped_image.size
            if 'cells' in updated_annotations:
                all_x_coords = []
                all_y_coords = []
                for cell in updated_annotations['cells']:
                    if 'bbox' in cell:
                        bbox = cell['bbox']
                        for point_key in ['p1', 'p2', 'p3', 'p4']:
                            if point_key in bbox:
                                x, y = bbox[point_key]
                                all_x_coords.append(x)
                                all_y_coords.append(y)

                if all_x_coords and all_y_coords:
                    min_x, max_x = min(all_x_coords), max(all_x_coords)
                    min_y, max_y = min(all_y_coords), max(all_y_coords)
                    self.logger.info(f"[MARGIN_CONTROL] 裁剪后标注坐标范围:")
                    self.logger.info(f"[MARGIN_CONTROL] X: [{min_x:.1f}, {max_x:.1f}] (图像宽度: {cropped_width})")
                    self.logger.info(f"[MARGIN_CONTROL] Y: [{min_y:.1f}, {max_y:.1f}] (图像高度: {cropped_height})")

                    # 最终验证：检查是否还有坐标超出边界
                    final_out_of_bounds_x = [x for x in all_x_coords if x < 0 or x >= cropped_width]
                    final_out_of_bounds_y = [y for y in all_y_coords if y < 0 or y >= cropped_height]

                    if final_out_of_bounds_x or final_out_of_bounds_y:
                        self.logger.error(f"[MARGIN_CONTROL] 裁剪后仍有坐标超出边界!")
                        if final_out_of_bounds_x:
                            self.logger.error(f"[MARGIN_CONTROL] 超界X坐标数量: {len(final_out_of_bounds_x)}")
                        if final_out_of_bounds_y:
                            self.logger.error(f"[MARGIN_CONTROL] 超界Y坐标数量: {len(final_out_of_bounds_y)}")
                    else:
                        self.logger.info(f"[MARGIN_CONTROL] ✓ 裁剪后所有坐标均在边界内")

            # 调试模式：保存裁剪过程的详细信息
            if self.debug_mode:
                self._save_crop_debug_info(image, cropped_image, annotations, updated_annotations,
                                         crop_box, None)

            # 转换回字节数据
            output_buffer = io.BytesIO()
            cropped_image.save(output_buffer, format='PNG')
            cropped_bytes = output_buffer.getvalue()

            return cropped_bytes, updated_annotations

        except Exception as e:
            self.logger.error(f"margin_control裁剪失败: {e}，返回原始图像")
            return image_bytes, annotations

    def _calculate_enhanced_crop_box_with_black_edge_priority(self, image, annotations: dict, margin_config, sample_seed: int, black_edge_info: dict) -> tuple:
        """
        V4.4.2新增：基于黑边信息的增强裁剪计算

        优先级处理：
        1. 确保表格区域完整性
        2. 尽可能移除黑边
        3. 在满足1、2的前提下考虑margin_control

        Args:
            image: PIL图像对象
            annotations: 标注数据
            margin_config: 边距控制配置
            sample_seed: 样本种子
            black_edge_info: 黑边信息

        Returns:
            最终裁剪区域 (left, top, right, bottom)
        """
        image_width, image_height = image.size

        # 优先级1：计算表格安全区域（绝对不能裁剪）
        table_safe_area = self._calculate_table_safe_area_from_annotations(annotations, image.size)
        self.logger.info(f"[ENHANCED_CROP] 表格安全区域: {table_safe_area}")

        # 优先级2：计算黑边移除需求（CSS模式增强）
        black_edge_free_area = self._calculate_edge_free_area_css_mode(
            black_edge_info, image.size
        )
        self.logger.info(f"[ENHANCED_CROP] 黑边移除目标区域: {black_edge_free_area}")

        # 优先级3：计算margin_control期望区域
        margin_preferred_area = self._calculate_margin_preferred_area(
            image, annotations, margin_config, sample_seed
        )
        self.logger.info(f"[ENHANCED_CROP] margin_control期望区域: {margin_preferred_area}")

        # 多约束求解：找到满足所有约束的最优裁剪区域
        final_crop_area = self._resolve_crop_constraints(
            table_safe_area,        # 硬约束：必须满足
            black_edge_free_area,   # 软约束1：尽量满足
            margin_preferred_area,  # 软约束2：参考满足
            image.size
        )

        self.logger.info(f"[ENHANCED_CROP] 约束求解结果: {final_crop_area}")

        return final_crop_area

    def _calculate_margin_control_crop_box(self, image, annotations: dict, margin_config, sample_seed: int) -> tuple:
        """
        强制执行margin_control配置的裁剪计算方法（带表格安全保护）
        V5.2增强：添加表格位置异常检测和自动修正

        Args:
            image: PIL图像对象
            annotations: 标注数据
            margin_config: 边距控制配置
            sample_seed: 样本种子

        Returns:
            裁剪区域 (left, top, right, bottom)
        """
        # V5.2新增：检测表格位置是否异常
        table_position_status = self._detect_table_position_anomaly(annotations, image.size)
        self.logger.info(f"[MARGIN_CONTROL_V52] 表格位置检测: {table_position_status}")

        if table_position_status['is_anomaly']:
            self.logger.warning(f"[MARGIN_CONTROL_V52] 检测到表格位置异常: {table_position_status['reason']}")
            # 使用强制居中裁剪策略
            return self._calculate_force_center_crop_box(annotations, margin_config, sample_seed, image.size)

        # 正常情况：使用原有逻辑
        # 1. 计算表格的真实边界
        table_bounds = self._calculate_table_bounds(annotations)
        self.logger.info(f"[MARGIN_CONTROL_CROP] 表格真实边界: {table_bounds}")

        # 2. 计算表格安全区域
        table_safe_area = self._calculate_table_safe_area_from_annotations(annotations, image.size)
        self.logger.info(f"[MARGIN_CONTROL_CROP] 表格安全区域: {table_safe_area}")

        # 3. 根据配置选择边距（传递表格边界信息以支持相对边距）
        margin = self._select_margin_from_config(margin_config, sample_seed, table_bounds)
        self.logger.info(f"[MARGIN_CONTROL_CROP] 选择边距: {margin}")

        # 4. 计算margin_control期望的裁剪区域
        margin_crop_box = self._calculate_crop_box(table_bounds, margin, image.size)
        self.logger.info(f"[MARGIN_CONTROL_CROP] margin期望区域: {margin_crop_box}")

        # 5. 强制确保包含表格安全区域
        safe_crop_box = self._force_include_table_safety(margin_crop_box, table_safe_area, image.size)
        self.logger.info(f"[MARGIN_CONTROL_CROP] 安全保护后区域: {safe_crop_box}")

        # 6. 最终安全检查
        final_crop_box = self._final_table_safety_check(safe_crop_box, table_safe_area, image.size)
        self.logger.info(f"[MARGIN_CONTROL_CROP] 最终裁剪区域: {final_crop_box}")

        return final_crop_box

    def _detect_table_position_anomaly(self, annotations: dict, image_size: tuple) -> dict:
        """
        V5.2新增：检测表格位置是否异常

        检测表格是否被错误定位到图像边缘，这通常发生在大容器场景下

        Args:
            annotations: 标注数据
            image_size: 图像尺寸 (width, height)

        Returns:
            检测结果字典: {
                'is_anomaly': bool,
                'reason': str,
                'table_center': tuple,
                'image_center': tuple,
                'distance_from_center': float,
                'distance_ratio': float
            }
        """
        try:
            image_width, image_height = image_size

            # 计算图像中心
            image_center_x = image_width / 2
            image_center_y = image_height / 2

            # 计算表格边界和中心
            table_bounds = self._calculate_table_bounds(annotations)
            table_center_x = (table_bounds['min_x'] + table_bounds['max_x']) / 2
            table_center_y = (table_bounds['min_y'] + table_bounds['max_y']) / 2

            # 计算表格中心与图像中心的距离
            distance_from_center = ((table_center_x - image_center_x) ** 2 +
                                  (table_center_y - image_center_y) ** 2) ** 0.5

            # 计算相对距离比例（相对于图像对角线长度）
            image_diagonal = (image_width ** 2 + image_height ** 2) ** 0.5
            distance_ratio = distance_from_center / image_diagonal

            # 判断异常的阈值
            ANOMALY_DISTANCE_THRESHOLD = 0.25  # 如果表格中心距离图像中心超过对角线的25%，认为异常

            is_anomaly = distance_ratio > ANOMALY_DISTANCE_THRESHOLD

            # 确定异常原因
            reason = ""
            if is_anomaly:
                if table_center_x < image_width * 0.2:
                    reason = "表格过于靠近左边缘"
                elif table_center_x > image_width * 0.8:
                    reason = "表格过于靠近右边缘"
                elif table_center_y < image_height * 0.2:
                    reason = "表格过于靠近上边缘"
                elif table_center_y > image_height * 0.8:
                    reason = "表格过于靠近下边缘"
                else:
                    reason = f"表格中心偏离图像中心过远 (距离比例: {distance_ratio:.3f})"
            else:
                reason = "表格位置正常"

            result = {
                'is_anomaly': is_anomaly,
                'reason': reason,
                'table_center': (table_center_x, table_center_y),
                'image_center': (image_center_x, image_center_y),
                'distance_from_center': distance_from_center,
                'distance_ratio': distance_ratio
            }

            self.logger.info(f"[POSITION_DETECT] 表格中心: ({table_center_x:.1f}, {table_center_y:.1f})")
            self.logger.info(f"[POSITION_DETECT] 图像中心: ({image_center_x:.1f}, {image_center_y:.1f})")
            self.logger.info(f"[POSITION_DETECT] 距离比例: {distance_ratio:.3f}, 阈值: {ANOMALY_DISTANCE_THRESHOLD}")

            return result

        except Exception as e:
            self.logger.error(f"[POSITION_DETECT] 表格位置检测失败: {e}")
            return {
                'is_anomaly': False,
                'reason': f"检测失败: {e}",
                'table_center': (0, 0),
                'image_center': (0, 0),
                'distance_from_center': 0,
                'distance_ratio': 0
            }

    def _calculate_force_center_crop_box(self, annotations: dict, margin_config, sample_seed: int, image_size: tuple) -> tuple:
        """
        V5.2新增：强制居中裁剪策略

        当检测到表格位置异常时，强制将表格居中并应用合理的边距

        Args:
            annotations: 标注数据
            margin_config: 边距控制配置
            sample_seed: 样本种子
            image_size: 图像尺寸

        Returns:
            裁剪区域 (left, top, right, bottom)
        """
        image_width, image_height = image_size

        # 计算表格边界
        table_bounds = self._calculate_table_bounds(annotations)
        table_width = table_bounds['max_x'] - table_bounds['min_x']
        table_height = table_bounds['max_y'] - table_bounds['min_y']

        self.logger.info(f"[FORCE_CENTER] 表格尺寸: {table_width:.1f}x{table_height:.1f}")

        # 选择边距
        margin = self._select_margin_from_config(margin_config, sample_seed, table_bounds)
        self.logger.info(f"[FORCE_CENTER] 选择边距: {margin}")

        # 计算理想的裁剪尺寸（表格 + 边距）
        ideal_crop_width = table_width + margin * 2
        ideal_crop_height = table_height + margin * 2

        # 确保裁剪尺寸不超过图像尺寸
        final_crop_width = min(ideal_crop_width, image_width)
        final_crop_height = min(ideal_crop_height, image_height)

        # 计算居中的裁剪位置
        crop_left = max(0, (image_width - final_crop_width) // 2)
        crop_top = max(0, (image_height - final_crop_height) // 2)
        crop_right = crop_left + final_crop_width
        crop_bottom = crop_top + final_crop_height

        # 确保不超出图像边界
        crop_right = min(crop_right, image_width)
        crop_bottom = min(crop_bottom, image_height)

        result = (int(crop_left), int(crop_top), int(crop_right), int(crop_bottom))

        self.logger.info(f"[FORCE_CENTER] 理想裁剪尺寸: {ideal_crop_width:.1f}x{ideal_crop_height:.1f}")
        self.logger.info(f"[FORCE_CENTER] 最终裁剪尺寸: {final_crop_width:.1f}x{final_crop_height:.1f}")
        self.logger.info(f"[FORCE_CENTER] 强制居中裁剪区域: {result}")

        return result

    def _calculate_margin_control_priority_crop(self, image, annotations: dict, margin_config, sample_seed: int, black_edge_info: dict) -> tuple:
        """
        margin_control优先的裁剪计算方法

        优先级：
        1. 表格内容完整性（绝对不能破坏）
        2. margin_control配置（强制执行）
        3. 黑边移除（在满足1、2的前提下尽量执行）

        Args:
            image: PIL图像对象
            annotations: 标注数据
            margin_config: 边距控制配置
            sample_seed: 样本种子
            black_edge_info: 黑边信息

        Returns:
            裁剪区域 (left, top, right, bottom)
        """
        # 1. 计算表格安全区域（硬约束）
        table_safe_area = self._calculate_table_safe_area_from_annotations(annotations, image.size)
        self.logger.info(f"[MARGIN_PRIORITY] 表格安全区域: {table_safe_area}")

        # 2. 计算margin_control期望区域（高优先级约束）
        table_bounds = self._calculate_table_bounds(annotations)
        margin = self._select_margin_from_config(margin_config, sample_seed)
        margin_required_area = self._calculate_crop_box(table_bounds, margin, image.size)
        self.logger.info(f"[MARGIN_PRIORITY] margin_control要求区域: {margin_required_area} (边距: {margin})")

        # 3. 获取黑边移除的建议区域（低优先级约束）
        if black_edge_info['has_black_edges']:
            black_edge_free_area = black_edge_info['content_bbox']
            self.logger.info(f"[MARGIN_PRIORITY] 黑边移除建议区域: {black_edge_free_area}")
        else:
            # 没有黑边，使用整个图像
            black_edge_free_area = (0, 0, image.size[0], image.size[1])
            self.logger.info("[MARGIN_PRIORITY] 无黑边，使用整个图像区域")

        # 4. 按优先级求解：优先满足margin_control，在此基础上尽量移除黑边
        final_crop_area = self._resolve_margin_priority_constraints(
            table_safe_area,        # 硬约束：必须满足
            margin_required_area,   # 高优先级：强制满足
            black_edge_free_area,   # 低优先级：尽量满足
            image.size
        )

        self.logger.info(f"[MARGIN_PRIORITY] 最终裁剪区域: {final_crop_area}")

        return final_crop_area

    def _resolve_margin_priority_constraints(self, table_safe_area: tuple, margin_required_area: tuple,
                                           black_edge_free_area: tuple, image_size: tuple) -> tuple:
        """
        margin优先的约束求解：优先满足margin_control，在此基础上尽量移除黑边

        Args:
            table_safe_area: 表格安全区域（硬约束）
            margin_required_area: margin_control要求区域（高优先级约束）
            black_edge_free_area: 黑边移除建议区域（低优先级约束）
            image_size: 图像尺寸

        Returns:
            最终裁剪区域 (left, top, right, bottom)
        """
        image_width, image_height = image_size

        # 步骤1：确保表格安全区域（硬约束，绝对不能违反）
        safe_left, safe_top, safe_right, safe_bottom = table_safe_area
        self.logger.info(f"[MARGIN_PRIORITY_SOLVE] 硬约束-表格安全区域: {table_safe_area}")

        # 步骤2：强制满足margin_control要求（高优先级约束）
        margin_left, margin_top, margin_right, margin_bottom = margin_required_area
        self.logger.info(f"[MARGIN_PRIORITY_SOLVE] 高优先级-margin_control要求: {margin_required_area}")

        # 步骤3：强制确保任何区域都必须包含表格安全区域（最高优先级）
        # 首先扩展margin区域以确保包含表格安全区域
        safe_margin_area = self._force_include_table_safety(
            margin_required_area, table_safe_area, image_size
        )
        self.logger.info(f"[MARGIN_PRIORITY_SOLVE] 强制包含表格后的margin区域: {safe_margin_area}")

        # 步骤4：在确保表格安全的基础上，尝试移除黑边
        final_area = self._safe_optimize_black_edge(
            safe_margin_area, black_edge_free_area, table_safe_area, image_size
        )

        # 步骤5：最终验证，确保表格安全区域被完全包含
        final_area = self._final_table_safety_check(final_area, table_safe_area, image_size)

        return final_area

    def _force_include_table_safety(self, margin_area: tuple, table_safe_area: tuple, image_size: tuple) -> tuple:
        """
        强制确保区域包含表格安全区域（最高优先级）

        Args:
            margin_area: margin_control要求的区域
            table_safe_area: 表格安全区域（绝对不能裁剪）
            image_size: 图像尺寸

        Returns:
            强制包含表格安全区域的区域
        """
        margin_left, margin_top, margin_right, margin_bottom = margin_area
        safe_left, safe_top, safe_right, safe_bottom = table_safe_area
        image_width, image_height = image_size

        # 强制扩展以包含表格安全区域
        forced_left = min(margin_left, safe_left)
        forced_top = min(margin_top, safe_top)
        forced_right = max(margin_right, safe_right)
        forced_bottom = max(margin_bottom, safe_bottom)

        # 确保不超出图像边界
        forced_left = max(0, forced_left)
        forced_top = max(0, forced_top)
        forced_right = min(image_width, forced_right)
        forced_bottom = min(image_height, forced_bottom)

        forced_area = (int(forced_left), int(forced_top), int(forced_right), int(forced_bottom))

        self.logger.info(f"[FORCE_TABLE_SAFETY] 原margin区域: {margin_area}")
        self.logger.info(f"[FORCE_TABLE_SAFETY] 表格安全区域: {table_safe_area}")
        self.logger.info(f"[FORCE_TABLE_SAFETY] 强制包含后区域: {forced_area}")

        return forced_area

    def _safe_optimize_black_edge(self, safe_margin_area: tuple, black_edge_free_area: tuple,
                                 table_safe_area: tuple, image_size: tuple) -> tuple:
        """
        在确保表格安全的前提下优化黑边移除

        Args:
            safe_margin_area: 已确保包含表格的margin区域
            black_edge_free_area: 黑边移除建议区域
            table_safe_area: 表格安全区域（绝对不能裁剪）
            image_size: 图像尺寸

        Returns:
            优化后的区域
        """
        margin_left, margin_top, margin_right, margin_bottom = safe_margin_area
        black_left, black_top, black_right, black_bottom = black_edge_free_area
        safe_left, safe_top, safe_right, safe_bottom = table_safe_area
        image_width, image_height = image_size

        # 在safe_margin_area内，尝试向黑边移除区域靠拢，但绝不能影响表格安全区域
        optimized_left = max(margin_left, black_left)
        optimized_top = max(margin_top, black_top)
        optimized_right = min(margin_right, black_right)
        optimized_bottom = min(margin_bottom, black_bottom)

        # 关键检查：确保优化后的区域仍然包含表格安全区域
        if not (optimized_left <= safe_left and optimized_top <= safe_top and
                optimized_right >= safe_right and optimized_bottom >= safe_bottom):
            self.logger.warning("[SAFE_BLACK_EDGE_OPTIMIZE] 黑边优化会影响表格安全，放弃优化")
            return safe_margin_area

        # 确保优化后的区域仍然有效
        if optimized_left >= optimized_right or optimized_top >= optimized_bottom:
            self.logger.warning("[SAFE_BLACK_EDGE_OPTIMIZE] 黑边优化导致无效区域，使用原区域")
            return safe_margin_area

        # 确保不超出图像边界
        optimized_left = max(0, optimized_left)
        optimized_top = max(0, optimized_top)
        optimized_right = min(image_width, optimized_right)
        optimized_bottom = min(image_height, optimized_bottom)

        optimized_area = (int(optimized_left), int(optimized_top), int(optimized_right), int(optimized_bottom))

        self.logger.info(f"[SAFE_BLACK_EDGE_OPTIMIZE] 安全margin区域: {safe_margin_area}")
        self.logger.info(f"[SAFE_BLACK_EDGE_OPTIMIZE] 黑边建议区域: {black_edge_free_area}")
        self.logger.info(f"[SAFE_BLACK_EDGE_OPTIMIZE] 优化后区域: {optimized_area}")

        return optimized_area

    def _final_table_safety_check(self, final_area: tuple, table_safe_area: tuple, image_size: tuple) -> tuple:
        """
        V5.1增强：最终的表格安全检查，确保表格区域绝对不会被裁剪，特别保护大表格

        Args:
            final_area: 最终计算的区域
            table_safe_area: 表格安全区域
            image_size: 图像尺寸

        Returns:
            经过最终安全检查的区域
        """
        final_left, final_top, final_right, final_bottom = final_area
        safe_left, safe_top, safe_right, safe_bottom = table_safe_area
        image_width, image_height = image_size

        # V5.1新增：检测是否为大表格
        table_width = safe_right - safe_left
        table_height = safe_bottom - safe_top
        is_large_table = table_width > 2000 or table_height > 1500

        if is_large_table:
            self.logger.info(f"[LARGE_TABLE_SAFETY] 检测到大表格: {table_width:.0f}x{table_height:.0f}")
            # 为大表格提供额外的安全边距
            extra_margin = 10  # 额外的安全边距
            safe_left -= extra_margin
            safe_top -= extra_margin
            safe_right += extra_margin
            safe_bottom += extra_margin
            self.logger.info(f"[LARGE_TABLE_SAFETY] 应用额外安全边距: {extra_margin}px")

        # 最终检查：确保表格安全区域被完全包含
        if not (final_left <= safe_left and final_top <= safe_top and
                final_right >= safe_right and final_bottom >= safe_bottom):

            if is_large_table:
                self.logger.error("[LARGE_TABLE_SAFETY] 检测到大表格安全区域可能被裁剪！强制修正")
            else:
                self.logger.error("[FINAL_SAFETY_CHECK] 检测到表格安全区域可能被裁剪！强制修正")

            # 强制修正以包含表格安全区域
            corrected_left = min(final_left, safe_left)
            corrected_top = min(final_top, safe_top)
            corrected_right = max(final_right, safe_right)
            corrected_bottom = max(final_bottom, safe_bottom)

            # V5.1改进：对于大表格，确保修正后的区域不会过小
            if is_large_table:
                # 确保修正后的区域至少包含表格的完整尺寸
                min_width = table_width + 40  # 表格宽度 + 最小边距
                min_height = table_height + 40  # 表格高度 + 最小边距

                current_width = corrected_right - corrected_left
                current_height = corrected_bottom - corrected_top

                if current_width < min_width:
                    # 扩展宽度
                    expand_width = min_width - current_width
                    corrected_left = max(0, corrected_left - expand_width // 2)
                    corrected_right = min(image_width, corrected_right + expand_width // 2)
                    self.logger.warning(f"[LARGE_TABLE_SAFETY] 扩展宽度: {expand_width}px")

                if current_height < min_height:
                    # 扩展高度
                    expand_height = min_height - current_height
                    corrected_top = max(0, corrected_top - expand_height // 2)
                    corrected_bottom = min(image_height, corrected_bottom + expand_height // 2)
                    self.logger.warning(f"[LARGE_TABLE_SAFETY] 扩展高度: {expand_height}px")

            # 确保不超出图像边界
            corrected_left = max(0, corrected_left)
            corrected_top = max(0, corrected_top)
            corrected_right = min(image_width, corrected_right)
            corrected_bottom = min(image_height, corrected_bottom)

            corrected_area = (int(corrected_left), int(corrected_top), int(corrected_right), int(corrected_bottom))

            if is_large_table:
                self.logger.error(f"[LARGE_TABLE_SAFETY] 原区域: {final_area}")
                self.logger.error(f"[LARGE_TABLE_SAFETY] 表格安全区域: {table_safe_area}")
                self.logger.error(f"[LARGE_TABLE_SAFETY] 修正后区域: {corrected_area}")
            else:
                self.logger.error(f"[FINAL_SAFETY_CHECK] 原区域: {final_area}")
                self.logger.error(f"[FINAL_SAFETY_CHECK] 表格安全区域: {table_safe_area}")
                self.logger.error(f"[FINAL_SAFETY_CHECK] 修正后区域: {corrected_area}")

            return corrected_area
        else:
            if is_large_table:
                self.logger.info("[LARGE_TABLE_SAFETY] 大表格安全区域检查通过")
            else:
                self.logger.info("[FINAL_SAFETY_CHECK] 表格安全区域检查通过")
            return final_area

    def _optimize_black_edge_within_margin_area(self, margin_area: tuple, black_edge_free_area: tuple, image_size: tuple) -> tuple:
        """
        在margin_control区域内尽量移除黑边

        Args:
            margin_area: margin_control要求的区域
            black_edge_free_area: 黑边移除建议区域
            image_size: 图像尺寸

        Returns:
            优化后的裁剪区域
        """
        margin_left, margin_top, margin_right, margin_bottom = margin_area
        black_left, black_top, black_right, black_bottom = black_edge_free_area
        image_width, image_height = image_size

        # 在margin区域内，尽量向黑边移除区域靠拢
        optimized_left = max(margin_left, black_left)
        optimized_top = max(margin_top, black_top)
        optimized_right = min(margin_right, black_right)
        optimized_bottom = min(margin_bottom, black_bottom)

        # 确保优化后的区域仍然有效
        if optimized_left >= optimized_right or optimized_top >= optimized_bottom:
            self.logger.warning("[BLACK_EDGE_OPTIMIZE] 黑边优化失败，使用原margin区域")
            return margin_area

        # 确保不超出图像边界
        optimized_left = max(0, optimized_left)
        optimized_top = max(0, optimized_top)
        optimized_right = min(image_width, optimized_right)
        optimized_bottom = min(image_height, optimized_bottom)

        optimized_area = (int(optimized_left), int(optimized_top), int(optimized_right), int(optimized_bottom))

        self.logger.info(f"[BLACK_EDGE_OPTIMIZE] margin区域: {margin_area}")
        self.logger.info(f"[BLACK_EDGE_OPTIMIZE] 黑边建议区域: {black_edge_free_area}")
        self.logger.info(f"[BLACK_EDGE_OPTIMIZE] 优化后区域: {optimized_area}")

        return optimized_area

    def _adjust_margin_area_for_table_safety(self, margin_area: tuple, table_safe_area: tuple, image_size: tuple) -> tuple:
        """
        调整margin区域以确保包含表格安全区域

        Args:
            margin_area: 原始margin区域
            table_safe_area: 表格安全区域
            image_size: 图像尺寸

        Returns:
            调整后的margin区域
        """
        margin_left, margin_top, margin_right, margin_bottom = margin_area
        safe_left, safe_top, safe_right, safe_bottom = table_safe_area
        image_width, image_height = image_size

        # 扩展margin区域以包含表格安全区域
        adjusted_left = min(margin_left, safe_left)
        adjusted_top = min(margin_top, safe_top)
        adjusted_right = max(margin_right, safe_right)
        adjusted_bottom = max(margin_bottom, safe_bottom)

        # 确保不超出图像边界
        adjusted_left = max(0, adjusted_left)
        adjusted_top = max(0, adjusted_top)
        adjusted_right = min(image_width, adjusted_right)
        adjusted_bottom = min(image_height, adjusted_bottom)

        adjusted_area = (int(adjusted_left), int(adjusted_top), int(adjusted_right), int(adjusted_bottom))

        self.logger.info(f"[MARGIN_ADJUST] 原始margin区域: {margin_area}")
        self.logger.info(f"[MARGIN_ADJUST] 表格安全区域: {table_safe_area}")
        self.logger.info(f"[MARGIN_ADJUST] 调整后margin区域: {adjusted_area}")

        return adjusted_area

    def _resolve_margin_control_with_black_edge_constraints(self, table_safe_area: tuple, black_edge_free_area: tuple,
                                                          margin_preferred_area: tuple, expected_margin: int, image_size: tuple) -> tuple:
        """
        黑边优先的约束求解：优先移除黑边，在无黑边区域内尽量满足margin_control

        Args:
            table_safe_area: 表格安全区域（硬约束）
            black_edge_free_area: 黑边移除区域（软约束1）
            margin_preferred_area: margin_control期望区域（软约束2）
            expected_margin: 期望的边距值
            image_size: 图像尺寸

        Returns:
            最终裁剪区域 (left, top, right, bottom)
        """
        image_width, image_height = image_size

        # 步骤1：确保表格安全区域（硬约束）
        safe_left, safe_top, safe_right, safe_bottom = table_safe_area
        self.logger.info(f"[CONSTRAINT_SOLVE_V2] 硬约束-表格安全区域: {table_safe_area}")

        # 步骤2：优先考虑黑边移除区域
        black_left, black_top, black_right, black_bottom = black_edge_free_area
        self.logger.info(f"[CONSTRAINT_SOLVE_V2] 软约束1-黑边移除区域: {black_edge_free_area}")

        # 步骤3：检查黑边移除区域是否包含表格安全区域
        if (black_left <= safe_left and black_top <= safe_top and
            black_right >= safe_right and black_bottom >= safe_bottom):
            # 黑边移除区域完全包含表格，可以安全使用
            self.logger.info("[CONSTRAINT_SOLVE_V2] 黑边移除区域包含表格安全区域")

            # 在黑边移除区域内尝试满足margin_control
            optimized_area = self._optimize_margin_within_black_edge_free_area(
                table_safe_area, black_edge_free_area, expected_margin, image_size
            )

            return optimized_area
        else:
            # 黑边移除区域与表格安全区域冲突，优先保证表格安全
            self.logger.warning("[CONSTRAINT_SOLVE_V2] 黑边移除区域与表格安全冲突，优先保证表格安全")

            # 扩展表格安全区域以包含必要的边距，但避免明显的黑边
            safe_area_with_margin = self._expand_safe_area_avoiding_black_edges(
                table_safe_area, black_edge_free_area, expected_margin, image_size
            )

            return safe_area_with_margin

    def _optimize_margin_within_black_edge_free_area(self, table_safe_area: tuple, black_edge_free_area: tuple,
                                                   expected_margin: int, image_size: tuple) -> tuple:
        """
        在黑边移除区域内优化边距，尽量满足margin_control配置

        Args:
            table_safe_area: 表格安全区域
            black_edge_free_area: 黑边移除区域
            expected_margin: 期望的边距值
            image_size: 图像尺寸

        Returns:
            优化后的裁剪区域
        """
        safe_left, safe_top, safe_right, safe_bottom = table_safe_area
        black_left, black_top, black_right, black_bottom = black_edge_free_area
        image_width, image_height = image_size

        # 计算在黑边移除区域内能达到的最大边距
        max_left_margin = safe_left - black_left
        max_top_margin = safe_top - black_top
        max_right_margin = black_right - safe_right
        max_bottom_margin = black_bottom - safe_bottom

        # 尝试使用期望边距，但不能超出黑边移除区域
        actual_left_margin = min(expected_margin, max_left_margin)
        actual_top_margin = min(expected_margin, max_top_margin)
        actual_right_margin = min(expected_margin, max_right_margin)
        actual_bottom_margin = min(expected_margin, max_bottom_margin)

        # 计算最终裁剪区域
        final_left = max(black_left, safe_left - actual_left_margin)
        final_top = max(black_top, safe_top - actual_top_margin)
        final_right = min(black_right, safe_right + actual_right_margin)
        final_bottom = min(black_bottom, safe_bottom + actual_bottom_margin)

        # 确保不超出图像边界
        final_left = max(0, final_left)
        final_top = max(0, final_top)
        final_right = min(image_width, final_right)
        final_bottom = min(image_height, final_bottom)

        optimized_area = (int(final_left), int(final_top), int(final_right), int(final_bottom))

        self.logger.info(f"[MARGIN_OPTIMIZE] 期望边距: {expected_margin}")
        self.logger.info(f"[MARGIN_OPTIMIZE] 实际边距: 左{actual_left_margin}, 上{actual_top_margin}, 右{actual_right_margin}, 下{actual_bottom_margin}")
        self.logger.info(f"[MARGIN_OPTIMIZE] 优化结果: {optimized_area}")

        return optimized_area

    def _expand_safe_area_avoiding_black_edges(self, table_safe_area: tuple, black_edge_free_area: tuple,
                                             expected_margin: int, image_size: tuple) -> tuple:
        """
        扩展表格安全区域以包含边距，但尽量避免黑边区域

        当黑边移除区域无法完全包含表格时使用此方法

        Args:
            table_safe_area: 表格安全区域
            black_edge_free_area: 黑边移除区域
            expected_margin: 期望的边距值
            image_size: 图像尺寸

        Returns:
            扩展后的安全区域
        """
        safe_left, safe_top, safe_right, safe_bottom = table_safe_area
        black_left, black_top, black_right, black_bottom = black_edge_free_area
        image_width, image_height = image_size

        # 在每个方向上，优先使用黑边移除区域的边界，其次考虑期望边距

        # 左边界：优先使用黑边移除区域的左边界，如果不够再扩展
        if black_left <= safe_left:
            final_left = black_left  # 使用黑边移除区域的边界
        else:
            final_left = max(0, safe_left - expected_margin)  # 使用期望边距

        # 上边界：同样逻辑
        if black_top <= safe_top:
            final_top = black_top
        else:
            final_top = max(0, safe_top - expected_margin)

        # 右边界：同样逻辑
        if black_right >= safe_right:
            final_right = black_right
        else:
            final_right = min(image_width, safe_right + expected_margin)

        # 下边界：同样逻辑
        if black_bottom >= safe_bottom:
            final_bottom = black_bottom
        else:
            final_bottom = min(image_height, safe_bottom + expected_margin)

        expanded_area = (int(final_left), int(final_top), int(final_right), int(final_bottom))

        self.logger.info(f"[SAFE_EXPAND] 表格安全区域: {table_safe_area}")
        self.logger.info(f"[SAFE_EXPAND] 黑边移除区域: {black_edge_free_area}")
        self.logger.info(f"[SAFE_EXPAND] 扩展后区域: {expanded_area}")

        return expanded_area

    def _calculate_traditional_crop_box(self, image, annotations: dict, margin_config, sample_seed: int) -> tuple:
        """
        传统的裁剪计算方法（保持向后兼容）
        """
        # 1. 计算表格的真实边界
        table_bounds = self._calculate_table_bounds(annotations)
        self.logger.info(f"[TRADITIONAL_CROP] 表格真实边界: {table_bounds}")

        # 2. 根据配置选择边距
        margin = self._select_margin_from_config(margin_config, sample_seed)
        self.logger.info(f"[TRADITIONAL_CROP] 选择边距: {margin}")

        # 3. 计算裁剪区域
        crop_box = self._calculate_crop_box(table_bounds, margin, image.size)
        self.logger.info(f"[TRADITIONAL_CROP] 裁剪区域: {crop_box}")

        return crop_box

    def _calculate_table_safe_area_from_annotations(self, annotations: dict, image_size: tuple) -> tuple:
        """
        从标注数据计算表格安全区域

        Args:
            annotations: 标注数据
            image_size: 图像尺寸 (width, height)

        Returns:
            表格安全区域 (left, top, right, bottom)
        """
        if not annotations.get('cells'):
            # 没有单元格数据，返回整个图像
            return (0, 0, image_size[0], image_size[1])

        all_x = []
        all_y = []

        for cell in annotations['cells']:
            bbox = cell['bbox']
            # 提取四个角点的坐标
            for point_key in ['p1', 'p2', 'p3', 'p4']:
                if point_key in bbox:
                    all_x.append(bbox[point_key][0])
                    all_y.append(bbox[point_key][1])

        if not all_x or not all_y:
            return (0, 0, image_size[0], image_size[1])

        # 计算表格的实际边界，添加更大的安全边距以绝对保护表格内容
        safety_margin = 10  # 增加安全边距到10像素，确保表格内容绝对不被裁剪
        left = int(max(0, int(min(all_x)) - safety_margin))
        top = int(max(0, int(min(all_y)) - safety_margin))
        right = int(min(image_size[0], int(max(all_x)) + safety_margin))
        bottom = int(min(image_size[1], int(max(all_y)) + safety_margin))

        safe_area = (left, top, right, bottom)
        self.logger.info(f"[TABLE_SAFE_AREA] 表格边界: min_x={min(all_x)}, min_y={min(all_y)}, max_x={max(all_x)}, max_y={max(all_y)}")
        self.logger.info(f"[TABLE_SAFE_AREA] 安全边距: {safety_margin}像素")
        self.logger.info(f"[TABLE_SAFE_AREA] 最终安全区域: {safe_area}")

        return safe_area

    def _calculate_margin_preferred_area(self, image, annotations: dict, margin_config, sample_seed: int) -> tuple:
        """
        计算margin_control的期望区域
        """
        # 使用传统方法计算margin_control期望的裁剪区域
        table_bounds = self._calculate_table_bounds(annotations)
        margin = self._select_margin_from_config(margin_config, sample_seed)
        margin_crop_box = self._calculate_crop_box(table_bounds, margin, image.size)

        return margin_crop_box

    def _calculate_edge_free_area_css_mode(self, black_edge_info: dict, image_size: tuple) -> tuple:
        """
        CSS模式专用：计算需要移除的边缘异常区域

        结合预测的黑边区域和实际检测的边缘异常

        Args:
            black_edge_info: 黑边信息（包含预测和实际检测结果）
            image_size: 图像尺寸

        Returns:
            无边缘异常的目标区域 (left, top, right, bottom)
        """
        image_width, image_height = image_size

        # 从预测的内容边界框开始
        if black_edge_info['has_black_edges']:
            predicted_area = black_edge_info['content_bbox']
            self.logger.info(f"[CSS_EDGE_CALC] 预测的内容区域: {predicted_area}")
        else:
            predicted_area = (0, 0, image_width, image_height)
            self.logger.info("[CSS_EDGE_CALC] 预测无黑边，使用整个图像")

        # 结合实际边缘异常检测结果
        if 'actual_edge_detection' in black_edge_info:
            actual_edge = black_edge_info['actual_edge_detection']

            # 检查是否有任何边缘异常（降低阈值，更敏感）
            has_any_edge_anomaly = any([
                actual_edge['top_anomaly']['anomaly_score'] > 0.3,
                actual_edge['bottom_anomaly']['anomaly_score'] > 0.3,
                actual_edge['left_anomaly']['anomaly_score'] > 0.3,
                actual_edge['right_anomaly']['anomaly_score'] > 0.3
            ])

            if actual_edge['has_significant_anomalies'] or has_any_edge_anomaly:
                # 根据实际检测的异常边缘调整区域
                adjusted_area = self._adjust_area_for_edge_anomalies(
                    predicted_area, actual_edge, image_size
                )
                self.logger.info(f"[CSS_EDGE_CALC] 检测到边缘异常，调整后区域: {adjusted_area}")
                return adjusted_area
            else:
                self.logger.info("[CSS_EDGE_CALC] 实际检测无显著异常，使用预测区域")
                return predicted_area
        else:
            self.logger.info("[CSS_EDGE_CALC] 无实际边缘检测数据，使用预测区域")
            return predicted_area

    def _adjust_area_for_edge_anomalies(self, base_area: tuple, actual_edge: dict, image_size: tuple) -> tuple:
        """
        根据实际检测的边缘异常调整目标区域

        Args:
            base_area: 基础区域
            actual_edge: 实际边缘异常检测结果
            image_size: 图像尺寸

        Returns:
            调整后的区域
        """
        left, top, right, bottom = base_area
        image_width, image_height = image_size
        edge_width = actual_edge['edge_width_pixels']

        # 根据各边的异常情况调整边界（降低阈值，更积极地处理异常）
        if actual_edge['top_anomaly']['anomaly_score'] > 0.3:  # 降低阈值
            # 上边有异常，向下收缩
            anomaly_strength = actual_edge['top_anomaly']['anomaly_score']
            shrink_amount = int(edge_width * anomaly_strength * 3)  # 增加收缩强度
            top = int(max(top, shrink_amount))
            self.logger.info(f"[EDGE_ADJUST] 上边异常(score={anomaly_strength:.3f})，收缩 {shrink_amount} 像素")

        if actual_edge['bottom_anomaly']['anomaly_score'] > 0.3:
            # 下边有异常，向上收缩
            anomaly_strength = actual_edge['bottom_anomaly']['anomaly_score']
            shrink_amount = int(edge_width * anomaly_strength * 3)
            bottom = int(min(bottom, image_height - shrink_amount))
            self.logger.info(f"[EDGE_ADJUST] 下边异常(score={anomaly_strength:.3f})，收缩 {shrink_amount} 像素")

        if actual_edge['left_anomaly']['anomaly_score'] > 0.3:
            # 左边有异常，向右收缩
            anomaly_strength = actual_edge['left_anomaly']['anomaly_score']
            shrink_amount = int(edge_width * anomaly_strength * 3)
            left = int(max(left, shrink_amount))
            self.logger.info(f"[EDGE_ADJUST] 左边异常(score={anomaly_strength:.3f})，收缩 {shrink_amount} 像素")

        if actual_edge['right_anomaly']['anomaly_score'] > 0.3:
            # 右边有异常，向左收缩
            anomaly_strength = actual_edge['right_anomaly']['anomaly_score']
            shrink_amount = int(edge_width * anomaly_strength * 3)
            right = int(min(right, image_width - shrink_amount))
            self.logger.info(f"[EDGE_ADJUST] 右边异常(score={anomaly_strength:.3f})，收缩 {shrink_amount} 像素")

        # 确保调整后的区域仍然有效
        if left >= right or top >= bottom:
            self.logger.warning("[EDGE_ADJUST] 调整后区域无效，使用原始区域")
            return base_area

        return (left, top, right, bottom)

    def _resolve_crop_constraints(self, table_safe_area: tuple, black_edge_free_area: tuple,
                                 margin_preferred_area: tuple, image_size: tuple) -> tuple:
        """
        多约束求解：按优先级解决裁剪约束

        Args:
            table_safe_area: 表格安全区域（硬约束）
            black_edge_free_area: 黑边移除区域（软约束1）
            margin_preferred_area: margin_control期望区域（软约束2）
            image_size: 图像尺寸

        Returns:
            最终裁剪区域 (left, top, right, bottom)
        """
        image_width, image_height = image_size

        # 步骤1：以表格安全区域为基础（硬约束，绝对不能违反）
        safe_left, safe_top, safe_right, safe_bottom = table_safe_area
        self.logger.info(f"[CONSTRAINT_SOLVE] 硬约束-表格安全区域: {table_safe_area}")

        # 步骤2：尝试扩展到黑边移除区域（软约束1，尽量满足）
        black_left, black_top, black_right, black_bottom = black_edge_free_area

        # 计算黑边移除的理想区域，但不能超出表格安全区域
        ideal_left = min(safe_left, black_left)
        ideal_top = min(safe_top, black_top)
        ideal_right = max(safe_right, black_right)
        ideal_bottom = max(safe_bottom, black_bottom)

        # 确保不超出图像边界
        ideal_left = max(0, ideal_left)
        ideal_top = max(0, ideal_top)
        ideal_right = min(image_width, ideal_right)
        ideal_bottom = min(image_height, ideal_bottom)

        self.logger.info(f"[CONSTRAINT_SOLVE] 软约束1-黑边移除理想区域: ({ideal_left}, {ideal_top}, {ideal_right}, {ideal_bottom})")

        # 步骤3：检查是否与margin_control期望区域兼容（软约束2，参考满足）
        margin_left, margin_top, margin_right, margin_bottom = margin_preferred_area

        # 计算与margin_control的兼容性
        margin_compatible = (
            margin_left <= ideal_left <= ideal_right <= margin_right and
            margin_top <= ideal_top <= ideal_bottom <= margin_bottom
        )

        if margin_compatible:
            # 如果与margin_control兼容，尝试进一步优化
            final_left = max(ideal_left, margin_left)
            final_top = max(ideal_top, margin_top)
            final_right = min(ideal_right, margin_right)
            final_bottom = min(ideal_bottom, margin_bottom)

            # 确保仍然包含表格安全区域
            if (final_left <= safe_left and final_top <= safe_top and
                final_right >= safe_right and final_bottom >= safe_bottom):
                self.logger.info("[CONSTRAINT_SOLVE] 所有约束兼容，使用优化后的区域")
                return (final_left, final_top, final_right, final_bottom)

        # 如果margin_control不兼容，按照优先级：表格安全 > 黑边移除 > margin_control
        self.logger.info("[CONSTRAINT_SOLVE] margin_control约束不兼容，优先保证表格安全和黑边移除")

        # 检查黑边移除区域是否包含表格安全区域
        if (black_left <= safe_left and black_top <= safe_top and
            black_right >= safe_right and black_bottom >= safe_bottom):
            # 黑边移除区域完全包含表格安全区域，使用黑边移除区域
            self.logger.info("[CONSTRAINT_SOLVE] 使用黑边移除区域（包含表格安全区域）")
            return black_edge_free_area
        else:
            # 黑边移除区域与表格安全区域有冲突，优先保证表格安全
            self.logger.warning("[CONSTRAINT_SOLVE] 黑边移除与表格安全冲突，优先保证表格安全")
            return table_safe_area

    def _save_crop_debug_info(self, original_image, cropped_image, original_annotations: dict,
                             updated_annotations: dict, crop_box: tuple, black_edge_info: dict = None):
        """
        保存裁剪过程的调试信息
        """
        try:
            import os
            from PIL import ImageDraw
            import json

            # 使用专门的调试输出目录
            base_debug_dir = self.config.debug.debug_output_dir if self.config.debug else './debug_output'
            debug_dir = os.path.join(base_debug_dir, f"debug_sample_{self._current_sample_index:06d}")
            os.makedirs(debug_dir, exist_ok=True)

            # 创建裁剪过程可视化
            vis_image = original_image.copy()
            draw = ImageDraw.Draw(vis_image)

            # 绘制裁剪框（红色）
            left, top, right, bottom = crop_box
            draw.rectangle([left, top, right, bottom], outline='red', width=3)

            # 如果有黑边信息，绘制黑边区域
            if black_edge_info and black_edge_info['has_black_edges']:
                for region in black_edge_info['black_edge_regions']:
                    r_left, r_top, r_right, r_bottom = region
                    draw.rectangle([r_left, r_top, r_right, r_bottom], outline='orange', width=2)

            # 绘制表格区域（绿色）
            if original_annotations.get('cells'):
                table_safe_area = self._calculate_table_safe_area_from_annotations(
                    original_annotations, original_image.size
                )
                t_left, t_top, t_right, t_bottom = table_safe_area
                draw.rectangle([t_left, t_top, t_right, t_bottom], outline='green', width=2)

            # 保存可视化图像
            vis_image.save(os.path.join(debug_dir, "crop_visualization.png"))

            # 保存详细的裁剪信息JSON
            crop_info = {
                "original_size": original_image.size,
                "cropped_size": cropped_image.size,
                "crop_box": crop_box,
                "black_edge_info": black_edge_info,
                "original_cells_count": len(original_annotations.get('cells', [])),
                "updated_cells_count": len(updated_annotations.get('cells', [])),

                # V4.4.2增强调试信息
                "debug_info": {
                    "perspective_applied": original_annotations.get('debug_perspective_applied', False),
                    "transform_matrix": original_annotations.get('debug_transform_matrix', None),
                    "processing_mode": "css_mode",
                    "margin_config_available": "margin_config_not_passed_to_debug_function"
                },

                # 处理流程分析
                "processing_analysis": {
                    "had_black_edge_info": black_edge_info is not None,
                    "predicted_black_edges": black_edge_info['has_black_edges'] if black_edge_info else False,
                    "actual_edge_anomalies": black_edge_info.get('actual_edge_detection', {}).get('has_significant_anomalies', False) if black_edge_info else False,
                    "crop_method_used": "enhanced_with_black_edge_priority" if black_edge_info else "traditional"
                }
            }

            # 确保数据可以JSON序列化
            serializable_crop_info = self._make_json_serializable(crop_info)
            with open(os.path.join(debug_dir, "crop_info.json"), 'w', encoding='utf-8') as f:
                json.dump(serializable_crop_info, f, indent=2, ensure_ascii=False)

            self.logger.info(f"[DEBUG] 裁剪调试信息已保存到: {debug_dir}")

        except Exception as e:
            self.logger.warning(f"保存裁剪调试信息失败: {e}")

    def _make_json_serializable(self, obj):
        """
        将对象转换为JSON可序列化的格式

        Args:
            obj: 要转换的对象

        Returns:
            JSON可序列化的对象
        """
        import numpy as np

        if isinstance(obj, dict):
            return {key: self._make_json_serializable(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, tuple):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, bool) or (hasattr(np, 'bool_') and isinstance(obj, np.bool_)):
            return bool(obj)
        elif hasattr(obj, '__dict__'):
            # 对于自定义对象，尝试转换其属性
            return self._make_json_serializable(obj.__dict__)
        else:
            # 对于其他类型，尝试直接返回，如果不能序列化会在json.dump时报错
            return obj

    def _calculate_table_bounds(self, annotations: dict) -> dict:
        """计算表格的真实边界"""
        all_x = []
        all_y = []

        for cell in annotations.get('cells', []):
            bbox = cell.get('bbox', {})
            for point_key in ['p1', 'p2', 'p3', 'p4']:
                if point_key in bbox:
                    x, y = bbox[point_key]
                    all_x.append(x)
                    all_y.append(y)

        if not all_x or not all_y:
            raise ValueError("无法从标注数据中提取有效的坐标点")

        return {
            "min_x": min(all_x),
            "min_y": min(all_y),
            "max_x": max(all_x),
            "max_y": max(all_y)
        }

    def _select_margin_from_config(self, margin_config, sample_seed: int, table_bounds: dict = None) -> int:
        """
        V5.1改进：根据边距配置选择边距值，支持大表格的相对边距

        Args:
            margin_config: 边距配置
            sample_seed: 随机种子
            table_bounds: 表格边界信息（用于计算相对边距）

        Returns:
            计算出的边距值（像素）
        """
        import numpy as np
        random_state = np.random.RandomState(sample_seed)

        if not margin_config or not hasattr(margin_config, 'range_list') or not margin_config.range_list:
            return 50  # 默认边距

        range_list = margin_config.range_list
        probability_list = getattr(margin_config, 'probability_list', None)

        if not probability_list or len(probability_list) != len(range_list):
            probability_list = [1.0 / len(range_list)] * len(range_list)

        # 归一化概率
        total_prob = sum(probability_list)
        if total_prob > 0:
            normalized_probabilities = [p / total_prob for p in probability_list]
        else:
            normalized_probabilities = [1.0 / len(range_list)] * len(range_list)

        # 选择边距范围
        selected_index = random_state.choice(len(range_list), p=normalized_probabilities)
        selected_range = range_list[selected_index]

        # 在范围内随机选择基础边距
        min_margin, max_margin = selected_range
        base_margin = random_state.randint(min_margin, max_margin + 1)

        # V5.1新增：为大表格计算相对边距
        if table_bounds:
            table_width = table_bounds.get('max_x', 0) - table_bounds.get('min_x', 0)
            table_height = table_bounds.get('max_y', 0) - table_bounds.get('min_y', 0)

            # 判断是否为大表格（宽度>2000px或高度>1500px）
            is_large_table = table_width > 2000 or table_height > 1500

            if is_large_table:
                # 对于大表格，使用相对边距（表格尺寸的百分比）
                relative_margin_ratio = base_margin / 100.0  # 将配置值转换为百分比

                # 基于表格较小维度计算相对边距，避免边距过大
                reference_dimension = min(table_width, table_height)
                relative_margin = int(reference_dimension * relative_margin_ratio)

                # 设置合理的边距范围
                min_relative_margin = max(base_margin, 20)  # 最小边距
                max_relative_margin = min(relative_margin, reference_dimension * 0.15)  # 最大不超过参考维度的15%

                actual_margin = max(min_relative_margin, min(relative_margin, max_relative_margin))

                self.logger.info(f"[LARGE_TABLE_MARGIN] 大表格相对边距计算")
                self.logger.info(f"[LARGE_TABLE_MARGIN] 表格尺寸: {table_width:.0f}x{table_height:.0f}")
                self.logger.info(f"[LARGE_TABLE_MARGIN] 基础边距: {base_margin}, 相对边距: {actual_margin}")
            else:
                # 普通表格使用原始边距
                actual_margin = base_margin
                self.logger.info(f"[MARGIN_SELECT] 普通表格边距: {actual_margin}")
        else:
            # 没有表格边界信息时使用原始边距
            actual_margin = base_margin
            self.logger.info(f"[MARGIN_SELECT] 默认边距: {actual_margin}")

        self.logger.info(f"[MARGIN_SELECT] 选择范围: {selected_range}, 最终边距: {actual_margin}")
        return actual_margin

    def _calculate_crop_box(self, table_bounds: dict, margin: int, image_size: tuple) -> tuple:
        """计算裁剪区域"""
        image_width, image_height = image_size

        # 基于表格边界和边距计算裁剪区域
        crop_left = max(0, int(table_bounds["min_x"] - margin))
        crop_top = max(0, int(table_bounds["min_y"] - margin))
        crop_right = min(image_width, int(table_bounds["max_x"] + margin))
        crop_bottom = min(image_height, int(table_bounds["max_y"] + margin))

        return (crop_left, crop_top, crop_right, crop_bottom)

    # V5.1新增：表格大小分析和性能监控辅助方法

    def _analyze_table_size(self, structure_params):
        """
        分析表格大小，判断是否为大表格

        Args:
            structure_params: 结构参数

        Returns:
            dict: 包含表格大小信息的字典
        """
        total_rows = structure_params.header_rows + structure_params.body_rows
        total_cols = structure_params.cols
        total_cells = total_rows * total_cols

        # 定义大表格的阈值
        is_large_table = (total_rows > 25 or total_cols > 25 or total_cells > 500)
        is_huge_table = (total_rows > 40 or total_cols > 40 or total_cells > 1000)

        if is_huge_table:
            size_category = "huge"
            description = f"超大表格: {total_rows}行 x {total_cols}列 ({total_cells}个单元格)"
        elif is_large_table:
            size_category = "large"
            description = f"大表格: {total_rows}行 x {total_cols}列 ({total_cells}个单元格)"
        else:
            size_category = "normal"
            description = f"普通表格: {total_rows}行 x {total_cols}列 ({total_cells}个单元格)"

        return {
            'total_rows': total_rows,
            'total_cols': total_cols,
            'total_cells': total_cells,
            'is_large_table': is_large_table,
            'is_huge_table': is_huge_table,
            'size_category': size_category,
            'description': description
        }

    def _analyze_generation_error(self, error, table_size_info):
        """
        分析生成错误并提供解决建议

        Args:
            error: 异常对象
            table_size_info: 表格大小信息

        Returns:
            dict: 错误分析结果
        """
        error_str = str(error).lower()
        error_type = type(error).__name__

        suggestions = []
        is_large_table_error = False

        # 检查是否为大表格相关错误
        if table_size_info.get('is_large_table', False):
            is_large_table_error = True

        # 内存相关错误
        if 'memory' in error_str or 'out of memory' in error_str or error_type == 'MemoryError':
            suggestions.extend([
                "内存不足，请减少表格大小或增加系统内存",
                "尝试减少配置文件中的最大行列数",
                "关闭其他占用内存的程序"
            ])
            is_large_table_error = True

        # 超时相关错误
        elif 'timeout' in error_str or 'time' in error_str:
            suggestions.extend([
                "渲染超时，请减少表格复杂度",
                "增加渲染超时时间限制",
                "简化表格样式和效果"
            ])
            if table_size_info.get('total_cells', 0) > 1000:
                is_large_table_error = True

        # 浏览器/渲染相关错误
        elif 'playwright' in error_str or 'browser' in error_str or 'viewport' in error_str:
            suggestions.extend([
                "浏览器渲染失败，可能是视口尺寸过大",
                "尝试减少表格尺寸",
                "检查系统是否支持大尺寸渲染"
            ])
            if table_size_info.get('total_cells', 0) > 500:
                is_large_table_error = True

        # 图像处理相关错误
        elif 'pil' in error_str or 'image' in error_str or 'opencv' in error_str:
            suggestions.extend([
                "图像处理失败，可能是图像尺寸过大",
                "减少表格尺寸或降低图像质量",
                "检查图像处理库的版本兼容性"
            ])
            is_large_table_error = True

        # 索引错误（新增：针对当前问题）
        elif 'index' in error_str and 'out of range' in error_str:
            suggestions.extend([
                "表格结构生成错误，可能产生了空行或空列",
                "这通常发生在大表格或高合并概率的情况下",
                "尝试减少表格大小或降低合并概率"
            ])
            is_large_table_error = True

        # 配置类型错误（V5.1新增：针对ProbabilisticRange错误）
        elif 'probabilisticrange' in error_str or 'unsupported operand type' in error_str:
            suggestions.extend([
                "配置值类型错误，ProbabilisticRange对象未被正确解析为数值",
                "这通常发生在尺寸估算过程中",
                "请检查配置文件中的概率化配置是否正确",
                "尝试使用简化的配置值进行测试"
            ])
            is_large_table_error = True

        # 文件系统相关错误
        elif 'permission' in error_str or 'file' in error_str:
            suggestions.extend([
                "文件系统错误，检查输出目录权限",
                "确保有足够的磁盘空间",
                "检查文件路径是否正确"
            ])

        # 配置相关错误
        elif 'config' in error_str or 'validation' in error_str:
            suggestions.extend([
                "配置验证失败，检查配置文件格式",
                "确保所有必需的配置项都已设置",
                "参考示例配置文件进行修正"
            ])

        # 通用建议
        if not suggestions:
            suggestions.append("未知错误，请检查日志获取更多信息")

        # 如果是大表格，添加特定建议
        if is_large_table_error and table_size_info:
            suggestions.append(f"当前表格: {table_size_info.get('description', '未知大小')}")
            suggestions.append("建议将最大行数限制在25行以内，列数限制在25列以内")

        return {
            'error_type': error_type,
            'error_message': str(error),
            'is_large_table_error': is_large_table_error,
            'suggestions': suggestions,
            'table_size_info': table_size_info
        }

    def _update_annotations_for_crop(self, annotations: dict, crop_box: tuple) -> dict:
        """
        更新标注坐标以适应裁剪

        Args:
            annotations: 原始标注数据
            crop_box: 裁剪框 (left, top, right, bottom)

        Returns:
            更新后的标注数据
        """
        import copy

        crop_left, crop_top, crop_right, crop_bottom = crop_box
        updated_annotations = copy.deepcopy(annotations)

        # 计算裁剪后的图像尺寸
        cropped_width = crop_right - crop_left
        cropped_height = crop_bottom - crop_top

        # 统计坐标修正情况
        total_points = 0
        corrected_points = 0
        out_of_bounds_points = 0

        # 更新所有单元格的坐标
        if 'cells' in updated_annotations:
            for cell in updated_annotations['cells']:
                if 'bbox' in cell:
                    bbox = cell['bbox']
                    for point_key in ['p1', 'p2', 'p3', 'p4']:
                        if point_key in bbox:
                            x, y = bbox[point_key]
                            total_points += 1

                            # 应用裁剪偏移
                            new_x = x - crop_left
                            new_y = y - crop_top

                            # V5.1修复：检查并修正超出边界的坐标
                            original_x, original_y = new_x, new_y

                            # 边界检查和修正
                            if new_x < 0:
                                new_x = 0
                                corrected_points += 1
                            elif new_x >= cropped_width:
                                new_x = cropped_width - 1
                                corrected_points += 1

                            if new_y < 0:
                                new_y = 0
                                corrected_points += 1
                            elif new_y >= cropped_height:
                                new_y = cropped_height - 1
                                corrected_points += 1

                            # 记录超出边界的情况
                            if (original_x < 0 or original_x >= cropped_width or
                                original_y < 0 or original_y >= cropped_height):
                                out_of_bounds_points += 1
                                self.logger.debug(f"[COORD_FIX] 坐标超界修正: ({original_x:.1f}, {original_y:.1f}) -> ({new_x}, {new_y})")

                            bbox[point_key] = [new_x, new_y]

        # 记录坐标修正统计信息
        if out_of_bounds_points > 0:
            self.logger.warning(f"[COORD_FIX] 坐标边界修正统计:")
            self.logger.warning(f"[COORD_FIX] 总坐标点数: {total_points}")
            self.logger.warning(f"[COORD_FIX] 超出边界点数: {out_of_bounds_points}")
            self.logger.warning(f"[COORD_FIX] 修正点数: {corrected_points}")
            self.logger.warning(f"[COORD_FIX] 裁剪后图像尺寸: {cropped_width}x{cropped_height}")
            self.logger.warning(f"[COORD_FIX] 裁剪区域: {crop_box}")
        else:
            self.logger.info(f"[COORD_FIX] 所有{total_points}个坐标点均在边界内，无需修正")

        # 更新表格边界框
        if 'table_bbox' in updated_annotations:
            table_bbox = updated_annotations['table_bbox']
            if len(table_bbox) >= 4:
                # 应用裁剪偏移
                new_left = table_bbox[0] - crop_left
                new_top = table_bbox[1] - crop_top
                new_right = table_bbox[2] - crop_left
                new_bottom = table_bbox[3] - crop_top

                # V5.1修复：边界检查和修正
                new_left = max(0, min(new_left, cropped_width - 1))
                new_top = max(0, min(new_top, cropped_height - 1))
                new_right = max(0, min(new_right, cropped_width - 1))
                new_bottom = max(0, min(new_bottom, cropped_height - 1))

                # 确保边界框的逻辑正确性
                if new_left >= new_right:
                    new_right = new_left + 1
                if new_top >= new_bottom:
                    new_bottom = new_top + 1

                updated_annotations['table_bbox'] = [new_left, new_top, new_right, new_bottom]

                self.logger.debug(f"[COORD_FIX] 表格边界框更新: {table_bbox} -> {updated_annotations['table_bbox']}")

        return updated_annotations

    def _validate_annotations_bounds(self, annotations: dict, image_width: int, image_height: int, context: str = "") -> dict:
        """
        验证标注坐标是否在图像边界内

        Args:
            annotations: 标注数据
            image_width: 图像宽度
            image_height: 图像高度
            context: 上下文信息（用于日志）

        Returns:
            验证结果字典
        """
        result = {
            'total_points': 0,
            'out_of_bounds_points': 0,
            'out_of_bounds_x': [],
            'out_of_bounds_y': [],
            'coord_range': {'min_x': float('inf'), 'max_x': float('-inf'),
                           'min_y': float('inf'), 'max_y': float('-inf')}
        }

        if 'cells' not in annotations:
            return result

        all_x_coords = []
        all_y_coords = []

        for cell in annotations['cells']:
            if 'bbox' in cell:
                bbox = cell['bbox']
                for point_key in ['p1', 'p2', 'p3', 'p4']:
                    if point_key in bbox:
                        x, y = bbox[point_key]
                        all_x_coords.append(x)
                        all_y_coords.append(y)
                        result['total_points'] += 1

                        # 检查边界
                        if x < 0 or x >= image_width:
                            result['out_of_bounds_x'].append(x)
                        if y < 0 or y >= image_height:
                            result['out_of_bounds_y'].append(y)

        if all_x_coords and all_y_coords:
            result['coord_range'] = {
                'min_x': min(all_x_coords), 'max_x': max(all_x_coords),
                'min_y': min(all_y_coords), 'max_y': max(all_y_coords)
            }
            result['out_of_bounds_points'] = len(set(result['out_of_bounds_x'] + result['out_of_bounds_y']))

        # 记录验证结果
        prefix = f"[{context}] " if context else ""
        self.logger.info(f"{prefix}坐标验证结果:")
        self.logger.info(f"{prefix}总坐标点数: {result['total_points']}")
        if result['coord_range']['min_x'] != float('inf'):
            self.logger.info(f"{prefix}坐标范围: X[{result['coord_range']['min_x']:.1f}, {result['coord_range']['max_x']:.1f}], Y[{result['coord_range']['min_y']:.1f}, {result['coord_range']['max_y']:.1f}]")
        self.logger.info(f"{prefix}图像尺寸: {image_width}x{image_height}")

        if result['out_of_bounds_x'] or result['out_of_bounds_y']:
            self.logger.warning(f"{prefix}发现超出边界的坐标:")
            if result['out_of_bounds_x']:
                self.logger.warning(f"{prefix}超界X坐标数量: {len(result['out_of_bounds_x'])}")
            if result['out_of_bounds_y']:
                self.logger.warning(f"{prefix}超界Y坐标数量: {len(result['out_of_bounds_y'])}")
                self.logger.warning(f"{prefix}最大Y坐标: {max(result['out_of_bounds_y']):.1f}")
        else:
            self.logger.info(f"{prefix}✓ 所有坐标均在图像边界内")

        return result


class LargeTablePerformanceMonitor:
    """
    V5.1新增：大表格性能监控器

    监控内存使用、渲染时间等性能指标，特别针对大表格优化
    """

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.performance_records = []
        self.process = psutil.Process()

    def get_memory_usage(self):
        """
        获取当前内存使用量（MB）
        """
        try:
            memory_info = self.process.memory_info()
            return memory_info.rss / 1024 / 1024  # 转换为MB
        except Exception as e:
            self.logger.warning(f"获取内存使用量失败: {e}")
            return 0

    def check_memory_availability(self, threshold_mb):
        """
        检查内存是否充足

        Args:
            threshold_mb: 内存阈值（MB）

        Returns:
            bool: 是否有足够内存
        """
        try:
            current_memory = self.get_memory_usage()
            available_memory = psutil.virtual_memory().available / 1024 / 1024

            if current_memory > threshold_mb:
                self.logger.warning(f"当前内存使用 {current_memory:.2f}MB 超过阈值 {threshold_mb}MB")
                return False

            if available_memory < threshold_mb:
                self.logger.warning(f"可用内存 {available_memory:.2f}MB 低于阈值 {threshold_mb}MB")
                return False

            return True
        except Exception as e:
            self.logger.warning(f"内存检查失败: {e}")
            return True  # 检查失败时允许继续

    def record_sample_performance(self, perf_info):
        """
        记录样本性能信息

        Args:
            perf_info: 性能信息字典
        """
        self.performance_records.append(perf_info)

        # 保持最近100条记录
        if len(self.performance_records) > 100:
            self.performance_records = self.performance_records[-100:]

    def get_performance_summary(self):
        """
        获取性能摘要

        Returns:
            dict: 性能摘要信息
        """
        if not self.performance_records:
            return {"message": "暂无性能记录"}

        # 分析大表格性能
        large_table_records = [r for r in self.performance_records
                              if r['table_size']['is_large_table']]
        normal_table_records = [r for r in self.performance_records
                               if not r['table_size']['is_large_table']]

        summary = {
            'total_samples': len(self.performance_records),
            'large_table_samples': len(large_table_records),
            'normal_table_samples': len(normal_table_records)
        }

        if large_table_records:
            large_durations = [r['duration_seconds'] for r in large_table_records]
            large_memory_deltas = [r['memory_delta_mb'] for r in large_table_records]

            summary['large_table_performance'] = {
                'avg_duration': sum(large_durations) / len(large_durations),
                'max_duration': max(large_durations),
                'avg_memory_delta': sum(large_memory_deltas) / len(large_memory_deltas),
                'max_memory_delta': max(large_memory_deltas)
            }

        if normal_table_records:
            normal_durations = [r['duration_seconds'] for r in normal_table_records]
            normal_memory_deltas = [r['memory_delta_mb'] for r in normal_table_records]

            summary['normal_table_performance'] = {
                'avg_duration': sum(normal_durations) / len(normal_durations),
                'max_duration': max(normal_durations),
                'avg_memory_delta': sum(normal_memory_deltas) / len(normal_memory_deltas),
                'max_memory_delta': max(normal_memory_deltas)
            }

        return summary
