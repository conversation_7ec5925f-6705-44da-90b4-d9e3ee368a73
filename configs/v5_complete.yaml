# TableRender V5 完整功能配置文件
# 包含TableRender所有功能特性的完整配置示例
# 
# 功能覆盖：
# - V5.0: CSV内容源优化、概率化目录选择、随机采样填充、空白控制、采样信息记录
# - V4.0: 内容溢出处理、图像后处理系统、背景图合成、智能边距控制、透视变换优化
# - V3.4: 概率化配置机制、可变行高列宽功能
# - V3.3: 精简样式架构、边框处理算法、合并单元格对齐优化
# - V3.2+: 样式继承机制、颜色对比度保证、分层样式系统
# - V3.0+: 高级样式系统、复杂表头支持、随机尺寸控制
# - V2.0+: 单元格合并、程序化内容生成
# - V1.0: 核心渲染流水线、结构化标注

# ==================== 输出配置 ====================
output:
  output_dir: "./output/"                    # 输出目录路径
#  label_suffix: "_table_annotation"          # 标注文件后缀，用于区分不同类型的标注文件

# ==================== 表格结构配置 ====================
# 使用V3.4概率化配置机制，支持多样化的表格结构生成
structure:
  # 概率化表头行数：适应不同表格的表头复杂度
  # 70%概率单行表头（最常见情况）
  # 25%概率双行表头（分类表头）
  # 5%概率三行表头（复杂层次表头）
  header_rows:
    range_list: [[1, 1], [2, 2], [3, 3]]
    probability_list: [0.6, 0.3, 0.1]

  # 概率化表体行数：平衡生成效率和表格复杂度
  # 60%概率生成3-5行的小表格（快速生成，适合测试）
  # 30%概率生成6-8行的中等表格（常见商业表格）
  # 10%概率生成9-12行的大表格（复杂报表）
  body_rows:
    range_list: [[2, 5], [6, 10], [11, 20], [21, 30], [31, 40]]
    probability_list: [0.2, 0.2, 0.4, 0.15, 0.05]

  # 概率化列数：模拟不同类型表格的列数分布
  # 40%概率生成3-4列的简单表格（基础数据表）
  # 40%概率生成5-6列的标准表格（常见业务表格）
  # 20%概率生成7-9列的复杂表格（详细报表）
  cols:
    range_list: [[2, 5], [6, 10], [11, 20], [21, 30], [31, 40]]
    probability_list: [0.2, 0.2, 0.4, 0.15, 0.05]

  # 概率化合并概率：控制表格复杂度和可读性的平衡
  # 50%概率低合并率(0.05-0.15)，保持表格结构清晰
  # 35%概率中等合并率(0.2-0.3)，增加适度复杂度
  # 15%概率高合并率(0.35-0.5)，生成复杂表格结构
  merge_probability:
    range_list: [[0, 0.1], [0.1, 0.15], [0.15, 0.2]]
    probability_list: [0.5, 0.3, 0.2]

  # 概率化最大行跨度：控制垂直合并的复杂度
  # 60%概率限制为2行跨度（常见合并情况）
  # 30%概率允许3行跨度（中等复杂度）
  # 10%概率允许4-5行跨度（高复杂度，特殊表格）
  max_row_span:
    option_list: [2, 5, 8]
    probability_list: [0.29, 0.7, 0.01]

  # 概率化最大列跨度：控制水平合并的复杂度
  # 65%概率限制为2列跨度（保持可读性）
  # 25%概率允许3列跨度（适度复杂）
  # 8%概率允许4列跨度（高复杂度）
  # 2%概率允许5列跨度（极高复杂度，罕见情况）
  max_col_span:
    option_list: [2, 5, 8]
    probability_list: [0.29, 0.7, 0.01]

# ==================== V5.0 内容配置 ====================
# 新增CSV内容源优化，支持概率化目录选择和随机采样填充
content:
  # V5.0推荐：使用CSV内容源，提供真实表格数据
  source_type: "csv"
  csv_source:
    # V5.0新增：概率化CSV目录选择，支持多数据源
    # 50%概率选择中文表格数据（适合中文场景）
    # 30%概率选择英文表格数据（适合英文场景）
    # 15%概率选择混合表格数据（中英文混合）
    # 5%概率选择特殊表格数据（特定领域数据）
    csv_dirs: 
      - "assets/corpus/nl2sql_train/"
      - "assets/corpus/wikisql_train/"
    csv_dir_probabilities: [0.5, 0.5]
    
    # V5.0新增：随机采样模式（行列对应，保持数据语义一致性）
    sampling_mode: "random"
    
    # V5.0新增：空白控制配置，增加表格真实性
    blank_control:
      trigger_probability: 0.01           # 15%概率某行/列被标记为可空白
      cell_blank_probability: 0.4        # 在标记的行/列中，25%概率单元格真的为空
    
    # 基本配置
    encoding: "utf-8"
    
    # 单文件模式示例（向后兼容，优先级低于csv_dirs）
    # file_path: "sample_data/processed_csv/4rows_batch_001.csv"
  
  # 备用：程序化内容生成（V2.0+功能，当CSV不可用时的回退选项）
  # source_type: "programmatic"
  # programmatic_types: ["date", "currency", "percentage", "text", "number", "email", "phone"]

# ==================== 样式配置 ====================
# V3.3+精简样式架构，V3.4概率化配置，V4.0内容溢出处理
style:
  # V4.0新增：内容溢出处理策略
  # "wrap": 内容自动换行以适应单元格宽度（推荐，保持内容完整性）
  # "truncate": 超出内容被截断并显示省略号（紧凑布局）
  overflow_strategy: "wrap"

  # 公共样式配置：作为表头和表体样式的基础
  common:
    # 字体配置 - V3.3概率化字体目录选择，V3.4增强
    font:
      # 概率化字体目录：支持多种字体风格
      # 50%概率中文字体（适合中文内容）
      # 30%概率英文字体（适合英文内容）
      # 15%概率混合字体（中英文混合）
      # 5%概率装饰字体（特殊设计需求）
      font_dirs: 
        - "/aipdf-mlp/jiacheng/code/text_render/example_data/font/common"
        - "/aipdf-mlp/jiacheng/code/text_render/example_data/font/rare"
      font_dir_probabilities: [0.8, 0.2]

      # 概率化字体族：平衡可读性和视觉多样性
      # 40%概率Arial（最佳可读性）
      # 25%概率Times New Roman（传统商务风格）
      # 20%概率Helvetica（现代简洁风格）
      # 10%概率Calibri（Office风格）
      # 5%概率其他字体（特殊需求）
      default_family:
        option_list: ["Arial", "Times New Roman", "Helvetica", "Calibri", "Verdana"]
        probability_list: [0.4, 0.25, 0.2, 0.1, 0.05]

      # 概率化字体大小：适应不同使用场景
      # 50%概率12-14px（标准文档大小）
      # 30%概率15-16px（易读性优先）
      # 15%概率10-11px（紧凑布局）
      # 5%概率17-20px（大字体，特殊需求）
      default_size:
        range_list: [[12, 14], [15, 16], [10, 11], [17, 20]]
        probability_list: [0.5, 0.3, 0.15, 0.05]

      bold_probability: 0.1                 # 15%概率使用粗体
      italic_probability: 0.02               # 8%概率使用斜体
      fallback_font: "Microsoft YaHei"       # 备用字体，确保兼容性
    
    # 概率化水平对齐：模拟真实文档中的对齐偏好
    # 45%概率左对齐（文本内容的自然选择）
    # 35%概率居中对齐（标题和重要数据）
    # 20%概率右对齐（数值和金额）
    horizontal_align:
      option_list: ["left", "center", "right"]
      probability_list: [0.25, 0.5, 0.25]

    # 概率化垂直对齐：平衡美观性和实用性
    # 35%概率顶部对齐（传统表格样式）
    # 45%概率中间对齐（现代表格样式，视觉平衡）
    # 20%概率底部对齐（特殊设计需求）
    vertical_align:
      option_list: ["top", "middle", "bottom"]
      probability_list: [0.25, 0.5, 0.25]

    # 概率化内边距：在紧凑性和可读性之间平衡
    # 60%概率使用标准内边距(6-8px)，平衡紧凑性和可读性
    # 25%概率使用较小内边距(4-5px)，紧凑布局
    # 15%概率使用较大内边距(9-12px)，提高可读性
    padding:
      range_list: [[1, 3], [4, 6], [7, 10]]
      probability_list: [0.3, 0.4, 0.3]
    
    # V3.3颜色随机化概率：控制是否使用随机颜色
    randomize_color_probability: 0.2        # 35%概率使用彩色，65%概率使用默认黑白配色

    # V3.3合并单元格居中概率：优化合并单元格的视觉效果
    merged_cell_center_probability: 1.0     # 65%概率将合并单元格设为相对居中对齐

    # V3.2颜色对比度配置：确保生成的颜色组合具有良好的可读性
    color_contrast:
      min_contrast_ratio: 4.5                # 符合WCAG AA标准的最小对比度
      use_soft_colors_probability: 0.1      # 75%概率使用柔和颜色，提升视觉舒适度

  # V3.2样式继承配置：控制表体样式相对于表头样式的变化概率
  inheritance:
    font_family_change_probability: 0.2     # 25%概率表体使用与表头不同的字体
    font_size_change_probability: 0.2       # 35%概率表体使用与表头不同的字体大小
    alignment_change_probability: 0.1       # 45%概率表体使用与表头不同的对齐方式
    padding_change_probability: 0.4          # 30%概率表体使用与表头不同的内边距
    text_color_change_probability: 0.2      # 35%概率表体使用与表头不同的文本颜色
    background_color_change_probability: 0.2 # 25%概率表体使用与表头不同的背景颜色

  # V3.4概率化边框模式配置：提供多样化的表格边框样式
  border_mode:
    mode_options:
      # 35%概率使用完整边框：传统表格样式，结构清晰，适合正式文档
      - probability: 0.5
        config:
          mode: "full"

      # 25%概率使用无边框：现代简洁样式，适合报告和演示
      - probability: 0.2
        config:
          mode: "none"

      # 40%概率使用半边框模式：部分边框，平衡美观性和结构性
      - probability: 0.3
        config:
          mode: "semi"
          semi_config:
            row_line_probability: 0.7       # 65%概率显示行线
            col_line_probability: 0.6       # 55%概率显示列线
            outer_frame: true                # 保留外框以维持表格边界
            header_separator: true           # 保留表头分割线以突出表头

  # V3.2斑马条纹概率：增加表格的视觉层次和可读性
  zebra_stripes: 0.4                         # 40%概率启用斑马条纹

  # V3.4可变行高列宽功能：模拟真实文档中的尺寸变化
  sizing:
    # 全局默认配置
    default_row_height: "auto"               # 默认行高自适应内容
    default_col_width: "auto"                # 默认列宽自适应内容

    # 行级差异化配置：模拟真实文档（如发票、报表）中的行高变化
#    row_configs:
#      # 配置1：表头行强调 - 45%概率启用
#      - name: "header_emphasis"
#        probability: 0.45
#        type: "specific"
#        target_rows: [0]                     # 第一行（表头）
#        height_range: [45, 60]               # 表头行使用较大高度，突出重要性
#
#      # 配置2：汇总行强调 - 35%概率启用
#      - name: "summary_emphasis"
#        probability: 0.35
#        type: "specific"
#        target_rows: [-1]                    # 最后一行（可能是汇总行）
#        height_range: [40, 55]               # 汇总行使用中等高度
#
#      # 配置3：随机行高变化 - 30%概率启用
#      - name: "random_variations"
#        probability: 0.3
#        type: "probabilistic"
#        per_row_probability: 0.2             # 每行20%概率被选中
#        height_range: [35, 50]               # 随机选中的行使用变化高度

    # 列级差异化配置：模拟发票等文档的列宽变化
#    col_configs:
#      # 配置1：标签列加宽 - 40%概率启用
#      - name: "wide_label_col"
#        probability: 0.4
#        type: "specific"
#        target_cols: [0]                     # 第一列（通常是标签列）
#        width_range: [120, 160]              # 标签列使用较大宽度
#
#      # 配置2：金额列适中 - 35%概率启用
#      - name: "amount_col"
#        probability: 0.35
#        type: "specific"
#        target_cols: [-1]                    # 最后一列（通常是金额列）
#        width_range: [90, 130]               # 金额列使用中等宽度
#
#      # 配置3：随机列宽变化 - 25%概率启用
#      - name: "random_narrow_cols"
#        probability: 0.25
#        type: "probabilistic"
#        per_col_probability: 0.25            # 每列25%概率被选中
#        width_range: [70, 100]               # 随机选中的列使用较小宽度

# ==================== V4.0 图像后处理配置 ====================
# 完整的图像增强和真实性模拟功能
postprocessing:
  # 模糊效果：模拟相机失焦、运动模糊或图像压缩
  blur:
    probability: 0.2                         # 20%概率应用模糊效果
    radius_range: [0.8, 2.5]                # 模糊半径范围，较小值模拟轻微失焦，较大值模拟运动模糊

  # 噪声效果：模拟传感器噪声、压缩伪影或扫描噪声
  noise:
    probability: 0.25                        # 25%概率应用噪声效果
    intensity_range: [3, 15]                 # 噪声强度范围，较小值模拟轻微噪声，较大值模拟明显噪声

  # V4.3表格透明度融合：增加表格与背景的自然融合效果
  table_blending:
    enable_transparency: true                # 启用表格透明度融合
    default_color_transparency: 0.1          # 默认色（白色/浅色）的透明度 (0.0-1.0)
    meaningful_color_transparency: 0.4       # 有意义颜色的透明度 (0.0-1.0)

  # 透视变换：模拟拍摄角度变化，增加图像真实性
  # V4.5优化：统一使用OpenCV实现，确保标注坐标精确匹配
  perspective:
    probability: 0.2                         # 60%概率应用透视变换
    # 透视变换强度范围配置（类似margin_control）
    range_list:
      - [0.01, 0.04]                     # 轻微：模拟正常拍摄偏差
      - [0.04, 0.07]                     # 中等：模拟手机拍摄角度
      - [0.07, 0.1]                     # 强烈：模拟极端拍摄条件
    probability_list: [0.4, 0.4, 0.2]     # 偏向轻微变换，保持可读性
    content_area_shrink_ratio: 0.1         # 内容区域缩小10%，保守避免黑边
    adaptive_scaling: [[10, 1], [40, 0.1]]
    decay_rate: 1.0

  # 背景图合成：将表格贴到真实背景上，模拟纸张、桌面等真实环境
  background:
    # 背景图目录配置：支持多种背景类型
    background_dirs:
      - "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white"        # 纸张背景（办公文档）
      - "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure"         # 桌面背景（工作环境）
      - "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/paper"      # 纹理背景（材质效果）
      - "assets/transparent_ink_bgs/"
    background_dir_probabilities: [0.6, 0.1, 0.1, 0.2]  # 对应概率分布

    max_scale_factor: 3.0                    # 最大缩放倍数，控制背景图相对表格的大小
    prefer_center_probability: 0.8            # 偏向中心的概率（0-1）

    # V4.2智能边距控制：基于实际渲染结果的精确边距控制
    margin_control:
      # 边距范围列表：定义不同使用场景的边距选项
      range_list:
        - [10, 30]                           # 紧凑边距：适合密集布局，文档扫描风格
        - [30, 60]                           # 标准边距：平衡紧凑性和可读性，常见商务文档
        - [60, 80]                          # 宽松边距：强调表格重要性，报告展示
        - [80, 100]                         # 很宽松边距：海报或演示用途，视觉冲击

      # 对应的概率分布：偏向标准边距，保持实用性
      probability_list: [0.6, 0.25, 0.1, 0.05]

# ==================== V5.0 高级功能配置 ====================
# V5.0新增功能和高级配置选项

# V5.0 CSV数据源高级配置（可选）：
# content:
#   csv_source:
#     # 多目录配置示例：
#     csv_dirs:
#       - "sample_data/financial_csv/"      # 金融表格数据
#       - "sample_data/medical_csv/"        # 医疗表格数据
#       - "sample_data/education_csv/"      # 教育表格数据
#       - "sample_data/retail_csv/"         # 零售表格数据
#     csv_dir_probabilities: [0.4, 0.25, 0.2, 0.15]
#
#     # 空白控制高级配置：
#     blank_control:
#       trigger_probability: 0.2            # 更高的空白概率，增加真实性
#       cell_blank_probability: 0.4         # 更高的单元格空白概率
#
#     # 位置对应模式（传统模式）：
#     # sampling_mode: "positional"
#     # mismatch_strategy: "fill_empty"

# ==================== 高级功能配置 ====================
# V3.0+分层样式系统：支持列、行、单元格级别的样式覆盖
# 注意：分层样式会覆盖公共样式，优先级：单元格 > 行 > 列 > 公共样式
#
# 示例配置（默认禁用，可根据需要启用）：
# style:
#   hierarchical:
#     # 列级样式覆盖：为特定列设置不同样式
#     column_styles:
#       0:  # 第一列（索引从0开始）
#         font_bold: true                    # 第一列使用粗体
#         horizontal_align: "left"           # 第一列左对齐
#         background_color: "#f0f0f0"        # 第一列浅灰背景
#       -1: # 最后一列
#         horizontal_align: "right"          # 最后一列右对齐（适合数值）
#         font_family: "Courier New"         # 最后一列使用等宽字体
#
#     # 行级样式覆盖：为特定行设置不同样式
#     row_styles:
#       0:  # 表头行
#         font_bold: true                    # 表头行使用粗体
#         background_color: "#e6f3ff"        # 表头行蓝色背景
#         text_color: "#003366"              # 表头行深蓝文字
#       -1: # 最后一行（汇总行）
#         font_bold: true                    # 汇总行使用粗体
#         background_color: "#fff2e6"        # 汇总行橙色背景
#
#     # 单元格级样式覆盖：为特定单元格设置样式
#     cell_styles:
#       "0,0":  # 第一行第一列（行,列格式）
#         font_size: 16                      # 标题单元格使用大字体
#         font_bold: true                    # 标题单元格使用粗体
#         horizontal_align: "center"         # 标题单元格居中对齐

# ==================== 调试和开发配置 ====================
# 以下配置项主要用于开发、调试和特殊需求

# 输出格式扩展（可选）：
# output:
#   debug_mode: false                        # 调试模式：生成中间文件和详细日志
#   annotation_format: "json"               # 标注格式：json（默认）或xml
#   image_format: "png"                     # 图像格式：png（默认）或jpg
#   image_quality: 95                       # 图像质量（仅jpg格式）

# V5.0 CSV内容生成高级配置（可选）：
# content:
#   csv_source:
#     locale: "zh_CN"                        # 本地化设置，影响CSV数据的解析
#     custom_content_mapping: "./data/content_mapping.json"  # 自定义内容映射文件
#     data_validation: true                  # 启用数据验证，确保CSV数据质量
#     max_file_size: "10MB"                  # 最大CSV文件大小限制

# 性能优化配置（可选）：
# performance:
#   max_render_time: 30                     # 最大渲染时间（秒）
#   memory_limit: "1GB"                     # 内存限制
#   parallel_workers: 4                     # 并行工作进程数
#   csv_cache_size: 100                     # CSV文件缓存数量（V5.0新增）

# ==================== 配置使用说明 ====================
#
# 1. V5.0基础使用（推荐）：
#    python -m table_render configs/v5_complete.yaml --num-samples 10
#
# 2. V5.0调试模式：
#    python -m table_render configs/v5_complete.yaml --num-samples 3 --debug
#
# 3. 指定输出目录：
#    python -m table_render configs/v5_complete.yaml --output-dir ./custom_output/
#
# 4. 批量生成：
#    python -m table_render configs/v5_complete.yaml --num-samples 1000 --batch-size 50
#
# 5. 配置验证：
#    python -m table_render configs/v5_complete.yaml --validate-only
#
# 6. V5.0 CSV数据准备：
#    python tests/json_to_csv_converter.py sample_data/ sample_data/processed_csv/
#
# ==================== V5.0 功能特性总结 ====================
#
# 本配置文件包含TableRender V5.0的所有功能特性：
#
# 🆕 V5.0新增功能：
# 📊 CSV内容源：概率化目录选择、随机采样填充、空白控制
# 🎯 智能采样：行列对应采样、语义一致性保证、重复控制
# 📝 采样信息：详细的采样信息记录到metadata，便于调试
# 🔧 数据清理：自动清理空行空列，确保数据质量
#
# 🔄 继承V4.0功能：
# 📊 表格结构：概率化行列数、表头配置、单元格合并控制
# 🎨 样式系统：字体、颜色、对齐、边框、斑马条纹、分层样式
# 📝 内容生成：程序化多类型数据生成、本地化支持
# 🖼️  图像处理：模糊、噪声、透视变换、背景图合成
# 📐 尺寸控制：可变行高列宽、智能边距控制
# 🔧 高级功能：内容溢出处理、样式冲突检测、性能优化
#
# V5.0配置原则：
# - 真实性优先：使用真实CSV数据，提供更真实的表格内容
# - 多样性保证：通过概率化配置和随机采样生成丰富变化
# - 语义一致：保持行列对应关系，确保数据逻辑性
# - 可控性增强：详细的采样信息记录，便于调试和验证
# - 向后兼容：完全兼容V4.0及以前版本的所有功能

# ==================== 随机种子配置 ====================
# 确保结果可复现，便于调试、验证和批量生成
# 相同的种子值将产生完全相同的表格序列
seed: 42
