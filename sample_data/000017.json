{"resolved_params": {"structure": {"body_rows": 13, "cols": 32, "header_rows": 2, "merge_probability": 0.14705684144336773, "max_row_span": 5, "max_col_span": 2, "complex_header": null}, "content": {"source_type": "csv", "csv_file_path": "assets/corpus/nl2sql_train/0051rows_batch_001.csv", "csv_encoding": "utf-8", "csv_mismatch_strategy": "truncate", "csv_dirs": ["assets/corpus/nl2sql_train/", "assets/corpus/wikisql_train/"], "csv_dir_probabilities": [0.5, 0.5], "sampling_mode": "random", "blank_control": {"trigger_probability": 0.01, "cell_blank_probability": 0.4}, "programmatic_types": ["date", "currency", "percentage"]}, "style": {"header": {"font": {"font_dirs": ["/aipdf-mlp/jiacheng/code/text_render/example_data/font/common"], "default_family": "<PERSON><PERSON>", "default_size": 12, "bold_probability": 0.7, "italic_probability": 0.02, "fallback_font": "Microsoft YaHei"}, "text_color": "#000000", "background_color": "#FFFFFF", "horizontal_align": "center", "vertical_align": "top", "padding": 4}, "body": {"font": {"font_dirs": ["/aipdf-mlp/jiacheng/code/text_render/example_data/font/common"], "default_family": "<PERSON><PERSON>", "default_size": 13, "bold_probability": 0.1, "italic_probability": 0.02, "fallback_font": "Microsoft YaHei"}, "text_color": "#000000", "background_color": "#FFFFFF", "horizontal_align": "center", "vertical_align": "bottom", "padding": 3}, "zebra_stripes": true, "zebra_colors": ["#731319", "#FFFFFF"], "sizing": {"row_heights": {}, "col_widths": {}, "default_row_height": "auto", "default_col_width": "auto"}, "hierarchical": null, "border_mode": "full", "border_details": null, "merged_cell_center_probability": 1.0, "randomize_border_color_probability": 0.0, "color_contrast": {"min_contrast_ratio": 4.5, "use_soft_colors_probability": 0.1}, "overflow_strategy": "wrap"}, "output": {"output_dir": "./output/", "label_suffix": null}, "seed": 613608295, "postprocessing": {"apply_blur": false, "blur_radius": null, "apply_noise": false, "noise_intensity": null, "apply_perspective": true, "perspective_offset_ratio": 0.0584536487050681, "content_area_shrink_ratio": 0.1, "apply_background": true, "background_image_path": "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white/classic_white_doc_bg.jpg", "max_scale_factor": 3.0, "css_background_width": 7680, "css_background_height": 4320, "css_table_left": 3893, "css_table_top": 2198, "css_crop_width": 7680, "css_crop_height": 4320, "css_bg_offset_x": 0, "css_bg_offset_y": 0, "margin_control": {"range_list": [[10, 30], [30, 60], [60, 80], [80, 100]], "probability_list": [0.6, 0.25, 0.1, 0.05]}, "enable_transparency": false, "default_color_transparency": 0.1, "meaningful_color_transparency": 0.7, "apply_degradation_blur": false, "apply_degradation_noise": false, "apply_degradation_fade_global": false, "apply_degradation_fade_local": false, "apply_degradation_uneven_lighting": false, "apply_degradation_jpeg": false, "apply_degradation_darker_brighter": false, "apply_degradation_gamma_correction": false}, "perspective_adaptive_debug": {"table_rows": 19.6, "table_cols": 17.6, "max_dimension": 19.6, "base_ratio": 0.06610517210337409, "scale_factor": 0.8842522732360385, "final_ratio": 0.0584536487050681, "min_table_size": 10.0, "max_table_size": 40.0, "min_scale_factor": 0.1, "max_scale_factor": 1.0, "decay_rate": 1.8, "adaptive_scaling_applied": true, "base_selection": {"selected_range": [0.04, 0.07], "selected_index": 1, "range_probability": 0.4, "selected_offset": 0.06610517210337409, "all_ranges": [[0.01, 0.04], [0.04, 0.07], [0.07, 0.1]], "all_probabilities": [0.4, 0.4, 0.2]}}}, "original_config": {"output": {"output_dir": "./output/", "label_suffix": null}, "structure": {"body_rows": {"range_list": [[2, 5], [6, 10], [11, 20], [21, 30], [31, 40]], "probability_list": [0.2, 0.2, 0.4, 0.15, 0.05]}, "cols": {"range_list": [[2, 5], [6, 10], [11, 20], [21, 30], [31, 40]], "probability_list": [0.2, 0.2, 0.4, 0.15, 0.05]}, "header_rows": {"range_list": [[1, 1], [2, 2], [3, 3]], "probability_list": [0.6, 0.3, 0.1]}, "merge_probability": {"range_list": [[0, 0.1], [0.1, 0.15], [0.15, 0.2]], "probability_list": [0.5, 0.3, 0.2]}, "max_row_span": {"option_list": [2, 5, 8], "probability_list": [0.29, 0.7, 0.01]}, "max_col_span": {"option_list": [2, 5, 8], "probability_list": [0.29, 0.7, 0.01]}, "complex_header": null}, "content": {"source_type": "csv", "programmatic_types": ["date", "currency", "percentage"], "csv_source": {"file_path": null, "csv_dirs": ["assets/corpus/nl2sql_train/", "assets/corpus/wikisql_train/"], "csv_dir_probabilities": [0.5, 0.5], "sampling_mode": "random", "blank_control": {"trigger_probability": 0.01, "cell_blank_probability": 0.4}, "encoding": "utf-8", "mismatch_strategy": "truncate"}}, "style": {"common": {"font": {"font_dirs": ["/aipdf-mlp/jiacheng/code/text_render/example_data/font/common", "/aipdf-mlp/jiacheng/code/text_render/example_data/font/rare"], "font_dir_probabilities": [0.8, 0.2], "default_family": {"option_list": ["<PERSON><PERSON>", "Times New Roman", "Helvetica", "Calib<PERSON>", "<PERSON><PERSON><PERSON>"], "probability_list": [0.4, 0.25, 0.2, 0.1, 0.05]}, "default_size": {"range_list": [[12, 14], [15, 16], [10, 11], [17, 20]], "probability_list": [0.5, 0.3, 0.15, 0.05]}, "bold_probability": 0.1, "italic_probability": 0.02, "fallback_font": "Microsoft YaHei"}, "horizontal_align": {"option_list": ["left", "center", "right"], "probability_list": [0.25, 0.5, 0.25]}, "vertical_align": {"option_list": ["top", "middle", "bottom"], "probability_list": [0.25, 0.5, 0.25]}, "padding": {"range_list": [[1, 3], [4, 6], [7, 10]], "probability_list": [0.3, 0.4, 0.3]}, "randomize_color_probability": 0.2, "randomize_border_color_probability": 0.0, "merged_cell_center_probability": 1.0, "color_contrast": {"min_contrast_ratio": 4.5, "use_soft_colors_probability": 0.1}}, "inheritance": {"font_family_change_probability": 0.2, "font_size_change_probability": 0.2, "alignment_change_probability": 0.1, "padding_change_probability": 0.4, "text_color_change_probability": 0.2, "background_color_change_probability": 0.2}, "border_mode": {"mode_options": [{"probability": 0.5, "config": {"mode": "full"}}, {"probability": 0.2, "config": {"mode": "none"}}, {"probability": 0.3, "config": {"mode": "semi", "semi_config": {"row_line_probability": 0.7, "col_line_probability": 0.6, "outer_frame": true, "header_separator": true}}}]}, "zebra_stripes": 0.4, "sizing": {"default_row_height": "auto", "default_col_width": "auto", "row_configs": null, "col_configs": null, "row_height": null, "col_width": null}, "hierarchical": null, "overflow_strategy": "wrap"}, "seed": 42, "postprocessing": {"blur": {"probability": 0.2, "radius_range": [0.8, 2.5]}, "noise": {"probability": 0.25, "intensity_range": [3, 15]}, "perspective": {"probability": 0.2, "max_offset_ratio": 0.05, "range_list": [[0.01, 0.04], [0.04, 0.07], [0.07, 0.1]], "probability_list": [0.4, 0.4, 0.2], "content_area_shrink_ratio": 0.1, "adaptive_scaling": [[10.0, 1.0], [40.0, 0.1]], "decay_rate": 1.8}, "background": {"background_dirs": ["/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white", "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure", "/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/paper", "assets/transparent_ink_bgs/"], "background_dir_probabilities": [0.6, 0.1, 0.1, 0.2], "max_scale_factor": 3.0, "margin_control": {"range_list": [[10, 30], [30, 60], [60, 80], [80, 100]], "probability_list": [0.6, 0.25, 0.1, 0.05]}, "prefer_center_probability": 0.6}, "table_blending": null, "degradation_blur": null, "degradation_noise": null, "degradation_fade_global": null, "degradation_fade_local": null, "degradation_uneven_lighting": null, "degradation_jpeg": null, "degradation_darker_brighter": null, "degradation_gamma_correction": null}, "debug": null}, "sample_seed": 613608295, "sample_index": 17, "csv_sampling_info": {"source_file": "assets/corpus/nl2sql_train/0051rows_batch_001.csv", "selected_columns": [142, 22, 23, 21, 77, 0, 51, 5, 115, 14, 54, 138, 64, 30, 60, 101, 151, 146, 28, 124, 44, 17, 122, 41, 80, 24, 33, 119, 103, 18, 105], "selected_rows": [43, 36, 22, 31, 13, 27, 48, 35, 42, 29, 19, 11, 32], "csv_structure": {"total_columns": 153, "total_data_rows": 50}, "sampling_mode": "random"}}