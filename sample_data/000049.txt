2025-07-26 17:46:58,961 - table_render.main_generator - INFO - 正在生成第 6/6 个样本...
2025-07-26 17:46:58,972 - table_render.utils.csv_utils - INFO - 选择CSV文件: assets/corpus/nl2sql_train/0101rows_batch_018.csv (来自目录: assets/corpus/nl2sql_train/)
2025-07-26 17:46:58,972 - table_render.resolver - WARNING - 样式冲突：表头和表体使用了相同的颜色组合 (文本色: #000000, 背景色: #FFFFFF)
2025-07-26 17:46:58,972 - table_render.resolver - INFO - 开始选择背景图，目录: ['/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure_white', '/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure', '/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/paper', 'assets/transparent_ink_bgs/']
2025-07-26 17:46:58,985 - table_render.resolver - INFO - 成功选择背景图: /aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure/color_30_5_doc_bg.jpg
2025-07-26 17:46:58,985 - table_render.resolver - INFO - [CSS_DEBUG] 开始计算CSS参数，背景图路径: /aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure/color_30_5_doc_bg.jpg
2025-07-26 17:46:58,985 - table_render.resolver - ERROR - [CSS_DEBUG] CSS参数计算失败: name 'config' is not defined
2025-07-26 17:46:58,985 - table_render.resolver - INFO - [CSS_DEBUG] 使用默认CSS参数，背景图路径保持: /aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure/color_30_5_doc_bg.jpg
2025-07-26 17:46:58,985 - table_render.resolver - INFO - [MARGIN_CONTROL] 边距控制配置已加载: 4 个选项
2025-07-26 17:46:58,985 - table_render.resolver - INFO - [CSS_DEBUG] CSS渲染模式参数:
2025-07-26 17:46:58,985 - table_render.resolver - INFO - [CSS_DEBUG]   背景图: color_30_5_doc_bg.jpg
2025-07-26 17:46:58,985 - table_render.resolver - INFO - [CSS_DEBUG]   输出尺寸: 1000x600
2025-07-26 17:46:58,985 - table_render.resolver - INFO - [CSS_DEBUG]   表格位置: (200, 150)
2025-07-26 17:46:58,985 - table_render.main_generator - INFO - [LARGE_TABLE] 检测到大表格: 大表格: 32行 x 13列 (416个单元格)
2025-07-26 17:46:58,992 - table_render.builders.content_builder - INFO - 开始CSV内容填充，模式: random, 文件: assets/corpus/nl2sql_train/0101rows_batch_018.csv
2025-07-26 17:46:58,995 - table_render.builders.style_builder - INFO - 生成sizing规则，行高配置: {}
2025-07-26 17:46:58,995 - table_render.builders.style_builder - INFO - 生成sizing规则，列宽配置: {}
2025-07-26 17:46:58,995 - table_render.builders.style_builder - WARNING - 没有特定的行高或列宽配置，只生成默认规则
2025-07-26 17:46:59,000 - table_render.renderers.html_renderer - INFO - [CSS_GEN_DEBUG] 生成CSS背景图样式
2025-07-26 17:46:59,000 - table_render.renderers.html_renderer - INFO - 生成背景CSS，路径: /aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure/color_30_5_doc_bg.jpg
2025-07-26 17:46:59,000 - table_render.renderers.html_renderer - INFO - [OFFSET_DEBUG] 背景图覆盖率: 100.0% (1000x600/1000x600)
2025-07-26 17:46:59,000 - table_render.renderers.html_renderer - INFO - [BG_PATH_DEBUG] 开始检查背景图路径: '/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure/color_30_5_doc_bg.jpg'
2025-07-26 17:46:59,000 - table_render.renderers.html_renderer - INFO - [BG_PATH_DEBUG] 路径类型: <class 'str'>
2025-07-26 17:46:59,000 - table_render.renderers.html_renderer - INFO - [BG_PATH_DEBUG] 路径长度: 91
2025-07-26 17:46:59,000 - table_render.renderers.html_renderer - INFO - [BG_PATH_DEBUG] os.path.exists('/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure/color_30_5_doc_bg.jpg'): True
2025-07-26 17:46:59,000 - table_render.renderers.html_renderer - INFO - [BG_PATH_DEBUG] os.path.isfile('/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure/color_30_5_doc_bg.jpg'): True
2025-07-26 17:46:59,000 - table_render.renderers.html_renderer - INFO - [BG_PATH_DEBUG] os.path.isdir('/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure/color_30_5_doc_bg.jpg'): False
2025-07-26 17:46:59,201 - table_render.renderers.html_renderer - INFO - [HTTP_SERVER_DEBUG] 启动HTTP服务器: http://localhost:47525/color_30_5_doc_bg.jpg
2025-07-26 17:46:59,201 - table_render.renderers.html_renderer - INFO - [HTTP_SERVER_DEBUG] 服务器根目录: /aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure
2025-07-26 17:46:59,201 - table_render.renderers.html_renderer - INFO - [HTTP_SERVER_DEBUG] 请求文件名: color_30_5_doc_bg.jpg
2025-07-26 17:46:59,201 - table_render.renderers.html_renderer - INFO - [HTTP_SERVER_DEBUG] 完整文件路径: /aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure/color_30_5_doc_bg.jpg
2025-07-26 17:46:59,201 - table_render.renderers.html_renderer - INFO - [HTTP_SERVER_DEBUG] ✓ 源文件存在 (大小: 33270 bytes)
2025-07-26 17:46:59,201 - table_render.renderers.html_renderer - INFO - [HTTP_SERVER_DEBUG] 服务器目录中的文件数量: 62
2025-07-26 17:46:59,201 - table_render.renderers.html_renderer - INFO - [HTTP_SERVER_DEBUG] ✓ 目标文件在服务器目录中: color_30_5_doc_bg.jpg
2025-07-26 17:46:59,201 - table_render.renderers.html_renderer - INFO - [BG_PATH_DEBUG] 使用HTTP服务器提供背景图: http://localhost:47525/color_30_5_doc_bg.jpg
2025-07-26 17:46:59,202 - table_render.renderers.html_renderer - INFO - [CSS_FINAL_DEBUG] 背景CSS生成完成
2025-07-26 17:46:59,202 - table_render.renderers.html_renderer - INFO - [CSS_FINAL_DEBUG] 背景图URL: http://localhost:47525/color_30_5_doc_bg.jpg
2025-07-26 17:46:59,202 - table_render.renderers.html_renderer - INFO - [CSS_FINAL_DEBUG] 背景尺寸: 1200x800, 位置: (-50, -50)
2025-07-26 17:46:59,202 - table_render.renderers.html_renderer - INFO - [CSS_TABLE_POS] 表格位置参数:
2025-07-26 17:46:59,202 - table_render.renderers.html_renderer - INFO - [CSS_TABLE_POS]   css_table_left (原始): 200
2025-07-26 17:46:59,202 - table_render.renderers.html_renderer - INFO - [CSS_TABLE_POS]   css_table_top (原始): 150
2025-07-26 17:46:59,202 - table_render.renderers.html_renderer - INFO - [CSS_TABLE_POS]   table_left (使用): 200
2025-07-26 17:46:59,202 - table_render.renderers.html_renderer - INFO - [CSS_TABLE_POS]   table_top (使用): 150
2025-07-26 17:46:59,202 - table_render.renderers.html_renderer - INFO - [CSS_GEN_DEBUG] CSS背景图样式生成完成
2025-07-26 17:46:59,225 - table_render.renderers.html_renderer - INFO - [HTML_DEBUG] 接收到背景参数:
2025-07-26 17:46:59,225 - table_render.renderers.html_renderer - INFO - [HTML_DEBUG]   apply_background: True
2025-07-26 17:46:59,225 - table_render.renderers.html_renderer - INFO - [HTML_DEBUG]   background_image_path: /aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/pure/color_30_5_doc_bg.jpg
2025-07-26 17:46:59,225 - table_render.renderers.html_renderer - INFO - [HTML_DEBUG]   css_crop_width: 1000
2025-07-26 17:46:59,225 - table_render.renderers.html_renderer - INFO - [HTML_DEBUG]   css_crop_height: 600
2025-07-26 17:46:59,225 - table_render.renderers.html_renderer - INFO - [LARGE_TABLE_VIEWPORT] 检测到大表格渲染需求
2025-07-26 17:46:59,225 - table_render.renderers.html_renderer - INFO - [LARGE_TABLE_VIEWPORT] 表格估算尺寸: 2590x1505
2025-07-26 17:46:59,225 - table_render.renderers.html_renderer - INFO - [LARGE_TABLE_VIEWPORT] 最终视口尺寸: 2790x1705
2025-07-26 17:46:59,226 - table_render.renderers.html_renderer - INFO - [HTML_DEBUG] CSS模式动态视口尺寸: 2790x1705
2025-07-26 17:46:59,265 - table_render.renderers.html_renderer - INFO - [LOAD_DEBUG] 等待背景图加载完成...
2025-07-26 17:47:00,772 - table_render.renderers.html_renderer - INFO - [LOAD_DEBUG] ✓ 背景图CSS已应用
2025-07-26 17:47:00,772 - table_render.renderers.html_renderer - INFO - [LOAD_DEBUG] 背景图加载等待完成
2025-07-26 17:47:01,178 - table_render.renderers.html_renderer - INFO - [CSS_COORD_FIX] 使用JavaScript获取的实际表格位置: (220, 170)
2025-07-26 17:47:01,178 - table_render.renderers.html_renderer - INFO - [CSS_COORD_FIX] 检测到CSS变换影响:
2025-07-26 17:47:01,178 - table_render.renderers.html_renderer - INFO - [CSS_COORD_FIX]   CSS设置位置: (200, 150)
2025-07-26 17:47:01,178 - table_render.renderers.html_renderer - INFO - [CSS_COORD_FIX]   实际视觉位置: (220, 170)
2025-07-26 17:47:01,178 - table_render.renderers.html_renderer - INFO - [CSS_COORD_FIX]   变换偏移: (20, 20)
2025-07-26 17:47:01,254 - table_render.renderers.html_renderer - INFO - [HTTP_SERVER_DEBUG] HTTP服务器已停止
2025-07-26 17:47:01,264 - table_render.main_generator - INFO - [MARGIN_CONTROL] CSS模式：开始强制margin_control裁剪
2025-07-26 17:47:01,264 - table_render.main_generator - INFO - [MARGIN_CONTROL] 原始图像尺寸: (2790, 1705)
2025-07-26 17:47:01,264 - table_render.main_generator - INFO - [MARGIN_CONTROL] 执行margin_control配置，无黑边信息
2025-07-26 17:47:01,264 - table_render.main_generator - INFO - [MARGIN_CONTROL_CROP] 表格真实边界: {'min_x': 220.5, 'min_y': 170.5, 'max_x': 2769.5, 'max_y': 1994.5}
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [TABLE_SAFE_AREA] 表格边界: min_x=220.5, min_y=170.5, max_x=2769.5, max_y=1994.5
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [TABLE_SAFE_AREA] 安全边距: 10像素
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [TABLE_SAFE_AREA] 最终安全区域: (210, 160, 2779, 1705)
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [MARGIN_CONTROL_CROP] 表格安全区域: (210, 160, 2779, 1705)
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [LARGE_TABLE_MARGIN] 大表格相对边距计算
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [LARGE_TABLE_MARGIN] 表格尺寸: 2549x1824
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [LARGE_TABLE_MARGIN] 基础边距: 80, 相对边距: 273.59999999999997
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [MARGIN_SELECT] 选择范围: [80, 100], 最终边距: 273.59999999999997
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [MARGIN_CONTROL_CROP] 选择边距: 273.59999999999997
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [MARGIN_CONTROL_CROP] margin期望区域: (0, 0, 2790, 1705)
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [FORCE_TABLE_SAFETY] 原margin区域: (0, 0, 2790, 1705)
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [FORCE_TABLE_SAFETY] 表格安全区域: (210, 160, 2779, 1705)
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [FORCE_TABLE_SAFETY] 强制包含后区域: (0, 0, 2790, 1705)
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [MARGIN_CONTROL_CROP] 安全保护后区域: (0, 0, 2790, 1705)
2025-07-26 17:47:01,265 - table_render.main_generator - INFO - [LARGE_TABLE_SAFETY] 检测到大表格: 2569x1545
2025-07-26 17:47:01,266 - table_render.main_generator - INFO - [LARGE_TABLE_SAFETY] 应用额外安全边距: 10px
2025-07-26 17:47:01,266 - table_render.main_generator - ERROR - [LARGE_TABLE_SAFETY] 检测到大表格安全区域可能被裁剪！强制修正
2025-07-26 17:47:01,266 - table_render.main_generator - ERROR - [LARGE_TABLE_SAFETY] 原区域: (0, 0, 2790, 1705)
2025-07-26 17:47:01,266 - table_render.main_generator - ERROR - [LARGE_TABLE_SAFETY] 表格安全区域: (210, 160, 2779, 1705)
2025-07-26 17:47:01,266 - table_render.main_generator - ERROR - [LARGE_TABLE_SAFETY] 修正后区域: (0, 0, 2790, 1705)
2025-07-26 17:47:01,266 - table_render.main_generator - INFO - [MARGIN_CONTROL_CROP] 最终裁剪区域: (0, 0, 2790, 1705)
2025-07-26 17:47:01,266 - table_render.main_generator - INFO - [MARGIN_CONTROL] 最终裁剪区域: (0, 0, 2790, 1705)
2025-07-26 17:47:01,346 - table_render.main_generator - INFO - [MARGIN_CONTROL] 裁剪后尺寸: (2790, 1705)
2025-07-26 17:47:01,623 - table_render.main_generator - INFO - [LARGE_TABLE_PERF] 样本 6 完成
2025-07-26 17:47:01,624 - table_render.main_generator - INFO - [LARGE_TABLE_PERF] 耗时: 2.66秒
2025-07-26 17:47:01,624 - table_render.main_generator - INFO - [LARGE_TABLE_PERF] 内存变化: -19.12MB
2025-07-26 17:47:01,624 - table_render.main_generator - INFO - 样本 6/6 生成完成 (耗时: 2.66秒)